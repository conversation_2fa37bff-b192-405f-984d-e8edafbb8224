{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-searchbar.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, l as config, n as forceUpdate, e as getIonMode, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { d as debounceEvent, b as inheritAttributes, c as componentOnReady, r as raf } from './helpers-1O4D2b7y.js';\nimport { i as isRTL } from './dir-C53feagD.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\nimport { a as arrowBackSharp, s as searchOutline, e as searchSharp, b as closeCircle, d as closeSharp } from './index-BLV6ykCk.js';\n\nconst searchbarIosCss = \".sc-ion-searchbar-ios-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-searchbar-ios-h{color:var(--ion-color-contrast)}.ion-color.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{background:var(--ion-color-base)}.ion-color.sc-ion-searchbar-ios-h .searchbar-clear-button.sc-ion-searchbar-ios,.ion-color.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.ion-color.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios{color:inherit}.searchbar-search-icon.sc-ion-searchbar-ios{color:var(--icon-color);pointer-events:none}.searchbar-input-container.sc-ion-searchbar-ios{display:block;position:relative;-ms-flex-negative:1;flex-shrink:1;width:100%}.searchbar-input.sc-ion-searchbar-ios{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;border-radius:var(--border-radius);display:block;width:100%;min-height:inherit;border:0;outline:none;background:var(--background);font-family:inherit;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-input.sc-ion-searchbar-ios::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::-webkit-search-cancel-button,.searchbar-input.sc-ion-searchbar-ios::-ms-clear{display:none}.searchbar-cancel-button.sc-ion-searchbar-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:none;height:100%;border:0;outline:none;color:var(--cancel-button-color);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-cancel-button.sc-ion-searchbar-ios>div.sc-ion-searchbar-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.searchbar-clear-button.sc-ion-searchbar-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:none;min-height:0;outline:none;color:var(--clear-button-color);-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-clear-button.sc-ion-searchbar-ios:focus{opacity:0.5}.searchbar-has-value.searchbar-should-show-clear.sc-ion-searchbar-ios-h .searchbar-clear-button.sc-ion-searchbar-ios{display:block}.searchbar-disabled.sc-ion-searchbar-ios-h{cursor:default;opacity:0.4;pointer-events:none}.sc-ion-searchbar-ios-h{--background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.07);--border-radius:10px;--box-shadow:none;--cancel-button-color:var(--ion-color-primary, #0054e9);--clear-button-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));--color:var(--ion-text-color, #000);--icon-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:12px;padding-bottom:12px;min-height:60px;contain:content}.searchbar-input-container.sc-ion-searchbar-ios{min-height:36px}.searchbar-search-icon.sc-ion-searchbar-ios{-webkit-margin-start:calc(50% - 60px);margin-inline-start:calc(50% - 60px);top:0;position:absolute;width:1.375rem;height:100%;contain:strict}.searchbar-search-icon.sc-ion-searchbar-ios{inset-inline-start:5px}.searchbar-input.sc-ion-searchbar-ios{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:6px;padding-bottom:6px;height:100%;font-size:1.0625rem;font-weight:400;contain:strict}.searchbar-has-value.searchbar-should-show-clear.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{-webkit-padding-start:1.75rem;padding-inline-start:1.75rem;-webkit-padding-end:1.75rem;padding-inline-end:1.75rem}.searchbar-clear-button.sc-ion-searchbar-ios{top:0;background-position:center;position:absolute;width:1.875rem;height:100%;border:0;background-color:transparent}.searchbar-clear-button.sc-ion-searchbar-ios{inset-inline-end:0}.searchbar-clear-icon.sc-ion-searchbar-ios{width:1.125rem;height:100%}.searchbar-cancel-button.sc-ion-searchbar-ios{-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0;-ms-flex-negative:0;flex-shrink:0;background-color:transparent;font-size:17px}.searchbar-left-aligned.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios{-webkit-margin-start:0;margin-inline-start:0}.searchbar-left-aligned.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{-webkit-padding-start:1.875rem;padding-inline-start:1.875rem}.searchbar-has-focus.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.searchbar-should-show-cancel.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{display:block}.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios,.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{-webkit-transition:all 300ms ease;transition:all 300ms ease}.searchbar-animated.searchbar-has-focus.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.searchbar-animated.searchbar-should-show-cancel.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{opacity:1;pointer-events:auto}.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{-webkit-margin-end:-100%;margin-inline-end:-100%;-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0);-webkit-transition:all 300ms ease;transition:all 300ms ease;opacity:0;pointer-events:none}.searchbar-no-animate.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios,.searchbar-no-animate.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios,.searchbar-no-animate.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{-webkit-transition-duration:0ms;transition-duration:0ms}.ion-color.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{color:var(--ion-color-base)}@media (any-hover: hover){.ion-color.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios:hover{color:var(--ion-color-tint)}}ion-toolbar.sc-ion-searchbar-ios-h,ion-toolbar .sc-ion-searchbar-ios-h{padding-top:1px;padding-bottom:15px;min-height:52px}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color),ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color){color:inherit}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-cancel-button.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-cancel-button.sc-ion-searchbar-ios{color:currentColor}ion-toolbar.ion-color.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios{color:currentColor;opacity:0.5}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-input.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-input.sc-ion-searchbar-ios{background:rgba(var(--ion-color-contrast-rgb), 0.07);color:currentColor}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-clear-button.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-clear-button.sc-ion-searchbar-ios{color:currentColor;opacity:0.5}\";\n\nconst searchbarMdCss = \".sc-ion-searchbar-md-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-searchbar-md-h{color:var(--ion-color-contrast)}.ion-color.sc-ion-searchbar-md-h .searchbar-input.sc-ion-searchbar-md{background:var(--ion-color-base)}.ion-color.sc-ion-searchbar-md-h .searchbar-clear-button.sc-ion-searchbar-md,.ion-color.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md,.ion-color.sc-ion-searchbar-md-h .searchbar-search-icon.sc-ion-searchbar-md{color:inherit}.searchbar-search-icon.sc-ion-searchbar-md{color:var(--icon-color);pointer-events:none}.searchbar-input-container.sc-ion-searchbar-md{display:block;position:relative;-ms-flex-negative:1;flex-shrink:1;width:100%}.searchbar-input.sc-ion-searchbar-md{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;border-radius:var(--border-radius);display:block;width:100%;min-height:inherit;border:0;outline:none;background:var(--background);font-family:inherit;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-input.sc-ion-searchbar-md::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::-webkit-search-cancel-button,.searchbar-input.sc-ion-searchbar-md::-ms-clear{display:none}.searchbar-cancel-button.sc-ion-searchbar-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:none;height:100%;border:0;outline:none;color:var(--cancel-button-color);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-cancel-button.sc-ion-searchbar-md>div.sc-ion-searchbar-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.searchbar-clear-button.sc-ion-searchbar-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:none;min-height:0;outline:none;color:var(--clear-button-color);-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-clear-button.sc-ion-searchbar-md:focus{opacity:0.5}.searchbar-has-value.searchbar-should-show-clear.sc-ion-searchbar-md-h .searchbar-clear-button.sc-ion-searchbar-md{display:block}.searchbar-disabled.sc-ion-searchbar-md-h{cursor:default;opacity:0.4;pointer-events:none}.sc-ion-searchbar-md-h{--background:var(--ion-background-color, #fff);--border-radius:2px;--box-shadow:0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);--cancel-button-color:var(--ion-color-step-900, var(--ion-text-color-step-100, #1a1a1a));--clear-button-color:initial;--color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));--icon-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;background:inherit}.searchbar-search-icon.sc-ion-searchbar-md{top:11px;width:1.3125rem;height:1.3125rem}.searchbar-search-icon.sc-ion-searchbar-md{inset-inline-start:16px}.searchbar-cancel-button.sc-ion-searchbar-md{top:0;background-color:transparent;font-size:1.5em}.searchbar-cancel-button.sc-ion-searchbar-md{inset-inline-start:9px}.searchbar-search-icon.sc-ion-searchbar-md,.searchbar-cancel-button.sc-ion-searchbar-md{position:absolute}.searchbar-search-icon.ion-activated.sc-ion-searchbar-md,.searchbar-cancel-button.ion-activated.sc-ion-searchbar-md{background-color:transparent}.searchbar-input.sc-ion-searchbar-md{-webkit-padding-start:3.4375rem;padding-inline-start:3.4375rem;-webkit-padding-end:3.4375rem;padding-inline-end:3.4375rem;padding-top:0.375rem;padding-bottom:0.375rem;background-position:left 8px center;height:auto;font-size:1rem;font-weight:400;line-height:30px}[dir=rtl].sc-ion-searchbar-md-h .searchbar-input.sc-ion-searchbar-md,[dir=rtl] .sc-ion-searchbar-md-h .searchbar-input.sc-ion-searchbar-md{background-position:right 8px center}[dir=rtl].sc-ion-searchbar-md .searchbar-input.sc-ion-searchbar-md{background-position:right 8px center}@supports selector(:dir(rtl)){.searchbar-input.sc-ion-searchbar-md:dir(rtl){background-position:right 8px center}}.searchbar-clear-button.sc-ion-searchbar-md{top:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;position:absolute;height:100%;border:0;background-color:transparent}.searchbar-clear-button.sc-ion-searchbar-md{inset-inline-end:13px}.searchbar-clear-button.ion-activated.sc-ion-searchbar-md{background-color:transparent}.searchbar-clear-icon.sc-ion-searchbar-md{width:1.375rem;height:100%}.searchbar-has-focus.sc-ion-searchbar-md-h .searchbar-search-icon.sc-ion-searchbar-md{display:block}.searchbar-has-focus.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md,.searchbar-should-show-cancel.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md{display:block}.searchbar-has-focus.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md+.searchbar-search-icon.sc-ion-searchbar-md,.searchbar-should-show-cancel.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md+.searchbar-search-icon.sc-ion-searchbar-md{display:none}ion-toolbar.sc-ion-searchbar-md-h,ion-toolbar .sc-ion-searchbar-md-h{-webkit-padding-start:7px;padding-inline-start:7px;-webkit-padding-end:7px;padding-inline-end:7px;padding-top:3px;padding-bottom:3px}\";\n\nconst Searchbar = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionInput = createEvent(this, \"ionInput\", 7);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionCancel = createEvent(this, \"ionCancel\", 7);\n        this.ionClear = createEvent(this, \"ionClear\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.isCancelVisible = false;\n        this.shouldAlignLeft = true;\n        this.inputId = `ion-searchbar-${searchbarIds++}`;\n        this.inheritedAttributes = {};\n        this.focused = false;\n        this.noAnimate = true;\n        /**\n         * If `true`, enable searchbar animation.\n         */\n        this.animated = false;\n        /**\n         * Indicates whether and how the text value should be automatically capitalized as it is entered/edited by the user.\n         * Available options: `\"off\"`, `\"none\"`, `\"on\"`, `\"sentences\"`, `\"words\"`, `\"characters\"`.\n         */\n        this.autocapitalize = 'off';\n        /**\n         * Set the input's autocomplete property.\n         */\n        this.autocomplete = 'off';\n        /**\n         * Set the input's autocorrect property.\n         */\n        this.autocorrect = 'off';\n        /**\n         * Set the cancel button icon. Only applies to `md` mode.\n         * Defaults to `arrow-back-sharp`.\n         */\n        this.cancelButtonIcon = config.get('backButtonIcon', arrowBackSharp);\n        /**\n         * Set the cancel button text. Only applies to `ios` mode.\n         */\n        this.cancelButtonText = 'Cancel';\n        /**\n         * If `true`, the user cannot interact with the input.\n         */\n        this.disabled = false;\n        /**\n         * If used in a form, set the name of the control, which is submitted with the form data.\n         */\n        this.name = this.inputId;\n        /**\n         * Set the input's placeholder.\n         * `placeholder` can accept either plaintext or HTML as a string.\n         * To display characters normally reserved for HTML, they\n         * must be escaped. For example `<Ionic>` would become\n         * `&lt;Ionic&gt;`\n         *\n         * For more information: [Security Documentation](https://ionicframework.com/docs/faq/security)\n         */\n        this.placeholder = 'Search';\n        /**\n         * Sets the behavior for the cancel button. Defaults to `\"never\"`.\n         * Setting to `\"focus\"` shows the cancel button on focus.\n         * Setting to `\"never\"` hides the cancel button.\n         * Setting to `\"always\"` shows the cancel button regardless\n         * of focus state.\n         */\n        this.showCancelButton = 'never';\n        /**\n         * Sets the behavior for the clear button. Defaults to `\"focus\"`.\n         * Setting to `\"focus\"` shows the clear button on focus if the\n         * input is not empty.\n         * Setting to `\"never\"` hides the clear button.\n         * Setting to `\"always\"` shows the clear button regardless\n         * of focus state, but only if the input is not empty.\n         */\n        this.showClearButton = 'always';\n        /**\n         * If `true`, enable spellcheck on the input.\n         */\n        this.spellcheck = false;\n        /**\n         * Set the type of the input.\n         */\n        this.type = 'search';\n        /**\n         * the value of the searchbar.\n         */\n        this.value = '';\n        /**\n         * Clears the input field and triggers the control change.\n         */\n        this.onClearInput = async (shouldFocus) => {\n            this.ionClear.emit();\n            return new Promise((resolve) => {\n                // setTimeout() fixes https://github.com/ionic-team/ionic-framework/issues/7527\n                // wait for 4 frames\n                setTimeout(() => {\n                    const value = this.getValue();\n                    if (value !== '') {\n                        this.value = '';\n                        this.emitInputChange();\n                        /**\n                         * When tapping clear button\n                         * ensure input is focused after\n                         * clearing input so users\n                         * can quickly start typing.\n                         */\n                        if (shouldFocus && !this.focused) {\n                            this.setFocus();\n                            /**\n                             * The setFocus call above will clear focusedValue,\n                             * but ionChange will never have gotten a chance to\n                             * fire. Manually revert focusedValue so onBlur can\n                             * compare against what was in the box before the clear.\n                             */\n                            this.focusedValue = value;\n                        }\n                    }\n                    resolve();\n                }, 16 * 4);\n            });\n        };\n        /**\n         * Clears the input field and tells the input to blur since\n         * the clearInput function doesn't want the input to blur\n         * then calls the custom cancel function if the user passed one in.\n         */\n        this.onCancelSearchbar = async (ev) => {\n            if (ev) {\n                ev.preventDefault();\n                ev.stopPropagation();\n            }\n            this.ionCancel.emit();\n            // get cached values before clearing the input\n            const value = this.getValue();\n            const focused = this.focused;\n            await this.onClearInput();\n            /**\n             * If there used to be something in the box, and we weren't focused\n             * beforehand (meaning no blur fired that would already handle this),\n             * manually fire ionChange.\n             */\n            if (value && !focused) {\n                this.emitValueChange(ev);\n            }\n            if (this.nativeInput) {\n                this.nativeInput.blur();\n            }\n        };\n        /**\n         * Update the Searchbar input value when the input changes\n         */\n        this.onInput = (ev) => {\n            const input = ev.target;\n            if (input) {\n                this.value = input.value;\n            }\n            this.emitInputChange(ev);\n        };\n        this.onChange = (ev) => {\n            this.emitValueChange(ev);\n        };\n        /**\n         * Sets the Searchbar to not focused and checks if it should align left\n         * based on whether there is a value in the searchbar or not.\n         */\n        this.onBlur = (ev) => {\n            this.focused = false;\n            this.ionBlur.emit();\n            this.positionElements();\n            if (this.focusedValue !== this.value) {\n                this.emitValueChange(ev);\n            }\n            this.focusedValue = undefined;\n        };\n        /**\n         * Sets the Searchbar to focused and active on input focus.\n         */\n        this.onFocus = () => {\n            this.focused = true;\n            this.focusedValue = this.value;\n            this.ionFocus.emit();\n            this.positionElements();\n        };\n    }\n    /**\n     * lang and dir are globally enumerated attributes.\n     * As a result, creating these as properties\n     * can have unintended side effects. Instead, we\n     * listen for attribute changes and inherit them\n     * to the inner `<input>` element.\n     */\n    onLangChanged(newValue) {\n        this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), { lang: newValue });\n        forceUpdate(this);\n    }\n    onDirChanged(newValue) {\n        this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), { dir: newValue });\n        forceUpdate(this);\n    }\n    debounceChanged() {\n        const { ionInput, debounce, originalIonInput } = this;\n        /**\n         * If debounce is undefined, we have to manually revert the ionInput emitter in case\n         * debounce used to be set to a number. Otherwise, the event would stay debounced.\n         */\n        this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n    }\n    valueChanged() {\n        const inputEl = this.nativeInput;\n        const value = this.getValue();\n        if (inputEl && inputEl.value !== value) {\n            inputEl.value = value;\n        }\n    }\n    showCancelButtonChanged() {\n        requestAnimationFrame(() => {\n            this.positionElements();\n            forceUpdate(this);\n        });\n    }\n    connectedCallback() {\n        this.emitStyle();\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = Object.assign({}, inheritAttributes(this.el, ['lang', 'dir']));\n    }\n    componentDidLoad() {\n        this.originalIonInput = this.ionInput;\n        this.positionElements();\n        this.debounceChanged();\n        setTimeout(() => {\n            this.noAnimate = false;\n        }, 300);\n    }\n    emitStyle() {\n        this.ionStyle.emit({\n            searchbar: true,\n        });\n    }\n    /**\n     * Sets focus on the native `input` in `ion-searchbar`. Use this method instead of the global\n     * `input.focus()`.\n     * Developers who wish to focus an input when a page enters\n     * should call `setFocus()` in the `ionViewDidEnter()` lifecycle method.\n     * Developers who wish to focus an input when an overlay is presented\n     * should call `setFocus` after `didPresent` has resolved.\n     *\n     * See [managing focus](/docs/developing/managing-focus) for more information.\n     */\n    async setFocus() {\n        if (this.nativeInput) {\n            this.nativeInput.focus();\n        }\n    }\n    /**\n     * Returns the native `<input>` element used under the hood.\n     */\n    async getInputElement() {\n        /**\n         * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n         * nativeInput won't be defined yet with the custom elements build, so wait for it to load in.\n         */\n        if (!this.nativeInput) {\n            await new Promise((resolve) => componentOnReady(this.el, resolve));\n        }\n        return Promise.resolve(this.nativeInput);\n    }\n    /**\n     * Emits an `ionChange` event.\n     *\n     * This API should be called for user committed changes.\n     * This API should not be used for external value changes.\n     */\n    emitValueChange(event) {\n        const { value } = this;\n        // Checks for both null and undefined values\n        const newValue = value == null ? value : value.toString();\n        // Emitting a value change should update the internal state for tracking the focused value\n        this.focusedValue = newValue;\n        this.ionChange.emit({ value: newValue, event });\n    }\n    /**\n     * Emits an `ionInput` event.\n     */\n    emitInputChange(event) {\n        const { value } = this;\n        this.ionInput.emit({ value, event });\n    }\n    /**\n     * Positions the input search icon, placeholder, and the cancel button\n     * based on the input value and if it is focused. (ios only)\n     */\n    positionElements() {\n        const value = this.getValue();\n        const prevAlignLeft = this.shouldAlignLeft;\n        const mode = getIonMode(this);\n        const shouldAlignLeft = !this.animated || value.trim() !== '' || !!this.focused;\n        this.shouldAlignLeft = shouldAlignLeft;\n        if (mode !== 'ios') {\n            return;\n        }\n        if (prevAlignLeft !== shouldAlignLeft) {\n            this.positionPlaceholder();\n        }\n        if (this.animated) {\n            this.positionCancelButton();\n        }\n    }\n    /**\n     * Positions the input placeholder\n     */\n    positionPlaceholder() {\n        const inputEl = this.nativeInput;\n        if (!inputEl) {\n            return;\n        }\n        const rtl = isRTL(this.el);\n        const iconEl = (this.el.shadowRoot || this.el).querySelector('.searchbar-search-icon');\n        if (this.shouldAlignLeft) {\n            inputEl.removeAttribute('style');\n            iconEl.removeAttribute('style');\n        }\n        else {\n            // Create a dummy span to get the placeholder width\n            const doc = document;\n            const tempSpan = doc.createElement('span');\n            tempSpan.innerText = this.placeholder || '';\n            doc.body.appendChild(tempSpan);\n            // Get the width of the span then remove it\n            raf(() => {\n                const textWidth = tempSpan.offsetWidth;\n                tempSpan.remove();\n                // Calculate the input padding\n                const inputLeft = 'calc(50% - ' + textWidth / 2 + 'px)';\n                // Calculate the icon margin\n                /**\n                 * We take the icon width to account\n                 * for any text scales applied to the icon\n                 * such as Dynamic Type on iOS as well as 8px\n                 * of padding.\n                 */\n                const iconLeft = 'calc(50% - ' + (textWidth / 2 + iconEl.clientWidth + 8) + 'px)';\n                // Set the input padding start and icon margin start\n                if (rtl) {\n                    inputEl.style.paddingRight = inputLeft;\n                    iconEl.style.marginRight = iconLeft;\n                }\n                else {\n                    inputEl.style.paddingLeft = inputLeft;\n                    iconEl.style.marginLeft = iconLeft;\n                }\n            });\n        }\n    }\n    /**\n     * Show the iOS Cancel button on focus, hide it offscreen otherwise\n     */\n    positionCancelButton() {\n        const rtl = isRTL(this.el);\n        const cancelButton = (this.el.shadowRoot || this.el).querySelector('.searchbar-cancel-button');\n        const shouldShowCancel = this.shouldShowCancelButton();\n        if (cancelButton !== null && shouldShowCancel !== this.isCancelVisible) {\n            const cancelStyle = cancelButton.style;\n            this.isCancelVisible = shouldShowCancel;\n            if (shouldShowCancel) {\n                if (rtl) {\n                    cancelStyle.marginLeft = '0';\n                }\n                else {\n                    cancelStyle.marginRight = '0';\n                }\n            }\n            else {\n                const offset = cancelButton.offsetWidth;\n                if (offset > 0) {\n                    if (rtl) {\n                        cancelStyle.marginLeft = -offset + 'px';\n                    }\n                    else {\n                        cancelStyle.marginRight = -offset + 'px';\n                    }\n                }\n            }\n        }\n    }\n    getValue() {\n        return this.value || '';\n    }\n    hasValue() {\n        return this.getValue() !== '';\n    }\n    /**\n     * Determines whether or not the cancel button should be visible onscreen.\n     * Cancel button should be shown if one of two conditions applies:\n     * 1. `showCancelButton` is set to `always`.\n     * 2. `showCancelButton` is set to `focus`, and the searchbar has been focused.\n     */\n    shouldShowCancelButton() {\n        if (this.showCancelButton === 'never' || (this.showCancelButton === 'focus' && !this.focused)) {\n            return false;\n        }\n        return true;\n    }\n    /**\n     * Determines whether or not the clear button should be visible onscreen.\n     * Clear button should be shown if one of two conditions applies:\n     * 1. `showClearButton` is set to `always`.\n     * 2. `showClearButton` is set to `focus`, and the searchbar has been focused.\n     */\n    shouldShowClearButton() {\n        if (this.showClearButton === 'never' || (this.showClearButton === 'focus' && !this.focused)) {\n            return false;\n        }\n        return true;\n    }\n    render() {\n        const { cancelButtonText, autocapitalize } = this;\n        const animated = this.animated && config.getBoolean('animated', true);\n        const mode = getIonMode(this);\n        const clearIcon = this.clearIcon || (mode === 'ios' ? closeCircle : closeSharp);\n        const searchIcon = this.searchIcon || (mode === 'ios' ? searchOutline : searchSharp);\n        const shouldShowCancelButton = this.shouldShowCancelButton();\n        const cancelButton = this.showCancelButton !== 'never' && (h(\"button\", { key: '19e18775856db87daeb4b9e3d7bca0461915a0df', \"aria-label\": cancelButtonText, \"aria-hidden\": shouldShowCancelButton ? undefined : 'true', type: \"button\", tabIndex: mode === 'ios' && !shouldShowCancelButton ? -1 : undefined, onMouseDown: this.onCancelSearchbar, onTouchStart: this.onCancelSearchbar, class: \"searchbar-cancel-button\" }, h(\"div\", { key: 'b3bbdcc033f3bd3441d619e4a252cef0dad4d07e', \"aria-hidden\": \"true\" }, mode === 'md' ? (h(\"ion-icon\", { \"aria-hidden\": \"true\", mode: mode, icon: this.cancelButtonIcon, lazy: false })) : (cancelButtonText))));\n        return (h(Host, { key: '074aa60e051bfb3225e87d44bbb6346c59c73574', role: \"search\", \"aria-disabled\": this.disabled ? 'true' : null, class: createColorClasses(this.color, {\n                [mode]: true,\n                'searchbar-animated': animated,\n                'searchbar-disabled': this.disabled,\n                'searchbar-no-animate': animated && this.noAnimate,\n                'searchbar-has-value': this.hasValue(),\n                'searchbar-left-aligned': this.shouldAlignLeft,\n                'searchbar-has-focus': this.focused,\n                'searchbar-should-show-clear': this.shouldShowClearButton(),\n                'searchbar-should-show-cancel': this.shouldShowCancelButton(),\n            }) }, h(\"div\", { key: '54f58a79fe36e85d9295157303f1be89c98bbdaf', class: \"searchbar-input-container\" }, h(\"input\", Object.assign({ key: 'f991a37fcf54d26b7ad10d89084764e03d97b9de', \"aria-label\": \"search text\", disabled: this.disabled, ref: (el) => (this.nativeInput = el), class: \"searchbar-input\", inputMode: this.inputmode, enterKeyHint: this.enterkeyhint, name: this.name, onInput: this.onInput, onChange: this.onChange, onBlur: this.onBlur, onFocus: this.onFocus, minLength: this.minlength, maxLength: this.maxlength, placeholder: this.placeholder, type: this.type, value: this.getValue(), autoCapitalize: autocapitalize === 'default' ? undefined : autocapitalize, autoComplete: this.autocomplete, autoCorrect: this.autocorrect, spellcheck: this.spellcheck }, this.inheritedAttributes)), mode === 'md' && cancelButton, h(\"ion-icon\", { key: '8b44dd90a3292c5cf872ef16a8520675f5673494', \"aria-hidden\": \"true\", mode: mode, icon: searchIcon, lazy: false, class: \"searchbar-search-icon\" }), h(\"button\", { key: '79d9cfed8f01268044f82811a35d323a12dca749', \"aria-label\": \"reset\", type: \"button\", \"no-blur\": true, class: \"searchbar-clear-button\", onPointerDown: (ev) => {\n                /**\n                 * This prevents mobile browsers from\n                 * blurring the input when the clear\n                 * button is activated.\n                 */\n                ev.preventDefault();\n            }, onClick: () => this.onClearInput(true) }, h(\"ion-icon\", { key: 'aa3b9fa8a61f853236783ac7bcd0b113ea65ece2', \"aria-hidden\": \"true\", mode: mode, icon: clearIcon, lazy: false, class: \"searchbar-clear-icon\" }))), mode === 'ios' && cancelButton));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"lang\": [\"onLangChanged\"],\n        \"dir\": [\"onDirChanged\"],\n        \"debounce\": [\"debounceChanged\"],\n        \"value\": [\"valueChanged\"],\n        \"showCancelButton\": [\"showCancelButtonChanged\"]\n    }; }\n};\nlet searchbarIds = 0;\nSearchbar.style = {\n    ios: searchbarIosCss,\n    md: searchbarMdCss\n};\n\nexport { Searchbar as ion_searchbar };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,kBAAkB;AAExB,IAAM,iBAAiB;AAEvB,IAAM,YAAY,MAAM;AAAA,EACpB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,SAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,UAAU,iBAAiB,cAAc;AAC9C,SAAK,sBAAsB,CAAC;AAC5B,SAAK,UAAU;AACf,SAAK,YAAY;AAIjB,SAAK,WAAW;AAKhB,SAAK,iBAAiB;AAItB,SAAK,eAAe;AAIpB,SAAK,cAAc;AAKnB,SAAK,mBAAmB,OAAO,IAAI,kBAAkB,cAAc;AAInE,SAAK,mBAAmB;AAIxB,SAAK,WAAW;AAIhB,SAAK,OAAO,KAAK;AAUjB,SAAK,cAAc;AAQnB,SAAK,mBAAmB;AASxB,SAAK,kBAAkB;AAIvB,SAAK,aAAa;AAIlB,SAAK,OAAO;AAIZ,SAAK,QAAQ;AAIb,SAAK,eAAe,CAAO,gBAAgB;AACvC,WAAK,SAAS,KAAK;AACnB,aAAO,IAAI,QAAQ,CAAC,YAAY;AAG5B,mBAAW,MAAM;AACb,gBAAM,QAAQ,KAAK,SAAS;AAC5B,cAAI,UAAU,IAAI;AACd,iBAAK,QAAQ;AACb,iBAAK,gBAAgB;AAOrB,gBAAI,eAAe,CAAC,KAAK,SAAS;AAC9B,mBAAK,SAAS;AAOd,mBAAK,eAAe;AAAA,YACxB;AAAA,UACJ;AACA,kBAAQ;AAAA,QACZ,GAAG,KAAK,CAAC;AAAA,MACb,CAAC;AAAA,IACL;AAMA,SAAK,oBAAoB,CAAO,OAAO;AACnC,UAAI,IAAI;AACJ,WAAG,eAAe;AAClB,WAAG,gBAAgB;AAAA,MACvB;AACA,WAAK,UAAU,KAAK;AAEpB,YAAM,QAAQ,KAAK,SAAS;AAC5B,YAAM,UAAU,KAAK;AACrB,YAAM,KAAK,aAAa;AAMxB,UAAI,SAAS,CAAC,SAAS;AACnB,aAAK,gBAAgB,EAAE;AAAA,MAC3B;AACA,UAAI,KAAK,aAAa;AAClB,aAAK,YAAY,KAAK;AAAA,MAC1B;AAAA,IACJ;AAIA,SAAK,UAAU,CAAC,OAAO;AACnB,YAAM,QAAQ,GAAG;AACjB,UAAI,OAAO;AACP,aAAK,QAAQ,MAAM;AAAA,MACvB;AACA,WAAK,gBAAgB,EAAE;AAAA,IAC3B;AACA,SAAK,WAAW,CAAC,OAAO;AACpB,WAAK,gBAAgB,EAAE;AAAA,IAC3B;AAKA,SAAK,SAAS,CAAC,OAAO;AAClB,WAAK,UAAU;AACf,WAAK,QAAQ,KAAK;AAClB,WAAK,iBAAiB;AACtB,UAAI,KAAK,iBAAiB,KAAK,OAAO;AAClC,aAAK,gBAAgB,EAAE;AAAA,MAC3B;AACA,WAAK,eAAe;AAAA,IACxB;AAIA,SAAK,UAAU,MAAM;AACjB,WAAK,UAAU;AACf,WAAK,eAAe,KAAK;AACzB,WAAK,SAAS,KAAK;AACnB,WAAK,iBAAiB;AAAA,IAC1B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,UAAU;AACpB,SAAK,sBAAsB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,mBAAmB,GAAG,EAAE,MAAM,SAAS,CAAC;AACxG,gBAAY,IAAI;AAAA,EACpB;AAAA,EACA,aAAa,UAAU;AACnB,SAAK,sBAAsB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,mBAAmB,GAAG,EAAE,KAAK,SAAS,CAAC;AACvG,gBAAY,IAAI;AAAA,EACpB;AAAA,EACA,kBAAkB;AACd,UAAM,EAAE,UAAU,UAAU,iBAAiB,IAAI;AAKjD,SAAK,WAAW,aAAa,SAAY,qBAAqB,QAAQ,qBAAqB,SAAS,mBAAmB,WAAW,cAAc,UAAU,QAAQ;AAAA,EACtK;AAAA,EACA,eAAe;AACX,UAAM,UAAU,KAAK;AACrB,UAAM,QAAQ,KAAK,SAAS;AAC5B,QAAI,WAAW,QAAQ,UAAU,OAAO;AACpC,cAAQ,QAAQ;AAAA,IACpB;AAAA,EACJ;AAAA,EACA,0BAA0B;AACtB,0BAAsB,MAAM;AACxB,WAAK,iBAAiB;AACtB,kBAAY,IAAI;AAAA,IACpB,CAAC;AAAA,EACL;AAAA,EACA,oBAAoB;AAChB,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,oBAAoB;AAChB,SAAK,sBAAsB,OAAO,OAAO,CAAC,GAAG,kBAAkB,KAAK,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAC;AAAA,EAC5F;AAAA,EACA,mBAAmB;AACf,SAAK,mBAAmB,KAAK;AAC7B,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,eAAW,MAAM;AACb,WAAK,YAAY;AAAA,IACrB,GAAG,GAAG;AAAA,EACV;AAAA,EACA,YAAY;AACR,SAAK,SAAS,KAAK;AAAA,MACf,WAAW;AAAA,IACf,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWM,WAAW;AAAA;AACb,UAAI,KAAK,aAAa;AAClB,aAAK,YAAY,MAAM;AAAA,MAC3B;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,kBAAkB;AAAA;AAKpB,UAAI,CAAC,KAAK,aAAa;AACnB,cAAM,IAAI,QAAQ,CAAC,YAAY,iBAAiB,KAAK,IAAI,OAAO,CAAC;AAAA,MACrE;AACA,aAAO,QAAQ,QAAQ,KAAK,WAAW;AAAA,IAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,OAAO;AACnB,UAAM,EAAE,MAAM,IAAI;AAElB,UAAM,WAAW,SAAS,OAAO,QAAQ,MAAM,SAAS;AAExD,SAAK,eAAe;AACpB,SAAK,UAAU,KAAK,EAAE,OAAO,UAAU,MAAM,CAAC;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,OAAO;AACnB,UAAM,EAAE,MAAM,IAAI;AAClB,SAAK,SAAS,KAAK,EAAE,OAAO,MAAM,CAAC;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACf,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,gBAAgB,KAAK;AAC3B,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,kBAAkB,CAAC,KAAK,YAAY,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,KAAK;AACxE,SAAK,kBAAkB;AACvB,QAAI,SAAS,OAAO;AAChB;AAAA,IACJ;AACA,QAAI,kBAAkB,iBAAiB;AACnC,WAAK,oBAAoB;AAAA,IAC7B;AACA,QAAI,KAAK,UAAU;AACf,WAAK,qBAAqB;AAAA,IAC9B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AAClB,UAAM,UAAU,KAAK;AACrB,QAAI,CAAC,SAAS;AACV;AAAA,IACJ;AACA,UAAM,MAAM,MAAM,KAAK,EAAE;AACzB,UAAM,UAAU,KAAK,GAAG,cAAc,KAAK,IAAI,cAAc,wBAAwB;AACrF,QAAI,KAAK,iBAAiB;AACtB,cAAQ,gBAAgB,OAAO;AAC/B,aAAO,gBAAgB,OAAO;AAAA,IAClC,OACK;AAED,YAAM,MAAM;AACZ,YAAM,WAAW,IAAI,cAAc,MAAM;AACzC,eAAS,YAAY,KAAK,eAAe;AACzC,UAAI,KAAK,YAAY,QAAQ;AAE7B,UAAI,MAAM;AACN,cAAM,YAAY,SAAS;AAC3B,iBAAS,OAAO;AAEhB,cAAM,YAAY,gBAAgB,YAAY,IAAI;AAQlD,cAAM,WAAW,iBAAiB,YAAY,IAAI,OAAO,cAAc,KAAK;AAE5E,YAAI,KAAK;AACL,kBAAQ,MAAM,eAAe;AAC7B,iBAAO,MAAM,cAAc;AAAA,QAC/B,OACK;AACD,kBAAQ,MAAM,cAAc;AAC5B,iBAAO,MAAM,aAAa;AAAA,QAC9B;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,uBAAuB;AACnB,UAAM,MAAM,MAAM,KAAK,EAAE;AACzB,UAAM,gBAAgB,KAAK,GAAG,cAAc,KAAK,IAAI,cAAc,0BAA0B;AAC7F,UAAM,mBAAmB,KAAK,uBAAuB;AACrD,QAAI,iBAAiB,QAAQ,qBAAqB,KAAK,iBAAiB;AACpE,YAAM,cAAc,aAAa;AACjC,WAAK,kBAAkB;AACvB,UAAI,kBAAkB;AAClB,YAAI,KAAK;AACL,sBAAY,aAAa;AAAA,QAC7B,OACK;AACD,sBAAY,cAAc;AAAA,QAC9B;AAAA,MACJ,OACK;AACD,cAAM,SAAS,aAAa;AAC5B,YAAI,SAAS,GAAG;AACZ,cAAI,KAAK;AACL,wBAAY,aAAa,CAAC,SAAS;AAAA,UACvC,OACK;AACD,wBAAY,cAAc,CAAC,SAAS;AAAA,UACxC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,WAAW;AACP,WAAO,KAAK,SAAS;AAAA,EACzB;AAAA,EACA,WAAW;AACP,WAAO,KAAK,SAAS,MAAM;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,yBAAyB;AACrB,QAAI,KAAK,qBAAqB,WAAY,KAAK,qBAAqB,WAAW,CAAC,KAAK,SAAU;AAC3F,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,wBAAwB;AACpB,QAAI,KAAK,oBAAoB,WAAY,KAAK,oBAAoB,WAAW,CAAC,KAAK,SAAU;AACzF,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,UAAM,EAAE,kBAAkB,eAAe,IAAI;AAC7C,UAAM,WAAW,KAAK,YAAY,OAAO,WAAW,YAAY,IAAI;AACpE,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,YAAY,KAAK,cAAc,SAAS,QAAQ,cAAc;AACpE,UAAM,aAAa,KAAK,eAAe,SAAS,QAAQ,gBAAgB;AACxE,UAAM,yBAAyB,KAAK,uBAAuB;AAC3D,UAAM,eAAe,KAAK,qBAAqB,WAAY,EAAE,UAAU,EAAE,KAAK,4CAA4C,cAAc,kBAAkB,eAAe,yBAAyB,SAAY,QAAQ,MAAM,UAAU,UAAU,SAAS,SAAS,CAAC,yBAAyB,KAAK,QAAW,aAAa,KAAK,mBAAmB,cAAc,KAAK,mBAAmB,OAAO,0BAA0B,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,eAAe,OAAO,GAAG,SAAS,OAAQ,EAAE,YAAY,EAAE,eAAe,QAAQ,MAAY,MAAM,KAAK,kBAAkB,MAAM,MAAM,CAAC,IAAM,gBAAiB,CAAC;AACtnB,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,MAAM,UAAU,iBAAiB,KAAK,WAAW,SAAS,MAAM,OAAO,mBAAmB,KAAK,OAAO;AAAA,MACjK,CAAC,IAAI,GAAG;AAAA,MACR,sBAAsB;AAAA,MACtB,sBAAsB,KAAK;AAAA,MAC3B,wBAAwB,YAAY,KAAK;AAAA,MACzC,uBAAuB,KAAK,SAAS;AAAA,MACrC,0BAA0B,KAAK;AAAA,MAC/B,uBAAuB,KAAK;AAAA,MAC5B,+BAA+B,KAAK,sBAAsB;AAAA,MAC1D,gCAAgC,KAAK,uBAAuB;AAAA,IAChE,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,4BAA4B,GAAG,EAAE,SAAS,OAAO,OAAO,EAAE,KAAK,4CAA4C,cAAc,eAAe,UAAU,KAAK,UAAU,KAAK,CAAC,OAAQ,KAAK,cAAc,IAAK,OAAO,mBAAmB,WAAW,KAAK,WAAW,cAAc,KAAK,cAAc,MAAM,KAAK,MAAM,SAAS,KAAK,SAAS,UAAU,KAAK,UAAU,QAAQ,KAAK,QAAQ,SAAS,KAAK,SAAS,WAAW,KAAK,WAAW,WAAW,KAAK,WAAW,aAAa,KAAK,aAAa,MAAM,KAAK,MAAM,OAAO,KAAK,SAAS,GAAG,gBAAgB,mBAAmB,YAAY,SAAY,gBAAgB,cAAc,KAAK,cAAc,aAAa,KAAK,aAAa,YAAY,KAAK,WAAW,GAAG,KAAK,mBAAmB,CAAC,GAAG,SAAS,QAAQ,cAAc,EAAE,YAAY,EAAE,KAAK,4CAA4C,eAAe,QAAQ,MAAY,MAAM,YAAY,MAAM,OAAO,OAAO,wBAAwB,CAAC,GAAG,EAAE,UAAU,EAAE,KAAK,4CAA4C,cAAc,SAAS,MAAM,UAAU,WAAW,MAAM,OAAO,0BAA0B,eAAe,CAAC,OAAO;AAMvoC,SAAG,eAAe;AAAA,IACtB,GAAG,SAAS,MAAM,KAAK,aAAa,IAAI,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,4CAA4C,eAAe,QAAQ,MAAY,MAAM,WAAW,MAAM,OAAO,OAAO,uBAAuB,CAAC,CAAC,CAAC,GAAG,SAAS,SAAS,YAAY;AAAA,EACzP;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,QAAQ,CAAC,eAAe;AAAA,MACxB,OAAO,CAAC,cAAc;AAAA,MACtB,YAAY,CAAC,iBAAiB;AAAA,MAC9B,SAAS,CAAC,cAAc;AAAA,MACxB,oBAAoB,CAAC,yBAAyB;AAAA,IAClD;AAAA,EAAG;AACP;AACA,IAAI,eAAe;AACnB,UAAU,QAAQ;AAAA,EACd,KAAK;AAAA,EACL,IAAI;AACR;", "names": []}