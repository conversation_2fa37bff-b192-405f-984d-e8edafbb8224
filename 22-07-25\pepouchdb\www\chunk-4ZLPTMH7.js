import{b as P,d as L}from"./chunk-BMJVVMK5.js";import{b as N,r as y}from"./chunk-XFXTD7QR.js";import{b as g,c as m,f as D,i as p,m as R,n as O,o as T,p as b}from"./chunk-EHNA26RN.js";import{g as h}from"./chunk-2R6CW7ES.js";var it=class{constructor(t){p(this,t),this.ionRouteDataChanged=b(this,"ionRouteDataChanged",7),this.url=""}onUpdate(t){this.ionRouteDataChanged.emit(t)}onComponentProps(t,e){if(t===e)return;let n=t?Object.keys(t):[],s=e?Object.keys(e):[];if(n.length!==s.length){this.onUpdate(t);return}for(let r of n)if(t[r]!==e[r]){this.onUpdate(t);return}}connectedCallback(){this.ionRouteDataChanged.emit()}static get watchers(){return{url:["onUpdate"],component:["onUpdate"],componentProps:["onComponentProps"]}}},at=class{constructor(t){p(this,t),this.ionRouteRedirectChanged=b(this,"ionRouteRedirectChanged",7)}propDidChange(){this.ionRouteRedirectChanged.emit()}connectedCallback(){this.ionRouteRedirectChanged.emit()}static get watchers(){return{from:["propDidChange"],to:["propDidChange"]}}},f="root",_="forward",q="back",d=t=>"/"+t.filter(n=>n.length>0).join("/"),H=(t,e,n)=>{let s=d(t);return e&&(s="#"+s),n!==void 0&&(s+="?"+n),s},M=(t,e,n,s,r,i,o)=>{let a=H([...l(e).segments,...s],n,o);r===_?t.pushState(i,"",a):t.replaceState(i,"",a)},F=t=>{let e=[];for(let n of t)for(let s of n.segments)if(s[0]===":"){let r=n.params&&n.params[s.slice(1)];if(!r)return null;e.push(r)}else s!==""&&e.push(s);return e},G=(t,e)=>{if(t.length>e.length)return null;if(t.length<=1&&t[0]==="")return e;for(let n=0;n<t.length;n++)if(t[n]!==e[n])return null;return e.length===t.length?[""]:e.slice(t.length)},B=(t,e,n)=>{let s=l(e).segments,r=n?t.hash.slice(1):t.pathname,i=l(r).segments;return G(s,i)},l=t=>{let e=[""],n;if(t!=null){let s=t.indexOf("?");s>-1&&(n=t.substring(s+1),t=t.substring(0,s)),e=t.split("/").map(r=>r.trim()).filter(r=>r.length>0),e.length===0&&(e=[""])}return{segments:e,queryString:n}},z=t=>{console.group(`[ion-core] ROUTES[${t.length}]`);for(let e of t){let n=[];e.forEach(r=>n.push(...r.segments));let s=e.map(r=>r.id);console.debug(`%c ${d(n)}`,"font-weight: bold; padding-left: 20px","=>	",`(${s.join(", ")})`)}console.groupEnd()},K=t=>{console.group(`[ion-core] REDIRECTS[${t.length}]`);for(let e of t)e.to&&console.debug("FROM: ",`$c ${d(e.from)}`,"font-weight: bold"," TO: ",`$c ${d(e.to.segments)}`,"font-weight: bold");console.groupEnd()},j=(t,e,n,s,r=!1,i)=>h(null,null,function*(){try{let o=k(t);if(s>=e.length||!o)return r;yield new Promise(u=>N(o,u));let a=e[s],c=yield o.setRouteId(a.id,a.params,n,i);return c.changed&&(n=f,r=!0),r=yield j(c.element,e,n,s+1,r,i),c.markVisible&&(yield c.markVisible()),r}catch(o){return m("[ion-router] - Exception in writeNavState:",o),!1}}),J=t=>h(null,null,function*(){let e=[],n,s=t;for(;n=k(s);){let r=yield n.getRouteId();if(r)s=r.element,r.element=void 0,e.push(r);else break}return{ids:e,outlet:n}}),Q=()=>k(document.body)?Promise.resolve():new Promise(t=>{window.addEventListener("ionNavWillLoad",()=>t(),{once:!0})}),U=":not([no-router]) ion-nav, :not([no-router]) ion-tabs, :not([no-router]) ion-router-outlet",k=t=>{if(!t)return;if(t.matches(U))return t;let e=t.querySelector(U);return e??void 0},X=(t,e)=>{let{from:n,to:s}=e;if(s===void 0||n.length>t.length)return!1;for(let r=0;r<n.length;r++){let i=n[r];if(i==="*")return!0;if(i!==t[r])return!1}return n.length===t.length},I=(t,e)=>e.find(n=>X(t,n)),Y=(t,e)=>{let n=Math.min(t.length,e.length),s=0;for(let r=0;r<n;r++){let i=t[r],o=e[r];if(i.id.toLowerCase()!==o.id)break;if(i.params){let a=Object.keys(i.params);if(a.length===o.segments.length){let c=a.map(u=>`:${u}`);for(let u=0;u<c.length&&c[u].toLowerCase()===o.segments[u];u++)s++}}s++}return s},Z=(t,e)=>{let n=new E(t),s=!1,r;for(let o=0;o<e.length;o++){let a=e[o].segments;if(a[0]==="")s=!0;else{for(let c of a){let u=n.next();if(c[0]===":"){if(u==="")return null;r=r||[];let A=r[o]||(r[o]={});A[c.slice(1)]=u}else if(u!==c)return null}s=!1}}return(s?s===(n.next()===""):!0)?r?e.map((o,a)=>({id:o.id,segments:o.segments,params:W(o.params,r[a]),beforeEnter:o.beforeEnter,beforeLeave:o.beforeLeave})):e:null},W=(t,e)=>t||e?Object.assign(Object.assign({},t),e):void 0,V=(t,e)=>{let n=null,s=0;for(let r of e){let i=Y(t,r);i>s&&(n=r,s=i)}return n?n.map((r,i)=>{var o;return{id:r.id,segments:r.segments,params:W(r.params,(o=t[i])===null||o===void 0?void 0:o.params)}}):null},C=(t,e)=>{let n=null,s=0;for(let r of e){let i=Z(t,r);if(i!==null){let o=tt(i);o>s&&(s=o,n=i)}}return n},tt=t=>{let e=1,n=1;for(let s of t)for(let r of s.segments)r[0]===":"?e+=Math.pow(1,n):r!==""&&(e+=Math.pow(2,n)),n++;return e},E=class{constructor(e){this.segments=e.slice()}next(){return this.segments.length>0?this.segments.shift():""}},v=(t,e)=>e in t?t[e]:t.hasAttribute(e)?t.getAttribute(e):null,S=t=>Array.from(t.children).filter(e=>e.tagName==="ION-ROUTE-REDIRECT").map(e=>{let n=v(e,"to");return{from:l(v(e,"from")).segments,to:n==null?void 0:l(n)}}),w=t=>et($(t)),$=t=>Array.from(t.children).filter(e=>e.tagName==="ION-ROUTE"&&e.component).map(e=>{let n=v(e,"component");return{segments:l(v(e,"url")).segments,id:n.toLowerCase(),params:e.componentProps,beforeLeave:e.beforeLeave,beforeEnter:e.beforeEnter,children:$(e)}}),et=t=>{let e=[];for(let n of t)x([],e,n);return e},x=(t,e,n)=>{if(t=[...t,{id:n.id,segments:n.segments,params:n.params,beforeLeave:n.beforeLeave,beforeEnter:n.beforeEnter}],n.children.length===0){e.push(t);return}for(let s of n.children)x(t,e,s)},ct=class{constructor(t){p(this,t),this.ionRouteWillChange=b(this,"ionRouteWillChange",7),this.ionRouteDidChange=b(this,"ionRouteDidChange",7),this.previousPath=null,this.busy=!1,this.state=0,this.lastState=0,this.root="/",this.useHash=!0}componentWillLoad(){return h(this,null,function*(){yield Q();let t=yield this.runGuards(this.getSegments());if(t!==!0){if(typeof t=="object"){let{redirect:e}=t,n=l(e);this.setSegments(n.segments,f,n.queryString),yield this.writeNavStateRoot(n.segments,f)}}else yield this.onRoutesChanged()})}componentDidLoad(){window.addEventListener("ionRouteRedirectChanged",y(this.onRedirectChanged.bind(this),10)),window.addEventListener("ionRouteDataChanged",y(this.onRoutesChanged.bind(this),100))}onPopState(){return h(this,null,function*(){let t=this.historyDirection(),e=this.getSegments(),n=yield this.runGuards(e);if(n!==!0)if(typeof n=="object")e=l(n.redirect).segments;else return!1;return this.writeNavStateRoot(e,t)})}onBackButton(t){t.detail.register(0,e=>{this.back(),e()})}canTransition(){return h(this,null,function*(){let t=yield this.runGuards();return t!==!0?typeof t=="object"?t.redirect:!1:!0})}push(t,e="forward",n){return h(this,null,function*(){var s;if(t.startsWith(".")){let o=(s=this.previousPath)!==null&&s!==void 0?s:"/",a=new URL(t,`https://host/${o}`);t=a.pathname+a.search}let r=l(t),i=yield this.runGuards(r.segments);if(i!==!0)if(typeof i=="object")r=l(i.redirect);else return!1;return this.setSegments(r.segments,e,r.queryString),this.writeNavStateRoot(r.segments,e,n)})}back(){return window.history.back(),Promise.resolve(this.waitPromise)}printDebug(){return h(this,null,function*(){z(w(this.el)),K(S(this.el))})}navChanged(t){return h(this,null,function*(){if(this.busy)return g("[ion-router] - Router is busy, navChanged was cancelled."),!1;let{ids:e,outlet:n}=yield J(window.document.body),s=w(this.el),r=V(e,s);if(!r)return g("[ion-router] - No matching URL for",e.map(o=>o.id)),!1;let i=F(r);return i?(this.setSegments(i,t),yield this.safeWriteNavState(n,r,f,i,null,e.length),!0):(g("[ion-router] - Router could not match path because some required param is missing."),!1)})}onRedirectChanged(){let t=this.getSegments();t&&I(t,S(this.el))&&this.writeNavStateRoot(t,f)}onRoutesChanged(){return this.writeNavStateRoot(this.getSegments(),f)}historyDirection(){var t;let e=window;e.history.state===null&&(this.state++,e.history.replaceState(this.state,e.document.title,(t=e.document.location)===null||t===void 0?void 0:t.href));let n=e.history.state,s=this.lastState;return this.lastState=n,n>s||n>=s&&s>0?_:n<s?q:f}writeNavStateRoot(t,e,n){return h(this,null,function*(){if(!t)return m("[ion-router] - URL is not part of the routing set."),!1;let s=S(this.el),r=I(t,s),i=null;if(r){let{segments:c,queryString:u}=r.to;this.setSegments(c,e,u),i=r.from,t=c}let o=w(this.el),a=C(t,o);return a?this.safeWriteNavState(document.body,a,e,t,i,0,n):(m("[ion-router] - The path does not match any route."),!1)})}safeWriteNavState(t,e,n,s,r,i=0,o){return h(this,null,function*(){let a=yield this.lock(),c=!1;try{c=yield this.writeNavState(t,e,n,s,r,i,o)}catch(u){m("[ion-router] - Exception in safeWriteNavState:",u)}return a(),c})}lock(){return h(this,null,function*(){let t=this.waitPromise,e;return this.waitPromise=new Promise(n=>e=n),t!==void 0&&(yield t),e})}runGuards(){return h(this,arguments,function*(t=this.getSegments(),e){if(e===void 0&&(e=l(this.previousPath).segments),!t||!e)return!0;let n=w(this.el),s=C(e,n),r=s&&s[s.length-1].beforeLeave,i=r?yield r():!0;if(i===!1||typeof i=="object")return i;let o=C(t,n),a=o&&o[o.length-1].beforeEnter;return a?a():!0})}writeNavState(t,e,n,s,r,i=0,o){return h(this,null,function*(){if(this.busy)return g("[ion-router] - Router is busy, transition was cancelled."),!1;this.busy=!0;let a=this.routeChangeEvent(s,r);a&&this.ionRouteWillChange.emit(a);let c=yield j(t,e,n,i,!1,o);return this.busy=!1,a&&this.ionRouteDidChange.emit(a),c})}setSegments(t,e,n){this.state++,M(window.history,this.root,this.useHash,t,e,this.state,n)}getSegments(){return B(window.location,this.root,this.useHash)}routeChangeEvent(t,e){let n=this.previousPath,s=d(t);if(this.previousPath=s,s===n)return null;let r=e?d(e):null;return{from:n,redirectedFrom:r,to:s}}get el(){return T(this)}},nt=":host{--background:transparent;--color:var(--ion-color-primary, #0054e9);background:var(--background);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}a{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit}",ut=(()=>{let t=class{constructor(e){p(this,e),this.routerDirection="forward",this.onClick=n=>{L(this.href,n,this.routerDirection,this.routerAnimation)}}render(){let e=D(this),n={href:this.href,rel:this.rel,target:this.target};return R(O,{key:"d7f2affcde45c5fbb6cb46cd1c30008ee92a68c5",onClick:this.onClick,class:P(this.color,{[e]:!0,"ion-activatable":!0})},R("a",Object.assign({key:"babafae85ca5c6429958d383feff0493ff8cf33e"},n),R("slot",{key:"50314e9555bbf6dffa0c50c3f763009dee59b10b"})))}};return t.style=nt,t})();export{it as ion_route,at as ion_route_redirect,ct as ion_router,ut as ion_router_link};
