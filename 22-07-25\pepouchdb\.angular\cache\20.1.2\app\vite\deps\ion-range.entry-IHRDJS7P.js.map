{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-range.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, m as printIonWarning, h, e as getIonMode, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { f as findClosestIonContent, d as disableContentScrollY, r as resetContentScrollY } from './index-BlJTBdxG.js';\nimport { j as isSafeNumber, e as clamp, d as debounceEvent, i as inheritAriaAttributes, a as renderHiddenInput } from './helpers-1O4D2b7y.js';\nimport { i as isRTL } from './dir-C53feagD.js';\nimport { h as hostContext, c as createColorClasses } from './theme-DiVJyqlX.js';\n\nfunction getDecimalPlaces(n) {\n    if (!isSafeNumber(n))\n        return 0;\n    if (n % 1 === 0)\n        return 0;\n    return n.toString().split('.')[1].length;\n}\n/**\n * Fixes floating point rounding errors in a result by rounding\n * to the same specificity, or number of decimal places (*not*\n * significant figures) as provided reference numbers. If multiple\n * references are provided, the highest number of decimal places\n * between them will be used.\n *\n * The main use case is when numbers x and y are added to produce n,\n * but x and y are floats, so n may have rounding errors (such as\n * 3.1000000004 instead of 3.1). As long as only addition/subtraction\n * occurs between x and y, the specificity of the result will never\n * increase, so x and y should be passed in as the references.\n *\n * If multiplication, division, or other operations were used to\n * calculate n, the rounded result may have less specificity than\n * desired. For example, 1 / 3 = 0.33333(...), but\n * roundToMaxDecimalPlaces((1 / 3), 1, 3) will return 0, since both\n * 1 and 3 are whole numbers.\n *\n * Note that extremely precise reference numbers may lead to rounding\n * errors not being trimmed, due to the error result having the same or\n * fewer decimal places as the reference(s). This is acceptable as we\n * would not be able to tell the difference between a rounding error\n * and correct value in this case, but it does mean there is an implicit\n * precision limit. If precision that high is needed, it is recommended\n * to use a third party data type designed to handle floating point\n * errors instead.\n *\n * @param n The number to round.\n * @param references Number(s) used to calculate n, or that should otherwise\n * be used as a reference for the desired specificity.\n */\nfunction roundToMaxDecimalPlaces(n, ...references) {\n    if (!isSafeNumber(n))\n        return 0;\n    const maxPlaces = Math.max(...references.map((r) => getDecimalPlaces(r)));\n    return Number(n.toFixed(maxPlaces));\n}\n\nconst rangeIosCss = \":host{--knob-handle-size:calc(var(--knob-size) * 2);display:-ms-flexbox;display:flex;position:relative;-ms-flex:3;flex:3;-ms-flex-align:center;align-items:center;font-family:var(--ion-font-family, inherit);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.range-disabled){pointer-events:none}::slotted(ion-label){-ms-flex:initial;flex:initial}::slotted(ion-icon[slot]){font-size:24px}.range-slider{position:relative;-ms-flex:1;flex:1;width:100%;height:var(--height);contain:size layout style;cursor:-webkit-grab;cursor:grab;-ms-touch-action:pan-y;touch-action:pan-y}:host(.range-pressed) .range-slider{cursor:-webkit-grabbing;cursor:grabbing}.range-pin{position:absolute;background:var(--ion-color-base);color:var(--ion-color-contrast);text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box}.range-knob-handle{top:calc((var(--height) - var(--knob-handle-size)) / 2);-webkit-margin-start:calc(0px - var(--knob-handle-size) / 2);margin-inline-start:calc(0px - var(--knob-handle-size) / 2);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-pack:center;justify-content:center;width:var(--knob-handle-size);height:var(--knob-handle-size);text-align:center}.range-knob-handle{inset-inline-start:0}:host-context([dir=rtl]) .range-knob-handle{left:unset}[dir=rtl] .range-knob-handle{left:unset}@supports selector(:dir(rtl)){.range-knob-handle:dir(rtl){left:unset}}.range-knob-handle:active,.range-knob-handle:focus{outline:none}.range-bar-container{border-radius:var(--bar-border-radius);top:calc((var(--height) - var(--bar-height)) / 2);position:absolute;width:100%;height:var(--bar-height)}.range-bar-container{inset-inline-start:0}:host-context([dir=rtl]) .range-bar-container{left:unset}[dir=rtl] .range-bar-container{left:unset}@supports selector(:dir(rtl)){.range-bar-container:dir(rtl){left:unset}}.range-bar{border-radius:var(--bar-border-radius);position:absolute;width:100%;height:var(--bar-height);background:var(--bar-background);pointer-events:none}.range-knob{border-radius:var(--knob-border-radius);top:calc(50% - var(--knob-size) / 2);position:absolute;width:var(--knob-size);height:var(--knob-size);background:var(--knob-background);-webkit-box-shadow:var(--knob-box-shadow);box-shadow:var(--knob-box-shadow);z-index:2;pointer-events:none}.range-knob{inset-inline-start:calc(50% - var(--knob-size) / 2)}:host-context([dir=rtl]) .range-knob{left:unset}[dir=rtl] .range-knob{left:unset}@supports selector(:dir(rtl)){.range-knob:dir(rtl){left:unset}}:host(.range-pressed) .range-bar-active{will-change:left, right}:host(.in-item){width:100%}:host([slot=start]),:host([slot=end]){width:auto}:host(.in-item) ::slotted(ion-label){-ms-flex-item-align:center;align-self:center}.range-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit}::slotted([slot=label]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center}:host(.range-label-placement-start) .range-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.range-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-end) .range-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.range-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.range-label-placement-stacked) .range-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:stretch;align-items:stretch}:host(.range-label-placement-stacked) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top;-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host-context([dir=rtl]):host(.range-label-placement-stacked) .label-text-wrapper,:host-context([dir=rtl]).range-label-placement-stacked .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.range-label-placement-stacked:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.in-item.range-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.range-label-placement-stacked) .native-wrapper{margin-bottom:0px}:host{--knob-border-radius:50%;--knob-background:#ffffff;--knob-box-shadow:0px 0.5px 4px rgba(0, 0, 0, 0.12), 0px 6px 13px rgba(0, 0, 0, 0.12);--knob-size:26px;--bar-height:4px;--bar-background:var(--ion-color-step-900, var(--ion-background-color-step-900, #e6e6e6));--bar-background-active:var(--ion-color-primary, #0054e9);--bar-border-radius:2px;--height:42px}:host(.range-item-start-adjustment){-webkit-padding-start:24px;padding-inline-start:24px}:host(.range-item-end-adjustment){-webkit-padding-end:24px;padding-inline-end:24px}:host(.ion-color) .range-bar-active,:host(.ion-color) .range-tick-active{background:var(--ion-color-base)}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-has-pin:not(.range-label-placement-stacked)){padding-top:calc(8px + 0.75rem)}:host(.range-has-pin.range-label-placement-stacked) .label-text-wrapper{margin-bottom:calc(8px + 0.75rem)}.range-bar-active{bottom:0;width:auto;background:var(--bar-background-active)}.range-bar-active.has-ticks{border-radius:0;-webkit-margin-start:-2px;margin-inline-start:-2px;-webkit-margin-end:-2px;margin-inline-end:-2px}.range-tick{-webkit-margin-start:-2px;margin-inline-start:-2px;border-radius:0;position:absolute;top:17px;width:4px;height:8px;background:var(--ion-color-step-900, var(--ion-background-color-step-900, #e6e6e6));pointer-events:none}.range-tick-active{background:var(--bar-background-active)}.range-pin{-webkit-transform:translate3d(0,  100%,  0) scale(0.01);transform:translate3d(0,  100%,  0) scale(0.01);-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;min-width:28px;-webkit-transition:-webkit-transform 120ms ease;transition:-webkit-transform 120ms ease;transition:transform 120ms ease;transition:transform 120ms ease, -webkit-transform 120ms ease;background:transparent;color:var(--ion-text-color, #000);font-size:0.75rem;text-align:center}.range-knob-pressed .range-pin,.range-knob-handle.ion-focused .range-pin{-webkit-transform:translate3d(0, calc(-100% + 11px), 0) scale(1);transform:translate3d(0, calc(-100% + 11px), 0) scale(1)}:host(.range-disabled){opacity:0.3}\";\n\nconst rangeMdCss = \":host{--knob-handle-size:calc(var(--knob-size) * 2);display:-ms-flexbox;display:flex;position:relative;-ms-flex:3;flex:3;-ms-flex-align:center;align-items:center;font-family:var(--ion-font-family, inherit);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.range-disabled){pointer-events:none}::slotted(ion-label){-ms-flex:initial;flex:initial}::slotted(ion-icon[slot]){font-size:24px}.range-slider{position:relative;-ms-flex:1;flex:1;width:100%;height:var(--height);contain:size layout style;cursor:-webkit-grab;cursor:grab;-ms-touch-action:pan-y;touch-action:pan-y}:host(.range-pressed) .range-slider{cursor:-webkit-grabbing;cursor:grabbing}.range-pin{position:absolute;background:var(--ion-color-base);color:var(--ion-color-contrast);text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box}.range-knob-handle{top:calc((var(--height) - var(--knob-handle-size)) / 2);-webkit-margin-start:calc(0px - var(--knob-handle-size) / 2);margin-inline-start:calc(0px - var(--knob-handle-size) / 2);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-pack:center;justify-content:center;width:var(--knob-handle-size);height:var(--knob-handle-size);text-align:center}.range-knob-handle{inset-inline-start:0}:host-context([dir=rtl]) .range-knob-handle{left:unset}[dir=rtl] .range-knob-handle{left:unset}@supports selector(:dir(rtl)){.range-knob-handle:dir(rtl){left:unset}}.range-knob-handle:active,.range-knob-handle:focus{outline:none}.range-bar-container{border-radius:var(--bar-border-radius);top:calc((var(--height) - var(--bar-height)) / 2);position:absolute;width:100%;height:var(--bar-height)}.range-bar-container{inset-inline-start:0}:host-context([dir=rtl]) .range-bar-container{left:unset}[dir=rtl] .range-bar-container{left:unset}@supports selector(:dir(rtl)){.range-bar-container:dir(rtl){left:unset}}.range-bar{border-radius:var(--bar-border-radius);position:absolute;width:100%;height:var(--bar-height);background:var(--bar-background);pointer-events:none}.range-knob{border-radius:var(--knob-border-radius);top:calc(50% - var(--knob-size) / 2);position:absolute;width:var(--knob-size);height:var(--knob-size);background:var(--knob-background);-webkit-box-shadow:var(--knob-box-shadow);box-shadow:var(--knob-box-shadow);z-index:2;pointer-events:none}.range-knob{inset-inline-start:calc(50% - var(--knob-size) / 2)}:host-context([dir=rtl]) .range-knob{left:unset}[dir=rtl] .range-knob{left:unset}@supports selector(:dir(rtl)){.range-knob:dir(rtl){left:unset}}:host(.range-pressed) .range-bar-active{will-change:left, right}:host(.in-item){width:100%}:host([slot=start]),:host([slot=end]){width:auto}:host(.in-item) ::slotted(ion-label){-ms-flex-item-align:center;align-self:center}.range-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit}::slotted([slot=label]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center}:host(.range-label-placement-start) .range-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.range-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-end) .range-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.range-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.range-label-placement-stacked) .range-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:stretch;align-items:stretch}:host(.range-label-placement-stacked) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top;-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host-context([dir=rtl]):host(.range-label-placement-stacked) .label-text-wrapper,:host-context([dir=rtl]).range-label-placement-stacked .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.range-label-placement-stacked:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.in-item.range-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.range-label-placement-stacked) .native-wrapper{margin-bottom:0px}:host{--knob-border-radius:50%;--knob-background:var(--bar-background-active);--knob-box-shadow:none;--knob-size:18px;--bar-height:2px;--bar-background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.26);--bar-background-active:var(--ion-color-primary, #0054e9);--bar-border-radius:0;--height:42px;--pin-background:var(--ion-color-primary, #0054e9);--pin-color:var(--ion-color-primary-contrast, #fff)}::slotted(:not(ion-icon)[slot=start]),::slotted(:not(ion-icon)[slot=end]),.native-wrapper{font-size:0.75rem}:host(.range-item-start-adjustment){-webkit-padding-start:18px;padding-inline-start:18px}:host(.range-item-end-adjustment){-webkit-padding-end:18px;padding-inline-end:18px}:host(.ion-color) .range-bar{background:rgba(var(--ion-color-base-rgb), 0.26)}:host(.ion-color) .range-bar-active,:host(.ion-color) .range-knob,:host(.ion-color) .range-knob::before,:host(.ion-color) .range-pin,:host(.ion-color) .range-pin::before,:host(.ion-color) .range-tick{background:var(--ion-color-base);color:var(--ion-color-contrast)}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:14px;margin-inline-end:14px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:14px;margin-inline-start:14px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-has-pin:not(.range-label-placement-stacked)){padding-top:1.75rem}:host(.range-has-pin.range-label-placement-stacked) .label-text-wrapper{margin-bottom:1.75rem}.range-bar-active{bottom:0;width:auto;background:var(--bar-background-active)}.range-knob{-webkit-transform:scale(0.67);transform:scale(0.67);-webkit-transition-duration:120ms;transition-duration:120ms;-webkit-transition-property:background-color, border, -webkit-transform;transition-property:background-color, border, -webkit-transform;transition-property:transform, background-color, border;transition-property:transform, background-color, border, -webkit-transform;-webkit-transition-timing-function:ease;transition-timing-function:ease;z-index:2}.range-knob::before{border-radius:50%;position:absolute;width:var(--knob-size);height:var(--knob-size);-webkit-transform:scale(1);transform:scale(1);-webkit-transition:0.267s cubic-bezier(0, 0, 0.58, 1);transition:0.267s cubic-bezier(0, 0, 0.58, 1);background:var(--knob-background);content:\\\"\\\";opacity:0.13;pointer-events:none}.range-knob::before{inset-inline-start:0}.range-tick{position:absolute;top:calc((var(--height) - var(--bar-height)) / 2);width:var(--bar-height);height:var(--bar-height);background:var(--bar-background-active);z-index:1;pointer-events:none}.range-tick-active{background:transparent}.range-pin{padding-left:0;padding-right:0;padding-top:8px;padding-bottom:8px;border-radius:50%;-webkit-transform:translate3d(0,  0,  0) scale(0.01);transform:translate3d(0,  0,  0) scale(0.01);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:1.75rem;height:1.75rem;-webkit-transition:background 120ms ease, -webkit-transform 120ms ease;transition:background 120ms ease, -webkit-transform 120ms ease;transition:transform 120ms ease, background 120ms ease;transition:transform 120ms ease, background 120ms ease, -webkit-transform 120ms ease;background:var(--pin-background);color:var(--pin-color)}.range-pin::before{bottom:-1px;-webkit-margin-start:-13px;margin-inline-start:-13px;border-radius:50% 50% 50% 0;position:absolute;width:26px;height:26px;-webkit-transform:rotate(-45deg);transform:rotate(-45deg);-webkit-transition:background 120ms ease;transition:background 120ms ease;background:var(--pin-background);content:\\\"\\\";z-index:-1}.range-pin::before{inset-inline-start:50%}:host-context([dir=rtl]) .range-pin::before{left:unset}[dir=rtl] .range-pin::before{left:unset}@supports selector(:dir(rtl)){.range-pin::before:dir(rtl){left:unset}}.range-knob-pressed .range-pin,.range-knob-handle.ion-focused .range-pin{-webkit-transform:translate3d(0, calc(-100% + 4px), 0) scale(1);transform:translate3d(0, calc(-100% + 4px), 0) scale(1)}@media (any-hover: hover){.range-knob-handle:hover .range-knob:before{-webkit-transform:scale(2);transform:scale(2);opacity:0.13}}.range-knob-handle.ion-activated .range-knob:before,.range-knob-handle.ion-focused .range-knob:before,.range-knob-handle.range-knob-pressed .range-knob:before{-webkit-transform:scale(2);transform:scale(2)}.range-knob-handle.ion-focused .range-knob::before{opacity:0.13}.range-knob-handle.ion-activated .range-knob::before,.range-knob-handle.range-knob-pressed .range-knob::before{opacity:0.25}:host(:not(.range-has-pin)) .range-knob-pressed .range-knob,:host(:not(.range-has-pin)) .range-knob-handle.ion-focused .range-knob{-webkit-transform:scale(1);transform:scale(1)}:host(.range-disabled) .range-bar-active,:host(.range-disabled) .range-bar,:host(.range-disabled) .range-tick{background-color:var(--ion-color-step-250, var(--ion-background-color-step-250, #bfbfbf))}:host(.range-disabled) .range-knob{-webkit-transform:scale(0.55);transform:scale(0.55);outline:5px solid #fff;background-color:var(--ion-color-step-250, var(--ion-background-color-step-250, #bfbfbf))}:host(.range-disabled) .label-text-wrapper,:host(.range-disabled) ::slotted([slot=start]),:host(.range-disabled) ::slotted([slot=end]){opacity:0.38}\";\n\nconst Range = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionInput = createEvent(this, \"ionInput\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionKnobMoveStart = createEvent(this, \"ionKnobMoveStart\", 7);\n        this.ionKnobMoveEnd = createEvent(this, \"ionKnobMoveEnd\", 7);\n        this.rangeId = `ion-r-${rangeIds++}`;\n        this.didLoad = false;\n        this.noUpdate = false;\n        this.hasFocus = false;\n        this.inheritedAttributes = {};\n        this.contentEl = null;\n        this.initialContentScrollY = true;\n        this.ratioA = 0;\n        this.ratioB = 0;\n        /**\n         * The name of the control, which is submitted with the form data.\n         */\n        this.name = this.rangeId;\n        /**\n         * Show two knobs.\n         */\n        this.dualKnobs = false;\n        /**\n         * Minimum integer value of the range.\n         */\n        this.min = 0;\n        /**\n         * Maximum integer value of the range.\n         */\n        this.max = 100;\n        /**\n         * If `true`, a pin with integer value is shown when the knob\n         * is pressed.\n         */\n        this.pin = false;\n        /**\n         * A callback used to format the pin text.\n         * By default the pin text is set to `Math.round(value)`.\n         *\n         * See https://ionicframework.com/docs/troubleshooting/runtime#accessing-this\n         * if you need to access `this` from within the callback.\n         */\n        this.pinFormatter = (value) => Math.round(value);\n        /**\n         * If `true`, the knob snaps to tick marks evenly spaced based\n         * on the step property value.\n         */\n        this.snaps = false;\n        /**\n         * Specifies the value granularity.\n         */\n        this.step = 1;\n        /**\n         * If `true`, tick marks are displayed based on the step value.\n         * Only applies when `snaps` is `true`.\n         */\n        this.ticks = true;\n        /**\n         * If `true`, the user cannot interact with the range.\n         */\n        this.disabled = false;\n        /**\n         * the value of the range.\n         */\n        this.value = 0;\n        /**\n         * Compares two RangeValue inputs to determine if they are different.\n         *\n         * @param newVal - The new value.\n         * @param oldVal - The old value.\n         * @returns `true` if the values are different, `false` otherwise.\n         */\n        this.compareValues = (newVal, oldVal) => {\n            if (typeof newVal === 'object' && typeof oldVal === 'object') {\n                return newVal.lower !== oldVal.lower || newVal.upper !== oldVal.upper;\n            }\n            return newVal !== oldVal;\n        };\n        this.clampBounds = (value) => {\n            return clamp(this.min, value, this.max);\n        };\n        this.ensureValueInBounds = (value) => {\n            if (this.dualKnobs) {\n                return {\n                    lower: this.clampBounds(value.lower),\n                    upper: this.clampBounds(value.upper),\n                };\n            }\n            else {\n                return this.clampBounds(value);\n            }\n        };\n        /**\n         * Where to place the label relative to the range.\n         * `\"start\"`: The label will appear to the left of the range in LTR and to the right in RTL.\n         * `\"end\"`: The label will appear to the right of the range in LTR and to the left in RTL.\n         * `\"fixed\"`: The label has the same behavior as `\"start\"` except it also has a fixed width. Long text will be truncated with ellipses (\"...\").\n         * `\"stacked\"`: The label will appear above the range regardless of the direction.\n         */\n        this.labelPlacement = 'start';\n        this.setupGesture = async () => {\n            const rangeSlider = this.rangeSlider;\n            if (rangeSlider) {\n                this.gesture = (await import('./index-CfgBF1SE.js')).createGesture({\n                    el: rangeSlider,\n                    gestureName: 'range',\n                    gesturePriority: 100,\n                    /**\n                     * Provide a threshold since the drag movement\n                     * might be a user scrolling the view.\n                     * If this is true, then the range\n                     * should not move.\n                     */\n                    threshold: 10,\n                    onStart: () => this.onStart(),\n                    onMove: (ev) => this.onMove(ev),\n                    onEnd: (ev) => this.onEnd(ev),\n                });\n                this.gesture.enable(!this.disabled);\n            }\n        };\n        this.handleKeyboard = (knob, isIncrease) => {\n            const { ensureValueInBounds } = this;\n            let step = this.step;\n            step = step > 0 ? step : 1;\n            step = step / (this.max - this.min);\n            if (!isIncrease) {\n                step *= -1;\n            }\n            if (knob === 'A') {\n                this.ratioA = clamp(0, this.ratioA + step, 1);\n            }\n            else {\n                this.ratioB = clamp(0, this.ratioB + step, 1);\n            }\n            this.ionKnobMoveStart.emit({ value: ensureValueInBounds(this.value) });\n            this.updateValue();\n            this.emitValueChange();\n            this.ionKnobMoveEnd.emit({ value: ensureValueInBounds(this.value) });\n        };\n        this.onBlur = () => {\n            if (this.hasFocus) {\n                this.hasFocus = false;\n                this.ionBlur.emit();\n            }\n        };\n        this.onFocus = () => {\n            if (!this.hasFocus) {\n                this.hasFocus = true;\n                this.ionFocus.emit();\n            }\n        };\n        this.onKnobFocus = (knob) => {\n            if (!this.hasFocus) {\n                this.hasFocus = true;\n                this.ionFocus.emit();\n            }\n            // Manually manage ion-focused class for dual knobs\n            if (this.dualKnobs && this.el.shadowRoot) {\n                const knobA = this.el.shadowRoot.querySelector('.range-knob-a');\n                const knobB = this.el.shadowRoot.querySelector('.range-knob-b');\n                // Remove ion-focused from both knobs first\n                knobA === null || knobA === void 0 ? void 0 : knobA.classList.remove('ion-focused');\n                knobB === null || knobB === void 0 ? void 0 : knobB.classList.remove('ion-focused');\n                // Add ion-focused only to the focused knob\n                const focusedKnobEl = knob === 'A' ? knobA : knobB;\n                focusedKnobEl === null || focusedKnobEl === void 0 ? void 0 : focusedKnobEl.classList.add('ion-focused');\n            }\n        };\n        this.onKnobBlur = () => {\n            // Check if focus is moving to another knob within the same range\n            // by delaying the reset to allow the new focus to register\n            setTimeout(() => {\n                var _a;\n                const activeElement = (_a = this.el.shadowRoot) === null || _a === void 0 ? void 0 : _a.activeElement;\n                const isStillFocusedOnKnob = activeElement && activeElement.classList.contains('range-knob-handle');\n                if (!isStillFocusedOnKnob) {\n                    if (this.hasFocus) {\n                        this.hasFocus = false;\n                        this.ionBlur.emit();\n                    }\n                    // Remove ion-focused from both knobs when focus leaves the range\n                    if (this.dualKnobs && this.el.shadowRoot) {\n                        const knobA = this.el.shadowRoot.querySelector('.range-knob-a');\n                        const knobB = this.el.shadowRoot.querySelector('.range-knob-b');\n                        knobA === null || knobA === void 0 ? void 0 : knobA.classList.remove('ion-focused');\n                        knobB === null || knobB === void 0 ? void 0 : knobB.classList.remove('ion-focused');\n                    }\n                }\n            }, 0);\n        };\n    }\n    debounceChanged() {\n        const { ionInput, debounce, originalIonInput } = this;\n        /**\n         * If debounce is undefined, we have to manually revert the ionInput emitter in case\n         * debounce used to be set to a number. Otherwise, the event would stay debounced.\n         */\n        this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n    }\n    minChanged(newValue) {\n        if (!isSafeNumber(newValue)) {\n            this.min = 0;\n        }\n        if (!this.noUpdate) {\n            this.updateRatio();\n        }\n    }\n    maxChanged(newValue) {\n        if (!isSafeNumber(newValue)) {\n            this.max = 100;\n        }\n        if (!this.noUpdate) {\n            this.updateRatio();\n        }\n    }\n    stepChanged(newValue) {\n        if (!isSafeNumber(newValue)) {\n            this.step = 1;\n        }\n    }\n    activeBarStartChanged() {\n        const { activeBarStart } = this;\n        if (activeBarStart !== undefined) {\n            if (activeBarStart > this.max) {\n                printIonWarning(`[ion-range] - The value of activeBarStart (${activeBarStart}) is greater than the max (${this.max}). Valid values are greater than or equal to the min value and less than or equal to the max value.`, this.el);\n                this.activeBarStart = this.max;\n            }\n            else if (activeBarStart < this.min) {\n                printIonWarning(`[ion-range] - The value of activeBarStart (${activeBarStart}) is less than the min (${this.min}). Valid values are greater than or equal to the min value and less than or equal to the max value.`, this.el);\n                this.activeBarStart = this.min;\n            }\n        }\n    }\n    disabledChanged() {\n        if (this.gesture) {\n            this.gesture.enable(!this.disabled);\n        }\n    }\n    valueChanged(newValue, oldValue) {\n        const valuesChanged = this.compareValues(newValue, oldValue);\n        if (valuesChanged) {\n            this.ionInput.emit({ value: this.value });\n        }\n        if (!this.noUpdate) {\n            this.updateRatio();\n        }\n    }\n    componentWillLoad() {\n        /**\n         * If user has custom ID set then we should\n         * not assign the default incrementing ID.\n         */\n        if (this.el.hasAttribute('id')) {\n            this.rangeId = this.el.getAttribute('id');\n        }\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n        // If min, max, or step are not safe, set them to 0, 100, and 1, respectively.\n        // Each watch does this, but not before the initial load.\n        this.min = isSafeNumber(this.min) ? this.min : 0;\n        this.max = isSafeNumber(this.max) ? this.max : 100;\n        this.step = isSafeNumber(this.step) ? this.step : 1;\n    }\n    componentDidLoad() {\n        this.originalIonInput = this.ionInput;\n        this.setupGesture();\n        this.updateRatio();\n        this.didLoad = true;\n    }\n    connectedCallback() {\n        var _a;\n        this.updateRatio();\n        this.debounceChanged();\n        this.disabledChanged();\n        this.activeBarStartChanged();\n        /**\n         * If we have not yet rendered\n         * ion-range, then rangeSlider is not defined.\n         * But if we are moving ion-range via appendChild,\n         * then rangeSlider will be defined.\n         */\n        if (this.didLoad) {\n            this.setupGesture();\n        }\n        const ionContent = findClosestIonContent(this.el);\n        this.contentEl = (_a = ionContent === null || ionContent === void 0 ? void 0 : ionContent.querySelector('.ion-content-scroll-host')) !== null && _a !== void 0 ? _a : ionContent;\n    }\n    disconnectedCallback() {\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n    }\n    getValue() {\n        var _a;\n        const value = (_a = this.value) !== null && _a !== void 0 ? _a : 0;\n        if (this.dualKnobs) {\n            if (typeof value === 'object') {\n                return value;\n            }\n            return {\n                lower: 0,\n                upper: value,\n            };\n        }\n        else {\n            if (typeof value === 'object') {\n                return value.upper;\n            }\n            return value;\n        }\n    }\n    /**\n     * Emits an `ionChange` event.\n     *\n     * This API should be called for user committed changes.\n     * This API should not be used for external value changes.\n     */\n    emitValueChange() {\n        this.value = this.ensureValueInBounds(this.value);\n        this.ionChange.emit({ value: this.value });\n    }\n    /**\n     * The value should be updated on touch end or\n     * when the component is being dragged.\n     * This follows the native behavior of mobile devices.\n     *\n     * For example: When the user lifts their finger from the\n     * screen after tapping the bar or dragging the bar or knob.\n     */\n    onStart() {\n        this.ionKnobMoveStart.emit({ value: this.ensureValueInBounds(this.value) });\n    }\n    /**\n     * The value should be updated while dragging the\n     * bar or knob.\n     *\n     * While the user is dragging, the view\n     * should not scroll. This is to prevent the user from\n     * feeling disoriented while dragging.\n     *\n     * The user can scroll on the view if the knob or\n     * bar is not being dragged.\n     *\n     * @param detail The details of the gesture event.\n     */\n    onMove(detail) {\n        const { contentEl, pressedKnob } = this;\n        const currentX = detail.currentX;\n        /**\n         * Since the user is dragging on the bar or knob, the view should not scroll.\n         *\n         * This only needs to be done once.\n         */\n        if (contentEl && this.pressedKnob === undefined) {\n            this.initialContentScrollY = disableContentScrollY(contentEl);\n        }\n        /**\n         * The `pressedKnob` can be undefined if the user just\n         * started dragging the knob.\n         *\n         * This is necessary to determine which knob the user is dragging,\n         * especially when it's a dual knob.\n         * Plus, it determines when to apply certain styles.\n         *\n         * This only needs to be done once since the knob won't change\n         * while the user is dragging.\n         */\n        if (pressedKnob === undefined) {\n            this.setPressedKnob(currentX);\n        }\n        this.update(currentX);\n    }\n    /**\n     * The value should be updated on touch end:\n     * - When the user lifts their finger from the screen after\n     * tapping the bar.\n     *\n     * @param detail The details of the gesture or mouse event.\n     */\n    onEnd(detail) {\n        var _a;\n        const { contentEl, initialContentScrollY } = this;\n        const currentX = (_a = detail.currentX) !== null && _a !== void 0 ? _a : detail.clientX;\n        /**\n         * The `pressedKnob` can be undefined if the user never\n         * dragged the knob. They just tapped on the bar.\n         *\n         * This is necessary to determine which knob the user is changing,\n         * especially when it's a dual knob.\n         * Plus, it determines when to apply certain styles.\n         */\n        if (this.pressedKnob === undefined) {\n            this.setPressedKnob(currentX);\n        }\n        /**\n         * The user is no longer dragging the bar or\n         * knob (if they were dragging it).\n         *\n         * The user can now scroll on the view in the next gesture event.\n         */\n        if (contentEl && this.pressedKnob !== undefined) {\n            resetContentScrollY(contentEl, initialContentScrollY);\n        }\n        // update the active knob's position\n        this.update(currentX);\n        /**\n         * Reset the pressed knob to undefined since the user\n         * may start dragging a different knob in the next gesture event.\n         */\n        this.pressedKnob = undefined;\n        this.emitValueChange();\n        this.ionKnobMoveEnd.emit({ value: this.ensureValueInBounds(this.value) });\n    }\n    update(currentX) {\n        // figure out where the pointer is currently at\n        // update the knob being interacted with\n        const rect = this.rect;\n        let ratio = clamp(0, (currentX - rect.left) / rect.width, 1);\n        if (isRTL(this.el)) {\n            ratio = 1 - ratio;\n        }\n        if (this.snaps) {\n            // snaps the ratio to the current value\n            ratio = valueToRatio(ratioToValue(ratio, this.min, this.max, this.step), this.min, this.max);\n        }\n        // update which knob is pressed\n        if (this.pressedKnob === 'A') {\n            this.ratioA = ratio;\n        }\n        else {\n            this.ratioB = ratio;\n        }\n        // Update input value\n        this.updateValue();\n    }\n    setPressedKnob(currentX) {\n        const rect = (this.rect = this.rangeSlider.getBoundingClientRect());\n        // figure out which knob they started closer to\n        let ratio = clamp(0, (currentX - rect.left) / rect.width, 1);\n        if (isRTL(this.el)) {\n            ratio = 1 - ratio;\n        }\n        this.pressedKnob = !this.dualKnobs || Math.abs(this.ratioA - ratio) < Math.abs(this.ratioB - ratio) ? 'A' : 'B';\n        this.setFocus(this.pressedKnob);\n    }\n    get valA() {\n        return ratioToValue(this.ratioA, this.min, this.max, this.step);\n    }\n    get valB() {\n        return ratioToValue(this.ratioB, this.min, this.max, this.step);\n    }\n    get ratioLower() {\n        if (this.dualKnobs) {\n            return Math.min(this.ratioA, this.ratioB);\n        }\n        const { activeBarStart } = this;\n        if (activeBarStart == null) {\n            return 0;\n        }\n        return valueToRatio(activeBarStart, this.min, this.max);\n    }\n    get ratioUpper() {\n        if (this.dualKnobs) {\n            return Math.max(this.ratioA, this.ratioB);\n        }\n        return this.ratioA;\n    }\n    updateRatio() {\n        const value = this.getValue();\n        const { min, max } = this;\n        if (this.dualKnobs) {\n            this.ratioA = valueToRatio(value.lower, min, max);\n            this.ratioB = valueToRatio(value.upper, min, max);\n        }\n        else {\n            this.ratioA = valueToRatio(value, min, max);\n        }\n    }\n    updateValue() {\n        this.noUpdate = true;\n        const { valA, valB } = this;\n        this.value = !this.dualKnobs\n            ? valA\n            : {\n                lower: Math.min(valA, valB),\n                upper: Math.max(valA, valB),\n            };\n        this.noUpdate = false;\n    }\n    setFocus(knob) {\n        if (this.el.shadowRoot) {\n            const knobEl = this.el.shadowRoot.querySelector(knob === 'A' ? '.range-knob-a' : '.range-knob-b');\n            if (knobEl) {\n                knobEl.focus();\n            }\n        }\n    }\n    /**\n     * Returns true if content was passed to the \"start\" slot\n     */\n    get hasStartSlotContent() {\n        return this.el.querySelector('[slot=\"start\"]') !== null;\n    }\n    /**\n     * Returns true if content was passed to the \"end\" slot\n     */\n    get hasEndSlotContent() {\n        return this.el.querySelector('[slot=\"end\"]') !== null;\n    }\n    get hasLabel() {\n        return this.label !== undefined || this.el.querySelector('[slot=\"label\"]') !== null;\n    }\n    renderRangeSlider() {\n        var _a;\n        const { min, max, step, handleKeyboard, pressedKnob, disabled, pin, ratioLower, ratioUpper, pinFormatter, inheritedAttributes, } = this;\n        let barStart = `${ratioLower * 100}%`;\n        let barEnd = `${100 - ratioUpper * 100}%`;\n        const rtl = isRTL(this.el);\n        const start = rtl ? 'right' : 'left';\n        const end = rtl ? 'left' : 'right';\n        const tickStyle = (tick) => {\n            return {\n                [start]: tick[start],\n            };\n        };\n        if (this.dualKnobs === false) {\n            /**\n             * When the value is less than the activeBarStart or the min value,\n             * the knob will display at the start of the active bar.\n             */\n            if (this.valA < ((_a = this.activeBarStart) !== null && _a !== void 0 ? _a : this.min)) {\n                /**\n                 * Sets the bar positions relative to the upper and lower limits.\n                 * Converts the ratio values into percentages, used as offsets for left/right styles.\n                 *\n                 * The ratioUpper refers to the knob position on the bar.\n                 * The ratioLower refers to the end position of the active bar (the value).\n                 */\n                barStart = `${ratioUpper * 100}%`;\n                barEnd = `${100 - ratioLower * 100}%`;\n            }\n            else {\n                /**\n                 * Otherwise, the knob will display at the end of the active bar.\n                 *\n                 * The ratioLower refers to the start position of the active bar (the value).\n                 * The ratioUpper refers to the knob position on the bar.\n                 */\n                barStart = `${ratioLower * 100}%`;\n                barEnd = `${100 - ratioUpper * 100}%`;\n            }\n        }\n        const barStyle = {\n            [start]: barStart,\n            [end]: barEnd,\n        };\n        const ticks = [];\n        if (this.snaps && this.ticks) {\n            for (let value = min; value <= max; value += step) {\n                const ratio = valueToRatio(value, min, max);\n                const ratioMin = Math.min(ratioLower, ratioUpper);\n                const ratioMax = Math.max(ratioLower, ratioUpper);\n                const tick = {\n                    ratio,\n                    /**\n                     * Sets the tick mark as active when the tick is between the min bounds and the knob.\n                     * When using activeBarStart, the tick mark will be active between the knob and activeBarStart.\n                     */\n                    active: ratio >= ratioMin && ratio <= ratioMax,\n                };\n                tick[start] = `${ratio * 100}%`;\n                ticks.push(tick);\n            }\n        }\n        return (h(\"div\", { class: \"range-slider\", ref: (rangeEl) => (this.rangeSlider = rangeEl),\n            /**\n             * Since the gesture has a threshold, the value\n             * won't change until the user has dragged past\n             * the threshold. This is to prevent the range\n             * from moving when the user is scrolling.\n             *\n             * This results in the value not being updated\n             * and the event emitters not being triggered\n             * if the user taps on the range. This is why\n             * we need to listen for the \"pointerUp\" event.\n             */\n            onPointerUp: (ev) => {\n                /**\n                 * If the user drags the knob on the web\n                 * version (does not occur on mobile),\n                 * the \"pointerUp\" event will be triggered\n                 * along with the gesture's events.\n                 * This leads to duplicate events.\n                 *\n                 * By checking if the pressedKnob is undefined,\n                 * we can determine if the \"pointerUp\" event was\n                 * triggered by a tap or a drag. If it was\n                 * dragged, the pressedKnob will be defined.\n                 */\n                if (this.pressedKnob === undefined) {\n                    this.onStart();\n                    this.onEnd(ev);\n                }\n            } }, ticks.map((tick) => (h(\"div\", { style: tickStyle(tick), role: \"presentation\", class: {\n                'range-tick': true,\n                'range-tick-active': tick.active,\n            }, part: tick.active ? 'tick-active' : 'tick' }))), h(\"div\", { class: \"range-bar-container\" }, h(\"div\", { class: \"range-bar\", role: \"presentation\", part: \"bar\" }), h(\"div\", { class: {\n                'range-bar': true,\n                'range-bar-active': true,\n                'has-ticks': ticks.length > 0,\n            }, role: \"presentation\", style: barStyle, part: \"bar-active\" })), renderKnob(rtl, {\n            knob: 'A',\n            pressed: pressedKnob === 'A',\n            value: this.valA,\n            ratio: this.ratioA,\n            pin,\n            pinFormatter,\n            disabled,\n            handleKeyboard,\n            min,\n            max,\n            inheritedAttributes,\n            onKnobFocus: this.onKnobFocus,\n            onKnobBlur: this.onKnobBlur,\n        }), this.dualKnobs &&\n            renderKnob(rtl, {\n                knob: 'B',\n                pressed: pressedKnob === 'B',\n                value: this.valB,\n                ratio: this.ratioB,\n                pin,\n                pinFormatter,\n                disabled,\n                handleKeyboard,\n                min,\n                max,\n                inheritedAttributes,\n                onKnobFocus: this.onKnobFocus,\n                onKnobBlur: this.onKnobBlur,\n            })));\n    }\n    render() {\n        const { disabled, el, hasLabel, rangeId, pin, pressedKnob, labelPlacement, label } = this;\n        const inItem = hostContext('ion-item', el);\n        /**\n         * If there is no start content then the knob at\n         * the min value will be cut off by the item margin.\n         */\n        const hasStartContent = (hasLabel && (labelPlacement === 'start' || labelPlacement === 'fixed')) || this.hasStartSlotContent;\n        const needsStartAdjustment = inItem && !hasStartContent;\n        /**\n         * If there is no end content then the knob at\n         * the max value will be cut off by the item margin.\n         */\n        const hasEndContent = (hasLabel && labelPlacement === 'end') || this.hasEndSlotContent;\n        const needsEndAdjustment = inItem && !hasEndContent;\n        const mode = getIonMode(this);\n        renderHiddenInput(true, el, this.name, JSON.stringify(this.getValue()), disabled);\n        return (h(Host, { key: 'ef7b01f80515bcaeb2983934ad7f10a6bd5d13ec', onFocusin: this.onFocus, onFocusout: this.onBlur, id: rangeId, class: createColorClasses(this.color, {\n                [mode]: true,\n                'in-item': inItem,\n                'range-disabled': disabled,\n                'range-pressed': pressedKnob !== undefined,\n                'range-has-pin': pin,\n                [`range-label-placement-${labelPlacement}`]: true,\n                'range-item-start-adjustment': needsStartAdjustment,\n                'range-item-end-adjustment': needsEndAdjustment,\n            }) }, h(\"label\", { key: 'fd8aa90a9d52be9da024b907e68858dae424449d', class: \"range-wrapper\", id: \"range-label\" }, h(\"div\", { key: '2172b4f329c22017dd23475c80aac25ba6e753eb', class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !hasLabel,\n            }, part: \"label\" }, label !== undefined ? h(\"div\", { class: \"label-text\" }, label) : h(\"slot\", { name: \"label\" })), h(\"div\", { key: '3c318bf2ea0576646d4c010bf44573fd0f483186', class: \"native-wrapper\" }, h(\"slot\", { key: '6586fd6fc96271e73f8a86c202d1913ad1a26f96', name: \"start\" }), this.renderRangeSlider(), h(\"slot\", { key: '74ac0bc2d2cb66ef708bb729f88b6ecbc1b2155d', name: \"end\" })))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"debounce\": [\"debounceChanged\"],\n        \"min\": [\"minChanged\"],\n        \"max\": [\"maxChanged\"],\n        \"step\": [\"stepChanged\"],\n        \"activeBarStart\": [\"activeBarStartChanged\"],\n        \"disabled\": [\"disabledChanged\"],\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nconst renderKnob = (rtl, { knob, value, ratio, min, max, disabled, pressed, pin, handleKeyboard, pinFormatter, inheritedAttributes, onKnobFocus, onKnobBlur, }) => {\n    const start = rtl ? 'right' : 'left';\n    const knobStyle = () => {\n        const style = {};\n        style[start] = `${ratio * 100}%`;\n        return style;\n    };\n    // The aria label should be preferred over visible text if both are specified\n    const ariaLabel = inheritedAttributes['aria-label'];\n    return (h(\"div\", { onKeyDown: (ev) => {\n            const key = ev.key;\n            if (key === 'ArrowLeft' || key === 'ArrowDown') {\n                handleKeyboard(knob, false);\n                ev.preventDefault();\n                ev.stopPropagation();\n            }\n            else if (key === 'ArrowRight' || key === 'ArrowUp') {\n                handleKeyboard(knob, true);\n                ev.preventDefault();\n                ev.stopPropagation();\n            }\n        }, onFocus: () => onKnobFocus(knob), onBlur: onKnobBlur, class: {\n            'range-knob-handle': true,\n            'range-knob-a': knob === 'A',\n            'range-knob-b': knob === 'B',\n            'range-knob-pressed': pressed,\n            'range-knob-min': value === min,\n            'range-knob-max': value === max,\n            'ion-activatable': true,\n            'ion-focusable': true,\n        }, style: knobStyle(), role: \"slider\", tabindex: disabled ? -1 : 0, \"aria-label\": ariaLabel !== undefined ? ariaLabel : null, \"aria-labelledby\": ariaLabel === undefined ? 'range-label' : null, \"aria-valuemin\": min, \"aria-valuemax\": max, \"aria-disabled\": disabled ? 'true' : null, \"aria-valuenow\": value }, pin && (h(\"div\", { class: \"range-pin\", role: \"presentation\", part: \"pin\" }, pinFormatter(value))), h(\"div\", { class: \"range-knob\", role: \"presentation\", part: \"knob\" })));\n};\nconst ratioToValue = (ratio, min, max, step) => {\n    let value = (max - min) * ratio;\n    if (step > 0) {\n        // round to nearest multiple of step, then add min\n        value = Math.round(value / step) * step + min;\n    }\n    const clampedValue = clamp(min, value, max);\n    return roundToMaxDecimalPlaces(clampedValue, min, max, step);\n};\nconst valueToRatio = (value, min, max) => {\n    return clamp(0, (value - min) / (max - min), 1);\n};\nlet rangeIds = 0;\nRange.style = {\n    ios: rangeIosCss,\n    md: rangeMdCss\n};\n\nexport { Range as ion_range };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,SAAS,iBAAiB,GAAG;AACzB,MAAI,CAAC,aAAa,CAAC;AACf,WAAO;AACX,MAAI,IAAI,MAAM;AACV,WAAO;AACX,SAAO,EAAE,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;AACtC;AAiCA,SAAS,wBAAwB,MAAM,YAAY;AAC/C,MAAI,CAAC,aAAa,CAAC;AACf,WAAO;AACX,QAAM,YAAY,KAAK,IAAI,GAAG,WAAW,IAAI,CAAC,MAAM,iBAAiB,CAAC,CAAC,CAAC;AACxE,SAAO,OAAO,EAAE,QAAQ,SAAS,CAAC;AACtC;AAEA,IAAM,cAAc;AAEpB,IAAM,aAAa;AAEnB,IAAM,QAAQ,MAAM;AAAA,EAChB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,SAAK,mBAAmB,YAAY,MAAM,oBAAoB,CAAC;AAC/D,SAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,SAAK,UAAU,SAAS,UAAU;AAClC,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,sBAAsB,CAAC;AAC5B,SAAK,YAAY;AACjB,SAAK,wBAAwB;AAC7B,SAAK,SAAS;AACd,SAAK,SAAS;AAId,SAAK,OAAO,KAAK;AAIjB,SAAK,YAAY;AAIjB,SAAK,MAAM;AAIX,SAAK,MAAM;AAKX,SAAK,MAAM;AAQX,SAAK,eAAe,CAAC,UAAU,KAAK,MAAM,KAAK;AAK/C,SAAK,QAAQ;AAIb,SAAK,OAAO;AAKZ,SAAK,QAAQ;AAIb,SAAK,WAAW;AAIhB,SAAK,QAAQ;AAQb,SAAK,gBAAgB,CAAC,QAAQ,WAAW;AACrC,UAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;AAC1D,eAAO,OAAO,UAAU,OAAO,SAAS,OAAO,UAAU,OAAO;AAAA,MACpE;AACA,aAAO,WAAW;AAAA,IACtB;AACA,SAAK,cAAc,CAAC,UAAU;AAC1B,aAAO,MAAM,KAAK,KAAK,OAAO,KAAK,GAAG;AAAA,IAC1C;AACA,SAAK,sBAAsB,CAAC,UAAU;AAClC,UAAI,KAAK,WAAW;AAChB,eAAO;AAAA,UACH,OAAO,KAAK,YAAY,MAAM,KAAK;AAAA,UACnC,OAAO,KAAK,YAAY,MAAM,KAAK;AAAA,QACvC;AAAA,MACJ,OACK;AACD,eAAO,KAAK,YAAY,KAAK;AAAA,MACjC;AAAA,IACJ;AAQA,SAAK,iBAAiB;AACtB,SAAK,eAAe,MAAY;AAC5B,YAAM,cAAc,KAAK;AACzB,UAAI,aAAa;AACb,aAAK,WAAW,MAAM,OAAO,8BAAqB,GAAG,cAAc;AAAA,UAC/D,IAAI;AAAA,UACJ,aAAa;AAAA,UACb,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOjB,WAAW;AAAA,UACX,SAAS,MAAM,KAAK,QAAQ;AAAA,UAC5B,QAAQ,CAAC,OAAO,KAAK,OAAO,EAAE;AAAA,UAC9B,OAAO,CAAC,OAAO,KAAK,MAAM,EAAE;AAAA,QAChC,CAAC;AACD,aAAK,QAAQ,OAAO,CAAC,KAAK,QAAQ;AAAA,MACtC;AAAA,IACJ;AACA,SAAK,iBAAiB,CAAC,MAAM,eAAe;AACxC,YAAM,EAAE,oBAAoB,IAAI;AAChC,UAAI,OAAO,KAAK;AAChB,aAAO,OAAO,IAAI,OAAO;AACzB,aAAO,QAAQ,KAAK,MAAM,KAAK;AAC/B,UAAI,CAAC,YAAY;AACb,gBAAQ;AAAA,MACZ;AACA,UAAI,SAAS,KAAK;AACd,aAAK,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,CAAC;AAAA,MAChD,OACK;AACD,aAAK,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,CAAC;AAAA,MAChD;AACA,WAAK,iBAAiB,KAAK,EAAE,OAAO,oBAAoB,KAAK,KAAK,EAAE,CAAC;AACrE,WAAK,YAAY;AACjB,WAAK,gBAAgB;AACrB,WAAK,eAAe,KAAK,EAAE,OAAO,oBAAoB,KAAK,KAAK,EAAE,CAAC;AAAA,IACvE;AACA,SAAK,SAAS,MAAM;AAChB,UAAI,KAAK,UAAU;AACf,aAAK,WAAW;AAChB,aAAK,QAAQ,KAAK;AAAA,MACtB;AAAA,IACJ;AACA,SAAK,UAAU,MAAM;AACjB,UAAI,CAAC,KAAK,UAAU;AAChB,aAAK,WAAW;AAChB,aAAK,SAAS,KAAK;AAAA,MACvB;AAAA,IACJ;AACA,SAAK,cAAc,CAAC,SAAS;AACzB,UAAI,CAAC,KAAK,UAAU;AAChB,aAAK,WAAW;AAChB,aAAK,SAAS,KAAK;AAAA,MACvB;AAEA,UAAI,KAAK,aAAa,KAAK,GAAG,YAAY;AACtC,cAAM,QAAQ,KAAK,GAAG,WAAW,cAAc,eAAe;AAC9D,cAAM,QAAQ,KAAK,GAAG,WAAW,cAAc,eAAe;AAE9D,kBAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,UAAU,OAAO,aAAa;AAClF,kBAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,UAAU,OAAO,aAAa;AAElF,cAAM,gBAAgB,SAAS,MAAM,QAAQ;AAC7C,0BAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU,IAAI,aAAa;AAAA,MAC3G;AAAA,IACJ;AACA,SAAK,aAAa,MAAM;AAGpB,iBAAW,MAAM;AACb,YAAI;AACJ,cAAM,iBAAiB,KAAK,KAAK,GAAG,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG;AACxF,cAAM,uBAAuB,iBAAiB,cAAc,UAAU,SAAS,mBAAmB;AAClG,YAAI,CAAC,sBAAsB;AACvB,cAAI,KAAK,UAAU;AACf,iBAAK,WAAW;AAChB,iBAAK,QAAQ,KAAK;AAAA,UACtB;AAEA,cAAI,KAAK,aAAa,KAAK,GAAG,YAAY;AACtC,kBAAM,QAAQ,KAAK,GAAG,WAAW,cAAc,eAAe;AAC9D,kBAAM,QAAQ,KAAK,GAAG,WAAW,cAAc,eAAe;AAC9D,sBAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,UAAU,OAAO,aAAa;AAClF,sBAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,UAAU,OAAO,aAAa;AAAA,UACtF;AAAA,QACJ;AAAA,MACJ,GAAG,CAAC;AAAA,IACR;AAAA,EACJ;AAAA,EACA,kBAAkB;AACd,UAAM,EAAE,UAAU,UAAU,iBAAiB,IAAI;AAKjD,SAAK,WAAW,aAAa,SAAY,qBAAqB,QAAQ,qBAAqB,SAAS,mBAAmB,WAAW,cAAc,UAAU,QAAQ;AAAA,EACtK;AAAA,EACA,WAAW,UAAU;AACjB,QAAI,CAAC,aAAa,QAAQ,GAAG;AACzB,WAAK,MAAM;AAAA,IACf;AACA,QAAI,CAAC,KAAK,UAAU;AAChB,WAAK,YAAY;AAAA,IACrB;AAAA,EACJ;AAAA,EACA,WAAW,UAAU;AACjB,QAAI,CAAC,aAAa,QAAQ,GAAG;AACzB,WAAK,MAAM;AAAA,IACf;AACA,QAAI,CAAC,KAAK,UAAU;AAChB,WAAK,YAAY;AAAA,IACrB;AAAA,EACJ;AAAA,EACA,YAAY,UAAU;AAClB,QAAI,CAAC,aAAa,QAAQ,GAAG;AACzB,WAAK,OAAO;AAAA,IAChB;AAAA,EACJ;AAAA,EACA,wBAAwB;AACpB,UAAM,EAAE,eAAe,IAAI;AAC3B,QAAI,mBAAmB,QAAW;AAC9B,UAAI,iBAAiB,KAAK,KAAK;AAC3B,wBAAgB,8CAA8C,cAAc,8BAA8B,KAAK,GAAG,uGAAuG,KAAK,EAAE;AAChO,aAAK,iBAAiB,KAAK;AAAA,MAC/B,WACS,iBAAiB,KAAK,KAAK;AAChC,wBAAgB,8CAA8C,cAAc,2BAA2B,KAAK,GAAG,uGAAuG,KAAK,EAAE;AAC7N,aAAK,iBAAiB,KAAK;AAAA,MAC/B;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,kBAAkB;AACd,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,OAAO,CAAC,KAAK,QAAQ;AAAA,IACtC;AAAA,EACJ;AAAA,EACA,aAAa,UAAU,UAAU;AAC7B,UAAM,gBAAgB,KAAK,cAAc,UAAU,QAAQ;AAC3D,QAAI,eAAe;AACf,WAAK,SAAS,KAAK,EAAE,OAAO,KAAK,MAAM,CAAC;AAAA,IAC5C;AACA,QAAI,CAAC,KAAK,UAAU;AAChB,WAAK,YAAY;AAAA,IACrB;AAAA,EACJ;AAAA,EACA,oBAAoB;AAKhB,QAAI,KAAK,GAAG,aAAa,IAAI,GAAG;AAC5B,WAAK,UAAU,KAAK,GAAG,aAAa,IAAI;AAAA,IAC5C;AACA,SAAK,sBAAsB,sBAAsB,KAAK,EAAE;AAGxD,SAAK,MAAM,aAAa,KAAK,GAAG,IAAI,KAAK,MAAM;AAC/C,SAAK,MAAM,aAAa,KAAK,GAAG,IAAI,KAAK,MAAM;AAC/C,SAAK,OAAO,aAAa,KAAK,IAAI,IAAI,KAAK,OAAO;AAAA,EACtD;AAAA,EACA,mBAAmB;AACf,SAAK,mBAAmB,KAAK;AAC7B,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,oBAAoB;AAChB,QAAI;AACJ,SAAK,YAAY;AACjB,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,sBAAsB;AAO3B,QAAI,KAAK,SAAS;AACd,WAAK,aAAa;AAAA,IACtB;AACA,UAAM,aAAa,sBAAsB,KAAK,EAAE;AAChD,SAAK,aAAa,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,cAAc,0BAA0B,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,EAC1K;AAAA,EACA,uBAAuB;AACnB,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,QAAQ;AACrB,WAAK,UAAU;AAAA,IACnB;AAAA,EACJ;AAAA,EACA,WAAW;AACP,QAAI;AACJ,UAAM,SAAS,KAAK,KAAK,WAAW,QAAQ,OAAO,SAAS,KAAK;AACjE,QAAI,KAAK,WAAW;AAChB,UAAI,OAAO,UAAU,UAAU;AAC3B,eAAO;AAAA,MACX;AACA,aAAO;AAAA,QACH,OAAO;AAAA,QACP,OAAO;AAAA,MACX;AAAA,IACJ,OACK;AACD,UAAI,OAAO,UAAU,UAAU;AAC3B,eAAO,MAAM;AAAA,MACjB;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB;AACd,SAAK,QAAQ,KAAK,oBAAoB,KAAK,KAAK;AAChD,SAAK,UAAU,KAAK,EAAE,OAAO,KAAK,MAAM,CAAC;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU;AACN,SAAK,iBAAiB,KAAK,EAAE,OAAO,KAAK,oBAAoB,KAAK,KAAK,EAAE,CAAC;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,OAAO,QAAQ;AACX,UAAM,EAAE,WAAW,YAAY,IAAI;AACnC,UAAM,WAAW,OAAO;AAMxB,QAAI,aAAa,KAAK,gBAAgB,QAAW;AAC7C,WAAK,wBAAwB,sBAAsB,SAAS;AAAA,IAChE;AAYA,QAAI,gBAAgB,QAAW;AAC3B,WAAK,eAAe,QAAQ;AAAA,IAChC;AACA,SAAK,OAAO,QAAQ;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,QAAQ;AACV,QAAI;AACJ,UAAM,EAAE,WAAW,sBAAsB,IAAI;AAC7C,UAAM,YAAY,KAAK,OAAO,cAAc,QAAQ,OAAO,SAAS,KAAK,OAAO;AAShF,QAAI,KAAK,gBAAgB,QAAW;AAChC,WAAK,eAAe,QAAQ;AAAA,IAChC;AAOA,QAAI,aAAa,KAAK,gBAAgB,QAAW;AAC7C,0BAAoB,WAAW,qBAAqB;AAAA,IACxD;AAEA,SAAK,OAAO,QAAQ;AAKpB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,eAAe,KAAK,EAAE,OAAO,KAAK,oBAAoB,KAAK,KAAK,EAAE,CAAC;AAAA,EAC5E;AAAA,EACA,OAAO,UAAU;AAGb,UAAM,OAAO,KAAK;AAClB,QAAI,QAAQ,MAAM,IAAI,WAAW,KAAK,QAAQ,KAAK,OAAO,CAAC;AAC3D,QAAI,MAAM,KAAK,EAAE,GAAG;AAChB,cAAQ,IAAI;AAAA,IAChB;AACA,QAAI,KAAK,OAAO;AAEZ,cAAQ,aAAa,aAAa,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK,GAAG;AAAA,IAC/F;AAEA,QAAI,KAAK,gBAAgB,KAAK;AAC1B,WAAK,SAAS;AAAA,IAClB,OACK;AACD,WAAK,SAAS;AAAA,IAClB;AAEA,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,eAAe,UAAU;AACrB,UAAM,OAAQ,KAAK,OAAO,KAAK,YAAY,sBAAsB;AAEjE,QAAI,QAAQ,MAAM,IAAI,WAAW,KAAK,QAAQ,KAAK,OAAO,CAAC;AAC3D,QAAI,MAAM,KAAK,EAAE,GAAG;AAChB,cAAQ,IAAI;AAAA,IAChB;AACA,SAAK,cAAc,CAAC,KAAK,aAAa,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,MAAM;AAC5G,SAAK,SAAS,KAAK,WAAW;AAAA,EAClC;AAAA,EACA,IAAI,OAAO;AACP,WAAO,aAAa,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI;AAAA,EAClE;AAAA,EACA,IAAI,OAAO;AACP,WAAO,aAAa,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI;AAAA,EAClE;AAAA,EACA,IAAI,aAAa;AACb,QAAI,KAAK,WAAW;AAChB,aAAO,KAAK,IAAI,KAAK,QAAQ,KAAK,MAAM;AAAA,IAC5C;AACA,UAAM,EAAE,eAAe,IAAI;AAC3B,QAAI,kBAAkB,MAAM;AACxB,aAAO;AAAA,IACX;AACA,WAAO,aAAa,gBAAgB,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1D;AAAA,EACA,IAAI,aAAa;AACb,QAAI,KAAK,WAAW;AAChB,aAAO,KAAK,IAAI,KAAK,QAAQ,KAAK,MAAM;AAAA,IAC5C;AACA,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,cAAc;AACV,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,EAAE,KAAK,IAAI,IAAI;AACrB,QAAI,KAAK,WAAW;AAChB,WAAK,SAAS,aAAa,MAAM,OAAO,KAAK,GAAG;AAChD,WAAK,SAAS,aAAa,MAAM,OAAO,KAAK,GAAG;AAAA,IACpD,OACK;AACD,WAAK,SAAS,aAAa,OAAO,KAAK,GAAG;AAAA,IAC9C;AAAA,EACJ;AAAA,EACA,cAAc;AACV,SAAK,WAAW;AAChB,UAAM,EAAE,MAAM,KAAK,IAAI;AACvB,SAAK,QAAQ,CAAC,KAAK,YACb,OACA;AAAA,MACE,OAAO,KAAK,IAAI,MAAM,IAAI;AAAA,MAC1B,OAAO,KAAK,IAAI,MAAM,IAAI;AAAA,IAC9B;AACJ,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,SAAS,MAAM;AACX,QAAI,KAAK,GAAG,YAAY;AACpB,YAAM,SAAS,KAAK,GAAG,WAAW,cAAc,SAAS,MAAM,kBAAkB,eAAe;AAChG,UAAI,QAAQ;AACR,eAAO,MAAM;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,sBAAsB;AACtB,WAAO,KAAK,GAAG,cAAc,gBAAgB,MAAM;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,oBAAoB;AACpB,WAAO,KAAK,GAAG,cAAc,cAAc,MAAM;AAAA,EACrD;AAAA,EACA,IAAI,WAAW;AACX,WAAO,KAAK,UAAU,UAAa,KAAK,GAAG,cAAc,gBAAgB,MAAM;AAAA,EACnF;AAAA,EACA,oBAAoB;AAChB,QAAI;AACJ,UAAM,EAAE,KAAK,KAAK,MAAM,gBAAgB,aAAa,UAAU,KAAK,YAAY,YAAY,cAAc,oBAAqB,IAAI;AACnI,QAAI,WAAW,GAAG,aAAa,GAAG;AAClC,QAAI,SAAS,GAAG,MAAM,aAAa,GAAG;AACtC,UAAM,MAAM,MAAM,KAAK,EAAE;AACzB,UAAM,QAAQ,MAAM,UAAU;AAC9B,UAAM,MAAM,MAAM,SAAS;AAC3B,UAAM,YAAY,CAAC,SAAS;AACxB,aAAO;AAAA,QACH,CAAC,KAAK,GAAG,KAAK,KAAK;AAAA,MACvB;AAAA,IACJ;AACA,QAAI,KAAK,cAAc,OAAO;AAK1B,UAAI,KAAK,SAAS,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,KAAK,KAAK,MAAM;AAQpF,mBAAW,GAAG,aAAa,GAAG;AAC9B,iBAAS,GAAG,MAAM,aAAa,GAAG;AAAA,MACtC,OACK;AAOD,mBAAW,GAAG,aAAa,GAAG;AAC9B,iBAAS,GAAG,MAAM,aAAa,GAAG;AAAA,MACtC;AAAA,IACJ;AACA,UAAM,WAAW;AAAA,MACb,CAAC,KAAK,GAAG;AAAA,MACT,CAAC,GAAG,GAAG;AAAA,IACX;AACA,UAAM,QAAQ,CAAC;AACf,QAAI,KAAK,SAAS,KAAK,OAAO;AAC1B,eAAS,QAAQ,KAAK,SAAS,KAAK,SAAS,MAAM;AAC/C,cAAM,QAAQ,aAAa,OAAO,KAAK,GAAG;AAC1C,cAAM,WAAW,KAAK,IAAI,YAAY,UAAU;AAChD,cAAM,WAAW,KAAK,IAAI,YAAY,UAAU;AAChD,cAAM,OAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA,UAKA,QAAQ,SAAS,YAAY,SAAS;AAAA,QAC1C;AACA,aAAK,KAAK,IAAI,GAAG,QAAQ,GAAG;AAC5B,cAAM,KAAK,IAAI;AAAA,MACnB;AAAA,IACJ;AACA,WAAQ,EAAE,OAAO;AAAA,MAAE,OAAO;AAAA,MAAgB,KAAK,CAAC,YAAa,KAAK,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAY5E,aAAa,CAAC,OAAO;AAajB,YAAI,KAAK,gBAAgB,QAAW;AAChC,eAAK,QAAQ;AACb,eAAK,MAAM,EAAE;AAAA,QACjB;AAAA,MACJ;AAAA,IAAE,GAAG,MAAM,IAAI,CAAC,SAAU,EAAE,OAAO,EAAE,OAAO,UAAU,IAAI,GAAG,MAAM,gBAAgB,OAAO;AAAA,MACtF,cAAc;AAAA,MACd,qBAAqB,KAAK;AAAA,IAC9B,GAAG,MAAM,KAAK,SAAS,gBAAgB,OAAO,CAAC,CAAE,GAAG,EAAE,OAAO,EAAE,OAAO,sBAAsB,GAAG,EAAE,OAAO,EAAE,OAAO,aAAa,MAAM,gBAAgB,MAAM,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO;AAAA,MAClL,aAAa;AAAA,MACb,oBAAoB;AAAA,MACpB,aAAa,MAAM,SAAS;AAAA,IAChC,GAAG,MAAM,gBAAgB,OAAO,UAAU,MAAM,aAAa,CAAC,CAAC,GAAG,WAAW,KAAK;AAAA,MAClF,MAAM;AAAA,MACN,SAAS,gBAAgB;AAAA,MACzB,OAAO,KAAK;AAAA,MACZ,OAAO,KAAK;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa,KAAK;AAAA,MAClB,YAAY,KAAK;AAAA,IACrB,CAAC,GAAG,KAAK,aACL,WAAW,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,gBAAgB;AAAA,MACzB,OAAO,KAAK;AAAA,MACZ,OAAO,KAAK;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa,KAAK;AAAA,MAClB,YAAY,KAAK;AAAA,IACrB,CAAC,CAAC;AAAA,EACV;AAAA,EACA,SAAS;AACL,UAAM,EAAE,UAAU,IAAI,UAAU,SAAS,KAAK,aAAa,gBAAgB,MAAM,IAAI;AACrF,UAAM,SAAS,YAAY,YAAY,EAAE;AAKzC,UAAM,kBAAmB,aAAa,mBAAmB,WAAW,mBAAmB,YAAa,KAAK;AACzG,UAAM,uBAAuB,UAAU,CAAC;AAKxC,UAAM,gBAAiB,YAAY,mBAAmB,SAAU,KAAK;AACrE,UAAM,qBAAqB,UAAU,CAAC;AACtC,UAAM,OAAO,WAAW,IAAI;AAC5B,sBAAkB,MAAM,IAAI,KAAK,MAAM,KAAK,UAAU,KAAK,SAAS,CAAC,GAAG,QAAQ;AAChF,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,WAAW,KAAK,SAAS,YAAY,KAAK,QAAQ,IAAI,SAAS,OAAO,mBAAmB,KAAK,OAAO;AAAA,MAChK,CAAC,IAAI,GAAG;AAAA,MACR,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,iBAAiB,gBAAgB;AAAA,MACjC,iBAAiB;AAAA,MACjB,CAAC,yBAAyB,cAAc,EAAE,GAAG;AAAA,MAC7C,+BAA+B;AAAA,MAC/B,6BAA6B;AAAA,IACjC,CAAC,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,4CAA4C,OAAO,iBAAiB,IAAI,cAAc,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO;AAAA,MAChL,sBAAsB;AAAA,MACtB,6BAA6B,CAAC;AAAA,IAClC,GAAG,MAAM,QAAQ,GAAG,UAAU,SAAY,EAAE,OAAO,EAAE,OAAO,aAAa,GAAG,KAAK,IAAI,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,iBAAiB,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,MAAM,QAAQ,CAAC,GAAG,KAAK,kBAAkB,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,MAAM,MAAM,CAAC,CAAC,CAAC,CAAC;AAAA,EACzY;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,YAAY,CAAC,iBAAiB;AAAA,MAC9B,OAAO,CAAC,YAAY;AAAA,MACpB,OAAO,CAAC,YAAY;AAAA,MACpB,QAAQ,CAAC,aAAa;AAAA,MACtB,kBAAkB,CAAC,uBAAuB;AAAA,MAC1C,YAAY,CAAC,iBAAiB;AAAA,MAC9B,SAAS,CAAC,cAAc;AAAA,IAC5B;AAAA,EAAG;AACP;AACA,IAAM,aAAa,CAAC,KAAK,EAAE,MAAM,OAAO,OAAO,KAAK,KAAK,UAAU,SAAS,KAAK,gBAAgB,cAAc,qBAAqB,aAAa,WAAY,MAAM;AAC/J,QAAM,QAAQ,MAAM,UAAU;AAC9B,QAAM,YAAY,MAAM;AACpB,UAAM,QAAQ,CAAC;AACf,UAAM,KAAK,IAAI,GAAG,QAAQ,GAAG;AAC7B,WAAO;AAAA,EACX;AAEA,QAAM,YAAY,oBAAoB,YAAY;AAClD,SAAQ,EAAE,OAAO,EAAE,WAAW,CAAC,OAAO;AAC9B,UAAM,MAAM,GAAG;AACf,QAAI,QAAQ,eAAe,QAAQ,aAAa;AAC5C,qBAAe,MAAM,KAAK;AAC1B,SAAG,eAAe;AAClB,SAAG,gBAAgB;AAAA,IACvB,WACS,QAAQ,gBAAgB,QAAQ,WAAW;AAChD,qBAAe,MAAM,IAAI;AACzB,SAAG,eAAe;AAClB,SAAG,gBAAgB;AAAA,IACvB;AAAA,EACJ,GAAG,SAAS,MAAM,YAAY,IAAI,GAAG,QAAQ,YAAY,OAAO;AAAA,IAC5D,qBAAqB;AAAA,IACrB,gBAAgB,SAAS;AAAA,IACzB,gBAAgB,SAAS;AAAA,IACzB,sBAAsB;AAAA,IACtB,kBAAkB,UAAU;AAAA,IAC5B,kBAAkB,UAAU;AAAA,IAC5B,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,EACrB,GAAG,OAAO,UAAU,GAAG,MAAM,UAAU,UAAU,WAAW,KAAK,GAAG,cAAc,cAAc,SAAY,YAAY,MAAM,mBAAmB,cAAc,SAAY,gBAAgB,MAAM,iBAAiB,KAAK,iBAAiB,KAAK,iBAAiB,WAAW,SAAS,MAAM,iBAAiB,MAAM,GAAG,OAAQ,EAAE,OAAO,EAAE,OAAO,aAAa,MAAM,gBAAgB,MAAM,MAAM,GAAG,aAAa,KAAK,CAAC,GAAI,EAAE,OAAO,EAAE,OAAO,cAAc,MAAM,gBAAgB,MAAM,OAAO,CAAC,CAAC;AACle;AACA,IAAM,eAAe,CAAC,OAAO,KAAK,KAAK,SAAS;AAC5C,MAAI,SAAS,MAAM,OAAO;AAC1B,MAAI,OAAO,GAAG;AAEV,YAAQ,KAAK,MAAM,QAAQ,IAAI,IAAI,OAAO;AAAA,EAC9C;AACA,QAAM,eAAe,MAAM,KAAK,OAAO,GAAG;AAC1C,SAAO,wBAAwB,cAAc,KAAK,KAAK,IAAI;AAC/D;AACA,IAAM,eAAe,CAAC,OAAO,KAAK,QAAQ;AACtC,SAAO,MAAM,IAAI,QAAQ,QAAQ,MAAM,MAAM,CAAC;AAClD;AACA,IAAI,WAAW;AACf,MAAM,QAAQ;AAAA,EACV,KAAK;AAAA,EACL,IAAI;AACR;", "names": []}