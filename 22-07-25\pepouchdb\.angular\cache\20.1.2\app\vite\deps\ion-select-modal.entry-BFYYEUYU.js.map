{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-select-modal.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, n as forceUpdate, e as getIonMode, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { s as safeCall } from './overlays-8Y2rA-ps.js';\nimport { g as getClassMap } from './theme-DiVJyqlX.js';\nimport './index-ZjP4CjeZ.js';\nimport './helpers-1O4D2b7y.js';\nimport './hardware-back-button-DcH0BbDp.js';\nimport './framework-delegate-DxcnWic_.js';\nimport './gesture-controller-BTEOs1at.js';\n\nconst ionicSelectModalMdCss = \".sc-ion-select-modal-ionic-h{height:100%}ion-list.sc-ion-select-modal-ionic ion-radio.sc-ion-select-modal-ionic::part(container){display:none}ion-list.sc-ion-select-modal-ionic ion-radio.sc-ion-select-modal-ionic::part(label){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-item.sc-ion-select-modal-ionic{--inner-border-width:0}.item-radio-checked.sc-ion-select-modal-ionic{--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08);--background-focused:var(--ion-color-primary, #0054e9);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #0054e9);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-modal-ionic{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #0054e9)}\";\n\nconst selectModalIosCss = \".sc-ion-select-modal-ios-h{height:100%}ion-item.sc-ion-select-modal-ios{--inner-padding-end:0}ion-radio.sc-ion-select-modal-ios::after{bottom:0;position:absolute;width:calc(100% - 0.9375rem - 16px);border-width:0px 0px 0.55px 0px;border-style:solid;border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));content:\\\"\\\"}ion-radio.sc-ion-select-modal-ios::after{inset-inline-start:calc(0.9375rem + 16px)}\";\n\nconst selectModalMdCss = \".sc-ion-select-modal-md-h{height:100%}ion-list.sc-ion-select-modal-md ion-radio.sc-ion-select-modal-md::part(container){display:none}ion-list.sc-ion-select-modal-md ion-radio.sc-ion-select-modal-md::part(label){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-item.sc-ion-select-modal-md{--inner-border-width:0}.item-radio-checked.sc-ion-select-modal-md{--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08);--background-focused:var(--ion-color-primary, #0054e9);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #0054e9);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-modal-md{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #0054e9)}\";\n\nconst SelectModal = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.options = [];\n    }\n    closeModal() {\n        const modal = this.el.closest('ion-modal');\n        if (modal) {\n            modal.dismiss();\n        }\n    }\n    findOptionFromEvent(ev) {\n        const { options } = this;\n        return options.find((o) => o.value === ev.target.value);\n    }\n    getValues(ev) {\n        const { multiple, options } = this;\n        if (multiple) {\n            // this is a modal with checkboxes (multiple value select)\n            // return an array of all the checked values\n            return options.filter((o) => o.checked).map((o) => o.value);\n        }\n        // this is a modal with radio buttons (single value select)\n        // return the value that was clicked, otherwise undefined\n        const option = ev ? this.findOptionFromEvent(ev) : null;\n        return option ? option.value : undefined;\n    }\n    callOptionHandler(ev) {\n        const option = this.findOptionFromEvent(ev);\n        const values = this.getValues(ev);\n        if (option === null || option === void 0 ? void 0 : option.handler) {\n            safeCall(option.handler, values);\n        }\n    }\n    setChecked(ev) {\n        const { multiple } = this;\n        const option = this.findOptionFromEvent(ev);\n        // this is a modal with checkboxes (multiple value select)\n        // we need to set the checked value for this option\n        if (multiple && option) {\n            option.checked = ev.detail.checked;\n        }\n    }\n    renderRadioOptions() {\n        const checked = this.options.filter((o) => o.checked).map((o) => o.value)[0];\n        return (h(\"ion-radio-group\", { value: checked, onIonChange: (ev) => this.callOptionHandler(ev) }, this.options.map((option) => (h(\"ion-item\", { lines: \"none\", class: Object.assign({\n                // TODO FW-4784\n                'item-radio-checked': option.value === checked\n            }, getClassMap(option.cssClass)) }, h(\"ion-radio\", { value: option.value, disabled: option.disabled, justify: \"start\", labelPlacement: \"end\", onClick: () => this.closeModal(), onKeyUp: (ev) => {\n                if (ev.key === ' ') {\n                    /**\n                     * Selecting a radio option with keyboard navigation,\n                     * either through the Enter or Space keys, should\n                     * dismiss the modal.\n                     */\n                    this.closeModal();\n                }\n            } }, option.text))))));\n    }\n    renderCheckboxOptions() {\n        return this.options.map((option) => (h(\"ion-item\", { class: Object.assign({\n                // TODO FW-4784\n                'item-checkbox-checked': option.checked\n            }, getClassMap(option.cssClass)) }, h(\"ion-checkbox\", { value: option.value, disabled: option.disabled, checked: option.checked, justify: \"start\", labelPlacement: \"end\", onIonChange: (ev) => {\n                this.setChecked(ev);\n                this.callOptionHandler(ev);\n                // TODO FW-4784\n                forceUpdate(this);\n            } }, option.text))));\n    }\n    render() {\n        return (h(Host, { key: 'b6c0dec240b2e41985b15fdf4e5a6d3a145c1567', class: getIonMode(this) }, h(\"ion-header\", { key: 'cd177e85ee0f62a60a3a708342d6ab6eb19a44dc' }, h(\"ion-toolbar\", { key: 'aee8222a5a4daa540ad202b2e4cac1ef93d9558c' }, this.header !== undefined && h(\"ion-title\", { key: '5f8fecc764d97bf840d3d4cfddeeccd118ab4436' }, this.header), h(\"ion-buttons\", { key: '919033950d7c2b0101f96a9c9698219de9f568ea', slot: \"end\" }, h(\"ion-button\", { key: '34b571cab6dced4bde555a077a21e91800829931', onClick: () => this.closeModal() }, \"Close\")))), h(\"ion-content\", { key: '3c9153d26ba7a5a03d3b20fcd628d0c3031661a7' }, h(\"ion-list\", { key: 'e00b222c071bc97c82ad1bba4db95a8a5c43ed6d' }, this.multiple === true ? this.renderCheckboxOptions() : this.renderRadioOptions()))));\n    }\n    get el() { return getElement(this); }\n};\nSelectModal.style = {\n    ionic: ionicSelectModalMdCss,\n    ios: selectModalIosCss,\n    md: selectModalMdCss\n};\n\nexport { SelectModal as ion_select_modal };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAYA,IAAM,wBAAwB;AAE9B,IAAM,oBAAoB;AAE1B,IAAM,mBAAmB;AAEzB,IAAM,cAAc,MAAM;AAAA,EACtB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,UAAU,CAAC;AAAA,EACpB;AAAA,EACA,aAAa;AACT,UAAM,QAAQ,KAAK,GAAG,QAAQ,WAAW;AACzC,QAAI,OAAO;AACP,YAAM,QAAQ;AAAA,IAClB;AAAA,EACJ;AAAA,EACA,oBAAoB,IAAI;AACpB,UAAM,EAAE,QAAQ,IAAI;AACpB,WAAO,QAAQ,KAAK,CAAC,MAAM,EAAE,UAAU,GAAG,OAAO,KAAK;AAAA,EAC1D;AAAA,EACA,UAAU,IAAI;AACV,UAAM,EAAE,UAAU,QAAQ,IAAI;AAC9B,QAAI,UAAU;AAGV,aAAO,QAAQ,OAAO,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK;AAAA,IAC9D;AAGA,UAAM,SAAS,KAAK,KAAK,oBAAoB,EAAE,IAAI;AACnD,WAAO,SAAS,OAAO,QAAQ;AAAA,EACnC;AAAA,EACA,kBAAkB,IAAI;AAClB,UAAM,SAAS,KAAK,oBAAoB,EAAE;AAC1C,UAAM,SAAS,KAAK,UAAU,EAAE;AAChC,QAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS;AAChE,eAAS,OAAO,SAAS,MAAM;AAAA,IACnC;AAAA,EACJ;AAAA,EACA,WAAW,IAAI;AACX,UAAM,EAAE,SAAS,IAAI;AACrB,UAAM,SAAS,KAAK,oBAAoB,EAAE;AAG1C,QAAI,YAAY,QAAQ;AACpB,aAAO,UAAU,GAAG,OAAO;AAAA,IAC/B;AAAA,EACJ;AAAA,EACA,qBAAqB;AACjB,UAAM,UAAU,KAAK,QAAQ,OAAO,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC;AAC3E,WAAQ,EAAE,mBAAmB,EAAE,OAAO,SAAS,aAAa,CAAC,OAAO,KAAK,kBAAkB,EAAE,EAAE,GAAG,KAAK,QAAQ,IAAI,CAAC,WAAY,EAAE,YAAY,EAAE,OAAO,QAAQ,OAAO,OAAO,OAAO;AAAA;AAAA,MAE5K,sBAAsB,OAAO,UAAU;AAAA,IAC3C,GAAG,YAAY,OAAO,QAAQ,CAAC,EAAE,GAAG,EAAE,aAAa,EAAE,OAAO,OAAO,OAAO,UAAU,OAAO,UAAU,SAAS,SAAS,gBAAgB,OAAO,SAAS,MAAM,KAAK,WAAW,GAAG,SAAS,CAAC,OAAO;AAC7L,UAAI,GAAG,QAAQ,KAAK;AAMhB,aAAK,WAAW;AAAA,MACpB;AAAA,IACJ,EAAE,GAAG,OAAO,IAAI,CAAC,CAAE,CAAC;AAAA,EAC5B;AAAA,EACA,wBAAwB;AACpB,WAAO,KAAK,QAAQ,IAAI,CAAC,WAAY,EAAE,YAAY,EAAE,OAAO,OAAO,OAAO;AAAA;AAAA,MAElE,yBAAyB,OAAO;AAAA,IACpC,GAAG,YAAY,OAAO,QAAQ,CAAC,EAAE,GAAG,EAAE,gBAAgB,EAAE,OAAO,OAAO,OAAO,UAAU,OAAO,UAAU,SAAS,OAAO,SAAS,SAAS,SAAS,gBAAgB,OAAO,aAAa,CAAC,OAAO;AAC3L,WAAK,WAAW,EAAE;AAClB,WAAK,kBAAkB,EAAE;AAEzB,kBAAY,IAAI;AAAA,IACpB,EAAE,GAAG,OAAO,IAAI,CAAC,CAAE;AAAA,EAC3B;AAAA,EACA,SAAS;AACL,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,OAAO,WAAW,IAAI,EAAE,GAAG,EAAE,cAAc,EAAE,KAAK,2CAA2C,GAAG,EAAE,eAAe,EAAE,KAAK,2CAA2C,GAAG,KAAK,WAAW,UAAa,EAAE,aAAa,EAAE,KAAK,2CAA2C,GAAG,KAAK,MAAM,GAAG,EAAE,eAAe,EAAE,KAAK,4CAA4C,MAAM,MAAM,GAAG,EAAE,cAAc,EAAE,KAAK,4CAA4C,SAAS,MAAM,KAAK,WAAW,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,eAAe,EAAE,KAAK,2CAA2C,GAAG,EAAE,YAAY,EAAE,KAAK,2CAA2C,GAAG,KAAK,aAAa,OAAO,KAAK,sBAAsB,IAAI,KAAK,mBAAmB,CAAC,CAAC,CAAC;AAAA,EAC/vB;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AACxC;AACA,YAAY,QAAQ;AAAA,EAChB,OAAO;AAAA,EACP,KAAK;AAAA,EACL,IAAI;AACR;", "names": []}