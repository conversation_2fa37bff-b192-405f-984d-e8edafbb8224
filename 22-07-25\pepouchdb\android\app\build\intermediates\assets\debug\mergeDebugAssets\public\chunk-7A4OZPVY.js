import{d as N,f as O,h as k}from"./chunk-KRSA4YKC.js";import{a as F,b as H}from"./chunk-XCHF2ADM.js";import"./chunk-FPOZYJOD.js";import{b as R,f as I,g as T,i as _}from"./chunk-XFXTD7QR.js";import{a as S,b as A}from"./chunk-CKP3SGE2.js";import"./chunk-EHNA26RN.js";import{g as h}from"./chunk-2R6CW7ES.js";var C=new WeakMap,P=(e,o,t,s=0,r=!1)=>{C.has(e)!==t&&(t?W(e,o,s,r):z(e,o))},G=e=>e===e.getRootNode().activeElement,W=(e,o,t,s=!1)=>{let r=o.parentNode,n=o.cloneNode(!1);n.classList.add("cloned-input"),n.tabIndex=-1,s&&(n.disabled=!0),r.appendChild(n),C.set(e,n);let a=e.ownerDocument.dir==="rtl"?9999:-9999;e.style.pointerEvents="none",o.style.transform=`translate3d(${a}px,${t}px,0) scale(0)`},z=(e,o)=>{let t=C.get(e);t&&(C.delete(e),t.remove()),e.style.pointerEvents="",o.style.transform=""},q=50,j=(e,o,t)=>{if(!t||!o)return()=>{};let s=a=>{G(o)&&P(e,o,a)},r=()=>P(e,o,!1),n=()=>s(!0),c=()=>s(!1);return I(t,"ionScrollStart",n),I(t,"ionScrollEnd",c),o.addEventListener("blur",r),()=>{T(t,"ionScrollStart",n),T(t,"ionScrollEnd",c),o.removeEventListener("blur",r)}},B="input, textarea, [no-blur], [contenteditable]",J=()=>{let e=!0,o=!1,t=document,s=()=>{o=!0},r=()=>{e=!0},n=c=>{if(o){o=!1;return}let a=t.activeElement;if(!a||a.matches(B))return;let f=c.target;f!==a&&(f.matches(B)||f.closest(B)||(e=!1,setTimeout(()=>{e||a.blur()},50)))};return I(t,"ionScrollStart",s),t.addEventListener("focusin",r,!0),t.addEventListener("touchend",n,!1),()=>{T(t,"ionScrollStart",s,!0),t.removeEventListener("focusin",r,!0),t.removeEventListener("touchend",n,!1)}},Q=.3,V=(e,o,t,s)=>{var r;let n=(r=e.closest("ion-item,[ion-item]"))!==null&&r!==void 0?r:e;return X(n.getBoundingClientRect(),o.getBoundingClientRect(),t,s)},X=(e,o,t,s)=>{let r=e.top,n=e.bottom,c=o.top,a=Math.min(o.bottom,s-t),f=c+15,u=a-q-n,d=f-r,m=Math.round(u<0?-u:d>0?-d:0),v=Math.min(m,r-c),i=Math.abs(v)/Q,y=Math.min(400,Math.max(150,i));return{scrollAmount:v,scrollDuration:y,scrollPadding:t,inputSafeY:-(r-f)+4}},$="$ionPaddingTimer",K=(e,o,t)=>{let s=e[$];s&&clearTimeout(s),o>0?e.style.setProperty("--keyboard-offset",`${o}px`):e[$]=setTimeout(()=>{e.style.setProperty("--keyboard-offset","0px"),t&&t()},120)},U=(e,o,t)=>{let s=()=>{o&&K(o,0,t)};e.addEventListener("focusout",s,{once:!0})},D=0,p="data-ionic-skip-scroll-assist",Z=(e,o,t,s,r,n,c,a=!1)=>{let f=n&&(c===void 0||c.mode===F.None),l=!1,u=S!==void 0?S.innerHeight:0,d=b=>{if(l===!1){l=!0;return}Y(e,o,t,s,b.detail.keyboardHeight,f,a,u,!1)},m=()=>{l=!1,S===null||S===void 0||S.removeEventListener("ionKeyboardDidShow",d),e.removeEventListener("focusout",m)},v=()=>h(null,null,function*(){if(o.hasAttribute(p)){o.removeAttribute(p);return}Y(e,o,t,s,r,f,a,u),S===null||S===void 0||S.addEventListener("ionKeyboardDidShow",d),e.addEventListener("focusout",m)});return e.addEventListener("focusin",v),()=>{e.removeEventListener("focusin",v),S===null||S===void 0||S.removeEventListener("ionKeyboardDidShow",d),e.removeEventListener("focusout",m)}},M=e=>{var o;if(document.activeElement===e)return;let t=e.getAttribute("id"),s=e.closest(`label[for="${t}"]`),r=(o=document.activeElement)===null||o===void 0?void 0:o.closest(`label[for="${t}"]`);s!==null&&s===r||(e.setAttribute(p,"true"),e.focus())},Y=(e,o,t,s,r,n,c=!1,a=0,f=!0)=>h(null,null,function*(){if(!t&&!s)return;let l=V(e,t||s,r,a);if(t&&Math.abs(l.scrollAmount)<4){M(o),n&&t!==null&&(K(t,D),U(o,t,()=>D=0));return}if(P(e,o,!0,l.inputSafeY,c),M(o),_(()=>e.click()),n&&t&&(D=l.scrollPadding,K(t,D)),typeof window<"u"){let u,d=()=>h(null,null,function*(){u!==void 0&&clearTimeout(u),window.removeEventListener("ionKeyboardDidShow",m),window.removeEventListener("ionKeyboardDidShow",d),t&&(yield k(t,0,l.scrollAmount,l.scrollDuration)),P(e,o,!1,l.inputSafeY),M(o),n&&U(o,t,()=>D=0)}),m=()=>{window.removeEventListener("ionKeyboardDidShow",m),window.addEventListener("ionKeyboardDidShow",d)};if(t){let v=yield N(t),b=v.scrollHeight-v.clientHeight;if(f&&l.scrollAmount>b-v.scrollTop){o.type==="password"?(l.scrollAmount+=q,window.addEventListener("ionKeyboardDidShow",m)):window.addEventListener("ionKeyboardDidShow",d),u=setTimeout(d,1e3);return}}d()}}),E=!0,ae=(e,o)=>h(null,null,function*(){if(A===void 0)return;let t=o==="ios",s=o==="android",r=e.getNumber("keyboardHeight",290),n=e.getBoolean("scrollAssist",!0),c=e.getBoolean("hideCaretOnScroll",t),a=e.getBoolean("inputBlurring",!1),f=e.getBoolean("scrollPadding",!0),l=Array.from(A.querySelectorAll("ion-input, ion-textarea")),u=new WeakMap,d=new WeakMap,m=yield H.getResizeMode(),v=i=>h(null,null,function*(){yield new Promise(g=>R(i,g));let y=i.shadowRoot||i,w=y.querySelector("input")||y.querySelector("textarea"),L=O(i),x=L?null:i.closest("ion-footer");if(!w)return;if(L&&c&&!u.has(i)){let g=j(i,w,L);u.set(i,g)}if(!(w.type==="date"||w.type==="datetime-local")&&(L||x)&&n&&!d.has(i)){let g=Z(i,w,L,x,r,f,m,s);d.set(i,g)}}),b=i=>{if(c){let y=u.get(i);y&&y(),u.delete(i)}if(n){let y=d.get(i);y&&y(),d.delete(i)}};a&&E&&J();for(let i of l)v(i);A.addEventListener("ionInputDidLoad",i=>{v(i.detail)}),A.addEventListener("ionInputDidUnload",i=>{b(i.detail)})});export{ae as startInputShims};
