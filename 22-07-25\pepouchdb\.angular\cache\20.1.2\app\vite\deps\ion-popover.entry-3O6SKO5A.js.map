{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-popover.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, m as printIonWarning, e as getIonMode, a as isPlatform, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { B as BACKDROP, j as prepareOverlay, k as setOverlayId, f as present, n as focusFirstDescendant, g as dismiss, h as eventMethod, F as FOCUS_TRAP_DISABLE_CLASS } from './overlays-8Y2rA-ps.js';\nimport { C as CoreDelegate, a as attachComponent, d as detachComponent } from './framework-delegate-DxcnWic_.js';\nimport { g as getElementRoot, r as raf, f as addEventListener, h as hasLazyBuild } from './helpers-1O4D2b7y.js';\nimport { c as createLockController } from './lock-controller-B-hirT0v.js';\nimport { g as getClassMap } from './theme-DiVJyqlX.js';\nimport { e as deepReady, w as waitForMount } from './index-DfBA5ztX.js';\nimport { c as createAnimation } from './animation-BWcUKtbn.js';\nimport './index-ZjP4CjeZ.js';\nimport './hardware-back-button-DcH0BbDp.js';\nimport './gesture-controller-BTEOs1at.js';\n\n/**\n * Returns the dimensions of the popover\n * arrow on `ios` mode. If arrow is disabled\n * returns (0, 0).\n */\nconst getArrowDimensions = (arrowEl) => {\n    if (!arrowEl) {\n        return { arrowWidth: 0, arrowHeight: 0 };\n    }\n    const { width, height } = arrowEl.getBoundingClientRect();\n    return { arrowWidth: width, arrowHeight: height };\n};\n/**\n * Returns the recommended dimensions of the popover\n * that takes into account whether or not the width\n * should match the trigger width.\n */\nconst getPopoverDimensions = (size, contentEl, triggerEl) => {\n    const contentDimentions = contentEl.getBoundingClientRect();\n    const contentHeight = contentDimentions.height;\n    let contentWidth = contentDimentions.width;\n    if (size === 'cover' && triggerEl) {\n        const triggerDimensions = triggerEl.getBoundingClientRect();\n        contentWidth = triggerDimensions.width;\n    }\n    return {\n        contentWidth,\n        contentHeight,\n    };\n};\nconst configureDismissInteraction = (triggerEl, triggerAction, popoverEl, parentPopoverEl) => {\n    let dismissCallbacks = [];\n    const root = getElementRoot(parentPopoverEl);\n    const parentContentEl = root.querySelector('.popover-content');\n    switch (triggerAction) {\n        case 'hover':\n            dismissCallbacks = [\n                {\n                    /**\n                     * Do not use mouseover here\n                     * as this will causes the event to\n                     * be dispatched on each underlying\n                     * element rather than on the popover\n                     * content as a whole.\n                     */\n                    eventName: 'mouseenter',\n                    callback: (ev) => {\n                        /**\n                         * Do not dismiss the popover is we\n                         * are hovering over its trigger.\n                         * This would be easier if we used mouseover\n                         * but this would cause the event to be dispatched\n                         * more often than we would like, potentially\n                         * causing performance issues.\n                         */\n                        const element = document.elementFromPoint(ev.clientX, ev.clientY);\n                        if (element === triggerEl) {\n                            return;\n                        }\n                        popoverEl.dismiss(undefined, undefined, false);\n                    },\n                },\n            ];\n            break;\n        case 'context-menu':\n        case 'click':\n        default:\n            dismissCallbacks = [\n                {\n                    eventName: 'click',\n                    callback: (ev) => {\n                        /**\n                         * Do not dismiss the popover is we\n                         * are hovering over its trigger.\n                         */\n                        const target = ev.target;\n                        const closestTrigger = target.closest('[data-ion-popover-trigger]');\n                        if (closestTrigger === triggerEl) {\n                            /**\n                             * stopPropagation here so if the\n                             * popover has dismissOnSelect=\"true\"\n                             * the popover does not dismiss since\n                             * we just clicked a trigger element.\n                             */\n                            ev.stopPropagation();\n                            return;\n                        }\n                        popoverEl.dismiss(undefined, undefined, false);\n                    },\n                },\n            ];\n            break;\n    }\n    dismissCallbacks.forEach(({ eventName, callback }) => parentContentEl.addEventListener(eventName, callback));\n    return () => {\n        dismissCallbacks.forEach(({ eventName, callback }) => parentContentEl.removeEventListener(eventName, callback));\n    };\n};\n/**\n * Configures the triggerEl to respond\n * to user interaction based upon the triggerAction\n * prop that devs have defined.\n */\nconst configureTriggerInteraction = (triggerEl, triggerAction, popoverEl) => {\n    let triggerCallbacks = [];\n    /**\n     * Based upon the kind of trigger interaction\n     * the user wants, we setup the correct event\n     * listeners.\n     */\n    switch (triggerAction) {\n        case 'hover':\n            let hoverTimeout;\n            triggerCallbacks = [\n                {\n                    eventName: 'mouseenter',\n                    callback: async (ev) => {\n                        ev.stopPropagation();\n                        if (hoverTimeout) {\n                            clearTimeout(hoverTimeout);\n                        }\n                        /**\n                         * Hovering over a trigger should not\n                         * immediately open the next popover.\n                         */\n                        hoverTimeout = setTimeout(() => {\n                            raf(() => {\n                                popoverEl.presentFromTrigger(ev);\n                                hoverTimeout = undefined;\n                            });\n                        }, 100);\n                    },\n                },\n                {\n                    eventName: 'mouseleave',\n                    callback: (ev) => {\n                        if (hoverTimeout) {\n                            clearTimeout(hoverTimeout);\n                        }\n                        /**\n                         * If mouse is over another popover\n                         * that is not this popover then we should\n                         * close this popover.\n                         */\n                        const target = ev.relatedTarget;\n                        if (!target) {\n                            return;\n                        }\n                        if (target.closest('ion-popover') !== popoverEl) {\n                            popoverEl.dismiss(undefined, undefined, false);\n                        }\n                    },\n                },\n                {\n                    /**\n                     * stopPropagation here prevents the popover\n                     * from dismissing when dismiss-on-select=\"true\".\n                     */\n                    eventName: 'click',\n                    callback: (ev) => ev.stopPropagation(),\n                },\n                {\n                    eventName: 'ionPopoverActivateTrigger',\n                    callback: (ev) => popoverEl.presentFromTrigger(ev, true),\n                },\n            ];\n            break;\n        case 'context-menu':\n            triggerCallbacks = [\n                {\n                    eventName: 'contextmenu',\n                    callback: (ev) => {\n                        /**\n                         * Prevents the platform context\n                         * menu from appearing.\n                         */\n                        ev.preventDefault();\n                        popoverEl.presentFromTrigger(ev);\n                    },\n                },\n                {\n                    eventName: 'click',\n                    callback: (ev) => ev.stopPropagation(),\n                },\n                {\n                    eventName: 'ionPopoverActivateTrigger',\n                    callback: (ev) => popoverEl.presentFromTrigger(ev, true),\n                },\n            ];\n            break;\n        case 'click':\n        default:\n            triggerCallbacks = [\n                {\n                    /**\n                     * Do not do a stopPropagation() here\n                     * because if you had two click triggers\n                     * then clicking the first trigger and then\n                     * clicking the second trigger would not cause\n                     * the first popover to dismiss.\n                     */\n                    eventName: 'click',\n                    callback: (ev) => popoverEl.presentFromTrigger(ev),\n                },\n                {\n                    eventName: 'ionPopoverActivateTrigger',\n                    callback: (ev) => popoverEl.presentFromTrigger(ev, true),\n                },\n            ];\n            break;\n    }\n    triggerCallbacks.forEach(({ eventName, callback }) => triggerEl.addEventListener(eventName, callback));\n    triggerEl.setAttribute('data-ion-popover-trigger', 'true');\n    return () => {\n        triggerCallbacks.forEach(({ eventName, callback }) => triggerEl.removeEventListener(eventName, callback));\n        triggerEl.removeAttribute('data-ion-popover-trigger');\n    };\n};\n/**\n * Returns the index of an ion-item in an array of ion-items.\n */\nconst getIndexOfItem = (items, item) => {\n    if (!item || item.tagName !== 'ION-ITEM') {\n        return -1;\n    }\n    return items.findIndex((el) => el === item);\n};\n/**\n * Given an array of elements and a currently focused ion-item\n * returns the next ion-item relative to the focused one or\n * undefined.\n */\nconst getNextItem = (items, currentItem) => {\n    const currentItemIndex = getIndexOfItem(items, currentItem);\n    return items[currentItemIndex + 1];\n};\n/**\n * Given an array of elements and a currently focused ion-item\n * returns the previous ion-item relative to the focused one or\n * undefined.\n */\nconst getPrevItem = (items, currentItem) => {\n    const currentItemIndex = getIndexOfItem(items, currentItem);\n    return items[currentItemIndex - 1];\n};\n/** Focus the internal button of the ion-item */\nconst focusItem = (item) => {\n    const root = getElementRoot(item);\n    const button = root.querySelector('button');\n    if (button) {\n        raf(() => button.focus());\n    }\n};\n/**\n * Returns `true` if `el` has been designated\n * as a trigger element for an ion-popover.\n */\nconst isTriggerElement = (el) => el.hasAttribute('data-ion-popover-trigger');\nconst configureKeyboardInteraction = (popoverEl) => {\n    const callback = async (ev) => {\n        var _a;\n        const activeElement = document.activeElement;\n        let items = [];\n        const targetTagName = (_a = ev.target) === null || _a === void 0 ? void 0 : _a.tagName;\n        /**\n         * Only handle custom keyboard interactions for the host popover element\n         * and children ion-item elements.\n         */\n        if (targetTagName !== 'ION-POPOVER' && targetTagName !== 'ION-ITEM') {\n            return;\n        }\n        /**\n         * Complex selectors with :not() are :not supported\n         * in older versions of Chromium so we need to do a\n         * try/catch here so errors are not thrown.\n         */\n        try {\n            /**\n             * Select all ion-items that are not children of child popovers.\n             * i.e. only select ion-item elements that are part of this popover\n             */\n            items = Array.from(popoverEl.querySelectorAll('ion-item:not(ion-popover ion-popover *):not([disabled])'));\n            /* eslint-disable-next-line */\n        }\n        catch (_b) { }\n        switch (ev.key) {\n            /**\n             * If we are in a child popover\n             * then pressing the left arrow key\n             * should close this popover and move\n             * focus to the popover that presented\n             * this one.\n             */\n            case 'ArrowLeft':\n                const parentPopover = await popoverEl.getParentPopover();\n                if (parentPopover) {\n                    popoverEl.dismiss(undefined, undefined, false);\n                }\n                break;\n            /**\n             * ArrowDown should move focus to the next focusable ion-item.\n             */\n            case 'ArrowDown':\n                // Disable movement/scroll with keyboard\n                ev.preventDefault();\n                const nextItem = getNextItem(items, activeElement);\n                if (nextItem !== undefined) {\n                    focusItem(nextItem);\n                }\n                break;\n            /**\n             * ArrowUp should move focus to the previous focusable ion-item.\n             */\n            case 'ArrowUp':\n                // Disable movement/scroll with keyboard\n                ev.preventDefault();\n                const prevItem = getPrevItem(items, activeElement);\n                if (prevItem !== undefined) {\n                    focusItem(prevItem);\n                }\n                break;\n            /**\n             * Home should move focus to the first focusable ion-item.\n             */\n            case 'Home':\n                ev.preventDefault();\n                const firstItem = items[0];\n                if (firstItem !== undefined) {\n                    focusItem(firstItem);\n                }\n                break;\n            /**\n             * End should move focus to the last focusable ion-item.\n             */\n            case 'End':\n                ev.preventDefault();\n                const lastItem = items[items.length - 1];\n                if (lastItem !== undefined) {\n                    focusItem(lastItem);\n                }\n                break;\n            /**\n             * ArrowRight, Spacebar, or Enter should activate\n             * the currently focused trigger item to open a\n             * popover if the element is a trigger item.\n             */\n            case 'ArrowRight':\n            case ' ':\n            case 'Enter':\n                if (activeElement && isTriggerElement(activeElement)) {\n                    const rightEvent = new CustomEvent('ionPopoverActivateTrigger');\n                    activeElement.dispatchEvent(rightEvent);\n                }\n                break;\n        }\n    };\n    popoverEl.addEventListener('keydown', callback);\n    return () => popoverEl.removeEventListener('keydown', callback);\n};\n/**\n * Positions a popover by taking into account\n * the reference point, preferred side, alignment\n * and viewport dimensions.\n */\nconst getPopoverPosition = (isRTL, contentWidth, contentHeight, arrowWidth, arrowHeight, reference, side, align, defaultPosition, triggerEl, event) => {\n    var _a;\n    let referenceCoordinates = {\n        top: 0,\n        left: 0,\n        width: 0,\n        height: 0,\n    };\n    /**\n     * Calculate position relative to the\n     * x-y coordinates in the event that\n     * was passed in\n     */\n    switch (reference) {\n        case 'event':\n            if (!event) {\n                return defaultPosition;\n            }\n            const mouseEv = event;\n            referenceCoordinates = {\n                top: mouseEv.clientY,\n                left: mouseEv.clientX,\n                width: 1,\n                height: 1,\n            };\n            break;\n        /**\n         * Calculate position relative to the bounding\n         * box on either the trigger element\n         * specified via the `trigger` prop or\n         * the target specified on the event\n         * that was passed in.\n         */\n        case 'trigger':\n        default:\n            const customEv = event;\n            /**\n             * ionShadowTarget is used when we need to align the\n             * popover with an element inside of the shadow root\n             * of an Ionic component. Ex: Presenting a popover\n             * by clicking on the collapsed indicator inside\n             * of `ion-breadcrumb` and centering it relative\n             * to the indicator rather than `ion-breadcrumb`\n             * as a whole.\n             */\n            const actualTriggerEl = (triggerEl ||\n                ((_a = customEv === null || customEv === void 0 ? void 0 : customEv.detail) === null || _a === void 0 ? void 0 : _a.ionShadowTarget) ||\n                (customEv === null || customEv === void 0 ? void 0 : customEv.target));\n            if (!actualTriggerEl) {\n                return defaultPosition;\n            }\n            const triggerBoundingBox = actualTriggerEl.getBoundingClientRect();\n            referenceCoordinates = {\n                top: triggerBoundingBox.top,\n                left: triggerBoundingBox.left,\n                width: triggerBoundingBox.width,\n                height: triggerBoundingBox.height,\n            };\n            break;\n    }\n    /**\n     * Get top/left offset that would allow\n     * popover to be positioned on the\n     * preferred side of the reference.\n     */\n    const coordinates = calculatePopoverSide(side, referenceCoordinates, contentWidth, contentHeight, arrowWidth, arrowHeight, isRTL);\n    /**\n     * Get the top/left adjustments that\n     * would allow the popover content\n     * to have the correct alignment.\n     */\n    const alignedCoordinates = calculatePopoverAlign(align, side, referenceCoordinates, contentWidth, contentHeight);\n    const top = coordinates.top + alignedCoordinates.top;\n    const left = coordinates.left + alignedCoordinates.left;\n    const { arrowTop, arrowLeft } = calculateArrowPosition(side, arrowWidth, arrowHeight, top, left, contentWidth, contentHeight, isRTL);\n    const { originX, originY } = calculatePopoverOrigin(side, align, isRTL);\n    return { top, left, referenceCoordinates, arrowTop, arrowLeft, originX, originY };\n};\n/**\n * Determines the transform-origin\n * of the popover animation so that it\n * is in line with what the side and alignment\n * prop values are. Currently only used\n * with the MD animation.\n */\nconst calculatePopoverOrigin = (side, align, isRTL) => {\n    switch (side) {\n        case 'top':\n            return { originX: getOriginXAlignment(align), originY: 'bottom' };\n        case 'bottom':\n            return { originX: getOriginXAlignment(align), originY: 'top' };\n        case 'left':\n            return { originX: 'right', originY: getOriginYAlignment(align) };\n        case 'right':\n            return { originX: 'left', originY: getOriginYAlignment(align) };\n        case 'start':\n            return { originX: isRTL ? 'left' : 'right', originY: getOriginYAlignment(align) };\n        case 'end':\n            return { originX: isRTL ? 'right' : 'left', originY: getOriginYAlignment(align) };\n    }\n};\nconst getOriginXAlignment = (align) => {\n    switch (align) {\n        case 'start':\n            return 'left';\n        case 'center':\n            return 'center';\n        case 'end':\n            return 'right';\n    }\n};\nconst getOriginYAlignment = (align) => {\n    switch (align) {\n        case 'start':\n            return 'top';\n        case 'center':\n            return 'center';\n        case 'end':\n            return 'bottom';\n    }\n};\n/**\n * Calculates where the arrow positioning\n * should be relative to the popover content.\n */\nconst calculateArrowPosition = (side, arrowWidth, arrowHeight, top, left, contentWidth, contentHeight, isRTL) => {\n    /**\n     * Note: When side is left, right, start, or end, the arrow is\n     * been rotated using a `transform`, so to move the arrow up or down\n     * by its dimension, you need to use `arrowWidth`.\n     */\n    const leftPosition = {\n        arrowTop: top + contentHeight / 2 - arrowWidth / 2,\n        arrowLeft: left + contentWidth - arrowWidth / 2,\n    };\n    /**\n     * Move the arrow to the left by arrowWidth and then\n     * again by half of its width because we have rotated\n     * the arrow using a transform.\n     */\n    const rightPosition = { arrowTop: top + contentHeight / 2 - arrowWidth / 2, arrowLeft: left - arrowWidth * 1.5 };\n    switch (side) {\n        case 'top':\n            return { arrowTop: top + contentHeight, arrowLeft: left + contentWidth / 2 - arrowWidth / 2 };\n        case 'bottom':\n            return { arrowTop: top - arrowHeight, arrowLeft: left + contentWidth / 2 - arrowWidth / 2 };\n        case 'left':\n            return leftPosition;\n        case 'right':\n            return rightPosition;\n        case 'start':\n            return isRTL ? rightPosition : leftPosition;\n        case 'end':\n            return isRTL ? leftPosition : rightPosition;\n        default:\n            return { arrowTop: 0, arrowLeft: 0 };\n    }\n};\n/**\n * Calculates the required top/left\n * values needed to position the popover\n * content on the side specified in the\n * `side` prop.\n */\nconst calculatePopoverSide = (side, triggerBoundingBox, contentWidth, contentHeight, arrowWidth, arrowHeight, isRTL) => {\n    const sideLeft = {\n        top: triggerBoundingBox.top,\n        left: triggerBoundingBox.left - contentWidth - arrowWidth,\n    };\n    const sideRight = {\n        top: triggerBoundingBox.top,\n        left: triggerBoundingBox.left + triggerBoundingBox.width + arrowWidth,\n    };\n    switch (side) {\n        case 'top':\n            return {\n                top: triggerBoundingBox.top - contentHeight - arrowHeight,\n                left: triggerBoundingBox.left,\n            };\n        case 'right':\n            return sideRight;\n        case 'bottom':\n            return {\n                top: triggerBoundingBox.top + triggerBoundingBox.height + arrowHeight,\n                left: triggerBoundingBox.left,\n            };\n        case 'left':\n            return sideLeft;\n        case 'start':\n            return isRTL ? sideRight : sideLeft;\n        case 'end':\n            return isRTL ? sideLeft : sideRight;\n    }\n};\n/**\n * Calculates the required top/left\n * offset values needed to provide the\n * correct alignment regardless while taking\n * into account the side the popover is on.\n */\nconst calculatePopoverAlign = (align, side, triggerBoundingBox, contentWidth, contentHeight) => {\n    switch (align) {\n        case 'center':\n            return calculatePopoverCenterAlign(side, triggerBoundingBox, contentWidth, contentHeight);\n        case 'end':\n            return calculatePopoverEndAlign(side, triggerBoundingBox, contentWidth, contentHeight);\n        case 'start':\n        default:\n            return { top: 0, left: 0 };\n    }\n};\n/**\n * Calculate the end alignment for\n * the popover. If side is on the x-axis\n * then the align values refer to the top\n * and bottom margins of the content.\n * If side is on the y-axis then the\n * align values refer to the left and right\n * margins of the content.\n */\nconst calculatePopoverEndAlign = (side, triggerBoundingBox, contentWidth, contentHeight) => {\n    switch (side) {\n        case 'start':\n        case 'end':\n        case 'left':\n        case 'right':\n            return {\n                top: -(contentHeight - triggerBoundingBox.height),\n                left: 0,\n            };\n        case 'top':\n        case 'bottom':\n        default:\n            return {\n                top: 0,\n                left: -(contentWidth - triggerBoundingBox.width),\n            };\n    }\n};\n/**\n * Calculate the center alignment for\n * the popover. If side is on the x-axis\n * then the align values refer to the top\n * and bottom margins of the content.\n * If side is on the y-axis then the\n * align values refer to the left and right\n * margins of the content.\n */\nconst calculatePopoverCenterAlign = (side, triggerBoundingBox, contentWidth, contentHeight) => {\n    switch (side) {\n        case 'start':\n        case 'end':\n        case 'left':\n        case 'right':\n            return {\n                top: -(contentHeight / 2 - triggerBoundingBox.height / 2),\n                left: 0,\n            };\n        case 'top':\n        case 'bottom':\n        default:\n            return {\n                top: 0,\n                left: -(contentWidth / 2 - triggerBoundingBox.width / 2),\n            };\n    }\n};\n/**\n * Adjusts popover positioning coordinates\n * such that popover does not appear offscreen\n * or overlapping safe area bounds.\n */\nconst calculateWindowAdjustment = (side, coordTop, coordLeft, bodyPadding, bodyWidth, bodyHeight, contentWidth, contentHeight, safeAreaMargin, contentOriginX, contentOriginY, triggerCoordinates, coordArrowTop = 0, coordArrowLeft = 0, arrowHeight = 0) => {\n    let arrowTop = coordArrowTop;\n    const arrowLeft = coordArrowLeft;\n    let left = coordLeft;\n    let top = coordTop;\n    let bottom;\n    let originX = contentOriginX;\n    let originY = contentOriginY;\n    let checkSafeAreaLeft = false;\n    let checkSafeAreaRight = false;\n    const triggerTop = triggerCoordinates\n        ? triggerCoordinates.top + triggerCoordinates.height\n        : bodyHeight / 2 - contentHeight / 2;\n    const triggerHeight = triggerCoordinates ? triggerCoordinates.height : 0;\n    let addPopoverBottomClass = false;\n    /**\n     * Adjust popover so it does not\n     * go off the left of the screen.\n     */\n    if (left < bodyPadding + safeAreaMargin) {\n        left = bodyPadding;\n        checkSafeAreaLeft = true;\n        originX = 'left';\n        /**\n         * Adjust popover so it does not\n         * go off the right of the screen.\n         */\n    }\n    else if (contentWidth + bodyPadding + left + safeAreaMargin > bodyWidth) {\n        checkSafeAreaRight = true;\n        left = bodyWidth - contentWidth - bodyPadding;\n        originX = 'right';\n    }\n    /**\n     * Adjust popover so it does not\n     * go off the top of the screen.\n     * If popover is on the left or the right of\n     * the trigger, then we should not adjust top\n     * margins.\n     */\n    if (triggerTop + triggerHeight + contentHeight > bodyHeight && (side === 'top' || side === 'bottom')) {\n        if (triggerTop - contentHeight > 0) {\n            /**\n             * While we strive to align the popover with the trigger\n             * on smaller screens this is not always possible. As a result,\n             * we adjust the popover up so that it does not hang\n             * off the bottom of the screen. However, we do not want to move\n             * the popover up so much that it goes off the top of the screen.\n             *\n             * We chose 12 here so that the popover position looks a bit nicer as\n             * it is not right up against the edge of the screen.\n             */\n            top = Math.max(12, triggerTop - contentHeight - triggerHeight - (arrowHeight - 1));\n            arrowTop = top + contentHeight;\n            originY = 'bottom';\n            addPopoverBottomClass = true;\n            /**\n             * If not enough room for popover to appear\n             * above trigger, then cut it off.\n             */\n        }\n        else {\n            bottom = bodyPadding;\n        }\n    }\n    return {\n        top,\n        left,\n        bottom,\n        originX,\n        originY,\n        checkSafeAreaLeft,\n        checkSafeAreaRight,\n        arrowTop,\n        arrowLeft,\n        addPopoverBottomClass,\n    };\n};\nconst shouldShowArrow = (side, didAdjustBounds = false, ev, trigger) => {\n    /**\n     * If no event provided and\n     * we do not have a trigger,\n     * then this popover was likely\n     * presented via the popoverController\n     * or users called `present` manually.\n     * In this case, the arrow should not be\n     * shown as we do not have a reference.\n     */\n    if (!ev && !trigger) {\n        return false;\n    }\n    /**\n     * If popover is on the left or the right\n     * of a trigger, but we needed to adjust the\n     * popover due to screen bounds, then we should\n     * hide the arrow as it will never be pointing\n     * at the trigger.\n     */\n    if (side !== 'top' && side !== 'bottom' && didAdjustBounds) {\n        return false;\n    }\n    return true;\n};\n\nconst POPOVER_IOS_BODY_PADDING = 5;\n/**\n * iOS Popover Enter Animation\n */\n// TODO(FW-2832): types\nconst iosEnterAnimation = (baseEl, opts) => {\n    var _a;\n    const { event: ev, size, trigger, reference, side, align } = opts;\n    const doc = baseEl.ownerDocument;\n    const isRTL = doc.dir === 'rtl';\n    const bodyWidth = doc.defaultView.innerWidth;\n    const bodyHeight = doc.defaultView.innerHeight;\n    const root = getElementRoot(baseEl);\n    const contentEl = root.querySelector('.popover-content');\n    const arrowEl = root.querySelector('.popover-arrow');\n    const referenceSizeEl = trigger || ((_a = ev === null || ev === void 0 ? void 0 : ev.detail) === null || _a === void 0 ? void 0 : _a.ionShadowTarget) || (ev === null || ev === void 0 ? void 0 : ev.target);\n    const { contentWidth, contentHeight } = getPopoverDimensions(size, contentEl, referenceSizeEl);\n    const { arrowWidth, arrowHeight } = getArrowDimensions(arrowEl);\n    const defaultPosition = {\n        top: bodyHeight / 2 - contentHeight / 2,\n        left: bodyWidth / 2 - contentWidth / 2,\n        originX: isRTL ? 'right' : 'left',\n        originY: 'top',\n    };\n    const results = getPopoverPosition(isRTL, contentWidth, contentHeight, arrowWidth, arrowHeight, reference, side, align, defaultPosition, trigger, ev);\n    const padding = size === 'cover' ? 0 : POPOVER_IOS_BODY_PADDING;\n    const margin = size === 'cover' ? 0 : 25;\n    const { originX, originY, top, left, bottom, checkSafeAreaLeft, checkSafeAreaRight, arrowTop, arrowLeft, addPopoverBottomClass, } = calculateWindowAdjustment(side, results.top, results.left, padding, bodyWidth, bodyHeight, contentWidth, contentHeight, margin, results.originX, results.originY, results.referenceCoordinates, results.arrowTop, results.arrowLeft, arrowHeight);\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const contentAnimation = createAnimation();\n    backdropAnimation\n        .addElement(root.querySelector('ion-backdrop'))\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    // In Chromium, if the wrapper animates, the backdrop filter doesn't work.\n    // The Chromium team stated that this behavior is expected and not a bug. The element animating opacity creates a backdrop root for the backdrop-filter.\n    // To get around this, instead of animating the wrapper, animate both the arrow and content.\n    // https://bugs.chromium.org/p/chromium/issues/detail?id=1148826\n    contentAnimation\n        .addElement(root.querySelector('.popover-arrow'))\n        .addElement(root.querySelector('.popover-content'))\n        .fromTo('opacity', 0.01, 1);\n    // TODO(FW-4376) Ensure that arrow also blurs when translucent\n    return baseAnimation\n        .easing('ease')\n        .duration(100)\n        .beforeAddWrite(() => {\n        if (size === 'cover') {\n            baseEl.style.setProperty('--width', `${contentWidth}px`);\n        }\n        if (addPopoverBottomClass) {\n            baseEl.classList.add('popover-bottom');\n        }\n        if (bottom !== undefined) {\n            contentEl.style.setProperty('bottom', `${bottom}px`);\n        }\n        const safeAreaLeft = ' + var(--ion-safe-area-left, 0)';\n        const safeAreaRight = ' - var(--ion-safe-area-right, 0)';\n        let leftValue = `${left}px`;\n        if (checkSafeAreaLeft) {\n            leftValue = `${left}px${safeAreaLeft}`;\n        }\n        if (checkSafeAreaRight) {\n            leftValue = `${left}px${safeAreaRight}`;\n        }\n        contentEl.style.setProperty('top', `calc(${top}px + var(--offset-y, 0))`);\n        contentEl.style.setProperty('left', `calc(${leftValue} + var(--offset-x, 0))`);\n        contentEl.style.setProperty('transform-origin', `${originY} ${originX}`);\n        if (arrowEl !== null) {\n            const didAdjustBounds = results.top !== top || results.left !== left;\n            const showArrow = shouldShowArrow(side, didAdjustBounds, ev, trigger);\n            if (showArrow) {\n                arrowEl.style.setProperty('top', `calc(${arrowTop}px + var(--offset-y, 0))`);\n                arrowEl.style.setProperty('left', `calc(${arrowLeft}px + var(--offset-x, 0))`);\n            }\n            else {\n                arrowEl.style.setProperty('display', 'none');\n            }\n        }\n    })\n        .addAnimation([backdropAnimation, contentAnimation]);\n};\n\n/**\n * iOS Popover Leave Animation\n */\nconst iosLeaveAnimation = (baseEl) => {\n    const root = getElementRoot(baseEl);\n    const contentEl = root.querySelector('.popover-content');\n    const arrowEl = root.querySelector('.popover-arrow');\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const contentAnimation = createAnimation();\n    backdropAnimation.addElement(root.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    contentAnimation\n        .addElement(root.querySelector('.popover-arrow'))\n        .addElement(root.querySelector('.popover-content'))\n        .fromTo('opacity', 0.99, 0);\n    return baseAnimation\n        .easing('ease')\n        .afterAddWrite(() => {\n        baseEl.style.removeProperty('--width');\n        baseEl.classList.remove('popover-bottom');\n        contentEl.style.removeProperty('top');\n        contentEl.style.removeProperty('left');\n        contentEl.style.removeProperty('bottom');\n        contentEl.style.removeProperty('transform-origin');\n        if (arrowEl) {\n            arrowEl.style.removeProperty('top');\n            arrowEl.style.removeProperty('left');\n            arrowEl.style.removeProperty('display');\n        }\n    })\n        .duration(300)\n        .addAnimation([backdropAnimation, contentAnimation]);\n};\n\nconst POPOVER_MD_BODY_PADDING = 12;\n/**\n * Md Popover Enter Animation\n */\n// TODO(FW-2832): types\nconst mdEnterAnimation = (baseEl, opts) => {\n    var _a;\n    const { event: ev, size, trigger, reference, side, align } = opts;\n    const doc = baseEl.ownerDocument;\n    const isRTL = doc.dir === 'rtl';\n    const bodyWidth = doc.defaultView.innerWidth;\n    const bodyHeight = doc.defaultView.innerHeight;\n    const root = getElementRoot(baseEl);\n    const contentEl = root.querySelector('.popover-content');\n    const referenceSizeEl = trigger || ((_a = ev === null || ev === void 0 ? void 0 : ev.detail) === null || _a === void 0 ? void 0 : _a.ionShadowTarget) || (ev === null || ev === void 0 ? void 0 : ev.target);\n    const { contentWidth, contentHeight } = getPopoverDimensions(size, contentEl, referenceSizeEl);\n    const defaultPosition = {\n        top: bodyHeight / 2 - contentHeight / 2,\n        left: bodyWidth / 2 - contentWidth / 2,\n        originX: isRTL ? 'right' : 'left',\n        originY: 'top',\n    };\n    const results = getPopoverPosition(isRTL, contentWidth, contentHeight, 0, 0, reference, side, align, defaultPosition, trigger, ev);\n    const padding = size === 'cover' ? 0 : POPOVER_MD_BODY_PADDING;\n    const { originX, originY, top, left, bottom } = calculateWindowAdjustment(side, results.top, results.left, padding, bodyWidth, bodyHeight, contentWidth, contentHeight, 0, results.originX, results.originY, results.referenceCoordinates);\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    const contentAnimation = createAnimation();\n    const viewportAnimation = createAnimation();\n    backdropAnimation\n        .addElement(root.querySelector('ion-backdrop'))\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    wrapperAnimation.addElement(root.querySelector('.popover-wrapper')).duration(150).fromTo('opacity', 0.01, 1);\n    contentAnimation\n        .addElement(contentEl)\n        .beforeStyles({\n        top: `calc(${top}px + var(--offset-y, 0px))`,\n        left: `calc(${left}px + var(--offset-x, 0px))`,\n        'transform-origin': `${originY} ${originX}`,\n    })\n        .beforeAddWrite(() => {\n        if (bottom !== undefined) {\n            contentEl.style.setProperty('bottom', `${bottom}px`);\n        }\n    })\n        .fromTo('transform', 'scale(0.8)', 'scale(1)');\n    viewportAnimation.addElement(root.querySelector('.popover-viewport')).fromTo('opacity', 0.01, 1);\n    return baseAnimation\n        .easing('cubic-bezier(0.36,0.66,0.04,1)')\n        .duration(300)\n        .beforeAddWrite(() => {\n        if (size === 'cover') {\n            baseEl.style.setProperty('--width', `${contentWidth}px`);\n        }\n        if (originY === 'bottom') {\n            baseEl.classList.add('popover-bottom');\n        }\n    })\n        .addAnimation([backdropAnimation, wrapperAnimation, contentAnimation, viewportAnimation]);\n};\n\n/**\n * Md Popover Leave Animation\n */\nconst mdLeaveAnimation = (baseEl) => {\n    const root = getElementRoot(baseEl);\n    const contentEl = root.querySelector('.popover-content');\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation.addElement(root.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    wrapperAnimation.addElement(root.querySelector('.popover-wrapper')).fromTo('opacity', 0.99, 0);\n    return baseAnimation\n        .easing('ease')\n        .afterAddWrite(() => {\n        baseEl.style.removeProperty('--width');\n        baseEl.classList.remove('popover-bottom');\n        contentEl.style.removeProperty('top');\n        contentEl.style.removeProperty('left');\n        contentEl.style.removeProperty('bottom');\n        contentEl.style.removeProperty('transform-origin');\n    })\n        .duration(150)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\nconst popoverIosCss = \":host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:200px;--max-height:90%;--box-shadow:none;--backdrop-opacity:var(--ion-backdrop-opacity, 0.08)}:host(.popover-desktop){--box-shadow:0px 4px 16px 0px rgba(0, 0, 0, 0.12)}.popover-content{border-radius:10px}:host(.popover-desktop) .popover-content{border:0.5px solid var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.popover-arrow{display:block;position:absolute;width:20px;height:10px;overflow:hidden;z-index:11}.popover-arrow::after{top:3px;border-radius:3px;position:absolute;width:14px;height:14px;-webkit-transform:rotate(45deg);transform:rotate(45deg);background:var(--background);content:\\\"\\\";z-index:10}.popover-arrow::after{inset-inline-start:3px}:host(.popover-bottom) .popover-arrow{top:auto;bottom:-10px}:host(.popover-bottom) .popover-arrow::after{top:-6px}:host(.popover-side-left) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host(.popover-side-right) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host(.popover-side-top) .popover-arrow{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.popover-side-start) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host-context([dir=rtl]):host(.popover-side-start) .popover-arrow,:host-context([dir=rtl]).popover-side-start .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}@supports selector(:dir(rtl)){:host(.popover-side-start:dir(rtl)) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}}:host(.popover-side-end) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host-context([dir=rtl]):host(.popover-side-end) .popover-arrow,:host-context([dir=rtl]).popover-side-end .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}@supports selector(:dir(rtl)){:host(.popover-side-end:dir(rtl)) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}}.popover-arrow,.popover-content{opacity:0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.popover-translucent) .popover-content,:host(.popover-translucent) .popover-arrow::after{background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}\";\n\nconst popoverMdCss = \":host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:250px;--max-height:90%;--box-shadow:0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}.popover-content{border-radius:4px;-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]) .popover-content{-webkit-transform-origin:right top;transform-origin:right top}[dir=rtl] .popover-content{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.popover-content:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}.popover-viewport{-webkit-transition-delay:100ms;transition-delay:100ms}.popover-wrapper{opacity:0}\";\n\nconst Popover = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.didPresent = createEvent(this, \"ionPopoverDidPresent\", 7);\n        this.willPresent = createEvent(this, \"ionPopoverWillPresent\", 7);\n        this.willDismiss = createEvent(this, \"ionPopoverWillDismiss\", 7);\n        this.didDismiss = createEvent(this, \"ionPopoverDidDismiss\", 7);\n        this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n        this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n        this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n        this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n        this.ionMount = createEvent(this, \"ionMount\", 7);\n        this.parentPopover = null;\n        this.coreDelegate = CoreDelegate();\n        this.lockController = createLockController();\n        this.inline = false;\n        this.focusDescendantOnPresent = false;\n        this.presented = false;\n        /** @internal */\n        this.hasController = false;\n        /**\n         * If `true`, the keyboard will be automatically dismissed when the overlay is presented.\n         */\n        this.keyboardClose = true;\n        /**\n         * If `true`, the popover will be dismissed when the backdrop is clicked.\n         */\n        this.backdropDismiss = true;\n        /**\n         * If `true`, a backdrop will be displayed behind the popover.\n         * This property controls whether or not the backdrop\n         * darkens the screen when the popover is presented.\n         * It does not control whether or not the backdrop\n         * is active or present in the DOM.\n         */\n        this.showBackdrop = true;\n        /**\n         * If `true`, the popover will be translucent.\n         * Only applies when the mode is `\"ios\"` and the device supports\n         * [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).\n         */\n        this.translucent = false;\n        /**\n         * If `true`, the popover will animate.\n         */\n        this.animated = true;\n        /**\n         * Describes what kind of interaction with the trigger that\n         * should cause the popover to open. Does not apply when the `trigger`\n         * property is `undefined`.\n         * If `\"click\"`, the popover will be presented when the trigger is left clicked.\n         * If `\"hover\"`, the popover will be presented when a pointer hovers over the trigger.\n         * If `\"context-menu\"`, the popover will be presented when the trigger is right\n         * clicked on desktop and long pressed on mobile. This will also prevent your\n         * device's normal context menu from appearing.\n         */\n        this.triggerAction = 'click';\n        /**\n         * Describes how to calculate the popover width.\n         * If `\"cover\"`, the popover width will match the width of the trigger.\n         * If `\"auto\"`, the popover width will be set to a static default value.\n         */\n        this.size = 'auto';\n        /**\n         * If `true`, the popover will be automatically\n         * dismissed when the content has been clicked.\n         */\n        this.dismissOnSelect = false;\n        /**\n         * Describes what to position the popover relative to.\n         * If `\"trigger\"`, the popover will be positioned relative\n         * to the trigger button. If passing in an event, this is\n         * determined via event.target.\n         * If `\"event\"`, the popover will be positioned relative\n         * to the x/y coordinates of the trigger action. If passing\n         * in an event, this is determined via event.clientX and event.clientY.\n         */\n        this.reference = 'trigger';\n        /**\n         * Describes which side of the `reference` point to position\n         * the popover on. The `\"start\"` and `\"end\"` values are RTL-aware,\n         * and the `\"left\"` and `\"right\"` values are not.\n         */\n        this.side = 'bottom';\n        /**\n         * If `true`, the popover will display an arrow that points at the\n         * `reference` when running in `ios` mode. Does not apply in `md` mode.\n         */\n        this.arrow = true;\n        /**\n         * If `true`, the popover will open. If `false`, the popover will close.\n         * Use this if you need finer grained control over presentation, otherwise\n         * just use the popoverController or the `trigger` property.\n         * Note: `isOpen` will not automatically be set back to `false` when\n         * the popover dismisses. You will need to do that in your code.\n         */\n        this.isOpen = false;\n        /**\n         * @internal\n         *\n         * If `true` the popover will not register its own keyboard event handlers.\n         * This allows the contents of the popover to handle their own keyboard interactions.\n         *\n         * If `false`, the popover will register its own keyboard event handlers for\n         * navigating `ion-list` items within a popover (up/down/home/<USER>/etc.).\n         * This will also cancel browser keyboard event bindings to prevent scroll\n         * behavior in a popover using a list of items.\n         */\n        this.keyboardEvents = false;\n        /**\n         * If `true`, focus will not be allowed to move outside of this overlay.\n         * If `false`, focus will be allowed to move outside of the overlay.\n         *\n         * In most scenarios this property should remain set to `true`. Setting\n         * this property to `false` can cause severe accessibility issues as users\n         * relying on assistive technologies may be able to move focus into\n         * a confusing state. We recommend only setting this to `false` when\n         * absolutely necessary.\n         *\n         * Developers may want to consider disabling focus trapping if this\n         * overlay presents a non-Ionic overlay from a 3rd party library.\n         * Developers would disable focus trapping on the Ionic overlay\n         * when presenting the 3rd party overlay and then re-enable\n         * focus trapping when dismissing the 3rd party overlay and moving\n         * focus back to the Ionic overlay.\n         */\n        this.focusTrap = true;\n        /**\n         * If `true`, the component passed into `ion-popover` will\n         * automatically be mounted when the popover is created. The\n         * component will remain mounted even when the popover is dismissed.\n         * However, the component will be destroyed when the popover is\n         * destroyed. This property is not reactive and should only be\n         * used when initially creating a popover.\n         *\n         * Note: This feature only applies to inline popovers in JavaScript\n         * frameworks such as Angular, React, and Vue.\n         */\n        this.keepContentsMounted = false;\n        this.onBackdropTap = () => {\n            this.dismiss(undefined, BACKDROP);\n        };\n        this.onLifecycle = (modalEvent) => {\n            const el = this.usersElement;\n            const name = LIFECYCLE_MAP[modalEvent.type];\n            if (el && name) {\n                const event = new CustomEvent(name, {\n                    bubbles: false,\n                    cancelable: false,\n                    detail: modalEvent.detail,\n                });\n                el.dispatchEvent(event);\n            }\n        };\n        this.configureTriggerInteraction = () => {\n            const { trigger, triggerAction, el, destroyTriggerInteraction } = this;\n            if (destroyTriggerInteraction) {\n                destroyTriggerInteraction();\n            }\n            if (trigger === undefined) {\n                return;\n            }\n            const triggerEl = (this.triggerEl = trigger !== undefined ? document.getElementById(trigger) : null);\n            if (!triggerEl) {\n                printIonWarning(`[ion-popover] - A trigger element with the ID \"${trigger}\" was not found in the DOM. The trigger element must be in the DOM when the \"trigger\" property is set on ion-popover.`, this.el);\n                return;\n            }\n            this.destroyTriggerInteraction = configureTriggerInteraction(triggerEl, triggerAction, el);\n        };\n        this.configureKeyboardInteraction = () => {\n            const { destroyKeyboardInteraction, el } = this;\n            if (destroyKeyboardInteraction) {\n                destroyKeyboardInteraction();\n            }\n            this.destroyKeyboardInteraction = configureKeyboardInteraction(el);\n        };\n        this.configureDismissInteraction = () => {\n            const { destroyDismissInteraction, parentPopover, triggerAction, triggerEl, el } = this;\n            if (!parentPopover || !triggerEl) {\n                return;\n            }\n            if (destroyDismissInteraction) {\n                destroyDismissInteraction();\n            }\n            this.destroyDismissInteraction = configureDismissInteraction(triggerEl, triggerAction, el, parentPopover);\n        };\n    }\n    onTriggerChange() {\n        this.configureTriggerInteraction();\n    }\n    onIsOpenChange(newValue, oldValue) {\n        if (newValue === true && oldValue === false) {\n            this.present();\n        }\n        else if (newValue === false && oldValue === true) {\n            this.dismiss();\n        }\n    }\n    connectedCallback() {\n        const { configureTriggerInteraction, el } = this;\n        prepareOverlay(el);\n        configureTriggerInteraction();\n    }\n    disconnectedCallback() {\n        const { destroyTriggerInteraction } = this;\n        if (destroyTriggerInteraction) {\n            destroyTriggerInteraction();\n        }\n    }\n    componentWillLoad() {\n        var _a, _b;\n        const { el } = this;\n        const popoverId = (_b = (_a = this.htmlAttributes) === null || _a === void 0 ? void 0 : _a.id) !== null && _b !== void 0 ? _b : setOverlayId(el);\n        this.parentPopover = el.closest(`ion-popover:not(#${popoverId})`);\n        if (this.alignment === undefined) {\n            this.alignment = getIonMode(this) === 'ios' ? 'center' : 'start';\n        }\n    }\n    componentDidLoad() {\n        const { parentPopover, isOpen } = this;\n        /**\n         * If popover was rendered with isOpen=\"true\"\n         * then we should open popover immediately.\n         */\n        if (isOpen === true) {\n            raf(() => this.present());\n        }\n        if (parentPopover) {\n            addEventListener(parentPopover, 'ionPopoverWillDismiss', () => {\n                this.dismiss(undefined, undefined, false);\n            });\n        }\n        /**\n         * When binding values in frameworks such as Angular\n         * it is possible for the value to be set after the Web Component\n         * initializes but before the value watcher is set up in Stencil.\n         * As a result, the watcher callback may not be fired.\n         * We work around this by manually calling the watcher\n         * callback when the component has loaded and the watcher\n         * is configured.\n         */\n        this.configureTriggerInteraction();\n    }\n    /**\n     * When opening a popover from a trigger, we should not be\n     * modifying the `event` prop from inside the component.\n     * Additionally, when pressing the \"Right\" arrow key, we need\n     * to shift focus to the first descendant in the newly presented\n     * popover.\n     *\n     * @internal\n     */\n    async presentFromTrigger(event, focusDescendant = false) {\n        this.focusDescendantOnPresent = focusDescendant;\n        await this.present(event);\n        this.focusDescendantOnPresent = false;\n    }\n    /**\n     * Determines whether or not an overlay\n     * is being used inline or via a controller/JS\n     * and returns the correct delegate.\n     * By default, subsequent calls to getDelegate\n     * will use a cached version of the delegate.\n     * This is useful for calling dismiss after\n     * present so that the correct delegate is given.\n     */\n    getDelegate(force = false) {\n        if (this.workingDelegate && !force) {\n            return {\n                delegate: this.workingDelegate,\n                inline: this.inline,\n            };\n        }\n        /**\n         * If using overlay inline\n         * we potentially need to use the coreDelegate\n         * so that this works in vanilla JS apps.\n         * If a developer has presented this component\n         * via a controller, then we can assume\n         * the component is already in the\n         * correct place.\n         */\n        const parentEl = this.el.parentNode;\n        const inline = (this.inline = parentEl !== null && !this.hasController);\n        const delegate = (this.workingDelegate = inline ? this.delegate || this.coreDelegate : this.delegate);\n        return { inline, delegate };\n    }\n    /**\n     * Present the popover overlay after it has been created.\n     * Developers can pass a mouse, touch, or pointer event\n     * to position the popover relative to where that event\n     * was dispatched.\n     *\n     * @param event The event to position the popover relative to.\n     */\n    async present(event) {\n        const unlock = await this.lockController.lock();\n        if (this.presented) {\n            unlock();\n            return;\n        }\n        const { el } = this;\n        const { inline, delegate } = this.getDelegate(true);\n        /**\n         * Emit ionMount so JS Frameworks have an opportunity\n         * to add the child component to the DOM. The child\n         * component will be assigned to this.usersElement below.\n         */\n        this.ionMount.emit();\n        this.usersElement = await attachComponent(delegate, el, this.component, ['popover-viewport'], this.componentProps, inline);\n        if (!this.keyboardEvents) {\n            this.configureKeyboardInteraction();\n        }\n        this.configureDismissInteraction();\n        /**\n         * When using the lazy loaded build of Stencil, we need to wait\n         * for every Stencil component instance to be ready before presenting\n         * otherwise there can be a flash of unstyled content. With the\n         * custom elements bundle we need to wait for the JS framework\n         * mount the inner contents of the overlay otherwise WebKit may\n         * get the transition incorrect.\n         */\n        if (hasLazyBuild(el)) {\n            await deepReady(this.usersElement);\n            /**\n             * If keepContentsMounted=\"true\" then the\n             * JS Framework has already mounted the inner\n             * contents so there is no need to wait.\n             * Otherwise, we need to wait for the JS\n             * Framework to mount the inner contents\n             * of this component.\n             */\n        }\n        else if (!this.keepContentsMounted) {\n            await waitForMount();\n        }\n        await present(this, 'popoverEnter', iosEnterAnimation, mdEnterAnimation, {\n            event: event || this.event,\n            size: this.size,\n            trigger: this.triggerEl,\n            reference: this.reference,\n            side: this.side,\n            align: this.alignment,\n        });\n        /**\n         * If popover is nested and was\n         * presented using the \"Right\" arrow key,\n         * we need to move focus to the first\n         * descendant inside of the popover.\n         */\n        if (this.focusDescendantOnPresent) {\n            focusFirstDescendant(el);\n        }\n        unlock();\n    }\n    /**\n     * Dismiss the popover overlay after it has been presented.\n     * This is a no-op if the overlay has not been presented yet. If you want\n     * to remove an overlay from the DOM that was never presented, use the\n     * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n     *\n     * @param data Any data to emit in the dismiss events.\n     * @param role The role of the element that is dismissing the popover. For example, `cancel` or `backdrop`.\n     * @param dismissParentPopover If `true`, dismissing this popover will also dismiss\n     * a parent popover if this popover is nested. Defaults to `true`.\n     */\n    async dismiss(data, role, dismissParentPopover = true) {\n        const unlock = await this.lockController.lock();\n        const { destroyKeyboardInteraction, destroyDismissInteraction } = this;\n        if (dismissParentPopover && this.parentPopover) {\n            this.parentPopover.dismiss(data, role, dismissParentPopover);\n        }\n        const shouldDismiss = await dismiss(this, data, role, 'popoverLeave', iosLeaveAnimation, mdLeaveAnimation, this.event);\n        if (shouldDismiss) {\n            if (destroyKeyboardInteraction) {\n                destroyKeyboardInteraction();\n                this.destroyKeyboardInteraction = undefined;\n            }\n            if (destroyDismissInteraction) {\n                destroyDismissInteraction();\n                this.destroyDismissInteraction = undefined;\n            }\n            /**\n             * If using popover inline\n             * we potentially need to use the coreDelegate\n             * so that this works in vanilla JS apps\n             */\n            const { delegate } = this.getDelegate();\n            await detachComponent(delegate, this.usersElement);\n        }\n        unlock();\n        return shouldDismiss;\n    }\n    /**\n     * @internal\n     */\n    async getParentPopover() {\n        return this.parentPopover;\n    }\n    /**\n     * Returns a promise that resolves when the popover did dismiss.\n     */\n    onDidDismiss() {\n        return eventMethod(this.el, 'ionPopoverDidDismiss');\n    }\n    /**\n     * Returns a promise that resolves when the popover will dismiss.\n     */\n    onWillDismiss() {\n        return eventMethod(this.el, 'ionPopoverWillDismiss');\n    }\n    render() {\n        const mode = getIonMode(this);\n        const { onLifecycle, parentPopover, dismissOnSelect, side, arrow, htmlAttributes, focusTrap } = this;\n        const desktop = isPlatform('desktop');\n        const enableArrow = arrow && !parentPopover;\n        return (h(Host, Object.assign({ key: '16866c02534968c982cf4730d2936d03a5107c8b', \"aria-modal\": \"true\", \"no-router\": true, tabindex: \"-1\" }, htmlAttributes, { style: {\n                zIndex: `${20000 + this.overlayIndex}`,\n            }, class: Object.assign(Object.assign({}, getClassMap(this.cssClass)), { [mode]: true, 'popover-translucent': this.translucent, 'overlay-hidden': true, 'popover-desktop': desktop, [`popover-side-${side}`]: true, [FOCUS_TRAP_DISABLE_CLASS]: focusTrap === false, 'popover-nested': !!parentPopover }), onIonPopoverDidPresent: onLifecycle, onIonPopoverWillPresent: onLifecycle, onIonPopoverWillDismiss: onLifecycle, onIonPopoverDidDismiss: onLifecycle, onIonBackdropTap: this.onBackdropTap }), !parentPopover && h(\"ion-backdrop\", { key: '0df258601a4d30df3c27aa8234a7d5e056c3ecbb', tappable: this.backdropDismiss, visible: this.showBackdrop, part: \"backdrop\" }), h(\"div\", { key: 'f94e80ed996b957b5cd09b826472b4f60e8fcc78', class: \"popover-wrapper ion-overlay-wrapper\", onClick: dismissOnSelect ? () => this.dismiss() : undefined }, enableArrow && h(\"div\", { key: '185ce22f6386e8444a9cc7b8818dbfc16c463c99', class: \"popover-arrow\", part: \"arrow\" }), h(\"div\", { key: '206202b299404e110de5397b229678cca18568d3', class: \"popover-content\", part: \"content\" }, h(\"slot\", { key: 'ee543a0b92d6e35a837c0a0e4617c7b0fc4ad0b0' })))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"trigger\": [\"onTriggerChange\"],\n        \"triggerAction\": [\"onTriggerChange\"],\n        \"isOpen\": [\"onIsOpenChange\"]\n    }; }\n};\nconst LIFECYCLE_MAP = {\n    ionPopoverDidPresent: 'ionViewDidEnter',\n    ionPopoverWillPresent: 'ionViewWillEnter',\n    ionPopoverWillDismiss: 'ionViewWillLeave',\n    ionPopoverDidDismiss: 'ionViewDidLeave',\n};\nPopover.style = {\n    ios: popoverIosCss,\n    md: popoverMdCss\n};\n\nexport { Popover as ion_popover };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,IAAM,qBAAqB,CAAC,YAAY;AACpC,MAAI,CAAC,SAAS;AACV,WAAO,EAAE,YAAY,GAAG,aAAa,EAAE;AAAA,EAC3C;AACA,QAAM,EAAE,OAAO,OAAO,IAAI,QAAQ,sBAAsB;AACxD,SAAO,EAAE,YAAY,OAAO,aAAa,OAAO;AACpD;AAMA,IAAM,uBAAuB,CAAC,MAAM,WAAW,cAAc;AACzD,QAAM,oBAAoB,UAAU,sBAAsB;AAC1D,QAAM,gBAAgB,kBAAkB;AACxC,MAAI,eAAe,kBAAkB;AACrC,MAAI,SAAS,WAAW,WAAW;AAC/B,UAAM,oBAAoB,UAAU,sBAAsB;AAC1D,mBAAe,kBAAkB;AAAA,EACrC;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAM,8BAA8B,CAAC,WAAW,eAAe,WAAW,oBAAoB;AAC1F,MAAI,mBAAmB,CAAC;AACxB,QAAM,OAAO,eAAe,eAAe;AAC3C,QAAM,kBAAkB,KAAK,cAAc,kBAAkB;AAC7D,UAAQ,eAAe;AAAA,IACnB,KAAK;AACD,yBAAmB;AAAA,QACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQI,WAAW;AAAA,UACX,UAAU,CAAC,OAAO;AASd,kBAAM,UAAU,SAAS,iBAAiB,GAAG,SAAS,GAAG,OAAO;AAChE,gBAAI,YAAY,WAAW;AACvB;AAAA,YACJ;AACA,sBAAU,QAAQ,QAAW,QAAW,KAAK;AAAA,UACjD;AAAA,QACJ;AAAA,MACJ;AACA;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL;AACI,yBAAmB;AAAA,QACf;AAAA,UACI,WAAW;AAAA,UACX,UAAU,CAAC,OAAO;AAKd,kBAAM,SAAS,GAAG;AAClB,kBAAM,iBAAiB,OAAO,QAAQ,4BAA4B;AAClE,gBAAI,mBAAmB,WAAW;AAO9B,iBAAG,gBAAgB;AACnB;AAAA,YACJ;AACA,sBAAU,QAAQ,QAAW,QAAW,KAAK;AAAA,UACjD;AAAA,QACJ;AAAA,MACJ;AACA;AAAA,EACR;AACA,mBAAiB,QAAQ,CAAC,EAAE,WAAW,SAAS,MAAM,gBAAgB,iBAAiB,WAAW,QAAQ,CAAC;AAC3G,SAAO,MAAM;AACT,qBAAiB,QAAQ,CAAC,EAAE,WAAW,SAAS,MAAM,gBAAgB,oBAAoB,WAAW,QAAQ,CAAC;AAAA,EAClH;AACJ;AAMA,IAAM,8BAA8B,CAAC,WAAW,eAAe,cAAc;AACzE,MAAI,mBAAmB,CAAC;AAMxB,UAAQ,eAAe;AAAA,IACnB,KAAK;AACD,UAAI;AACJ,yBAAmB;AAAA,QACf;AAAA,UACI,WAAW;AAAA,UACX,UAAU,CAAO,OAAO;AACpB,eAAG,gBAAgB;AACnB,gBAAI,cAAc;AACd,2BAAa,YAAY;AAAA,YAC7B;AAKA,2BAAe,WAAW,MAAM;AAC5B,kBAAI,MAAM;AACN,0BAAU,mBAAmB,EAAE;AAC/B,+BAAe;AAAA,cACnB,CAAC;AAAA,YACL,GAAG,GAAG;AAAA,UACV;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,UACX,UAAU,CAAC,OAAO;AACd,gBAAI,cAAc;AACd,2BAAa,YAAY;AAAA,YAC7B;AAMA,kBAAM,SAAS,GAAG;AAClB,gBAAI,CAAC,QAAQ;AACT;AAAA,YACJ;AACA,gBAAI,OAAO,QAAQ,aAAa,MAAM,WAAW;AAC7C,wBAAU,QAAQ,QAAW,QAAW,KAAK;AAAA,YACjD;AAAA,UACJ;AAAA,QACJ;AAAA,QACA;AAAA;AAAA;AAAA;AAAA;AAAA,UAKI,WAAW;AAAA,UACX,UAAU,CAAC,OAAO,GAAG,gBAAgB;AAAA,QACzC;AAAA,QACA;AAAA,UACI,WAAW;AAAA,UACX,UAAU,CAAC,OAAO,UAAU,mBAAmB,IAAI,IAAI;AAAA,QAC3D;AAAA,MACJ;AACA;AAAA,IACJ,KAAK;AACD,yBAAmB;AAAA,QACf;AAAA,UACI,WAAW;AAAA,UACX,UAAU,CAAC,OAAO;AAKd,eAAG,eAAe;AAClB,sBAAU,mBAAmB,EAAE;AAAA,UACnC;AAAA,QACJ;AAAA,QACA;AAAA,UACI,WAAW;AAAA,UACX,UAAU,CAAC,OAAO,GAAG,gBAAgB;AAAA,QACzC;AAAA,QACA;AAAA,UACI,WAAW;AAAA,UACX,UAAU,CAAC,OAAO,UAAU,mBAAmB,IAAI,IAAI;AAAA,QAC3D;AAAA,MACJ;AACA;AAAA,IACJ,KAAK;AAAA,IACL;AACI,yBAAmB;AAAA,QACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQI,WAAW;AAAA,UACX,UAAU,CAAC,OAAO,UAAU,mBAAmB,EAAE;AAAA,QACrD;AAAA,QACA;AAAA,UACI,WAAW;AAAA,UACX,UAAU,CAAC,OAAO,UAAU,mBAAmB,IAAI,IAAI;AAAA,QAC3D;AAAA,MACJ;AACA;AAAA,EACR;AACA,mBAAiB,QAAQ,CAAC,EAAE,WAAW,SAAS,MAAM,UAAU,iBAAiB,WAAW,QAAQ,CAAC;AACrG,YAAU,aAAa,4BAA4B,MAAM;AACzD,SAAO,MAAM;AACT,qBAAiB,QAAQ,CAAC,EAAE,WAAW,SAAS,MAAM,UAAU,oBAAoB,WAAW,QAAQ,CAAC;AACxG,cAAU,gBAAgB,0BAA0B;AAAA,EACxD;AACJ;AAIA,IAAM,iBAAiB,CAAC,OAAO,SAAS;AACpC,MAAI,CAAC,QAAQ,KAAK,YAAY,YAAY;AACtC,WAAO;AAAA,EACX;AACA,SAAO,MAAM,UAAU,CAAC,OAAO,OAAO,IAAI;AAC9C;AAMA,IAAM,cAAc,CAAC,OAAO,gBAAgB;AACxC,QAAM,mBAAmB,eAAe,OAAO,WAAW;AAC1D,SAAO,MAAM,mBAAmB,CAAC;AACrC;AAMA,IAAM,cAAc,CAAC,OAAO,gBAAgB;AACxC,QAAM,mBAAmB,eAAe,OAAO,WAAW;AAC1D,SAAO,MAAM,mBAAmB,CAAC;AACrC;AAEA,IAAM,YAAY,CAAC,SAAS;AACxB,QAAM,OAAO,eAAe,IAAI;AAChC,QAAM,SAAS,KAAK,cAAc,QAAQ;AAC1C,MAAI,QAAQ;AACR,QAAI,MAAM,OAAO,MAAM,CAAC;AAAA,EAC5B;AACJ;AAKA,IAAM,mBAAmB,CAAC,OAAO,GAAG,aAAa,0BAA0B;AAC3E,IAAM,+BAA+B,CAAC,cAAc;AAChD,QAAM,WAAW,CAAO,OAAO;AAC3B,QAAI;AACJ,UAAM,gBAAgB,SAAS;AAC/B,QAAI,QAAQ,CAAC;AACb,UAAM,iBAAiB,KAAK,GAAG,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAK/E,QAAI,kBAAkB,iBAAiB,kBAAkB,YAAY;AACjE;AAAA,IACJ;AAMA,QAAI;AAKA,cAAQ,MAAM,KAAK,UAAU,iBAAiB,yDAAyD,CAAC;AAAA,IAE5G,SACO,IAAI;AAAA,IAAE;AACb,YAAQ,GAAG,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQZ,KAAK;AACD,cAAM,gBAAgB,MAAM,UAAU,iBAAiB;AACvD,YAAI,eAAe;AACf,oBAAU,QAAQ,QAAW,QAAW,KAAK;AAAA,QACjD;AACA;AAAA;AAAA;AAAA;AAAA,MAIJ,KAAK;AAED,WAAG,eAAe;AAClB,cAAM,WAAW,YAAY,OAAO,aAAa;AACjD,YAAI,aAAa,QAAW;AACxB,oBAAU,QAAQ;AAAA,QACtB;AACA;AAAA;AAAA;AAAA;AAAA,MAIJ,KAAK;AAED,WAAG,eAAe;AAClB,cAAM,WAAW,YAAY,OAAO,aAAa;AACjD,YAAI,aAAa,QAAW;AACxB,oBAAU,QAAQ;AAAA,QACtB;AACA;AAAA;AAAA;AAAA;AAAA,MAIJ,KAAK;AACD,WAAG,eAAe;AAClB,cAAM,YAAY,MAAM,CAAC;AACzB,YAAI,cAAc,QAAW;AACzB,oBAAU,SAAS;AAAA,QACvB;AACA;AAAA;AAAA;AAAA;AAAA,MAIJ,KAAK;AACD,WAAG,eAAe;AAClB,cAAM,WAAW,MAAM,MAAM,SAAS,CAAC;AACvC,YAAI,aAAa,QAAW;AACxB,oBAAU,QAAQ;AAAA,QACtB;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,YAAI,iBAAiB,iBAAiB,aAAa,GAAG;AAClD,gBAAM,aAAa,IAAI,YAAY,2BAA2B;AAC9D,wBAAc,cAAc,UAAU;AAAA,QAC1C;AACA;AAAA,IACR;AAAA,EACJ;AACA,YAAU,iBAAiB,WAAW,QAAQ;AAC9C,SAAO,MAAM,UAAU,oBAAoB,WAAW,QAAQ;AAClE;AAMA,IAAM,qBAAqB,CAAC,OAAO,cAAc,eAAe,YAAY,aAAa,WAAW,MAAM,OAAO,iBAAiB,WAAW,UAAU;AACnJ,MAAI;AACJ,MAAI,uBAAuB;AAAA,IACvB,KAAK;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,EACZ;AAMA,UAAQ,WAAW;AAAA,IACf,KAAK;AACD,UAAI,CAAC,OAAO;AACR,eAAO;AAAA,MACX;AACA,YAAM,UAAU;AAChB,6BAAuB;AAAA,QACnB,KAAK,QAAQ;AAAA,QACb,MAAM,QAAQ;AAAA,QACd,OAAO;AAAA,QACP,QAAQ;AAAA,MACZ;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQJ,KAAK;AAAA,IACL;AACI,YAAM,WAAW;AAUjB,YAAM,kBAAmB,eACnB,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,qBACnH,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS;AAClE,UAAI,CAAC,iBAAiB;AAClB,eAAO;AAAA,MACX;AACA,YAAM,qBAAqB,gBAAgB,sBAAsB;AACjE,6BAAuB;AAAA,QACnB,KAAK,mBAAmB;AAAA,QACxB,MAAM,mBAAmB;AAAA,QACzB,OAAO,mBAAmB;AAAA,QAC1B,QAAQ,mBAAmB;AAAA,MAC/B;AACA;AAAA,EACR;AAMA,QAAM,cAAc,qBAAqB,MAAM,sBAAsB,cAAc,eAAe,YAAY,aAAa,KAAK;AAMhI,QAAM,qBAAqB,sBAAsB,OAAO,MAAM,sBAAsB,cAAc,aAAa;AAC/G,QAAM,MAAM,YAAY,MAAM,mBAAmB;AACjD,QAAM,OAAO,YAAY,OAAO,mBAAmB;AACnD,QAAM,EAAE,UAAU,UAAU,IAAI,uBAAuB,MAAM,YAAY,aAAa,KAAK,MAAM,cAAc,eAAe,KAAK;AACnI,QAAM,EAAE,SAAS,QAAQ,IAAI,uBAAuB,MAAM,OAAO,KAAK;AACtE,SAAO,EAAE,KAAK,MAAM,sBAAsB,UAAU,WAAW,SAAS,QAAQ;AACpF;AAQA,IAAM,yBAAyB,CAAC,MAAM,OAAO,UAAU;AACnD,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,aAAO,EAAE,SAAS,oBAAoB,KAAK,GAAG,SAAS,SAAS;AAAA,IACpE,KAAK;AACD,aAAO,EAAE,SAAS,oBAAoB,KAAK,GAAG,SAAS,MAAM;AAAA,IACjE,KAAK;AACD,aAAO,EAAE,SAAS,SAAS,SAAS,oBAAoB,KAAK,EAAE;AAAA,IACnE,KAAK;AACD,aAAO,EAAE,SAAS,QAAQ,SAAS,oBAAoB,KAAK,EAAE;AAAA,IAClE,KAAK;AACD,aAAO,EAAE,SAAS,QAAQ,SAAS,SAAS,SAAS,oBAAoB,KAAK,EAAE;AAAA,IACpF,KAAK;AACD,aAAO,EAAE,SAAS,QAAQ,UAAU,QAAQ,SAAS,oBAAoB,KAAK,EAAE;AAAA,EACxF;AACJ;AACA,IAAM,sBAAsB,CAAC,UAAU;AACnC,UAAQ,OAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,EACf;AACJ;AACA,IAAM,sBAAsB,CAAC,UAAU;AACnC,UAAQ,OAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,EACf;AACJ;AAKA,IAAM,yBAAyB,CAAC,MAAM,YAAY,aAAa,KAAK,MAAM,cAAc,eAAe,UAAU;AAM7G,QAAM,eAAe;AAAA,IACjB,UAAU,MAAM,gBAAgB,IAAI,aAAa;AAAA,IACjD,WAAW,OAAO,eAAe,aAAa;AAAA,EAClD;AAMA,QAAM,gBAAgB,EAAE,UAAU,MAAM,gBAAgB,IAAI,aAAa,GAAG,WAAW,OAAO,aAAa,IAAI;AAC/G,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,aAAO,EAAE,UAAU,MAAM,eAAe,WAAW,OAAO,eAAe,IAAI,aAAa,EAAE;AAAA,IAChG,KAAK;AACD,aAAO,EAAE,UAAU,MAAM,aAAa,WAAW,OAAO,eAAe,IAAI,aAAa,EAAE;AAAA,IAC9F,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO,QAAQ,gBAAgB;AAAA,IACnC,KAAK;AACD,aAAO,QAAQ,eAAe;AAAA,IAClC;AACI,aAAO,EAAE,UAAU,GAAG,WAAW,EAAE;AAAA,EAC3C;AACJ;AAOA,IAAM,uBAAuB,CAAC,MAAM,oBAAoB,cAAc,eAAe,YAAY,aAAa,UAAU;AACpH,QAAM,WAAW;AAAA,IACb,KAAK,mBAAmB;AAAA,IACxB,MAAM,mBAAmB,OAAO,eAAe;AAAA,EACnD;AACA,QAAM,YAAY;AAAA,IACd,KAAK,mBAAmB;AAAA,IACxB,MAAM,mBAAmB,OAAO,mBAAmB,QAAQ;AAAA,EAC/D;AACA,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,aAAO;AAAA,QACH,KAAK,mBAAmB,MAAM,gBAAgB;AAAA,QAC9C,MAAM,mBAAmB;AAAA,MAC7B;AAAA,IACJ,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,QACH,KAAK,mBAAmB,MAAM,mBAAmB,SAAS;AAAA,QAC1D,MAAM,mBAAmB;AAAA,MAC7B;AAAA,IACJ,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO,QAAQ,YAAY;AAAA,IAC/B,KAAK;AACD,aAAO,QAAQ,WAAW;AAAA,EAClC;AACJ;AAOA,IAAM,wBAAwB,CAAC,OAAO,MAAM,oBAAoB,cAAc,kBAAkB;AAC5F,UAAQ,OAAO;AAAA,IACX,KAAK;AACD,aAAO,4BAA4B,MAAM,oBAAoB,cAAc,aAAa;AAAA,IAC5F,KAAK;AACD,aAAO,yBAAyB,MAAM,oBAAoB,cAAc,aAAa;AAAA,IACzF,KAAK;AAAA,IACL;AACI,aAAO,EAAE,KAAK,GAAG,MAAM,EAAE;AAAA,EACjC;AACJ;AAUA,IAAM,2BAA2B,CAAC,MAAM,oBAAoB,cAAc,kBAAkB;AACxF,UAAQ,MAAM;AAAA,IACV,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,KAAK,EAAE,gBAAgB,mBAAmB;AAAA,QAC1C,MAAM;AAAA,MACV;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL;AACI,aAAO;AAAA,QACH,KAAK;AAAA,QACL,MAAM,EAAE,eAAe,mBAAmB;AAAA,MAC9C;AAAA,EACR;AACJ;AAUA,IAAM,8BAA8B,CAAC,MAAM,oBAAoB,cAAc,kBAAkB;AAC3F,UAAQ,MAAM;AAAA,IACV,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,KAAK,EAAE,gBAAgB,IAAI,mBAAmB,SAAS;AAAA,QACvD,MAAM;AAAA,MACV;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL;AACI,aAAO;AAAA,QACH,KAAK;AAAA,QACL,MAAM,EAAE,eAAe,IAAI,mBAAmB,QAAQ;AAAA,MAC1D;AAAA,EACR;AACJ;AAMA,IAAM,4BAA4B,CAAC,MAAM,UAAU,WAAW,aAAa,WAAW,YAAY,cAAc,eAAe,gBAAgB,gBAAgB,gBAAgB,oBAAoB,gBAAgB,GAAG,iBAAiB,GAAG,cAAc,MAAM;AAC1P,MAAI,WAAW;AACf,QAAM,YAAY;AAClB,MAAI,OAAO;AACX,MAAI,MAAM;AACV,MAAI;AACJ,MAAI,UAAU;AACd,MAAI,UAAU;AACd,MAAI,oBAAoB;AACxB,MAAI,qBAAqB;AACzB,QAAM,aAAa,qBACb,mBAAmB,MAAM,mBAAmB,SAC5C,aAAa,IAAI,gBAAgB;AACvC,QAAM,gBAAgB,qBAAqB,mBAAmB,SAAS;AACvE,MAAI,wBAAwB;AAK5B,MAAI,OAAO,cAAc,gBAAgB;AACrC,WAAO;AACP,wBAAoB;AACpB,cAAU;AAAA,EAKd,WACS,eAAe,cAAc,OAAO,iBAAiB,WAAW;AACrE,yBAAqB;AACrB,WAAO,YAAY,eAAe;AAClC,cAAU;AAAA,EACd;AAQA,MAAI,aAAa,gBAAgB,gBAAgB,eAAe,SAAS,SAAS,SAAS,WAAW;AAClG,QAAI,aAAa,gBAAgB,GAAG;AAWhC,YAAM,KAAK,IAAI,IAAI,aAAa,gBAAgB,iBAAiB,cAAc,EAAE;AACjF,iBAAW,MAAM;AACjB,gBAAU;AACV,8BAAwB;AAAA,IAK5B,OACK;AACD,eAAS;AAAA,IACb;AAAA,EACJ;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAM,kBAAkB,CAAC,MAAM,kBAAkB,OAAO,IAAI,YAAY;AAUpE,MAAI,CAAC,MAAM,CAAC,SAAS;AACjB,WAAO;AAAA,EACX;AAQA,MAAI,SAAS,SAAS,SAAS,YAAY,iBAAiB;AACxD,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,IAAM,2BAA2B;AAKjC,IAAM,oBAAoB,CAAC,QAAQ,SAAS;AACxC,MAAI;AACJ,QAAM,EAAE,OAAO,IAAI,MAAM,SAAS,WAAW,MAAM,MAAM,IAAI;AAC7D,QAAM,MAAM,OAAO;AACnB,QAAM,QAAQ,IAAI,QAAQ;AAC1B,QAAM,YAAY,IAAI,YAAY;AAClC,QAAM,aAAa,IAAI,YAAY;AACnC,QAAM,OAAO,eAAe,MAAM;AAClC,QAAM,YAAY,KAAK,cAAc,kBAAkB;AACvD,QAAM,UAAU,KAAK,cAAc,gBAAgB;AACnD,QAAM,kBAAkB,aAAa,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,qBAAqB,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG;AACrM,QAAM,EAAE,cAAc,cAAc,IAAI,qBAAqB,MAAM,WAAW,eAAe;AAC7F,QAAM,EAAE,YAAY,YAAY,IAAI,mBAAmB,OAAO;AAC9D,QAAM,kBAAkB;AAAA,IACpB,KAAK,aAAa,IAAI,gBAAgB;AAAA,IACtC,MAAM,YAAY,IAAI,eAAe;AAAA,IACrC,SAAS,QAAQ,UAAU;AAAA,IAC3B,SAAS;AAAA,EACb;AACA,QAAM,UAAU,mBAAmB,OAAO,cAAc,eAAe,YAAY,aAAa,WAAW,MAAM,OAAO,iBAAiB,SAAS,EAAE;AACpJ,QAAM,UAAU,SAAS,UAAU,IAAI;AACvC,QAAM,SAAS,SAAS,UAAU,IAAI;AACtC,QAAM,EAAE,SAAS,SAAS,KAAK,MAAM,QAAQ,mBAAmB,oBAAoB,UAAU,WAAW,sBAAuB,IAAI,0BAA0B,MAAM,QAAQ,KAAK,QAAQ,MAAM,SAAS,WAAW,YAAY,cAAc,eAAe,QAAQ,QAAQ,SAAS,QAAQ,SAAS,QAAQ,sBAAsB,QAAQ,UAAU,QAAQ,WAAW,WAAW;AACpX,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,oBAAoB,gBAAgB;AAC1C,QAAM,mBAAmB,gBAAgB;AACzC,oBACK,WAAW,KAAK,cAAc,cAAc,CAAC,EAC7C,OAAO,WAAW,MAAM,yBAAyB,EACjD,aAAa;AAAA,IACd,kBAAkB;AAAA,EACtB,CAAC,EACI,iBAAiB,CAAC,gBAAgB,CAAC;AAKxC,mBACK,WAAW,KAAK,cAAc,gBAAgB,CAAC,EAC/C,WAAW,KAAK,cAAc,kBAAkB,CAAC,EACjD,OAAO,WAAW,MAAM,CAAC;AAE9B,SAAO,cACF,OAAO,MAAM,EACb,SAAS,GAAG,EACZ,eAAe,MAAM;AACtB,QAAI,SAAS,SAAS;AAClB,aAAO,MAAM,YAAY,WAAW,GAAG,YAAY,IAAI;AAAA,IAC3D;AACA,QAAI,uBAAuB;AACvB,aAAO,UAAU,IAAI,gBAAgB;AAAA,IACzC;AACA,QAAI,WAAW,QAAW;AACtB,gBAAU,MAAM,YAAY,UAAU,GAAG,MAAM,IAAI;AAAA,IACvD;AACA,UAAM,eAAe;AACrB,UAAM,gBAAgB;AACtB,QAAI,YAAY,GAAG,IAAI;AACvB,QAAI,mBAAmB;AACnB,kBAAY,GAAG,IAAI,KAAK,YAAY;AAAA,IACxC;AACA,QAAI,oBAAoB;AACpB,kBAAY,GAAG,IAAI,KAAK,aAAa;AAAA,IACzC;AACA,cAAU,MAAM,YAAY,OAAO,QAAQ,GAAG,0BAA0B;AACxE,cAAU,MAAM,YAAY,QAAQ,QAAQ,SAAS,wBAAwB;AAC7E,cAAU,MAAM,YAAY,oBAAoB,GAAG,OAAO,IAAI,OAAO,EAAE;AACvE,QAAI,YAAY,MAAM;AAClB,YAAM,kBAAkB,QAAQ,QAAQ,OAAO,QAAQ,SAAS;AAChE,YAAM,YAAY,gBAAgB,MAAM,iBAAiB,IAAI,OAAO;AACpE,UAAI,WAAW;AACX,gBAAQ,MAAM,YAAY,OAAO,QAAQ,QAAQ,0BAA0B;AAC3E,gBAAQ,MAAM,YAAY,QAAQ,QAAQ,SAAS,0BAA0B;AAAA,MACjF,OACK;AACD,gBAAQ,MAAM,YAAY,WAAW,MAAM;AAAA,MAC/C;AAAA,IACJ;AAAA,EACJ,CAAC,EACI,aAAa,CAAC,mBAAmB,gBAAgB,CAAC;AAC3D;AAKA,IAAM,oBAAoB,CAAC,WAAW;AAClC,QAAM,OAAO,eAAe,MAAM;AAClC,QAAM,YAAY,KAAK,cAAc,kBAAkB;AACvD,QAAM,UAAU,KAAK,cAAc,gBAAgB;AACnD,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,oBAAoB,gBAAgB;AAC1C,QAAM,mBAAmB,gBAAgB;AACzC,oBAAkB,WAAW,KAAK,cAAc,cAAc,CAAC,EAAE,OAAO,WAAW,2BAA2B,CAAC;AAC/G,mBACK,WAAW,KAAK,cAAc,gBAAgB,CAAC,EAC/C,WAAW,KAAK,cAAc,kBAAkB,CAAC,EACjD,OAAO,WAAW,MAAM,CAAC;AAC9B,SAAO,cACF,OAAO,MAAM,EACb,cAAc,MAAM;AACrB,WAAO,MAAM,eAAe,SAAS;AACrC,WAAO,UAAU,OAAO,gBAAgB;AACxC,cAAU,MAAM,eAAe,KAAK;AACpC,cAAU,MAAM,eAAe,MAAM;AACrC,cAAU,MAAM,eAAe,QAAQ;AACvC,cAAU,MAAM,eAAe,kBAAkB;AACjD,QAAI,SAAS;AACT,cAAQ,MAAM,eAAe,KAAK;AAClC,cAAQ,MAAM,eAAe,MAAM;AACnC,cAAQ,MAAM,eAAe,SAAS;AAAA,IAC1C;AAAA,EACJ,CAAC,EACI,SAAS,GAAG,EACZ,aAAa,CAAC,mBAAmB,gBAAgB,CAAC;AAC3D;AAEA,IAAM,0BAA0B;AAKhC,IAAM,mBAAmB,CAAC,QAAQ,SAAS;AACvC,MAAI;AACJ,QAAM,EAAE,OAAO,IAAI,MAAM,SAAS,WAAW,MAAM,MAAM,IAAI;AAC7D,QAAM,MAAM,OAAO;AACnB,QAAM,QAAQ,IAAI,QAAQ;AAC1B,QAAM,YAAY,IAAI,YAAY;AAClC,QAAM,aAAa,IAAI,YAAY;AACnC,QAAM,OAAO,eAAe,MAAM;AAClC,QAAM,YAAY,KAAK,cAAc,kBAAkB;AACvD,QAAM,kBAAkB,aAAa,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,qBAAqB,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG;AACrM,QAAM,EAAE,cAAc,cAAc,IAAI,qBAAqB,MAAM,WAAW,eAAe;AAC7F,QAAM,kBAAkB;AAAA,IACpB,KAAK,aAAa,IAAI,gBAAgB;AAAA,IACtC,MAAM,YAAY,IAAI,eAAe;AAAA,IACrC,SAAS,QAAQ,UAAU;AAAA,IAC3B,SAAS;AAAA,EACb;AACA,QAAM,UAAU,mBAAmB,OAAO,cAAc,eAAe,GAAG,GAAG,WAAW,MAAM,OAAO,iBAAiB,SAAS,EAAE;AACjI,QAAM,UAAU,SAAS,UAAU,IAAI;AACvC,QAAM,EAAE,SAAS,SAAS,KAAK,MAAM,OAAO,IAAI,0BAA0B,MAAM,QAAQ,KAAK,QAAQ,MAAM,SAAS,WAAW,YAAY,cAAc,eAAe,GAAG,QAAQ,SAAS,QAAQ,SAAS,QAAQ,oBAAoB;AACzO,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,oBAAoB,gBAAgB;AAC1C,QAAM,mBAAmB,gBAAgB;AACzC,QAAM,mBAAmB,gBAAgB;AACzC,QAAM,oBAAoB,gBAAgB;AAC1C,oBACK,WAAW,KAAK,cAAc,cAAc,CAAC,EAC7C,OAAO,WAAW,MAAM,yBAAyB,EACjD,aAAa;AAAA,IACd,kBAAkB;AAAA,EACtB,CAAC,EACI,iBAAiB,CAAC,gBAAgB,CAAC;AACxC,mBAAiB,WAAW,KAAK,cAAc,kBAAkB,CAAC,EAAE,SAAS,GAAG,EAAE,OAAO,WAAW,MAAM,CAAC;AAC3G,mBACK,WAAW,SAAS,EACpB,aAAa;AAAA,IACd,KAAK,QAAQ,GAAG;AAAA,IAChB,MAAM,QAAQ,IAAI;AAAA,IAClB,oBAAoB,GAAG,OAAO,IAAI,OAAO;AAAA,EAC7C,CAAC,EACI,eAAe,MAAM;AACtB,QAAI,WAAW,QAAW;AACtB,gBAAU,MAAM,YAAY,UAAU,GAAG,MAAM,IAAI;AAAA,IACvD;AAAA,EACJ,CAAC,EACI,OAAO,aAAa,cAAc,UAAU;AACjD,oBAAkB,WAAW,KAAK,cAAc,mBAAmB,CAAC,EAAE,OAAO,WAAW,MAAM,CAAC;AAC/F,SAAO,cACF,OAAO,gCAAgC,EACvC,SAAS,GAAG,EACZ,eAAe,MAAM;AACtB,QAAI,SAAS,SAAS;AAClB,aAAO,MAAM,YAAY,WAAW,GAAG,YAAY,IAAI;AAAA,IAC3D;AACA,QAAI,YAAY,UAAU;AACtB,aAAO,UAAU,IAAI,gBAAgB;AAAA,IACzC;AAAA,EACJ,CAAC,EACI,aAAa,CAAC,mBAAmB,kBAAkB,kBAAkB,iBAAiB,CAAC;AAChG;AAKA,IAAM,mBAAmB,CAAC,WAAW;AACjC,QAAM,OAAO,eAAe,MAAM;AAClC,QAAM,YAAY,KAAK,cAAc,kBAAkB;AACvD,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,oBAAoB,gBAAgB;AAC1C,QAAM,mBAAmB,gBAAgB;AACzC,oBAAkB,WAAW,KAAK,cAAc,cAAc,CAAC,EAAE,OAAO,WAAW,2BAA2B,CAAC;AAC/G,mBAAiB,WAAW,KAAK,cAAc,kBAAkB,CAAC,EAAE,OAAO,WAAW,MAAM,CAAC;AAC7F,SAAO,cACF,OAAO,MAAM,EACb,cAAc,MAAM;AACrB,WAAO,MAAM,eAAe,SAAS;AACrC,WAAO,UAAU,OAAO,gBAAgB;AACxC,cAAU,MAAM,eAAe,KAAK;AACpC,cAAU,MAAM,eAAe,MAAM;AACrC,cAAU,MAAM,eAAe,QAAQ;AACvC,cAAU,MAAM,eAAe,kBAAkB;AAAA,EACrD,CAAC,EACI,SAAS,GAAG,EACZ,aAAa,CAAC,mBAAmB,gBAAgB,CAAC;AAC3D;AAEA,IAAM,gBAAgB;AAEtB,IAAM,eAAe;AAErB,IAAM,UAAU,MAAM;AAAA,EAClB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,aAAa,YAAY,MAAM,wBAAwB,CAAC;AAC7D,SAAK,cAAc,YAAY,MAAM,yBAAyB,CAAC;AAC/D,SAAK,cAAc,YAAY,MAAM,yBAAyB,CAAC;AAC/D,SAAK,aAAa,YAAY,MAAM,wBAAwB,CAAC;AAC7D,SAAK,sBAAsB,YAAY,MAAM,cAAc,CAAC;AAC5D,SAAK,uBAAuB,YAAY,MAAM,eAAe,CAAC;AAC9D,SAAK,uBAAuB,YAAY,MAAM,eAAe,CAAC;AAC9D,SAAK,sBAAsB,YAAY,MAAM,cAAc,CAAC;AAC5D,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,gBAAgB;AACrB,SAAK,eAAe,aAAa;AACjC,SAAK,iBAAiB,qBAAqB;AAC3C,SAAK,SAAS;AACd,SAAK,2BAA2B;AAChC,SAAK,YAAY;AAEjB,SAAK,gBAAgB;AAIrB,SAAK,gBAAgB;AAIrB,SAAK,kBAAkB;AAQvB,SAAK,eAAe;AAMpB,SAAK,cAAc;AAInB,SAAK,WAAW;AAWhB,SAAK,gBAAgB;AAMrB,SAAK,OAAO;AAKZ,SAAK,kBAAkB;AAUvB,SAAK,YAAY;AAMjB,SAAK,OAAO;AAKZ,SAAK,QAAQ;AAQb,SAAK,SAAS;AAYd,SAAK,iBAAiB;AAkBtB,SAAK,YAAY;AAYjB,SAAK,sBAAsB;AAC3B,SAAK,gBAAgB,MAAM;AACvB,WAAK,QAAQ,QAAW,QAAQ;AAAA,IACpC;AACA,SAAK,cAAc,CAAC,eAAe;AAC/B,YAAM,KAAK,KAAK;AAChB,YAAM,OAAO,cAAc,WAAW,IAAI;AAC1C,UAAI,MAAM,MAAM;AACZ,cAAM,QAAQ,IAAI,YAAY,MAAM;AAAA,UAChC,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ,WAAW;AAAA,QACvB,CAAC;AACD,WAAG,cAAc,KAAK;AAAA,MAC1B;AAAA,IACJ;AACA,SAAK,8BAA8B,MAAM;AACrC,YAAM,EAAE,SAAS,eAAe,IAAI,0BAA0B,IAAI;AAClE,UAAI,2BAA2B;AAC3B,kCAA0B;AAAA,MAC9B;AACA,UAAI,YAAY,QAAW;AACvB;AAAA,MACJ;AACA,YAAM,YAAa,KAAK,YAAY,YAAY,SAAY,SAAS,eAAe,OAAO,IAAI;AAC/F,UAAI,CAAC,WAAW;AACZ,wBAAgB,kDAAkD,OAAO,yHAAyH,KAAK,EAAE;AACzM;AAAA,MACJ;AACA,WAAK,4BAA4B,4BAA4B,WAAW,eAAe,EAAE;AAAA,IAC7F;AACA,SAAK,+BAA+B,MAAM;AACtC,YAAM,EAAE,4BAA4B,GAAG,IAAI;AAC3C,UAAI,4BAA4B;AAC5B,mCAA2B;AAAA,MAC/B;AACA,WAAK,6BAA6B,6BAA6B,EAAE;AAAA,IACrE;AACA,SAAK,8BAA8B,MAAM;AACrC,YAAM,EAAE,2BAA2B,eAAe,eAAe,WAAW,GAAG,IAAI;AACnF,UAAI,CAAC,iBAAiB,CAAC,WAAW;AAC9B;AAAA,MACJ;AACA,UAAI,2BAA2B;AAC3B,kCAA0B;AAAA,MAC9B;AACA,WAAK,4BAA4B,4BAA4B,WAAW,eAAe,IAAI,aAAa;AAAA,IAC5G;AAAA,EACJ;AAAA,EACA,kBAAkB;AACd,SAAK,4BAA4B;AAAA,EACrC;AAAA,EACA,eAAe,UAAU,UAAU;AAC/B,QAAI,aAAa,QAAQ,aAAa,OAAO;AACzC,WAAK,QAAQ;AAAA,IACjB,WACS,aAAa,SAAS,aAAa,MAAM;AAC9C,WAAK,QAAQ;AAAA,IACjB;AAAA,EACJ;AAAA,EACA,oBAAoB;AAChB,UAAM,EAAE,6BAAAA,8BAA6B,GAAG,IAAI;AAC5C,mBAAe,EAAE;AACjB,IAAAA,6BAA4B;AAAA,EAChC;AAAA,EACA,uBAAuB;AACnB,UAAM,EAAE,0BAA0B,IAAI;AACtC,QAAI,2BAA2B;AAC3B,gCAA0B;AAAA,IAC9B;AAAA,EACJ;AAAA,EACA,oBAAoB;AAChB,QAAI,IAAI;AACR,UAAM,EAAE,GAAG,IAAI;AACf,UAAM,aAAa,MAAM,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,QAAQ,OAAO,SAAS,KAAK,aAAa,EAAE;AAC/I,SAAK,gBAAgB,GAAG,QAAQ,oBAAoB,SAAS,GAAG;AAChE,QAAI,KAAK,cAAc,QAAW;AAC9B,WAAK,YAAY,WAAW,IAAI,MAAM,QAAQ,WAAW;AAAA,IAC7D;AAAA,EACJ;AAAA,EACA,mBAAmB;AACf,UAAM,EAAE,eAAe,OAAO,IAAI;AAKlC,QAAI,WAAW,MAAM;AACjB,UAAI,MAAM,KAAK,QAAQ,CAAC;AAAA,IAC5B;AACA,QAAI,eAAe;AACf,uBAAiB,eAAe,yBAAyB,MAAM;AAC3D,aAAK,QAAQ,QAAW,QAAW,KAAK;AAAA,MAC5C,CAAC;AAAA,IACL;AAUA,SAAK,4BAA4B;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUM,mBAAmB,OAAO,kBAAkB,OAAO;AAAA;AACrD,WAAK,2BAA2B;AAChC,YAAM,KAAK,QAAQ,KAAK;AACxB,WAAK,2BAA2B;AAAA,IACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,YAAY,QAAQ,OAAO;AACvB,QAAI,KAAK,mBAAmB,CAAC,OAAO;AAChC,aAAO;AAAA,QACH,UAAU,KAAK;AAAA,QACf,QAAQ,KAAK;AAAA,MACjB;AAAA,IACJ;AAUA,UAAM,WAAW,KAAK,GAAG;AACzB,UAAM,SAAU,KAAK,SAAS,aAAa,QAAQ,CAAC,KAAK;AACzD,UAAM,WAAY,KAAK,kBAAkB,SAAS,KAAK,YAAY,KAAK,eAAe,KAAK;AAC5F,WAAO,EAAE,QAAQ,SAAS;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASM,QAAQ,OAAO;AAAA;AACjB,YAAM,SAAS,MAAM,KAAK,eAAe,KAAK;AAC9C,UAAI,KAAK,WAAW;AAChB,eAAO;AACP;AAAA,MACJ;AACA,YAAM,EAAE,GAAG,IAAI;AACf,YAAM,EAAE,QAAQ,SAAS,IAAI,KAAK,YAAY,IAAI;AAMlD,WAAK,SAAS,KAAK;AACnB,WAAK,eAAe,MAAM,gBAAgB,UAAU,IAAI,KAAK,WAAW,CAAC,kBAAkB,GAAG,KAAK,gBAAgB,MAAM;AACzH,UAAI,CAAC,KAAK,gBAAgB;AACtB,aAAK,6BAA6B;AAAA,MACtC;AACA,WAAK,4BAA4B;AASjC,UAAI,aAAa,EAAE,GAAG;AAClB,cAAM,UAAU,KAAK,YAAY;AAAA,MASrC,WACS,CAAC,KAAK,qBAAqB;AAChC,cAAM,aAAa;AAAA,MACvB;AACA,YAAM,QAAQ,MAAM,gBAAgB,mBAAmB,kBAAkB;AAAA,QACrE,OAAO,SAAS,KAAK;AAAA,QACrB,MAAM,KAAK;AAAA,QACX,SAAS,KAAK;AAAA,QACd,WAAW,KAAK;AAAA,QAChB,MAAM,KAAK;AAAA,QACX,OAAO,KAAK;AAAA,MAChB,CAAC;AAOD,UAAI,KAAK,0BAA0B;AAC/B,6BAAqB,EAAE;AAAA,MAC3B;AACA,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYM,QAAQ,MAAM,MAAM,uBAAuB,MAAM;AAAA;AACnD,YAAM,SAAS,MAAM,KAAK,eAAe,KAAK;AAC9C,YAAM,EAAE,4BAA4B,0BAA0B,IAAI;AAClE,UAAI,wBAAwB,KAAK,eAAe;AAC5C,aAAK,cAAc,QAAQ,MAAM,MAAM,oBAAoB;AAAA,MAC/D;AACA,YAAM,gBAAgB,MAAM,QAAQ,MAAM,MAAM,MAAM,gBAAgB,mBAAmB,kBAAkB,KAAK,KAAK;AACrH,UAAI,eAAe;AACf,YAAI,4BAA4B;AAC5B,qCAA2B;AAC3B,eAAK,6BAA6B;AAAA,QACtC;AACA,YAAI,2BAA2B;AAC3B,oCAA0B;AAC1B,eAAK,4BAA4B;AAAA,QACrC;AAMA,cAAM,EAAE,SAAS,IAAI,KAAK,YAAY;AACtC,cAAM,gBAAgB,UAAU,KAAK,YAAY;AAAA,MACrD;AACA,aAAO;AACP,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,mBAAmB;AAAA;AACrB,aAAO,KAAK;AAAA,IAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACX,WAAO,YAAY,KAAK,IAAI,sBAAsB;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACZ,WAAO,YAAY,KAAK,IAAI,uBAAuB;AAAA,EACvD;AAAA,EACA,SAAS;AACL,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,EAAE,aAAa,eAAe,iBAAiB,MAAM,OAAO,gBAAgB,UAAU,IAAI;AAChG,UAAM,UAAU,WAAW,SAAS;AACpC,UAAM,cAAc,SAAS,CAAC;AAC9B,WAAQ,EAAE,MAAM,OAAO,OAAO,EAAE,KAAK,4CAA4C,cAAc,QAAQ,aAAa,MAAM,UAAU,KAAK,GAAG,gBAAgB,EAAE,OAAO;AAAA,MAC7J,QAAQ,GAAG,MAAQ,KAAK,YAAY;AAAA,IACxC,GAAG,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,KAAK,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,GAAG,MAAM,uBAAuB,KAAK,aAAa,kBAAkB,MAAM,mBAAmB,SAAS,CAAC,gBAAgB,IAAI,EAAE,GAAG,MAAM,CAAC,wBAAwB,GAAG,cAAc,OAAO,kBAAkB,CAAC,CAAC,cAAc,CAAC,GAAG,wBAAwB,aAAa,yBAAyB,aAAa,yBAAyB,aAAa,wBAAwB,aAAa,kBAAkB,KAAK,cAAc,CAAC,GAAG,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,KAAK,4CAA4C,UAAU,KAAK,iBAAiB,SAAS,KAAK,cAAc,MAAM,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,uCAAuC,SAAS,kBAAkB,MAAM,KAAK,QAAQ,IAAI,OAAU,GAAG,eAAe,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,iBAAiB,MAAM,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,mBAAmB,MAAM,UAAU,GAAG,EAAE,QAAQ,EAAE,KAAK,2CAA2C,CAAC,CAAC,CAAC,CAAC;AAAA,EACjmC;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,WAAW,CAAC,iBAAiB;AAAA,MAC7B,iBAAiB,CAAC,iBAAiB;AAAA,MACnC,UAAU,CAAC,gBAAgB;AAAA,IAC/B;AAAA,EAAG;AACP;AACA,IAAM,gBAAgB;AAAA,EAClB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAC1B;AACA,QAAQ,QAAQ;AAAA,EACZ,KAAK;AAAA,EACL,IAAI;AACR;", "names": ["configureTriggerInteraction"]}