import{i as v}from"./chunk-XFXTD7QR.js";import{a as f}from"./chunk-CKP3SGE2.js";import{c as p}from"./chunk-EHNA26RN.js";var h=(r,t,o)=>{let n,e;if(f!==void 0&&"MutationObserver"in f){let d=Array.isArray(t)?t:[t];n=new MutationObserver(s=>{for(let c of s)for(let i of c.addedNodes)if(i.nodeType===Node.ELEMENT_NODE&&d.includes(i.slot)){o(),v(()=>u(i));return}}),n.observe(r,{childList:!0,subtree:!0})}let u=d=>{var s;e&&(e.disconnect(),e=void 0),e=new MutationObserver(c=>{o();for(let i of c)for(let l of i.removedNodes)l.nodeType===Node.ELEMENT_NODE&&l.slot===t&&a()}),e.observe((s=d.parentElement)!==null&&s!==void 0?s:d,{subtree:!0,childList:!0})},E=()=>{n&&(n.disconnect(),n=void 0),a()},a=()=>{e&&(e.disconnect(),e=void 0)};return{destroy:E}},C=(r,t,o)=>{let n=r==null?0:r.toString().length,e=b(n,t);if(o===void 0)return e;try{return o(n,t)}catch(u){return p("[ion-input] - Exception in provided `counterFormatter`:",u),e}},b=(r,t)=>`${r} / ${t}`;export{h as a,C as b};
