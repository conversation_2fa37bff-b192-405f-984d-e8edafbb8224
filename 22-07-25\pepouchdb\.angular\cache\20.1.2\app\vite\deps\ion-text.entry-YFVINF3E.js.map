{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-text.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, e as getIonMode, h, j as Host } from './index-B_U9CtaY.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\n\nconst textCss = \":host(.ion-color){color:var(--ion-color-base)}\";\n\nconst Text = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '361035eae7b92dc109794348d39bad2f596eb6be', class: createColorClasses(this.color, {\n                [mode]: true,\n            }) }, h(\"slot\", { key: 'c7b8835cf485ba9ecd73298f0529276ce1ea0852' })));\n    }\n};\nText.style = textCss;\n\nexport { Text as ion_text };\n"], "mappings": ";;;;;;;;;;;;AAMA,IAAM,UAAU;AAEhB,IAAM,OAAO,MAAM;AAAA,EACf,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAAA,EAClC;AAAA,EACA,SAAS;AACL,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,OAAO,mBAAmB,KAAK,OAAO;AAAA,MACjG,CAAC,IAAI,GAAG;AAAA,IACZ,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,2CAA2C,CAAC,CAAC;AAAA,EAC5E;AACJ;AACA,KAAK,QAAQ;", "names": []}