
/* ===== RESPONSIVE DESIGN FOR ALL DEVICES ===== */

/* Base styles for ion-content */
ion-content {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 8px;
  background: #f8f9fa;
  min-height: 100vh;
}

/* Form container - responsive width and padding */
form {
  width: 100%;
  max-width: 1200px;
  border: 1px solid #dcdcdc;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background: #ffffff;
  padding: 12px;
  margin: 0 auto;
}

/* Cards inside the form */
ion-card {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* Card headers */
ion-card-header {
  background-color: #d4edda;
  border-bottom: 1px solid #c3e6cb;
  border-radius: 12px 12px 0 0;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

ion-card-header:hover {
  background-color: #c3e6cb;
}

/* Responsive grid layout */
.gridp {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  margin-top: 16px;
  --background: #f0faff;
}

/* Section background */
.section0 > ion-item {
  --background: #f0faff;
}

/* Full width items */
ion-item.full-width {
  grid-column: span 1;
}

/* Card titles */
ion-card-title {
  font-weight: 600;
  color: #155724;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
}

/* Button styling */
ion-button {
  margin-top: 16px;
  width: 100%;
  border-radius: 8px;
  font-weight: 500;
}

/* Ion-item consistent spacing */
ion-item {
  --inner-padding-end: 8px;
  --inner-padding-start: 8px;
  --highlight-height: 0px;
  --border-radius: 8px;
  margin-bottom: 8px;
}

/* ===== RESPONSIVE BREAKPOINTS ===== */

/* Small Mobile Devices (320px - 480px) */
@media (max-width: 480px) {
  ion-content {
    padding: 4px;
  }

  form {
    padding: 8px;
    border-radius: 8px;
  }

  ion-card {
    margin-bottom: 12px;
    border-radius: 8px;
  }

  ion-card-header {
    padding: 8px 12px;
    border-radius: 8px 8px 0 0;
  }

  ion-card-title {
    font-size: 14px;
  }

  .gridp {
    grid-template-columns: 1fr;
    gap: 8px;
    margin-top: 12px;
  }

  ion-item {
    --inner-padding-end: 4px;
    --inner-padding-start: 4px;
    margin-bottom: 6px;
  }

  ion-button {
    margin-top: 12px;
    font-size: 14px;
  }
}

/* Large Mobile Devices (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  ion-content {
    padding: 8px;
  }

  form {
    padding: 12px;
  }

  .gridp {
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  ion-item.full-width {
    grid-column: span 2;
  }
}

/* Tablet Devices (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  ion-content {
    padding: 16px;
  }

  form {
    padding: 20px;
    max-width: 900px;
  }

  .gridp {
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }

  ion-item.full-width {
    grid-column: span 2;
  }

  ion-card-title {
    font-size: 18px;
  }
}

/* Desktop and Large Screens (1025px+) */
@media (min-width: 1025px) {
  ion-content {
    padding: 24px;
  }

  form {
    padding: 24px;
    max-width: 1200px;
  }

  .gridp {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
  }

  ion-item.full-width {
    grid-column: span 3;
  }

  ion-card-title {
    font-size: 20px;
  }
}

/* ===== RESPONSIVE TABLE STYLING ===== */

/* Base table styling */
.data-table {
  width: 100%;
  border-collapse: collapse;
  font-family: Arial, sans-serif;
  font-size: 14px;
  color: #333;
  overflow-x: auto;
}

.data-table ion-grid {
  width: 100%;
  min-width: 800px; /* Minimum width for horizontal scroll */
}

.data-table ion-row.table-header {
  background-color: #007bff;
  color: white;
  font-weight: bold;
  text-transform: uppercase;
  font-size: 13px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-table ion-row {
  border-bottom: 1px solid #ddd;
  transition: background-color 0.3s ease;
}

.data-table ion-row:hover {
  background-color: #f1f1f1;
  cursor: pointer;
}

.data-table ion-col {
  padding: 8px 6px;
  border-right: 1px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.data-table ion-col:last-child {
  border-right: none;
}

.data-table ion-col img {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.data-table ion-col img:hover {
  transform: scale(1.1);
}

.data-table ion-col ul {
  list-style: none;
  padding: 0;
  margin: 0;
  max-width: 120px;
}

.data-table ion-col ul li {
  color: #007bff;
  text-decoration: underline;
  cursor: pointer;
  margin-bottom: 4px;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.data-table ion-col ul li:hover {
  color: #0056b3;
}

/* ===== RESPONSIVE CAMERA AND VIDEO STYLING ===== */

/* Video elements responsive styling */
video {
  width: 100%;
  max-width: 400px;
  height: auto;
  border: 2px solid #ddd;
  border-radius: 8px;
  background: #000;
}

/* Camera controls */
.camera-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
  align-items: center;
}

.camera-controls select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  min-width: 120px;
}

/* Upload controls */
.upload-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.upload-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.upload-buttons button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* Radio button styling */
.radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
  padding: 8px 0;
}

.radio-group label {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 14px;
}

/* ===== RESPONSIVE MODAL STYLING ===== */

ion-modal {
  --background: rgba(0, 0, 0, 0.8);
}

ion-modal ion-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
}

ion-modal ion-img {
  max-width: 95vw;
  max-height: 85vh;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  margin-bottom: 16px;
  object-fit: contain;
}

ion-modal ion-button {
  min-width: 120px;
  border-radius: 8px;
  font-weight: bold;
  background-color: #007bff;
  color: white;
  --background: #007bff;
  --background-hover: #0056b3;
  --background-activated: #004085;
}

/* ===== RESPONSIVE BREAKPOINTS FOR COMPONENTS ===== */

/* Mobile specific responsive styles */
@media (max-width: 480px) {
  /* Video elements on mobile */
  video {
    max-width: 100%;
    height: 200px;
  }

  /* Camera controls on mobile */
  .camera-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .camera-controls select {
    width: 100%;
    min-width: auto;
  }

  /* Upload buttons on mobile */
  .upload-buttons {
    flex-direction: column;
  }

  .upload-buttons button {
    width: 100%;
    padding: 12px;
  }

  /* Radio groups on mobile */
  .radio-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  /* Table responsive on mobile */
  .data-table ion-grid {
    min-width: 600px;
  }

  .data-table ion-col {
    padding: 6px 4px;
    font-size: 12px;
  }

  .data-table ion-col img {
    width: 30px;
    height: 30px;
  }

  /* Modal on mobile */
  ion-modal ion-content {
    padding: 8px;
  }

  ion-modal ion-img {
    max-width: 98vw;
    max-height: 80vh;
  }
}

/* Tablet specific responsive styles */
@media (min-width: 481px) and (max-width: 768px) {
  video {
    max-width: 350px;
    height: 250px;
  }

  .camera-controls {
    justify-content: center;
  }

  .upload-buttons {
    justify-content: center;
  }

  .radio-group {
    justify-content: center;
  }

  .data-table ion-grid {
    min-width: 900px;
  }
}

/* Desktop specific responsive styles */
@media (min-width: 1025px) {
  video {
    max-width: 500px;
    height: 350px;
  }

  .camera-controls {
    justify-content: flex-start;
  }

  .upload-buttons {
    justify-content: flex-start;
  }

  .data-table ion-grid {
    min-width: 1200px;
  }

  .data-table ion-col {
    padding: 10px 8px;
  }

  .data-table ion-col img {
    width: 50px;
    height: 50px;
  }
}

/* ===== UTILITY CLASSES ===== */

/* Text utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Spacing utilities */
.margin-top-small { margin-top: 8px; }
.margin-top-medium { margin-top: 16px; }
.margin-top-large { margin-top: 24px; }

.margin-bottom-small { margin-bottom: 8px; }
.margin-bottom-medium { margin-bottom: 16px; }
.margin-bottom-large { margin-bottom: 24px; }

.padding-small { padding: 8px; }
.padding-medium { padding: 16px; }
.padding-large { padding: 24px; }

/* Flex utilities */
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

/* Width utilities */
.width-full { width: 100%; }
.width-half { width: 50%; }
.width-quarter { width: 25%; }

/* Border utilities */
.border-radius-small { border-radius: 4px; }
.border-radius-medium { border-radius: 8px; }
.border-radius-large { border-radius: 12px; }

/* Shadow utilities */
.shadow-small { box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); }
.shadow-medium { box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); }
.shadow-large { box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); }

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */

/* Focus states */
ion-button:focus,
ion-input:focus,
ion-select:focus,
button:focus,
select:focus,
input:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  ion-card {
    border: 2px solid #000;
  }

  ion-card-header {
    border-bottom: 2px solid #000;
  }

  .data-table ion-row {
    border-bottom: 2px solid #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  ion-header,
  ion-button,
  .camera-controls,
  .upload-buttons {
    display: none !important;
  }

  ion-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }

  .data-table {
    font-size: 10px;
  }
}


