{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/components/status-tap.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { readTask, writeTask } from '@stencil/core/internal/client';\nimport { a as findClosestIonContent, s as scrollToTop } from './index8.js';\nimport { c as componentOnReady } from './helpers.js';\n\nconst startStatusTap = () => {\n    const win = window;\n    win.addEventListener('statusTap', () => {\n        readTask(() => {\n            const width = win.innerWidth;\n            const height = win.innerHeight;\n            const el = document.elementFromPoint(width / 2, height / 2);\n            if (!el) {\n                return;\n            }\n            const contentEl = findClosestIonContent(el);\n            if (contentEl) {\n                new Promise((resolve) => componentOnReady(contentEl, resolve)).then(() => {\n                    writeTask(async () => {\n                        /**\n                         * If scrolling and user taps status bar,\n                         * only calling scrollToTop is not enough\n                         * as engines like WebKit will jump the\n                         * scroll position back down and complete\n                         * any in-progress momentum scrolling.\n                         */\n                        contentEl.style.setProperty('--overflow', 'hidden');\n                        await scrollToTop(contentEl, 300);\n                        contentEl.style.removeProperty('--overflow');\n                    });\n                });\n            }\n        });\n    });\n};\n\nexport { startStatusTap };\n"], "mappings": ";;;;;;;;;;;;;;;;;AAOA,IAAM,iBAAiB,MAAM;AACzB,QAAM,MAAM;AACZ,MAAI,iBAAiB,aAAa,MAAM;AACpC,aAAS,MAAM;AACX,YAAM,QAAQ,IAAI;AAClB,YAAM,SAAS,IAAI;AACnB,YAAM,KAAK,SAAS,iBAAiB,QAAQ,GAAG,SAAS,CAAC;AAC1D,UAAI,CAAC,IAAI;AACL;AAAA,MACJ;AACA,YAAM,YAAY,sBAAsB,EAAE;AAC1C,UAAI,WAAW;AACX,YAAI,QAAQ,CAAC,YAAY,iBAAiB,WAAW,OAAO,CAAC,EAAE,KAAK,MAAM;AACtE,oBAAU,MAAY;AAQlB,sBAAU,MAAM,YAAY,cAAc,QAAQ;AAClD,kBAAM,YAAY,WAAW,GAAG;AAChC,sBAAU,MAAM,eAAe,YAAY;AAAA,UAC/C,EAAC;AAAA,QACL,CAAC;AAAA,MACL;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;", "names": []}