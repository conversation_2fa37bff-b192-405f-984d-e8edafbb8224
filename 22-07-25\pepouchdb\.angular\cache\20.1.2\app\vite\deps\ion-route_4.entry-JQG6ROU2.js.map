{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-route_4.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, o as printIonError, m as printIonWarning, k as getElement, e as getIonMode, h, j as Host } from './index-B_U9CtaY.js';\nimport { c as componentOnReady, p as debounce } from './helpers-1O4D2b7y.js';\nimport { o as openURL, c as createColorClasses } from './theme-DiVJyqlX.js';\n\nconst Route = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionRouteDataChanged = createEvent(this, \"ionRouteDataChanged\", 7);\n        /**\n         * Relative path that needs to match in order for this route to apply.\n         *\n         * Accepts paths similar to expressjs so that you can define parameters\n         * in the url /foo/:bar where bar would be available in incoming props.\n         */\n        this.url = '';\n    }\n    onUpdate(newValue) {\n        this.ionRouteDataChanged.emit(newValue);\n    }\n    onComponentProps(newValue, oldValue) {\n        if (newValue === oldValue) {\n            return;\n        }\n        const keys1 = newValue ? Object.keys(newValue) : [];\n        const keys2 = oldValue ? Object.keys(oldValue) : [];\n        if (keys1.length !== keys2.length) {\n            this.onUpdate(newValue);\n            return;\n        }\n        for (const key of keys1) {\n            if (newValue[key] !== oldValue[key]) {\n                this.onUpdate(newValue);\n                return;\n            }\n        }\n    }\n    connectedCallback() {\n        this.ionRouteDataChanged.emit();\n    }\n    static get watchers() { return {\n        \"url\": [\"onUpdate\"],\n        \"component\": [\"onUpdate\"],\n        \"componentProps\": [\"onComponentProps\"]\n    }; }\n};\n\nconst RouteRedirect = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionRouteRedirectChanged = createEvent(this, \"ionRouteRedirectChanged\", 7);\n    }\n    propDidChange() {\n        this.ionRouteRedirectChanged.emit();\n    }\n    connectedCallback() {\n        this.ionRouteRedirectChanged.emit();\n    }\n    static get watchers() { return {\n        \"from\": [\"propDidChange\"],\n        \"to\": [\"propDidChange\"]\n    }; }\n};\n\nconst ROUTER_INTENT_NONE = 'root';\nconst ROUTER_INTENT_FORWARD = 'forward';\nconst ROUTER_INTENT_BACK = 'back';\n\n/** Join the non empty segments with \"/\". */\nconst generatePath = (segments) => {\n    const path = segments.filter((s) => s.length > 0).join('/');\n    return '/' + path;\n};\nconst generateUrl = (segments, useHash, queryString) => {\n    let url = generatePath(segments);\n    if (useHash) {\n        url = '#' + url;\n    }\n    if (queryString !== undefined) {\n        url += '?' + queryString;\n    }\n    return url;\n};\nconst writeSegments = (history, root, useHash, segments, direction, state, queryString) => {\n    const url = generateUrl([...parsePath(root).segments, ...segments], useHash, queryString);\n    if (direction === ROUTER_INTENT_FORWARD) {\n        history.pushState(state, '', url);\n    }\n    else {\n        history.replaceState(state, '', url);\n    }\n};\n/**\n * Transforms a chain to a list of segments.\n *\n * Notes:\n * - parameter segments of the form :param are replaced with their value,\n * - null is returned when a value is missing for any parameter segment.\n */\nconst chainToSegments = (chain) => {\n    const segments = [];\n    for (const route of chain) {\n        for (const segment of route.segments) {\n            if (segment[0] === ':') {\n                // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n                const param = route.params && route.params[segment.slice(1)];\n                if (!param) {\n                    return null;\n                }\n                segments.push(param);\n            }\n            else if (segment !== '') {\n                segments.push(segment);\n            }\n        }\n    }\n    return segments;\n};\n/**\n * Removes the prefix segments from the path segments.\n *\n * Return:\n * - null when the path segments do not start with the passed prefix,\n * - the path segments after the prefix otherwise.\n */\nconst removePrefix = (prefix, segments) => {\n    if (prefix.length > segments.length) {\n        return null;\n    }\n    if (prefix.length <= 1 && prefix[0] === '') {\n        return segments;\n    }\n    for (let i = 0; i < prefix.length; i++) {\n        if (prefix[i] !== segments[i]) {\n            return null;\n        }\n    }\n    if (segments.length === prefix.length) {\n        return [''];\n    }\n    return segments.slice(prefix.length);\n};\nconst readSegments = (loc, root, useHash) => {\n    const prefix = parsePath(root).segments;\n    const pathname = useHash ? loc.hash.slice(1) : loc.pathname;\n    const segments = parsePath(pathname).segments;\n    return removePrefix(prefix, segments);\n};\n/**\n * Parses the path to:\n * - segments an array of '/' separated parts,\n * - queryString (undefined when no query string).\n */\nconst parsePath = (path) => {\n    let segments = [''];\n    let queryString;\n    if (path != null) {\n        const qsStart = path.indexOf('?');\n        if (qsStart > -1) {\n            queryString = path.substring(qsStart + 1);\n            path = path.substring(0, qsStart);\n        }\n        segments = path\n            .split('/')\n            .map((s) => s.trim())\n            .filter((s) => s.length > 0);\n        if (segments.length === 0) {\n            segments = [''];\n        }\n    }\n    return { segments, queryString };\n};\n\nconst printRoutes = (routes) => {\n    console.group(`[ion-core] ROUTES[${routes.length}]`);\n    for (const chain of routes) {\n        const segments = [];\n        chain.forEach((r) => segments.push(...r.segments));\n        const ids = chain.map((r) => r.id);\n        console.debug(`%c ${generatePath(segments)}`, 'font-weight: bold; padding-left: 20px', '=>\\t', `(${ids.join(', ')})`);\n    }\n    console.groupEnd();\n};\nconst printRedirects = (redirects) => {\n    console.group(`[ion-core] REDIRECTS[${redirects.length}]`);\n    for (const redirect of redirects) {\n        if (redirect.to) {\n            console.debug('FROM: ', `$c ${generatePath(redirect.from)}`, 'font-weight: bold', ' TO: ', `$c ${generatePath(redirect.to.segments)}`, 'font-weight: bold');\n        }\n    }\n    console.groupEnd();\n};\n\n/**\n * Activates the passed route chain.\n *\n * There must be exactly one outlet per route entry in the chain.\n *\n * The methods calls setRouteId on each of the outlet with the corresponding route entry in the chain.\n * setRouteId will create or select the view in the outlet.\n */\nconst writeNavState = async (root, chain, direction, index, changed = false, animation) => {\n    try {\n        // find next navigation outlet in the DOM\n        const outlet = searchNavNode(root);\n        // make sure we can continue interacting the DOM, otherwise abort\n        if (index >= chain.length || !outlet) {\n            return changed;\n        }\n        await new Promise((resolve) => componentOnReady(outlet, resolve));\n        const route = chain[index];\n        const result = await outlet.setRouteId(route.id, route.params, direction, animation);\n        // if the outlet changed the page, reset navigation to neutral (no direction)\n        // this means nested outlets will not animate\n        if (result.changed) {\n            direction = ROUTER_INTENT_NONE;\n            changed = true;\n        }\n        // recursively set nested outlets\n        changed = await writeNavState(result.element, chain, direction, index + 1, changed, animation);\n        // once all nested outlets are visible let's make the parent visible too,\n        // using markVisible prevents flickering\n        if (result.markVisible) {\n            await result.markVisible();\n        }\n        return changed;\n    }\n    catch (e) {\n        printIonError('[ion-router] - Exception in writeNavState:', e);\n        return false;\n    }\n};\n/**\n * Recursively walks the outlet in the DOM.\n *\n * The function returns a list of RouteID corresponding to each of the outlet and the last outlet without a RouteID.\n */\nconst readNavState = async (root) => {\n    const ids = [];\n    let outlet;\n    let node = root;\n    // eslint-disable-next-line no-cond-assign\n    while ((outlet = searchNavNode(node))) {\n        const id = await outlet.getRouteId();\n        if (id) {\n            node = id.element;\n            id.element = undefined;\n            ids.push(id);\n        }\n        else {\n            break;\n        }\n    }\n    return { ids, outlet };\n};\nconst waitUntilNavNode = () => {\n    if (searchNavNode(document.body)) {\n        return Promise.resolve();\n    }\n    return new Promise((resolve) => {\n        window.addEventListener('ionNavWillLoad', () => resolve(), { once: true });\n    });\n};\n/** Selector for all the outlets supported by the router. */\nconst OUTLET_SELECTOR = ':not([no-router]) ion-nav, :not([no-router]) ion-tabs, :not([no-router]) ion-router-outlet';\nconst searchNavNode = (root) => {\n    if (!root) {\n        return undefined;\n    }\n    if (root.matches(OUTLET_SELECTOR)) {\n        return root;\n    }\n    const outlet = root.querySelector(OUTLET_SELECTOR);\n    return outlet !== null && outlet !== void 0 ? outlet : undefined;\n};\n\n/**\n * Returns whether the given redirect matches the given path segments.\n *\n * A redirect matches when the segments of the path and redirect.from are equal.\n * Note that segments are only checked until redirect.from contains a '*' which matches any path segment.\n * The path ['some', 'path', 'to', 'page'] matches both ['some', 'path', 'to', 'page'] and ['some', 'path', '*'].\n */\nconst matchesRedirect = (segments, redirect) => {\n    const { from, to } = redirect;\n    if (to === undefined) {\n        return false;\n    }\n    if (from.length > segments.length) {\n        return false;\n    }\n    for (let i = 0; i < from.length; i++) {\n        const expected = from[i];\n        if (expected === '*') {\n            return true;\n        }\n        if (expected !== segments[i]) {\n            return false;\n        }\n    }\n    return from.length === segments.length;\n};\n/** Returns the first redirect matching the path segments or undefined when no match found. */\nconst findRouteRedirect = (segments, redirects) => {\n    return redirects.find((redirect) => matchesRedirect(segments, redirect));\n};\nconst matchesIDs = (ids, chain) => {\n    const len = Math.min(ids.length, chain.length);\n    let score = 0;\n    for (let i = 0; i < len; i++) {\n        const routeId = ids[i];\n        const routeChain = chain[i];\n        // Skip results where the route id does not match the chain at the same index\n        if (routeId.id.toLowerCase() !== routeChain.id) {\n            break;\n        }\n        if (routeId.params) {\n            const routeIdParams = Object.keys(routeId.params);\n            // Only compare routes with the chain that have the same number of parameters.\n            if (routeIdParams.length === routeChain.segments.length) {\n                // Maps the route's params into a path based on the path variable names,\n                // to compare against the route chain format.\n                //\n                // Before:\n                // ```ts\n                // {\n                //  params: {\n                //    s1: 'a',\n                //    s2: 'b'\n                //  }\n                // }\n                // ```\n                //\n                // After:\n                // ```ts\n                // [':s1',':s2']\n                // ```\n                //\n                const pathWithParams = routeIdParams.map((key) => `:${key}`);\n                for (let j = 0; j < pathWithParams.length; j++) {\n                    // Skip results where the path variable is not a match\n                    if (pathWithParams[j].toLowerCase() !== routeChain.segments[j]) {\n                        break;\n                    }\n                    // Weight path matches for the same index higher.\n                    score++;\n                }\n            }\n        }\n        // Weight id matches\n        score++;\n    }\n    return score;\n};\n/**\n * Matches the segments against the chain.\n *\n * Returns:\n * - null when there is no match,\n * - a chain with the params properties updated with the parameter segments on match.\n */\nconst matchesSegments = (segments, chain) => {\n    const inputSegments = new RouterSegments(segments);\n    let matchesDefault = false;\n    let allparams;\n    for (let i = 0; i < chain.length; i++) {\n        const chainSegments = chain[i].segments;\n        if (chainSegments[0] === '') {\n            matchesDefault = true;\n        }\n        else {\n            for (const segment of chainSegments) {\n                const data = inputSegments.next();\n                // data param\n                if (segment[0] === ':') {\n                    if (data === '') {\n                        return null;\n                    }\n                    allparams = allparams || [];\n                    const params = allparams[i] || (allparams[i] = {});\n                    params[segment.slice(1)] = data;\n                }\n                else if (data !== segment) {\n                    return null;\n                }\n            }\n            matchesDefault = false;\n        }\n    }\n    const matches = matchesDefault ? matchesDefault === (inputSegments.next() === '') : true;\n    if (!matches) {\n        return null;\n    }\n    if (allparams) {\n        return chain.map((route, i) => ({\n            id: route.id,\n            segments: route.segments,\n            params: mergeParams(route.params, allparams[i]),\n            beforeEnter: route.beforeEnter,\n            beforeLeave: route.beforeLeave,\n        }));\n    }\n    return chain;\n};\n/**\n * Merges the route parameter objects.\n * Returns undefined when both parameters are undefined.\n */\nconst mergeParams = (a, b) => {\n    return a || b ? Object.assign(Object.assign({}, a), b) : undefined;\n};\n/**\n * Finds the best match for the ids in the chains.\n *\n * Returns the best match or null when no match is found.\n * When a chain is returned the parameters are updated from the RouteIDs.\n * That is they contain both the componentProps of the <ion-route> and the parameter segment.\n */\nconst findChainForIDs = (ids, chains) => {\n    let match = null;\n    let maxMatches = 0;\n    for (const chain of chains) {\n        const score = matchesIDs(ids, chain);\n        if (score > maxMatches) {\n            match = chain;\n            maxMatches = score;\n        }\n    }\n    if (match) {\n        return match.map((route, i) => {\n            var _a;\n            return ({\n                id: route.id,\n                segments: route.segments,\n                params: mergeParams(route.params, (_a = ids[i]) === null || _a === void 0 ? void 0 : _a.params),\n            });\n        });\n    }\n    return null;\n};\n/**\n * Finds the best match for the segments in the chains.\n *\n * Returns the best match or null when no match is found.\n * When a chain is returned the parameters are updated from the segments.\n * That is they contain both the componentProps of the <ion-route> and the parameter segments.\n */\nconst findChainForSegments = (segments, chains) => {\n    let match = null;\n    let bestScore = 0;\n    for (const chain of chains) {\n        const matchedChain = matchesSegments(segments, chain);\n        if (matchedChain !== null) {\n            const score = computePriority(matchedChain);\n            if (score > bestScore) {\n                bestScore = score;\n                match = matchedChain;\n            }\n        }\n    }\n    return match;\n};\n/**\n * Computes the priority of a chain.\n *\n * Parameter segments are given a lower priority over fixed segments.\n *\n * Considering the following 2 chains matching the path /path/to/page:\n * - /path/to/:where\n * - /path/to/page\n *\n * The second one will be given a higher priority because \"page\" is a fixed segment (vs \":where\", a parameter segment).\n */\nconst computePriority = (chain) => {\n    let score = 1;\n    let level = 1;\n    for (const route of chain) {\n        for (const segment of route.segments) {\n            if (segment[0] === ':') {\n                score += Math.pow(1, level);\n            }\n            else if (segment !== '') {\n                score += Math.pow(2, level);\n            }\n            level++;\n        }\n    }\n    return score;\n};\nclass RouterSegments {\n    constructor(segments) {\n        this.segments = segments.slice();\n    }\n    next() {\n        if (this.segments.length > 0) {\n            return this.segments.shift();\n        }\n        return '';\n    }\n}\n\nconst readProp = (el, prop) => {\n    if (prop in el) {\n        return el[prop];\n    }\n    if (el.hasAttribute(prop)) {\n        return el.getAttribute(prop);\n    }\n    return null;\n};\n/**\n * Extracts the redirects (that is <ion-route-redirect> elements inside the root).\n *\n * The redirects are returned as a list of RouteRedirect.\n */\nconst readRedirects = (root) => {\n    return Array.from(root.children)\n        .filter((el) => el.tagName === 'ION-ROUTE-REDIRECT')\n        .map((el) => {\n        const to = readProp(el, 'to');\n        return {\n            from: parsePath(readProp(el, 'from')).segments,\n            to: to == null ? undefined : parsePath(to),\n        };\n    });\n};\n/**\n * Extracts all the routes (that is <ion-route> elements inside the root).\n *\n * The routes are returned as a list of chains - the flattened tree.\n */\nconst readRoutes = (root) => {\n    return flattenRouterTree(readRouteNodes(root));\n};\n/**\n * Reads the route nodes as a tree modeled after the DOM tree of <ion-route> elements.\n *\n * Note: routes without a component are ignored together with their children.\n */\nconst readRouteNodes = (node) => {\n    return Array.from(node.children)\n        .filter((el) => el.tagName === 'ION-ROUTE' && el.component)\n        .map((el) => {\n        const component = readProp(el, 'component');\n        return {\n            segments: parsePath(readProp(el, 'url')).segments,\n            id: component.toLowerCase(),\n            params: el.componentProps,\n            beforeLeave: el.beforeLeave,\n            beforeEnter: el.beforeEnter,\n            children: readRouteNodes(el),\n        };\n    });\n};\n/**\n * Flattens a RouterTree in a list of chains.\n *\n * Each chain represents a path from the root node to a terminal node.\n */\nconst flattenRouterTree = (nodes) => {\n    const chains = [];\n    for (const node of nodes) {\n        flattenNode([], chains, node);\n    }\n    return chains;\n};\n/** Flattens a route node recursively and push each branch to the chains list. */\nconst flattenNode = (chain, chains, node) => {\n    chain = [\n        ...chain,\n        {\n            id: node.id,\n            segments: node.segments,\n            params: node.params,\n            beforeLeave: node.beforeLeave,\n            beforeEnter: node.beforeEnter,\n        },\n    ];\n    if (node.children.length === 0) {\n        chains.push(chain);\n        return;\n    }\n    for (const child of node.children) {\n        flattenNode(chain, chains, child);\n    }\n};\n\nconst Router = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionRouteWillChange = createEvent(this, \"ionRouteWillChange\", 7);\n        this.ionRouteDidChange = createEvent(this, \"ionRouteDidChange\", 7);\n        this.previousPath = null;\n        this.busy = false;\n        this.state = 0;\n        this.lastState = 0;\n        /**\n         * The root path to use when matching URLs. By default, this is set to \"/\", but you can specify\n         * an alternate prefix for all URL paths.\n         */\n        this.root = '/';\n        /**\n         * The router can work in two \"modes\":\n         * - With hash: `/index.html#/path/to/page`\n         * - Without hash: `/path/to/page`\n         *\n         * Using one or another might depend in the requirements of your app and/or where it's deployed.\n         *\n         * Usually \"hash-less\" navigation works better for SEO and it's more user friendly too, but it might\n         * requires additional server-side configuration in order to properly work.\n         *\n         * On the other side hash-navigation is much easier to deploy, it even works over the file protocol.\n         *\n         * By default, this property is `true`, change to `false` to allow hash-less URLs.\n         */\n        this.useHash = true;\n    }\n    async componentWillLoad() {\n        await waitUntilNavNode();\n        const canProceed = await this.runGuards(this.getSegments());\n        if (canProceed !== true) {\n            if (typeof canProceed === 'object') {\n                const { redirect } = canProceed;\n                const path = parsePath(redirect);\n                this.setSegments(path.segments, ROUTER_INTENT_NONE, path.queryString);\n                await this.writeNavStateRoot(path.segments, ROUTER_INTENT_NONE);\n            }\n        }\n        else {\n            await this.onRoutesChanged();\n        }\n    }\n    componentDidLoad() {\n        window.addEventListener('ionRouteRedirectChanged', debounce(this.onRedirectChanged.bind(this), 10));\n        window.addEventListener('ionRouteDataChanged', debounce(this.onRoutesChanged.bind(this), 100));\n    }\n    async onPopState() {\n        const direction = this.historyDirection();\n        let segments = this.getSegments();\n        const canProceed = await this.runGuards(segments);\n        if (canProceed !== true) {\n            if (typeof canProceed === 'object') {\n                segments = parsePath(canProceed.redirect).segments;\n            }\n            else {\n                return false;\n            }\n        }\n        return this.writeNavStateRoot(segments, direction);\n    }\n    onBackButton(ev) {\n        ev.detail.register(0, (processNextHandler) => {\n            this.back();\n            processNextHandler();\n        });\n    }\n    /** @internal */\n    async canTransition() {\n        const canProceed = await this.runGuards();\n        if (canProceed !== true) {\n            if (typeof canProceed === 'object') {\n                return canProceed.redirect;\n            }\n            else {\n                return false;\n            }\n        }\n        return true;\n    }\n    /**\n     * Navigate to the specified path.\n     *\n     * @param path The path to navigate to.\n     * @param direction The direction of the animation. Defaults to `\"forward\"`.\n     * @param animation A custom animation to use for the transition.\n     */\n    async push(path, direction = 'forward', animation) {\n        var _a;\n        if (path.startsWith('.')) {\n            const currentPath = (_a = this.previousPath) !== null && _a !== void 0 ? _a : '/';\n            // Convert currentPath to an URL by pre-pending a protocol and a host to resolve the relative path.\n            const url = new URL(path, `https://host/${currentPath}`);\n            path = url.pathname + url.search;\n        }\n        let parsedPath = parsePath(path);\n        const canProceed = await this.runGuards(parsedPath.segments);\n        if (canProceed !== true) {\n            if (typeof canProceed === 'object') {\n                parsedPath = parsePath(canProceed.redirect);\n            }\n            else {\n                return false;\n            }\n        }\n        this.setSegments(parsedPath.segments, direction, parsedPath.queryString);\n        return this.writeNavStateRoot(parsedPath.segments, direction, animation);\n    }\n    /** Go back to previous page in the window.history. */\n    back() {\n        window.history.back();\n        return Promise.resolve(this.waitPromise);\n    }\n    /** @internal */\n    async printDebug() {\n        printRoutes(readRoutes(this.el));\n        printRedirects(readRedirects(this.el));\n    }\n    /** @internal */\n    async navChanged(direction) {\n        if (this.busy) {\n            printIonWarning('[ion-router] - Router is busy, navChanged was cancelled.');\n            return false;\n        }\n        const { ids, outlet } = await readNavState(window.document.body);\n        const routes = readRoutes(this.el);\n        const chain = findChainForIDs(ids, routes);\n        if (!chain) {\n            printIonWarning('[ion-router] - No matching URL for', ids.map((i) => i.id));\n            return false;\n        }\n        const segments = chainToSegments(chain);\n        if (!segments) {\n            printIonWarning('[ion-router] - Router could not match path because some required param is missing.');\n            return false;\n        }\n        this.setSegments(segments, direction);\n        await this.safeWriteNavState(outlet, chain, ROUTER_INTENT_NONE, segments, null, ids.length);\n        return true;\n    }\n    /** This handler gets called when a `ion-route-redirect` component is added to the DOM or if the from or to property of such node changes. */\n    onRedirectChanged() {\n        const segments = this.getSegments();\n        if (segments && findRouteRedirect(segments, readRedirects(this.el))) {\n            this.writeNavStateRoot(segments, ROUTER_INTENT_NONE);\n        }\n    }\n    /** This handler gets called when a `ion-route` component is added to the DOM or if the from or to property of such node changes. */\n    onRoutesChanged() {\n        return this.writeNavStateRoot(this.getSegments(), ROUTER_INTENT_NONE);\n    }\n    historyDirection() {\n        var _a;\n        const win = window;\n        if (win.history.state === null) {\n            this.state++;\n            win.history.replaceState(this.state, win.document.title, (_a = win.document.location) === null || _a === void 0 ? void 0 : _a.href);\n        }\n        const state = win.history.state;\n        const lastState = this.lastState;\n        this.lastState = state;\n        if (state > lastState || (state >= lastState && lastState > 0)) {\n            return ROUTER_INTENT_FORWARD;\n        }\n        if (state < lastState) {\n            return ROUTER_INTENT_BACK;\n        }\n        return ROUTER_INTENT_NONE;\n    }\n    async writeNavStateRoot(segments, direction, animation) {\n        if (!segments) {\n            printIonError('[ion-router] - URL is not part of the routing set.');\n            return false;\n        }\n        // lookup redirect rule\n        const redirects = readRedirects(this.el);\n        const redirect = findRouteRedirect(segments, redirects);\n        let redirectFrom = null;\n        if (redirect) {\n            const { segments: toSegments, queryString } = redirect.to;\n            this.setSegments(toSegments, direction, queryString);\n            redirectFrom = redirect.from;\n            segments = toSegments;\n        }\n        // lookup route chain\n        const routes = readRoutes(this.el);\n        const chain = findChainForSegments(segments, routes);\n        if (!chain) {\n            printIonError('[ion-router] - The path does not match any route.');\n            return false;\n        }\n        // write DOM give\n        return this.safeWriteNavState(document.body, chain, direction, segments, redirectFrom, 0, animation);\n    }\n    async safeWriteNavState(node, chain, direction, segments, redirectFrom, index = 0, animation) {\n        const unlock = await this.lock();\n        let changed = false;\n        try {\n            changed = await this.writeNavState(node, chain, direction, segments, redirectFrom, index, animation);\n        }\n        catch (e) {\n            printIonError('[ion-router] - Exception in safeWriteNavState:', e);\n        }\n        unlock();\n        return changed;\n    }\n    async lock() {\n        const p = this.waitPromise;\n        let resolve;\n        this.waitPromise = new Promise((r) => (resolve = r));\n        if (p !== undefined) {\n            await p;\n        }\n        return resolve;\n    }\n    /**\n     * Executes the beforeLeave hook of the source route and the beforeEnter hook of the target route if they exist.\n     *\n     * When the beforeLeave hook does not return true (to allow navigating) then that value is returned early and the beforeEnter is executed.\n     * Otherwise the beforeEnterHook hook of the target route is executed.\n     */\n    async runGuards(to = this.getSegments(), from) {\n        if (from === undefined) {\n            from = parsePath(this.previousPath).segments;\n        }\n        if (!to || !from) {\n            return true;\n        }\n        const routes = readRoutes(this.el);\n        const fromChain = findChainForSegments(from, routes);\n        // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n        const beforeLeaveHook = fromChain && fromChain[fromChain.length - 1].beforeLeave;\n        const canLeave = beforeLeaveHook ? await beforeLeaveHook() : true;\n        if (canLeave === false || typeof canLeave === 'object') {\n            return canLeave;\n        }\n        const toChain = findChainForSegments(to, routes);\n        // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n        const beforeEnterHook = toChain && toChain[toChain.length - 1].beforeEnter;\n        return beforeEnterHook ? beforeEnterHook() : true;\n    }\n    async writeNavState(node, chain, direction, segments, redirectFrom, index = 0, animation) {\n        if (this.busy) {\n            printIonWarning('[ion-router] - Router is busy, transition was cancelled.');\n            return false;\n        }\n        this.busy = true;\n        // generate route event and emit will change\n        const routeEvent = this.routeChangeEvent(segments, redirectFrom);\n        if (routeEvent) {\n            this.ionRouteWillChange.emit(routeEvent);\n        }\n        const changed = await writeNavState(node, chain, direction, index, false, animation);\n        this.busy = false;\n        // emit did change\n        if (routeEvent) {\n            this.ionRouteDidChange.emit(routeEvent);\n        }\n        return changed;\n    }\n    setSegments(segments, direction, queryString) {\n        this.state++;\n        writeSegments(window.history, this.root, this.useHash, segments, direction, this.state, queryString);\n    }\n    getSegments() {\n        return readSegments(window.location, this.root, this.useHash);\n    }\n    routeChangeEvent(toSegments, redirectFromSegments) {\n        const from = this.previousPath;\n        const to = generatePath(toSegments);\n        this.previousPath = to;\n        if (to === from) {\n            return null;\n        }\n        const redirectedFrom = redirectFromSegments ? generatePath(redirectFromSegments) : null;\n        return {\n            from,\n            redirectedFrom,\n            to,\n        };\n    }\n    get el() { return getElement(this); }\n};\n\nconst routerLinkCss = \":host{--background:transparent;--color:var(--ion-color-primary, #0054e9);background:var(--background);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}a{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit}\";\n\nconst RouterLink = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        /**\n         * When using a router, it specifies the transition direction when navigating to\n         * another page using `href`.\n         */\n        this.routerDirection = 'forward';\n        this.onClick = (ev) => {\n            openURL(this.href, ev, this.routerDirection, this.routerAnimation);\n        };\n    }\n    render() {\n        const mode = getIonMode(this);\n        const attrs = {\n            href: this.href,\n            rel: this.rel,\n            target: this.target,\n        };\n        return (h(Host, { key: 'd7f2affcde45c5fbb6cb46cd1c30008ee92a68c5', onClick: this.onClick, class: createColorClasses(this.color, {\n                [mode]: true,\n                'ion-activatable': true,\n            }) }, h(\"a\", Object.assign({ key: 'babafae85ca5c6429958d383feff0493ff8cf33e' }, attrs), h(\"slot\", { key: '50314e9555bbf6dffa0c50c3f763009dee59b10b' }))));\n    }\n};\nRouterLink.style = routerLinkCss;\n\nexport { Route as ion_route, RouteRedirect as ion_route_redirect, Router as ion_router, RouterLink as ion_router_link };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,QAAQ,MAAM;AAAA,EAChB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,sBAAsB,YAAY,MAAM,uBAAuB,CAAC;AAOrE,SAAK,MAAM;AAAA,EACf;AAAA,EACA,SAAS,UAAU;AACf,SAAK,oBAAoB,KAAK,QAAQ;AAAA,EAC1C;AAAA,EACA,iBAAiB,UAAU,UAAU;AACjC,QAAI,aAAa,UAAU;AACvB;AAAA,IACJ;AACA,UAAM,QAAQ,WAAW,OAAO,KAAK,QAAQ,IAAI,CAAC;AAClD,UAAM,QAAQ,WAAW,OAAO,KAAK,QAAQ,IAAI,CAAC;AAClD,QAAI,MAAM,WAAW,MAAM,QAAQ;AAC/B,WAAK,SAAS,QAAQ;AACtB;AAAA,IACJ;AACA,eAAW,OAAO,OAAO;AACrB,UAAI,SAAS,GAAG,MAAM,SAAS,GAAG,GAAG;AACjC,aAAK,SAAS,QAAQ;AACtB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,oBAAoB;AAChB,SAAK,oBAAoB,KAAK;AAAA,EAClC;AAAA,EACA,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,OAAO,CAAC,UAAU;AAAA,MAClB,aAAa,CAAC,UAAU;AAAA,MACxB,kBAAkB,CAAC,kBAAkB;AAAA,IACzC;AAAA,EAAG;AACP;AAEA,IAAM,gBAAgB,MAAM;AAAA,EACxB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,0BAA0B,YAAY,MAAM,2BAA2B,CAAC;AAAA,EACjF;AAAA,EACA,gBAAgB;AACZ,SAAK,wBAAwB,KAAK;AAAA,EACtC;AAAA,EACA,oBAAoB;AAChB,SAAK,wBAAwB,KAAK;AAAA,EACtC;AAAA,EACA,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,QAAQ,CAAC,eAAe;AAAA,MACxB,MAAM,CAAC,eAAe;AAAA,IAC1B;AAAA,EAAG;AACP;AAEA,IAAM,qBAAqB;AAC3B,IAAM,wBAAwB;AAC9B,IAAM,qBAAqB;AAG3B,IAAM,eAAe,CAAC,aAAa;AAC/B,QAAM,OAAO,SAAS,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1D,SAAO,MAAM;AACjB;AACA,IAAM,cAAc,CAAC,UAAU,SAAS,gBAAgB;AACpD,MAAI,MAAM,aAAa,QAAQ;AAC/B,MAAI,SAAS;AACT,UAAM,MAAM;AAAA,EAChB;AACA,MAAI,gBAAgB,QAAW;AAC3B,WAAO,MAAM;AAAA,EACjB;AACA,SAAO;AACX;AACA,IAAM,gBAAgB,CAAC,SAAS,MAAM,SAAS,UAAU,WAAW,OAAO,gBAAgB;AACvF,QAAM,MAAM,YAAY,CAAC,GAAG,UAAU,IAAI,EAAE,UAAU,GAAG,QAAQ,GAAG,SAAS,WAAW;AACxF,MAAI,cAAc,uBAAuB;AACrC,YAAQ,UAAU,OAAO,IAAI,GAAG;AAAA,EACpC,OACK;AACD,YAAQ,aAAa,OAAO,IAAI,GAAG;AAAA,EACvC;AACJ;AAQA,IAAM,kBAAkB,CAAC,UAAU;AAC/B,QAAM,WAAW,CAAC;AAClB,aAAW,SAAS,OAAO;AACvB,eAAW,WAAW,MAAM,UAAU;AAClC,UAAI,QAAQ,CAAC,MAAM,KAAK;AAEpB,cAAM,QAAQ,MAAM,UAAU,MAAM,OAAO,QAAQ,MAAM,CAAC,CAAC;AAC3D,YAAI,CAAC,OAAO;AACR,iBAAO;AAAA,QACX;AACA,iBAAS,KAAK,KAAK;AAAA,MACvB,WACS,YAAY,IAAI;AACrB,iBAAS,KAAK,OAAO;AAAA,MACzB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAQA,IAAM,eAAe,CAAC,QAAQ,aAAa;AACvC,MAAI,OAAO,SAAS,SAAS,QAAQ;AACjC,WAAO;AAAA,EACX;AACA,MAAI,OAAO,UAAU,KAAK,OAAO,CAAC,MAAM,IAAI;AACxC,WAAO;AAAA,EACX;AACA,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,QAAI,OAAO,CAAC,MAAM,SAAS,CAAC,GAAG;AAC3B,aAAO;AAAA,IACX;AAAA,EACJ;AACA,MAAI,SAAS,WAAW,OAAO,QAAQ;AACnC,WAAO,CAAC,EAAE;AAAA,EACd;AACA,SAAO,SAAS,MAAM,OAAO,MAAM;AACvC;AACA,IAAM,eAAe,CAAC,KAAK,MAAM,YAAY;AACzC,QAAM,SAAS,UAAU,IAAI,EAAE;AAC/B,QAAM,WAAW,UAAU,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI;AACnD,QAAM,WAAW,UAAU,QAAQ,EAAE;AACrC,SAAO,aAAa,QAAQ,QAAQ;AACxC;AAMA,IAAM,YAAY,CAAC,SAAS;AACxB,MAAI,WAAW,CAAC,EAAE;AAClB,MAAI;AACJ,MAAI,QAAQ,MAAM;AACd,UAAM,UAAU,KAAK,QAAQ,GAAG;AAChC,QAAI,UAAU,IAAI;AACd,oBAAc,KAAK,UAAU,UAAU,CAAC;AACxC,aAAO,KAAK,UAAU,GAAG,OAAO;AAAA,IACpC;AACA,eAAW,KACN,MAAM,GAAG,EACT,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,EACnB,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC;AAC/B,QAAI,SAAS,WAAW,GAAG;AACvB,iBAAW,CAAC,EAAE;AAAA,IAClB;AAAA,EACJ;AACA,SAAO,EAAE,UAAU,YAAY;AACnC;AAEA,IAAM,cAAc,CAAC,WAAW;AAC5B,UAAQ,MAAM,qBAAqB,OAAO,MAAM,GAAG;AACnD,aAAW,SAAS,QAAQ;AACxB,UAAM,WAAW,CAAC;AAClB,UAAM,QAAQ,CAAC,MAAM,SAAS,KAAK,GAAG,EAAE,QAAQ,CAAC;AACjD,UAAM,MAAM,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE;AACjC,YAAQ,MAAM,MAAM,aAAa,QAAQ,CAAC,IAAI,yCAAyC,OAAQ,IAAI,IAAI,KAAK,IAAI,CAAC,GAAG;AAAA,EACxH;AACA,UAAQ,SAAS;AACrB;AACA,IAAM,iBAAiB,CAAC,cAAc;AAClC,UAAQ,MAAM,wBAAwB,UAAU,MAAM,GAAG;AACzD,aAAW,YAAY,WAAW;AAC9B,QAAI,SAAS,IAAI;AACb,cAAQ,MAAM,UAAU,MAAM,aAAa,SAAS,IAAI,CAAC,IAAI,qBAAqB,SAAS,MAAM,aAAa,SAAS,GAAG,QAAQ,CAAC,IAAI,mBAAmB;AAAA,IAC9J;AAAA,EACJ;AACA,UAAQ,SAAS;AACrB;AAUA,IAAM,gBAAgB,CAAO,MAAM,OAAO,WAAW,OAAO,UAAU,OAAO,cAAc;AACvF,MAAI;AAEA,UAAM,SAAS,cAAc,IAAI;AAEjC,QAAI,SAAS,MAAM,UAAU,CAAC,QAAQ;AAClC,aAAO;AAAA,IACX;AACA,UAAM,IAAI,QAAQ,CAAC,YAAY,iBAAiB,QAAQ,OAAO,CAAC;AAChE,UAAM,QAAQ,MAAM,KAAK;AACzB,UAAM,SAAS,MAAM,OAAO,WAAW,MAAM,IAAI,MAAM,QAAQ,WAAW,SAAS;AAGnF,QAAI,OAAO,SAAS;AAChB,kBAAY;AACZ,gBAAU;AAAA,IACd;AAEA,cAAU,MAAM,cAAc,OAAO,SAAS,OAAO,WAAW,QAAQ,GAAG,SAAS,SAAS;AAG7F,QAAI,OAAO,aAAa;AACpB,YAAM,OAAO,YAAY;AAAA,IAC7B;AACA,WAAO;AAAA,EACX,SACO,GAAG;AACN,kBAAc,8CAA8C,CAAC;AAC7D,WAAO;AAAA,EACX;AACJ;AAMA,IAAM,eAAe,CAAO,SAAS;AACjC,QAAM,MAAM,CAAC;AACb,MAAI;AACJ,MAAI,OAAO;AAEX,SAAQ,SAAS,cAAc,IAAI,GAAI;AACnC,UAAM,KAAK,MAAM,OAAO,WAAW;AACnC,QAAI,IAAI;AACJ,aAAO,GAAG;AACV,SAAG,UAAU;AACb,UAAI,KAAK,EAAE;AAAA,IACf,OACK;AACD;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,EAAE,KAAK,OAAO;AACzB;AACA,IAAM,mBAAmB,MAAM;AAC3B,MAAI,cAAc,SAAS,IAAI,GAAG;AAC9B,WAAO,QAAQ,QAAQ;AAAA,EAC3B;AACA,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC5B,WAAO,iBAAiB,kBAAkB,MAAM,QAAQ,GAAG,EAAE,MAAM,KAAK,CAAC;AAAA,EAC7E,CAAC;AACL;AAEA,IAAM,kBAAkB;AACxB,IAAM,gBAAgB,CAAC,SAAS;AAC5B,MAAI,CAAC,MAAM;AACP,WAAO;AAAA,EACX;AACA,MAAI,KAAK,QAAQ,eAAe,GAAG;AAC/B,WAAO;AAAA,EACX;AACA,QAAM,SAAS,KAAK,cAAc,eAAe;AACjD,SAAO,WAAW,QAAQ,WAAW,SAAS,SAAS;AAC3D;AASA,IAAM,kBAAkB,CAAC,UAAU,aAAa;AAC5C,QAAM,EAAE,MAAM,GAAG,IAAI;AACrB,MAAI,OAAO,QAAW;AAClB,WAAO;AAAA,EACX;AACA,MAAI,KAAK,SAAS,SAAS,QAAQ;AAC/B,WAAO;AAAA,EACX;AACA,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,UAAM,WAAW,KAAK,CAAC;AACvB,QAAI,aAAa,KAAK;AAClB,aAAO;AAAA,IACX;AACA,QAAI,aAAa,SAAS,CAAC,GAAG;AAC1B,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO,KAAK,WAAW,SAAS;AACpC;AAEA,IAAM,oBAAoB,CAAC,UAAU,cAAc;AAC/C,SAAO,UAAU,KAAK,CAAC,aAAa,gBAAgB,UAAU,QAAQ,CAAC;AAC3E;AACA,IAAM,aAAa,CAAC,KAAK,UAAU;AAC/B,QAAM,MAAM,KAAK,IAAI,IAAI,QAAQ,MAAM,MAAM;AAC7C,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,UAAM,UAAU,IAAI,CAAC;AACrB,UAAM,aAAa,MAAM,CAAC;AAE1B,QAAI,QAAQ,GAAG,YAAY,MAAM,WAAW,IAAI;AAC5C;AAAA,IACJ;AACA,QAAI,QAAQ,QAAQ;AAChB,YAAM,gBAAgB,OAAO,KAAK,QAAQ,MAAM;AAEhD,UAAI,cAAc,WAAW,WAAW,SAAS,QAAQ;AAmBrD,cAAM,iBAAiB,cAAc,IAAI,CAAC,QAAQ,IAAI,GAAG,EAAE;AAC3D,iBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAE5C,cAAI,eAAe,CAAC,EAAE,YAAY,MAAM,WAAW,SAAS,CAAC,GAAG;AAC5D;AAAA,UACJ;AAEA;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA;AAAA,EACJ;AACA,SAAO;AACX;AAQA,IAAM,kBAAkB,CAAC,UAAU,UAAU;AACzC,QAAM,gBAAgB,IAAI,eAAe,QAAQ;AACjD,MAAI,iBAAiB;AACrB,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAM,gBAAgB,MAAM,CAAC,EAAE;AAC/B,QAAI,cAAc,CAAC,MAAM,IAAI;AACzB,uBAAiB;AAAA,IACrB,OACK;AACD,iBAAW,WAAW,eAAe;AACjC,cAAM,OAAO,cAAc,KAAK;AAEhC,YAAI,QAAQ,CAAC,MAAM,KAAK;AACpB,cAAI,SAAS,IAAI;AACb,mBAAO;AAAA,UACX;AACA,sBAAY,aAAa,CAAC;AAC1B,gBAAM,SAAS,UAAU,CAAC,MAAM,UAAU,CAAC,IAAI,CAAC;AAChD,iBAAO,QAAQ,MAAM,CAAC,CAAC,IAAI;AAAA,QAC/B,WACS,SAAS,SAAS;AACvB,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,uBAAiB;AAAA,IACrB;AAAA,EACJ;AACA,QAAM,UAAU,iBAAiB,oBAAoB,cAAc,KAAK,MAAM,MAAM;AACpF,MAAI,CAAC,SAAS;AACV,WAAO;AAAA,EACX;AACA,MAAI,WAAW;AACX,WAAO,MAAM,IAAI,CAAC,OAAO,OAAO;AAAA,MAC5B,IAAI,MAAM;AAAA,MACV,UAAU,MAAM;AAAA,MAChB,QAAQ,YAAY,MAAM,QAAQ,UAAU,CAAC,CAAC;AAAA,MAC9C,aAAa,MAAM;AAAA,MACnB,aAAa,MAAM;AAAA,IACvB,EAAE;AAAA,EACN;AACA,SAAO;AACX;AAKA,IAAM,cAAc,CAAC,GAAG,MAAM;AAC1B,SAAO,KAAK,IAAI,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI;AAC7D;AAQA,IAAM,kBAAkB,CAAC,KAAK,WAAW;AACrC,MAAI,QAAQ;AACZ,MAAI,aAAa;AACjB,aAAW,SAAS,QAAQ;AACxB,UAAM,QAAQ,WAAW,KAAK,KAAK;AACnC,QAAI,QAAQ,YAAY;AACpB,cAAQ;AACR,mBAAa;AAAA,IACjB;AAAA,EACJ;AACA,MAAI,OAAO;AACP,WAAO,MAAM,IAAI,CAAC,OAAO,MAAM;AAC3B,UAAI;AACJ,aAAQ;AAAA,QACJ,IAAI,MAAM;AAAA,QACV,UAAU,MAAM;AAAA,QAChB,QAAQ,YAAY,MAAM,SAAS,KAAK,IAAI,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,MAClG;AAAA,IACJ,CAAC;AAAA,EACL;AACA,SAAO;AACX;AAQA,IAAM,uBAAuB,CAAC,UAAU,WAAW;AAC/C,MAAI,QAAQ;AACZ,MAAI,YAAY;AAChB,aAAW,SAAS,QAAQ;AACxB,UAAM,eAAe,gBAAgB,UAAU,KAAK;AACpD,QAAI,iBAAiB,MAAM;AACvB,YAAM,QAAQ,gBAAgB,YAAY;AAC1C,UAAI,QAAQ,WAAW;AACnB,oBAAY;AACZ,gBAAQ;AAAA,MACZ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAYA,IAAM,kBAAkB,CAAC,UAAU;AAC/B,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,aAAW,SAAS,OAAO;AACvB,eAAW,WAAW,MAAM,UAAU;AAClC,UAAI,QAAQ,CAAC,MAAM,KAAK;AACpB,iBAAS,KAAK,IAAI,GAAG,KAAK;AAAA,MAC9B,WACS,YAAY,IAAI;AACrB,iBAAS,KAAK,IAAI,GAAG,KAAK;AAAA,MAC9B;AACA;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,iBAAN,MAAqB;AAAA,EACjB,YAAY,UAAU;AAClB,SAAK,WAAW,SAAS,MAAM;AAAA,EACnC;AAAA,EACA,OAAO;AACH,QAAI,KAAK,SAAS,SAAS,GAAG;AAC1B,aAAO,KAAK,SAAS,MAAM;AAAA,IAC/B;AACA,WAAO;AAAA,EACX;AACJ;AAEA,IAAM,WAAW,CAAC,IAAI,SAAS;AAC3B,MAAI,QAAQ,IAAI;AACZ,WAAO,GAAG,IAAI;AAAA,EAClB;AACA,MAAI,GAAG,aAAa,IAAI,GAAG;AACvB,WAAO,GAAG,aAAa,IAAI;AAAA,EAC/B;AACA,SAAO;AACX;AAMA,IAAM,gBAAgB,CAAC,SAAS;AAC5B,SAAO,MAAM,KAAK,KAAK,QAAQ,EAC1B,OAAO,CAAC,OAAO,GAAG,YAAY,oBAAoB,EAClD,IAAI,CAAC,OAAO;AACb,UAAM,KAAK,SAAS,IAAI,IAAI;AAC5B,WAAO;AAAA,MACH,MAAM,UAAU,SAAS,IAAI,MAAM,CAAC,EAAE;AAAA,MACtC,IAAI,MAAM,OAAO,SAAY,UAAU,EAAE;AAAA,IAC7C;AAAA,EACJ,CAAC;AACL;AAMA,IAAM,aAAa,CAAC,SAAS;AACzB,SAAO,kBAAkB,eAAe,IAAI,CAAC;AACjD;AAMA,IAAM,iBAAiB,CAAC,SAAS;AAC7B,SAAO,MAAM,KAAK,KAAK,QAAQ,EAC1B,OAAO,CAAC,OAAO,GAAG,YAAY,eAAe,GAAG,SAAS,EACzD,IAAI,CAAC,OAAO;AACb,UAAM,YAAY,SAAS,IAAI,WAAW;AAC1C,WAAO;AAAA,MACH,UAAU,UAAU,SAAS,IAAI,KAAK,CAAC,EAAE;AAAA,MACzC,IAAI,UAAU,YAAY;AAAA,MAC1B,QAAQ,GAAG;AAAA,MACX,aAAa,GAAG;AAAA,MAChB,aAAa,GAAG;AAAA,MAChB,UAAU,eAAe,EAAE;AAAA,IAC/B;AAAA,EACJ,CAAC;AACL;AAMA,IAAM,oBAAoB,CAAC,UAAU;AACjC,QAAM,SAAS,CAAC;AAChB,aAAW,QAAQ,OAAO;AACtB,gBAAY,CAAC,GAAG,QAAQ,IAAI;AAAA,EAChC;AACA,SAAO;AACX;AAEA,IAAM,cAAc,CAAC,OAAO,QAAQ,SAAS;AACzC,UAAQ;AAAA,IACJ,GAAG;AAAA,IACH;AAAA,MACI,IAAI,KAAK;AAAA,MACT,UAAU,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,MACb,aAAa,KAAK;AAAA,MAClB,aAAa,KAAK;AAAA,IACtB;AAAA,EACJ;AACA,MAAI,KAAK,SAAS,WAAW,GAAG;AAC5B,WAAO,KAAK,KAAK;AACjB;AAAA,EACJ;AACA,aAAW,SAAS,KAAK,UAAU;AAC/B,gBAAY,OAAO,QAAQ,KAAK;AAAA,EACpC;AACJ;AAEA,IAAM,SAAS,MAAM;AAAA,EACjB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,qBAAqB,YAAY,MAAM,sBAAsB,CAAC;AACnE,SAAK,oBAAoB,YAAY,MAAM,qBAAqB,CAAC;AACjE,SAAK,eAAe;AACpB,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,YAAY;AAKjB,SAAK,OAAO;AAeZ,SAAK,UAAU;AAAA,EACnB;AAAA,EACM,oBAAoB;AAAA;AACtB,YAAM,iBAAiB;AACvB,YAAM,aAAa,MAAM,KAAK,UAAU,KAAK,YAAY,CAAC;AAC1D,UAAI,eAAe,MAAM;AACrB,YAAI,OAAO,eAAe,UAAU;AAChC,gBAAM,EAAE,SAAS,IAAI;AACrB,gBAAM,OAAO,UAAU,QAAQ;AAC/B,eAAK,YAAY,KAAK,UAAU,oBAAoB,KAAK,WAAW;AACpE,gBAAM,KAAK,kBAAkB,KAAK,UAAU,kBAAkB;AAAA,QAClE;AAAA,MACJ,OACK;AACD,cAAM,KAAK,gBAAgB;AAAA,MAC/B;AAAA,IACJ;AAAA;AAAA,EACA,mBAAmB;AACf,WAAO,iBAAiB,2BAA2B,SAAS,KAAK,kBAAkB,KAAK,IAAI,GAAG,EAAE,CAAC;AAClG,WAAO,iBAAiB,uBAAuB,SAAS,KAAK,gBAAgB,KAAK,IAAI,GAAG,GAAG,CAAC;AAAA,EACjG;AAAA,EACM,aAAa;AAAA;AACf,YAAM,YAAY,KAAK,iBAAiB;AACxC,UAAI,WAAW,KAAK,YAAY;AAChC,YAAM,aAAa,MAAM,KAAK,UAAU,QAAQ;AAChD,UAAI,eAAe,MAAM;AACrB,YAAI,OAAO,eAAe,UAAU;AAChC,qBAAW,UAAU,WAAW,QAAQ,EAAE;AAAA,QAC9C,OACK;AACD,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO,KAAK,kBAAkB,UAAU,SAAS;AAAA,IACrD;AAAA;AAAA,EACA,aAAa,IAAI;AACb,OAAG,OAAO,SAAS,GAAG,CAAC,uBAAuB;AAC1C,WAAK,KAAK;AACV,yBAAmB;AAAA,IACvB,CAAC;AAAA,EACL;AAAA;AAAA,EAEM,gBAAgB;AAAA;AAClB,YAAM,aAAa,MAAM,KAAK,UAAU;AACxC,UAAI,eAAe,MAAM;AACrB,YAAI,OAAO,eAAe,UAAU;AAChC,iBAAO,WAAW;AAAA,QACtB,OACK;AACD,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQM,KAAK,MAAM,YAAY,WAAW,WAAW;AAAA;AAC/C,UAAI;AACJ,UAAI,KAAK,WAAW,GAAG,GAAG;AACtB,cAAM,eAAe,KAAK,KAAK,kBAAkB,QAAQ,OAAO,SAAS,KAAK;AAE9E,cAAM,MAAM,IAAI,IAAI,MAAM,gBAAgB,WAAW,EAAE;AACvD,eAAO,IAAI,WAAW,IAAI;AAAA,MAC9B;AACA,UAAI,aAAa,UAAU,IAAI;AAC/B,YAAM,aAAa,MAAM,KAAK,UAAU,WAAW,QAAQ;AAC3D,UAAI,eAAe,MAAM;AACrB,YAAI,OAAO,eAAe,UAAU;AAChC,uBAAa,UAAU,WAAW,QAAQ;AAAA,QAC9C,OACK;AACD,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,WAAK,YAAY,WAAW,UAAU,WAAW,WAAW,WAAW;AACvE,aAAO,KAAK,kBAAkB,WAAW,UAAU,WAAW,SAAS;AAAA,IAC3E;AAAA;AAAA;AAAA,EAEA,OAAO;AACH,WAAO,QAAQ,KAAK;AACpB,WAAO,QAAQ,QAAQ,KAAK,WAAW;AAAA,EAC3C;AAAA;AAAA,EAEM,aAAa;AAAA;AACf,kBAAY,WAAW,KAAK,EAAE,CAAC;AAC/B,qBAAe,cAAc,KAAK,EAAE,CAAC;AAAA,IACzC;AAAA;AAAA;AAAA,EAEM,WAAW,WAAW;AAAA;AACxB,UAAI,KAAK,MAAM;AACX,wBAAgB,0DAA0D;AAC1E,eAAO;AAAA,MACX;AACA,YAAM,EAAE,KAAK,OAAO,IAAI,MAAM,aAAa,OAAO,SAAS,IAAI;AAC/D,YAAM,SAAS,WAAW,KAAK,EAAE;AACjC,YAAM,QAAQ,gBAAgB,KAAK,MAAM;AACzC,UAAI,CAAC,OAAO;AACR,wBAAgB,sCAAsC,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;AAC1E,eAAO;AAAA,MACX;AACA,YAAM,WAAW,gBAAgB,KAAK;AACtC,UAAI,CAAC,UAAU;AACX,wBAAgB,oFAAoF;AACpG,eAAO;AAAA,MACX;AACA,WAAK,YAAY,UAAU,SAAS;AACpC,YAAM,KAAK,kBAAkB,QAAQ,OAAO,oBAAoB,UAAU,MAAM,IAAI,MAAM;AAC1F,aAAO;AAAA,IACX;AAAA;AAAA;AAAA,EAEA,oBAAoB;AAChB,UAAM,WAAW,KAAK,YAAY;AAClC,QAAI,YAAY,kBAAkB,UAAU,cAAc,KAAK,EAAE,CAAC,GAAG;AACjE,WAAK,kBAAkB,UAAU,kBAAkB;AAAA,IACvD;AAAA,EACJ;AAAA;AAAA,EAEA,kBAAkB;AACd,WAAO,KAAK,kBAAkB,KAAK,YAAY,GAAG,kBAAkB;AAAA,EACxE;AAAA,EACA,mBAAmB;AACf,QAAI;AACJ,UAAM,MAAM;AACZ,QAAI,IAAI,QAAQ,UAAU,MAAM;AAC5B,WAAK;AACL,UAAI,QAAQ,aAAa,KAAK,OAAO,IAAI,SAAS,QAAQ,KAAK,IAAI,SAAS,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI;AAAA,IACtI;AACA,UAAM,QAAQ,IAAI,QAAQ;AAC1B,UAAM,YAAY,KAAK;AACvB,SAAK,YAAY;AACjB,QAAI,QAAQ,aAAc,SAAS,aAAa,YAAY,GAAI;AAC5D,aAAO;AAAA,IACX;AACA,QAAI,QAAQ,WAAW;AACnB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACM,kBAAkB,UAAU,WAAW,WAAW;AAAA;AACpD,UAAI,CAAC,UAAU;AACX,sBAAc,oDAAoD;AAClE,eAAO;AAAA,MACX;AAEA,YAAM,YAAY,cAAc,KAAK,EAAE;AACvC,YAAM,WAAW,kBAAkB,UAAU,SAAS;AACtD,UAAI,eAAe;AACnB,UAAI,UAAU;AACV,cAAM,EAAE,UAAU,YAAY,YAAY,IAAI,SAAS;AACvD,aAAK,YAAY,YAAY,WAAW,WAAW;AACnD,uBAAe,SAAS;AACxB,mBAAW;AAAA,MACf;AAEA,YAAM,SAAS,WAAW,KAAK,EAAE;AACjC,YAAM,QAAQ,qBAAqB,UAAU,MAAM;AACnD,UAAI,CAAC,OAAO;AACR,sBAAc,mDAAmD;AACjE,eAAO;AAAA,MACX;AAEA,aAAO,KAAK,kBAAkB,SAAS,MAAM,OAAO,WAAW,UAAU,cAAc,GAAG,SAAS;AAAA,IACvG;AAAA;AAAA,EACM,kBAAkB,MAAM,OAAO,WAAW,UAAU,cAAc,QAAQ,GAAG,WAAW;AAAA;AAC1F,YAAM,SAAS,MAAM,KAAK,KAAK;AAC/B,UAAI,UAAU;AACd,UAAI;AACA,kBAAU,MAAM,KAAK,cAAc,MAAM,OAAO,WAAW,UAAU,cAAc,OAAO,SAAS;AAAA,MACvG,SACO,GAAG;AACN,sBAAc,kDAAkD,CAAC;AAAA,MACrE;AACA,aAAO;AACP,aAAO;AAAA,IACX;AAAA;AAAA,EACM,OAAO;AAAA;AACT,YAAM,IAAI,KAAK;AACf,UAAI;AACJ,WAAK,cAAc,IAAI,QAAQ,CAAC,MAAO,UAAU,CAAE;AACnD,UAAI,MAAM,QAAW;AACjB,cAAM;AAAA,MACV;AACA,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOM,YAAyC;AAAA,+CAA/B,KAAK,KAAK,YAAY,GAAG,MAAM;AAC3C,UAAI,SAAS,QAAW;AACpB,eAAO,UAAU,KAAK,YAAY,EAAE;AAAA,MACxC;AACA,UAAI,CAAC,MAAM,CAAC,MAAM;AACd,eAAO;AAAA,MACX;AACA,YAAM,SAAS,WAAW,KAAK,EAAE;AACjC,YAAM,YAAY,qBAAqB,MAAM,MAAM;AAEnD,YAAM,kBAAkB,aAAa,UAAU,UAAU,SAAS,CAAC,EAAE;AACrE,YAAM,WAAW,kBAAkB,MAAM,gBAAgB,IAAI;AAC7D,UAAI,aAAa,SAAS,OAAO,aAAa,UAAU;AACpD,eAAO;AAAA,MACX;AACA,YAAM,UAAU,qBAAqB,IAAI,MAAM;AAE/C,YAAM,kBAAkB,WAAW,QAAQ,QAAQ,SAAS,CAAC,EAAE;AAC/D,aAAO,kBAAkB,gBAAgB,IAAI;AAAA,IACjD;AAAA;AAAA,EACM,cAAc,MAAM,OAAO,WAAW,UAAU,cAAc,QAAQ,GAAG,WAAW;AAAA;AACtF,UAAI,KAAK,MAAM;AACX,wBAAgB,0DAA0D;AAC1E,eAAO;AAAA,MACX;AACA,WAAK,OAAO;AAEZ,YAAM,aAAa,KAAK,iBAAiB,UAAU,YAAY;AAC/D,UAAI,YAAY;AACZ,aAAK,mBAAmB,KAAK,UAAU;AAAA,MAC3C;AACA,YAAM,UAAU,MAAM,cAAc,MAAM,OAAO,WAAW,OAAO,OAAO,SAAS;AACnF,WAAK,OAAO;AAEZ,UAAI,YAAY;AACZ,aAAK,kBAAkB,KAAK,UAAU;AAAA,MAC1C;AACA,aAAO;AAAA,IACX;AAAA;AAAA,EACA,YAAY,UAAU,WAAW,aAAa;AAC1C,SAAK;AACL,kBAAc,OAAO,SAAS,KAAK,MAAM,KAAK,SAAS,UAAU,WAAW,KAAK,OAAO,WAAW;AAAA,EACvG;AAAA,EACA,cAAc;AACV,WAAO,aAAa,OAAO,UAAU,KAAK,MAAM,KAAK,OAAO;AAAA,EAChE;AAAA,EACA,iBAAiB,YAAY,sBAAsB;AAC/C,UAAM,OAAO,KAAK;AAClB,UAAM,KAAK,aAAa,UAAU;AAClC,SAAK,eAAe;AACpB,QAAI,OAAO,MAAM;AACb,aAAO;AAAA,IACX;AACA,UAAM,iBAAiB,uBAAuB,aAAa,oBAAoB,IAAI;AACnF,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AACxC;AAEA,IAAM,gBAAgB;AAEtB,IAAM,aAAa,MAAM;AAAA,EACrB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAK9B,SAAK,kBAAkB;AACvB,SAAK,UAAU,CAAC,OAAO;AACnB,cAAQ,KAAK,MAAM,IAAI,KAAK,iBAAiB,KAAK,eAAe;AAAA,IACrE;AAAA,EACJ;AAAA,EACA,SAAS;AACL,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,QAAQ;AAAA,MACV,MAAM,KAAK;AAAA,MACX,KAAK,KAAK;AAAA,MACV,QAAQ,KAAK;AAAA,IACjB;AACA,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,SAAS,KAAK,SAAS,OAAO,mBAAmB,KAAK,OAAO;AAAA,MACxH,CAAC,IAAI,GAAG;AAAA,MACR,mBAAmB;AAAA,IACvB,CAAC,EAAE,GAAG,EAAE,KAAK,OAAO,OAAO,EAAE,KAAK,2CAA2C,GAAG,KAAK,GAAG,EAAE,QAAQ,EAAE,KAAK,2CAA2C,CAAC,CAAC,CAAC;AAAA,EAC/J;AACJ;AACA,WAAW,QAAQ;", "names": []}