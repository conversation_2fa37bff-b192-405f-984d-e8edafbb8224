import{b as O}from"./chunk-EHNA26RN.js";var v=(e,n)=>e.month===n.month&&e.day===n.day&&e.year===n.year,w=(e,n)=>e.year<n.year||e.year===n.year&&e.month<n.month||e.year===n.year&&e.month===n.month&&e.day!==null&&e.day<n.day,I=(e,n)=>e.year>n.year||e.year===n.year&&e.month>n.month||e.year===n.year&&e.month===n.month&&e.day!==null&&e.day>n.day,le=(e,n,t)=>{let o=Array.isArray(e)?e:[e];for(let i of o)if(n!==void 0&&w(i,n)||t!==void 0&&I(i,t)){O(`[ion-datetime] - The value provided to ion-datetime is out of bounds.

Min: ${JSON.stringify(n)}
Max: ${JSON.stringify(t)}
Value: ${JSON.stringify(e)}`);break}},z=e=>e%4===0&&e%100!==0||e%400===0,E=(e,n)=>{if(n!==void 0)return n;let t=new Intl.DateTimeFormat(e,{hour:"numeric"}),o=t.resolvedOptions();if(o.hourCycle!==void 0)return o.hourCycle;let i=new Date("5/18/2021 00:00"),u=t.formatToParts(i).find(r=>r.type==="hour");if(!u)throw new Error("Hour value not found from DateTimeFormat");switch(u.value){case"0":return"h11";case"12":return"h12";case"00":return"h23";case"24":return"h24";default:throw new Error(`Invalid hour cycle "${n}"`)}},$=e=>e==="h23"||e==="h24",g=(e,n)=>e===4||e===6||e===9||e===11?30:e===2?z(n)?29:28:31,fe=(e,n={month:"numeric",year:"numeric"})=>new Intl.DateTimeFormat(e,n).formatToParts(new Date)[0].type==="month",ye=e=>new Intl.DateTimeFormat(e,{hour:"numeric"}).formatToParts(new Date)[0].type==="dayPeriod",_=/^(\d{4}|[+\-]\d{6})(?:-(\d{2})(?:-(\d{2}))?)?(?:T(\d{2}):(\d{2})(?::(\d{2})(?:\.(\d{3}))?)?(?:(Z)|([+\-])(\d{2})(?::(\d{2}))?)?)?$/,G=/^((\d{2}):(\d{2})(?::(\d{2})(?:\.(\d{3}))?)?(?:(Z)|([+\-])(\d{2})(?::(\d{2}))?)?)?$/,he=e=>{if(e===void 0)return;let n=e;typeof e=="string"&&(n=e.replace(/\[|\]|\s/g,"").split(","));let t;return Array.isArray(n)?t=n.map(o=>parseInt(o,10)).filter(isFinite):t=[n],t},me=e=>({month:parseInt(e.getAttribute("data-month"),10),day:parseInt(e.getAttribute("data-day"),10),year:parseInt(e.getAttribute("data-year"),10),dayOfWeek:parseInt(e.getAttribute("data-day-of-week"),10)});function b(e){if(Array.isArray(e)){let t=[];for(let o of e){let i=b(o);if(!i)return;t.push(i)}return t}let n=null;if(e!=null&&e!==""&&(n=G.exec(e),n?(n.unshift(void 0,void 0),n[2]=n[3]=void 0):n=_.exec(e)),n===null){O(`[ion-datetime] - Unable to parse date string: ${e}. Please provide a valid ISO 8601 datetime string.`);return}for(let t=1;t<8;t++)n[t]=n[t]!==void 0?parseInt(n[t],10):void 0;return{year:n[1],month:n[2],day:n[3],hour:n[4],minute:n[5],ampm:n[4]<12?"am":"pm"}}var R=(e,n,t)=>n&&w(e,n)?n:t&&I(e,t)?t:e,B=e=>e>=12?"pm":"am",ge=(e,n)=>{let t=b(e);if(t===void 0)return;let{month:o,day:i,year:d,hour:u,minute:r}=t,s=d??n.year,a=o??12;return{month:a,day:i??g(a,s),year:s,hour:u??23,minute:r??59}},ve=(e,n)=>{let t=b(e);if(t===void 0)return;let{month:o,day:i,year:d,hour:u,minute:r}=t;return{month:o??1,day:i??1,year:d??n.year,hour:u??0,minute:r??0}},D=e=>("0"+(e!==void 0?Math.abs(e):"0")).slice(-2),J=e=>("000"+(e!==void 0?Math.abs(e):"0")).slice(-4);function S(e){if(Array.isArray(e))return e.map(t=>S(t));let n="";return e.year!==void 0?(n=J(e.year),e.month!==void 0&&(n+="-"+D(e.month),e.day!==void 0&&(n+="-"+D(e.day),e.hour!==void 0&&(n+=`T${D(e.hour)}:${D(e.minute)}:00`)))):e.hour!==void 0&&(n=D(e.hour)+":"+D(e.minute)),n}var N=(e,n)=>n===void 0?e:n==="am"?e===12?0:e:e===12?12:e+12,De=e=>{let{dayOfWeek:n}=e;if(n==null)throw new Error("No day of week provided");return j(e,n)},pe=e=>{let{dayOfWeek:n}=e;if(n==null)throw new Error("No day of week provided");return F(e,6-n)},Te=e=>F(e,1),we=e=>j(e,1),Ie=e=>j(e,7),Me=e=>F(e,7),j=(e,n)=>{let{month:t,day:o,year:i}=e;if(o===null)throw new Error("No day provided");let d={month:t,day:o,year:i};if(d.day=o-n,d.day<1&&(d.month-=1),d.month<1&&(d.month=12,d.year-=1),d.day<1){let u=g(d.month,d.year);d.day=u+d.day}return d},F=(e,n)=>{let{month:t,day:o,year:i}=e;if(o===null)throw new Error("No day provided");let d={month:t,day:o,year:i},u=g(t,i);return d.day=o+n,d.day>u&&(d.day-=u,d.month+=1),d.month>12&&(d.month=1,d.year+=1),d},C=e=>{let n=e.month===1?12:e.month-1,t=e.month===1?e.year-1:e.year,o=g(n,t),i=o<e.day?o:e.day;return{month:n,year:t,day:i}},A=e=>{let n=e.month===12?1:e.month+1,t=e.month===12?e.year+1:e.year,o=g(n,t),i=o<e.day?o:e.day;return{month:n,year:t,day:i}},Z=(e,n)=>{let t=e.month,o=e.year+n,i=g(t,o),d=i<e.day?i:e.day;return{month:t,year:o,day:d}},Oe=e=>Z(e,-1),Ae=e=>Z(e,1),X=(e,n,t)=>n?e:N(e,t),$e=(e,n)=>{let{ampm:t,hour:o}=e,i=o;return t==="am"&&n==="pm"?i=N(i,"pm"):t==="pm"&&n==="am"&&(i=Math.abs(i-12)),i},be=(e,n,t)=>{let{month:o,day:i,year:d}=e,u=R(Object.assign({},e),n,t),r=g(o,d);return i!==null&&r<i&&(u.day=r),n!==void 0&&v(u,n)&&u.hour!==void 0&&n.hour!==void 0&&(u.hour<n.hour?(u.hour=n.hour,u.minute=n.minute):u.hour===n.hour&&u.minute!==void 0&&n.minute!==void 0&&u.minute<n.minute&&(u.minute=n.minute)),t!==void 0&&v(e,t)&&u.hour!==void 0&&t.hour!==void 0&&(u.hour>t.hour?(u.hour=t.hour,u.minute=t.minute):u.hour===t.hour&&u.minute!==void 0&&t.minute!==void 0&&u.minute>t.minute&&(u.minute=t.minute)),u},je=({refParts:e,monthValues:n,dayValues:t,yearValues:o,hourValues:i,minuteValues:d,minParts:u,maxParts:r})=>{let{hour:s,minute:a,day:l,month:m,year:y}=e,c=Object.assign(Object.assign({},e),{dayOfWeek:void 0});if(o!==void 0){let f=o.filter(h=>!(u!==void 0&&h<u.year||r!==void 0&&h>r.year));c.year=T(y,f)}if(n!==void 0){let f=n.filter(h=>!(u!==void 0&&c.year===u.year&&h<u.month||r!==void 0&&c.year===r.year&&h>r.month));c.month=T(m,f)}if(l!==null&&t!==void 0){let f=t.filter(h=>!(u!==void 0&&w(Object.assign(Object.assign({},c),{day:h}),u)||r!==void 0&&I(Object.assign(Object.assign({},c),{day:h}),r)));c.day=T(l,f)}if(s!==void 0&&i!==void 0){let f=i.filter(h=>!(u?.hour!==void 0&&v(c,u)&&h<u.hour||r?.hour!==void 0&&v(c,r)&&h>r.hour));c.hour=T(s,f),c.ampm=B(c.hour)}if(a!==void 0&&d!==void 0){let f=d.filter(h=>!(u?.minute!==void 0&&v(c,u)&&c.hour===u.hour&&h<u.minute||r?.minute!==void 0&&v(c,r)&&c.hour===r.hour&&h>r.minute));c.minute=T(a,f)}return c},T=(e,n)=>{let t=n[0],o=Math.abs(t-e);for(let i=1;i<n.length;i++){let d=n[i],u=Math.abs(d-e);u<o&&(t=d,o=u)}return t},q=e=>e===void 0?"":e.toUpperCase(),U=e=>Object.assign(Object.assign({},e),{timeZone:"UTC",timeZoneName:void 0}),Fe=(e,n,t,o={hour:"numeric",minute:"numeric"})=>{let i={hour:n.hour,minute:n.minute};return i.hour===void 0||i.minute===void 0?"Invalid Time":new Intl.DateTimeFormat(e,Object.assign(Object.assign({},U(o)),{hourCycle:t})).format(new Date(S(Object.assign({year:2023,day:1,month:1},i))+"Z"))},W=e=>{let n=e.toString();return n.length>1?n:`0${n}`},K=(e,n)=>{if(e===0)switch(n){case"h11":return"0";case"h12":return"12";case"h23":return"00";case"h24":return"24";default:throw new Error(`Invalid hour cycle "${n}"`)}return $(n)?W(e):e.toString()},ke=(e,n,t)=>{if(t.day===null)return null;let o=M(t),i=new Intl.DateTimeFormat(e,{weekday:"long",month:"long",day:"numeric",timeZone:"UTC"}).format(o);return n?`Today, ${i}`:i},Ce=(e,n)=>{let t=M(n);return new Intl.DateTimeFormat(e,{month:"long",year:"numeric",timeZone:"UTC"}).format(t)},He=(e,n)=>x(e,n,{day:"numeric"}).find(t=>t.type==="day").value,Q=(e,n)=>V(e,n,{year:"numeric"}),M=e=>{var n,t,o;let i=e.hour!==void 0&&e.minute!==void 0?` ${e.hour}:${e.minute}`:"";return new Date(`${(n=e.month)!==null&&n!==void 0?n:1}/${(t=e.day)!==null&&t!==void 0?t:1}/${(o=e.year)!==null&&o!==void 0?o:2023}${i} GMT+0000`)},V=(e,n,t)=>{let o=M(n);return L(e,U(t)).format(o)},x=(e,n,t)=>{let o=M(n);return L(e,t).formatToParts(o)},L=(e,n)=>new Intl.DateTimeFormat(e,Object.assign(Object.assign({},n),{timeZone:"UTC"})),P=e=>{if("RelativeTimeFormat"in Intl){let n=new Intl.RelativeTimeFormat(e,{numeric:"auto"}).format(0,"day");return n.charAt(0).toUpperCase()+n.slice(1)}else return"Today"},k=e=>{let n=e.getTimezoneOffset();return e.setMinutes(e.getMinutes()-n),e},ee=k(new Date("2022T01:00")),ne=k(new Date("2022T13:00")),H=(e,n)=>{let t=n==="am"?ee:ne,o=new Intl.DateTimeFormat(e,{hour:"numeric",timeZone:"UTC"}).formatToParts(t).find(i=>i.type==="dayPeriod");return o?o.value:q(n)},Ee=e=>Array.isArray(e)?e.join(","):e,Se=()=>k(new Date).toISOString(),te=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59],oe=[0,1,2,3,4,5,6,7,8,9,10,11],ue=[0,1,2,3,4,5,6,7,8,9,10,11],ie=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],re=[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,0],Ne=(e,n,t=0)=>{let o=n==="ios"?"short":"narrow",i=new Intl.DateTimeFormat(e,{weekday:o}),d=new Date("11/01/2020"),u=[];for(let r=t;r<t+7;r++){let s=new Date(d);s.setDate(s.getDate()+r),u.push(i.format(s))}return u},Ze=(e,n,t,o=!1)=>{let i=g(e,n),d;e===1?d=g(12,n-1):d=g(e-1,n);let u=new Date(`${e}/1/${n}`).getDay(),r=u>=t?u-(t+1):6-(t-u),s=[];for(let a=1;a<=i;a++)s.push({day:a,dayOfWeek:(r+a)%7,isAdjacentDay:!1});if(o){for(let l=0;l<=r;l++)s=[{day:d-l,dayOfWeek:(d-l)%7,isAdjacentDay:!0},...s];let a=41-(i+r);for(let l=0;l<a;l++)s.push({day:l+1,dayOfWeek:(i+r+l)%7,isAdjacentDay:!0})}else for(let a=0;a<=r;a++)s=[{day:null,dayOfWeek:null,isAdjacentDay:!1},...s];return s},de=e=>{switch(e){case"h11":return oe;case"h12":return ue;case"h23":return ie;case"h24":return re;default:throw new Error(`Invalid hour cycle "${e}"`)}},se=(e,n,t="h12",o,i,d,u)=>{let r=E(e,t),s=$(r),a=de(r),l=te,m=!0,y=!0;if(d&&(a=a.filter(c=>d.includes(c))),u&&(l=l.filter(c=>u.includes(c))),o)if(v(n,o)){if(o.hour!==void 0&&(a=a.filter(c=>{let f=n.ampm==="pm"?(c+12)%24:c;return(s?c:f)>=o.hour}),m=o.hour<13),o.minute!==void 0){let c=!1;o.hour!==void 0&&n.hour!==void 0&&n.hour>o.hour&&(c=!0),l=l.filter(f=>c?!0:f>=o.minute)}}else w(n,o)&&(a=[],l=[],m=y=!1);return i&&(v(n,i)?(i.hour!==void 0&&(a=a.filter(c=>{let f=n.ampm==="pm"?(c+12)%24:c;return(s?c:f)<=i.hour}),y=i.hour>=12),i.minute!==void 0&&n.hour===i.hour&&(l=l.filter(c=>c<=i.minute))):I(n,i)&&(a=[],l=[],m=y=!1)),{hours:a,minutes:l,am:m,pm:y}},Ue=(e,n)=>{let t={month:e.month,year:e.year,day:e.day};if(n!==void 0&&(e.month!==n.month||e.year!==n.year)){let o={month:n.month,year:n.year,day:n.day};return w(o,t)?[o,t,A(e)]:[C(e),t,o]}return[C(e),t,A(e)]},We=(e,n,t,o,i,d={month:"long"})=>{let{year:u}=n,r=[];if(i!==void 0){let s=i;o?.month!==void 0&&(s=s.filter(a=>a<=o.month)),t?.month!==void 0&&(s=s.filter(a=>a>=t.month)),s.forEach(a=>{let l=new Date(`${a}/1/${u} GMT+0000`),m=new Intl.DateTimeFormat(e,Object.assign(Object.assign({},d),{timeZone:"UTC"})).format(l);r.push({text:m,value:a})})}else{let s=o&&o.year===u?o.month:12,a=t&&t.year===u?t.month:1;for(let l=a;l<=s;l++){let m=new Date(`${l}/1/${u} GMT+0000`),y=new Intl.DateTimeFormat(e,Object.assign(Object.assign({},d),{timeZone:"UTC"})).format(m);r.push({text:y,value:l})}}return r},ce=(e,n,t,o,i,d={day:"numeric"})=>{let{month:u,year:r}=n,s=[],a=g(u,r),l=o?.day!==null&&o?.day!==void 0&&o.year===r&&o.month===u?o.day:a,m=t?.day!==null&&t?.day!==void 0&&t.year===r&&t.month===u?t.day:1;if(i!==void 0){let y=i;y=y.filter(c=>c>=m&&c<=l),y.forEach(c=>{let f=new Date(`${u}/${c}/${r} GMT+0000`),h=new Intl.DateTimeFormat(e,Object.assign(Object.assign({},d),{timeZone:"UTC"})).format(f);s.push({text:h,value:c})})}else for(let y=m;y<=l;y++){let c=new Date(`${u}/${y}/${r} GMT+0000`),f=new Intl.DateTimeFormat(e,Object.assign(Object.assign({},d),{timeZone:"UTC"})).format(c);s.push({text:f,value:y})}return s},Le=(e,n,t,o,i)=>{var d,u;let r=[];if(i!==void 0)r=i,o?.year!==void 0&&(r=r.filter(s=>s<=o.year)),t?.year!==void 0&&(r=r.filter(s=>s>=t.year));else{let{year:s}=n,a=(d=o?.year)!==null&&d!==void 0?d:s,l=(u=t?.year)!==null&&u!==void 0?u:s-100;for(let m=l;m<=a;m++)r.push(m)}return r.map(s=>({text:Q(e,{year:s,month:n.month,day:n.day}),value:s}))},Y=(e,n)=>e.month===n.month&&e.year===n.year?[e]:[e,...Y(A(e),n)],Ye=(e,n,t,o,i,d)=>{let u=[],r=[],s=Y(t,o);return d&&(s=s.filter(({month:a})=>d.includes(a))),s.forEach(a=>{let l={month:a.month,day:null,year:a.year},m=ce(e,l,t,o,i,{month:"short",day:"numeric",weekday:"short"}),y=[],c=[];m.forEach(f=>{let h=v(Object.assign(Object.assign({},l),{day:f.value}),n);c.push({text:h?P(e):f.text,value:`${l.year}-${l.month}-${f.value}`}),y.push({month:l.month,year:l.year,day:f.value})}),r=[...r,...y],u=[...u,...c]}),{parts:r,items:u}},ze=(e,n,t,o,i,d,u)=>{let r=E(e,t),s=$(r),{hours:a,minutes:l,am:m,pm:y}=se(e,n,r,o,i,d,u),c=a.map(p=>({text:K(p,r),value:X(p,s,n.ampm)})),f=l.map(p=>({text:W(p),value:p})),h=[];return m&&!s&&h.push({text:H(e,"am"),value:"am"}),y&&!s&&h.push({text:H(e,"pm"),value:"pm"}),{minutesData:f,hoursData:c,dayPeriodData:h}};export{v as a,w as b,I as c,le as d,E as e,g as f,fe as g,ye as h,he as i,me as j,b as k,R as l,B as m,ge as n,ve as o,S as p,De as q,pe as r,Te as s,we as t,Ie as u,Me as v,C as w,A as x,Oe as y,Ae as z,$e as A,be as B,je as C,Fe as D,ke as E,Ce as F,He as G,V as H,Ee as I,Se as J,Ne as K,Ze as L,Ue as M,We as N,ce as O,Le as P,Ye as Q,ze as R};
