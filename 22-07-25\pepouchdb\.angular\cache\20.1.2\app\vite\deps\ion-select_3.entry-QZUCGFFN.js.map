{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-select_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, m as printIonWarning, e as getIonMode, h, j as Host, k as getElement, n as forceUpdate } from './index-B_U9CtaY.js';\nimport { c as createNotchController } from './notch-controller-C5LPspO8.js';\nimport { i as isOptionSelected, c as compareOptions } from './compare-with-utils-sObYyvOy.js';\nimport { b as inheritAttributes, a as renderHiddenInput, n as focusVisibleElement } from './helpers-1O4D2b7y.js';\nimport { c as popoverController, b as actionSheetController, a as alertController, m as modalController, s as safeCall } from './overlays-8Y2rA-ps.js';\nimport { i as isRTL } from './dir-C53feagD.js';\nimport { h as hostContext, c as createColorClasses, g as getClassMap } from './theme-DiVJyqlX.js';\nimport { w as watchForOptions } from './watch-options-Dtdm8lKC.js';\nimport { w as chevronExpand, q as caretDownSharp } from './index-BLV6ykCk.js';\nimport './index-ZjP4CjeZ.js';\nimport './hardware-back-button-DcH0BbDp.js';\nimport './framework-delegate-DxcnWic_.js';\nimport './gesture-controller-BTEOs1at.js';\n\nconst selectIosCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0}:host(.select-disabled){pointer-events:none}:host(.has-focus) button{border:2px solid #5e9ed6}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.select-bottom{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}:host(.has-focus.ion-valid),:host(.select-expanded.ion-valid),:host(.ion-touched.ion-invalid),:host(.select-expanded.ion-touched.ion-invalid){--border-color:var(--highlight-color)}.select-bottom .error-text{display:none;color:var(--highlight-color-invalid)}.select-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .select-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .select-bottom .helper-text{display:none}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-focus.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]:last-of-type){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]:first-of-type){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host{--border-width:0.55px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--highlight-height:0px}.select-icon{width:1.125rem;height:1.125rem;color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959))}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 1.125rem - 4px)}:host(.select-disabled){opacity:0.3}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\n\nconst selectMdCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0}:host(.select-disabled){pointer-events:none}:host(.has-focus) button{border:2px solid #5e9ed6}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.select-bottom{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}:host(.has-focus.ion-valid),:host(.select-expanded.ion-valid),:host(.ion-touched.ion-invalid),:host(.select-expanded.ion-touched.ion-invalid){--border-color:var(--highlight-color)}.select-bottom .error-text{display:none;color:var(--highlight-color-invalid)}.select-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .select-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .select-bottom .helper-text{display:none}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-focus.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]:last-of-type){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]:first-of-type){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.select-fill-solid){--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-500, var(--ion-background-color-step-500, gray));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-solid) .select-wrapper{border-bottom:var(--border-width) var(--border-style) var(--border-color)}:host(.select-expanded.select-fill-solid.ion-valid),:host(.has-focus.select-fill-solid.ion-valid),:host(.select-fill-solid.ion-touched.ion-invalid){--border-color:var(--highlight-color)}:host(.select-fill-solid) .select-bottom{border-top:none}@media (any-hover: hover){:host(.select-fill-solid:hover){--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}:host(.select-fill-solid.select-expanded),:host(.select-fill-solid.has-focus){--background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--border-color:var(--highlight-color)}:host(.select-fill-solid) .select-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0px;border-end-start-radius:0px}:host(.label-floating.select-fill-solid) .label-text-wrapper{max-width:calc(100% / 0.75)}:host(.in-item.select-expanded.select-fill-solid) .select-wrapper .select-icon,:host(.in-item.has-focus.select-fill-solid) .select-wrapper .select-icon,:host(.in-item.has-focus.ion-valid.select-fill-solid) .select-wrapper .select-icon,:host(.in-item.ion-touched.ion-invalid.select-fill-solid) .select-wrapper .select-icon{color:var(--highlight-color)}:host(.select-fill-outline){--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-outline.select-shape-round){--border-radius:28px;--padding-start:32px;--padding-end:32px}:host(.has-focus.select-fill-outline.ion-valid),:host(.select-fill-outline.ion-touched.ion-invalid){--border-color:var(--highlight-color)}@media (any-hover: hover){:host(.select-fill-outline:hover){--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}:host(.select-fill-outline.select-expanded),:host(.select-fill-outline.has-focus){--border-width:var(--highlight-height);--border-color:var(--highlight-color)}:host(.select-fill-outline) .select-bottom{border-top:none}:host(.select-fill-outline) .select-wrapper{border-bottom:none}:host(.select-ltr.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-fill-outline.select-label-placement-floating) .label-text-wrapper{position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .label-text-wrapper{position:relative;z-index:1}:host(.label-floating.select-fill-outline) .label-text-wrapper{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}:host(.select-fill-outline.select-label-placement-stacked) select,:host(.select-fill-outline.select-label-placement-floating) select{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}:host(.select-fill-outline) .select-outline-container{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-end{pointer-events:none}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-notch,:host(.select-fill-outline) .select-outline-end{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.select-fill-outline) .select-outline-notch{max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .notch-spacer{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none}:host(.select-fill-outline) .select-outline-start{-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color)}:host(.select-fill-outline) .select-outline-start{border-start-start-radius:var(--border-radius);border-start-end-radius:0px;border-end-end-radius:0px;border-end-start-radius:var(--border-radius)}:host(.select-fill-outline) .select-outline-start{width:calc(var(--padding-start) - 4px)}:host(.select-fill-outline) .select-outline-end{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color)}:host(.select-fill-outline) .select-outline-end{border-start-start-radius:0px;border-start-end-radius:var(--border-radius);border-end-end-radius:var(--border-radius);border-end-start-radius:0px}:host(.select-fill-outline) .select-outline-end{-ms-flex-positive:1;flex-grow:1}:host(.label-floating.select-fill-outline) .select-outline-notch{border-top:none}:host(.in-item.select-expanded.select-fill-outline) .select-wrapper .select-icon,:host(.in-item.has-focus.select-fill-outline) .select-wrapper .select-icon,:host(.in-item.has-focus.ion-valid.select-fill-outline) .select-wrapper .select-icon,:host(.in-item.ion-touched.ion-invalid.select-fill-outline) .select-wrapper .select-icon{color:var(--highlight-color)}:host{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--highlight-height:2px}:host(.select-label-placement-floating.select-expanded) .label-text-wrapper,:host(.select-label-placement-floating.has-focus) .label-text-wrapper,:host(.select-label-placement-stacked.select-expanded) .label-text-wrapper,:host(.select-label-placement-stacked.has-focus) .label-text-wrapper{color:var(--highlight-color)}:host(.has-focus.select-label-placement-floating.ion-valid) .label-text-wrapper,:host(.select-label-placement-floating.ion-touched.ion-invalid) .label-text-wrapper,:host(.has-focus.select-label-placement-stacked.ion-valid) .label-text-wrapper,:host(.select-label-placement-stacked.ion-touched.ion-invalid) .label-text-wrapper{color:var(--highlight-color)}.select-highlight{bottom:-1px;position:absolute;width:100%;height:var(--highlight-height);-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}.select-highlight{inset-inline-start:0}:host(.select-expanded) .select-highlight,:host(.has-focus) .select-highlight{-webkit-transform:scale(1);transform:scale(1)}:host(.in-item) .select-highlight{bottom:0}:host(.in-item) .select-highlight{inset-inline-start:0}.select-icon{width:0.8125rem;-webkit-transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray))}:host(.select-expanded:not(.has-expanded-icon)) .select-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.in-item.select-expanded) .select-wrapper .select-icon,:host(.in-item.has-focus) .select-wrapper .select-icon,:host(.in-item.has-focus.ion-valid) .select-wrapper .select-icon,:host(.in-item.ion-touched.ion-invalid) .select-wrapper .select-icon{color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray))}:host(.select-expanded) .select-wrapper .select-icon,:host(.has-focus.ion-valid) .select-wrapper .select-icon,:host(.ion-touched.ion-invalid) .select-wrapper .select-icon,:host(.has-focus) .select-wrapper .select-icon{color:var(--highlight-color)}:host(.select-shape-round){--border-radius:16px}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 0.8125rem - 4px)}:host(.select-disabled){opacity:0.38}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\n\nconst Select = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionCancel = createEvent(this, \"ionCancel\", 7);\n        this.ionDismiss = createEvent(this, \"ionDismiss\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.inputId = `ion-sel-${selectIds++}`;\n        this.helperTextId = `${this.inputId}-helper-text`;\n        this.errorTextId = `${this.inputId}-error-text`;\n        this.inheritedAttributes = {};\n        this.isExpanded = false;\n        /**\n         * The `hasFocus` state ensures the focus class is\n         * added regardless of how the element is focused.\n         * The `ion-focused` class only applies when focused\n         * via tabbing, not by clicking.\n         * The `has-focus` logic was added to ensure the class\n         * is applied in both cases.\n         */\n        this.hasFocus = false;\n        /**\n         * The text to display on the cancel button.\n         */\n        this.cancelText = 'Cancel';\n        /**\n         * If `true`, the user cannot interact with the select.\n         */\n        this.disabled = false;\n        /**\n         * The interface the select should use: `action-sheet`, `popover`, `alert`, or `modal`.\n         */\n        this.interface = 'alert';\n        /**\n         * Any additional options that the `alert`, `action-sheet` or `popover` interface\n         * can take. See the [ion-alert docs](./alert), the\n         * [ion-action-sheet docs](./action-sheet), the\n         * [ion-popover docs](./popover), and the [ion-modal docs](./modal) for the\n         * create options for each interface.\n         *\n         * Note: `interfaceOptions` will not override `inputs` or `buttons` with the `alert` interface.\n         */\n        this.interfaceOptions = {};\n        /**\n         * Where to place the label relative to the select.\n         * `\"start\"`: The label will appear to the left of the select in LTR and to the right in RTL.\n         * `\"end\"`: The label will appear to the right of the select in LTR and to the left in RTL.\n         * `\"floating\"`: The label will appear smaller and above the select when the select is focused or it has a value. Otherwise it will appear on top of the select.\n         * `\"stacked\"`: The label will appear smaller and above the select regardless even when the select is blurred or has no value.\n         * `\"fixed\"`: The label has the same behavior as `\"start\"` except it also has a fixed width. Long text will be truncated with ellipses (\"...\").\n         * When using `\"floating\"` or `\"stacked\"` we recommend initializing the select with either a `value` or a `placeholder`.\n         */\n        this.labelPlacement = 'start';\n        /**\n         * If `true`, the select can accept multiple values.\n         */\n        this.multiple = false;\n        /**\n         * The name of the control, which is submitted with the form data.\n         */\n        this.name = this.inputId;\n        /**\n         * The text to display on the ok button.\n         */\n        this.okText = 'OK';\n        /**\n         * If true, screen readers will announce it as a required field. This property\n         * works only for accessibility purposes, it will not prevent the form from\n         * submitting if the value is invalid.\n         */\n        this.required = false;\n        this.onClick = (ev) => {\n            const target = ev.target;\n            const closestSlot = target.closest('[slot=\"start\"], [slot=\"end\"]');\n            if (target === this.el || closestSlot === null) {\n                this.setFocus();\n                this.open(ev);\n            }\n            else {\n                /**\n                 * Prevent clicks to the start/end slots from opening the select.\n                 * We ensure the target isn't this element in case the select is slotted\n                 * in, for example, an item. This would prevent the select from ever\n                 * being opened since the element itself has slot=\"start\"/\"end\".\n                 *\n                 * Clicking a slotted element also causes a click\n                 * on the <label> element (since it wraps the slots).\n                 * Clicking <label> dispatches another click event on\n                 * the native form control that then bubbles up to this\n                 * listener. This additional event targets the host\n                 * element, so the select overlay is opened.\n                 *\n                 * When the slotted elements are clicked (and therefore\n                 * the ancestor <label> element) we want to prevent the label\n                 * from dispatching another click event.\n                 *\n                 * Do not call stopPropagation() because this will cause\n                 * click handlers on the slotted elements to never fire in React.\n                 * When developers do onClick in React a native \"click\" listener\n                 * is added on the root element, not the slotted element. When that\n                 * native click listener fires, React then dispatches the synthetic\n                 * click event on the slotted element. However, if stopPropagation\n                 * is called then the native click event will never bubble up\n                 * to the root element.\n                 */\n                ev.preventDefault();\n            }\n        };\n        this.onFocus = () => {\n            this.hasFocus = true;\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.hasFocus = false;\n            this.ionBlur.emit();\n        };\n        /**\n         * Stops propagation when the label is clicked,\n         * otherwise, two clicks will be triggered.\n         */\n        this.onLabelClick = (ev) => {\n            // Only stop propagation if the click was directly on the label\n            // and not on the input or other child elements\n            if (ev.target === ev.currentTarget) {\n                ev.stopPropagation();\n            }\n        };\n    }\n    styleChanged() {\n        this.emitStyle();\n    }\n    setValue(value) {\n        this.value = value;\n        this.ionChange.emit({ value });\n    }\n    async connectedCallback() {\n        const { el } = this;\n        this.notchController = createNotchController(el, () => this.notchSpacerEl, () => this.labelSlot);\n        this.updateOverlayOptions();\n        this.emitStyle();\n        this.mutationO = watchForOptions(this.el, 'ion-select-option', async () => {\n            this.updateOverlayOptions();\n            /**\n             * We need to re-render the component\n             * because one of the new ion-select-option\n             * elements may match the value. In this case,\n             * the rendered selected text should be updated.\n             */\n            forceUpdate(this);\n        });\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAttributes(this.el, ['aria-label']);\n    }\n    componentDidLoad() {\n        /**\n         * If any of the conditions that trigger the styleChanged callback\n         * are met on component load, it is possible the event emitted\n         * prior to a parent web component registering an event listener.\n         *\n         * To ensure the parent web component receives the event, we\n         * emit the style event again after the component has loaded.\n         *\n         * This is often seen in Angular with the `dist` output target.\n         */\n        this.emitStyle();\n    }\n    disconnectedCallback() {\n        if (this.mutationO) {\n            this.mutationO.disconnect();\n            this.mutationO = undefined;\n        }\n        if (this.notchController) {\n            this.notchController.destroy();\n            this.notchController = undefined;\n        }\n    }\n    /**\n     * Open the select overlay. The overlay is either an alert, action sheet, or popover,\n     * depending on the `interface` property on the `ion-select`.\n     *\n     * @param event The user interface event that called the open.\n     */\n    async open(event) {\n        if (this.disabled || this.isExpanded) {\n            return undefined;\n        }\n        this.isExpanded = true;\n        const overlay = (this.overlay = await this.createOverlay(event));\n        // Add logic to scroll selected item into view before presenting\n        const scrollSelectedIntoView = () => {\n            const indexOfSelected = this.childOpts.findIndex((o) => o.value === this.value);\n            if (indexOfSelected > -1) {\n                const selectedItem = overlay.querySelector(`.select-interface-option:nth-of-type(${indexOfSelected + 1})`);\n                if (selectedItem) {\n                    /**\n                     * Browsers such as Firefox do not\n                     * correctly delegate focus when manually\n                     * focusing an element with delegatesFocus.\n                     * We work around this by manually focusing\n                     * the interactive element.\n                     * ion-radio and ion-checkbox are the only\n                     * elements that ion-select-popover uses, so\n                     * we only need to worry about those two components\n                     * when focusing.\n                     */\n                    const interactiveEl = selectedItem.querySelector('ion-radio, ion-checkbox');\n                    if (interactiveEl) {\n                        selectedItem.scrollIntoView({ block: 'nearest' });\n                        // Needs to be called before `focusVisibleElement` to prevent issue with focus event bubbling\n                        // and removing `ion-focused` style\n                        interactiveEl.setFocus();\n                    }\n                    focusVisibleElement(selectedItem);\n                }\n            }\n            else {\n                /**\n                 * If no value is set then focus the first enabled option.\n                 */\n                const firstEnabledOption = overlay.querySelector('ion-radio:not(.radio-disabled), ion-checkbox:not(.checkbox-disabled)');\n                if (firstEnabledOption) {\n                    /**\n                     * Focus the option for the same reason as we do above.\n                     *\n                     * Needs to be called before `focusVisibleElement` to prevent issue with focus event bubbling\n                     * and removing `ion-focused` style\n                     */\n                    firstEnabledOption.setFocus();\n                    focusVisibleElement(firstEnabledOption.closest('ion-item'));\n                }\n            }\n        };\n        // For modals and popovers, we can scroll before they're visible\n        if (this.interface === 'modal') {\n            overlay.addEventListener('ionModalWillPresent', scrollSelectedIntoView, { once: true });\n        }\n        else if (this.interface === 'popover') {\n            overlay.addEventListener('ionPopoverWillPresent', scrollSelectedIntoView, { once: true });\n        }\n        else {\n            /**\n             * For alerts and action sheets, we need to wait a frame after willPresent\n             * because these overlays don't have their content in the DOM immediately\n             * when willPresent fires. By waiting a frame, we ensure the content is\n             * rendered and can be properly scrolled into view.\n             */\n            const scrollAfterRender = () => {\n                requestAnimationFrame(() => {\n                    scrollSelectedIntoView();\n                });\n            };\n            if (this.interface === 'alert') {\n                overlay.addEventListener('ionAlertWillPresent', scrollAfterRender, { once: true });\n            }\n            else if (this.interface === 'action-sheet') {\n                overlay.addEventListener('ionActionSheetWillPresent', scrollAfterRender, { once: true });\n            }\n        }\n        overlay.onDidDismiss().then(() => {\n            this.overlay = undefined;\n            this.isExpanded = false;\n            this.ionDismiss.emit();\n            this.setFocus();\n        });\n        await overlay.present();\n        return overlay;\n    }\n    createOverlay(ev) {\n        let selectInterface = this.interface;\n        if (selectInterface === 'action-sheet' && this.multiple) {\n            printIonWarning(`[ion-select] - Interface cannot be \"${selectInterface}\" with a multi-value select. Using the \"alert\" interface instead.`);\n            selectInterface = 'alert';\n        }\n        if (selectInterface === 'popover' && !ev) {\n            printIonWarning(`[ion-select] - Interface cannot be a \"${selectInterface}\" without passing an event. Using the \"alert\" interface instead.`);\n            selectInterface = 'alert';\n        }\n        if (selectInterface === 'action-sheet') {\n            return this.openActionSheet();\n        }\n        if (selectInterface === 'popover') {\n            return this.openPopover(ev);\n        }\n        if (selectInterface === 'modal') {\n            return this.openModal();\n        }\n        return this.openAlert();\n    }\n    updateOverlayOptions() {\n        const overlay = this.overlay;\n        if (!overlay) {\n            return;\n        }\n        const childOpts = this.childOpts;\n        const value = this.value;\n        switch (this.interface) {\n            case 'action-sheet':\n                overlay.buttons = this.createActionSheetButtons(childOpts, value);\n                break;\n            case 'popover':\n                const popover = overlay.querySelector('ion-select-popover');\n                if (popover) {\n                    popover.options = this.createOverlaySelectOptions(childOpts, value);\n                }\n                break;\n            case 'modal':\n                const modal = overlay.querySelector('ion-select-modal');\n                if (modal) {\n                    modal.options = this.createOverlaySelectOptions(childOpts, value);\n                }\n                break;\n            case 'alert':\n                const inputType = this.multiple ? 'checkbox' : 'radio';\n                overlay.inputs = this.createAlertInputs(childOpts, inputType, value);\n                break;\n        }\n    }\n    createActionSheetButtons(data, selectValue) {\n        const actionSheetButtons = data.map((option) => {\n            const value = getOptionValue(option);\n            // Remove hydrated before copying over classes\n            const copyClasses = Array.from(option.classList)\n                .filter((cls) => cls !== 'hydrated')\n                .join(' ');\n            const optClass = `${OPTION_CLASS} ${copyClasses}`;\n            return {\n                role: isOptionSelected(selectValue, value, this.compareWith) ? 'selected' : '',\n                text: option.textContent,\n                cssClass: optClass,\n                handler: () => {\n                    this.setValue(value);\n                },\n            };\n        });\n        // Add \"cancel\" button\n        actionSheetButtons.push({\n            text: this.cancelText,\n            role: 'cancel',\n            handler: () => {\n                this.ionCancel.emit();\n            },\n        });\n        return actionSheetButtons;\n    }\n    createAlertInputs(data, inputType, selectValue) {\n        const alertInputs = data.map((option) => {\n            const value = getOptionValue(option);\n            // Remove hydrated before copying over classes\n            const copyClasses = Array.from(option.classList)\n                .filter((cls) => cls !== 'hydrated')\n                .join(' ');\n            const optClass = `${OPTION_CLASS} ${copyClasses}`;\n            return {\n                type: inputType,\n                cssClass: optClass,\n                label: option.textContent || '',\n                value,\n                checked: isOptionSelected(selectValue, value, this.compareWith),\n                disabled: option.disabled,\n            };\n        });\n        return alertInputs;\n    }\n    createOverlaySelectOptions(data, selectValue) {\n        const popoverOptions = data.map((option) => {\n            const value = getOptionValue(option);\n            // Remove hydrated before copying over classes\n            const copyClasses = Array.from(option.classList)\n                .filter((cls) => cls !== 'hydrated')\n                .join(' ');\n            const optClass = `${OPTION_CLASS} ${copyClasses}`;\n            return {\n                text: option.textContent || '',\n                cssClass: optClass,\n                value,\n                checked: isOptionSelected(selectValue, value, this.compareWith),\n                disabled: option.disabled,\n                handler: (selected) => {\n                    this.setValue(selected);\n                    if (!this.multiple) {\n                        this.close();\n                    }\n                },\n            };\n        });\n        return popoverOptions;\n    }\n    async openPopover(ev) {\n        const { fill, labelPlacement } = this;\n        const interfaceOptions = this.interfaceOptions;\n        const mode = getIonMode(this);\n        const showBackdrop = mode === 'md' ? false : true;\n        const multiple = this.multiple;\n        const value = this.value;\n        let event = ev;\n        let size = 'auto';\n        const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n        /**\n         * The popover should take up the full width\n         * when using a fill in MD mode or if the\n         * label is floating/stacked.\n         */\n        if (hasFloatingOrStackedLabel || (mode === 'md' && fill !== undefined)) {\n            size = 'cover';\n            /**\n             * Otherwise the popover\n             * should be positioned relative\n             * to the native element.\n             */\n        }\n        else {\n            event = Object.assign(Object.assign({}, ev), { detail: {\n                    ionShadowTarget: this.nativeWrapperEl,\n                } });\n        }\n        const popoverOpts = Object.assign(Object.assign({ mode,\n            event, alignment: 'center', size,\n            showBackdrop }, interfaceOptions), { component: 'ion-select-popover', cssClass: ['select-popover', interfaceOptions.cssClass], componentProps: {\n                header: interfaceOptions.header,\n                subHeader: interfaceOptions.subHeader,\n                message: interfaceOptions.message,\n                multiple,\n                value,\n                options: this.createOverlaySelectOptions(this.childOpts, value),\n            } });\n        return popoverController.create(popoverOpts);\n    }\n    async openActionSheet() {\n        const mode = getIonMode(this);\n        const interfaceOptions = this.interfaceOptions;\n        const actionSheetOpts = Object.assign(Object.assign({ mode }, interfaceOptions), { buttons: this.createActionSheetButtons(this.childOpts, this.value), cssClass: ['select-action-sheet', interfaceOptions.cssClass] });\n        return actionSheetController.create(actionSheetOpts);\n    }\n    async openAlert() {\n        const interfaceOptions = this.interfaceOptions;\n        const inputType = this.multiple ? 'checkbox' : 'radio';\n        const mode = getIonMode(this);\n        const alertOpts = Object.assign(Object.assign({ mode }, interfaceOptions), { header: interfaceOptions.header ? interfaceOptions.header : this.labelText, inputs: this.createAlertInputs(this.childOpts, inputType, this.value), buttons: [\n                {\n                    text: this.cancelText,\n                    role: 'cancel',\n                    handler: () => {\n                        this.ionCancel.emit();\n                    },\n                },\n                {\n                    text: this.okText,\n                    handler: (selectedValues) => {\n                        this.setValue(selectedValues);\n                    },\n                },\n            ], cssClass: [\n                'select-alert',\n                interfaceOptions.cssClass,\n                this.multiple ? 'multiple-select-alert' : 'single-select-alert',\n            ] });\n        return alertController.create(alertOpts);\n    }\n    openModal() {\n        const { multiple, value, interfaceOptions } = this;\n        const mode = getIonMode(this);\n        const modalOpts = Object.assign(Object.assign({}, interfaceOptions), { mode, cssClass: ['select-modal', interfaceOptions.cssClass], component: 'ion-select-modal', componentProps: {\n                header: interfaceOptions.header,\n                multiple,\n                value,\n                options: this.createOverlaySelectOptions(this.childOpts, value),\n            } });\n        return modalController.create(modalOpts);\n    }\n    /**\n     * Close the select interface.\n     */\n    close() {\n        if (!this.overlay) {\n            return Promise.resolve(false);\n        }\n        return this.overlay.dismiss();\n    }\n    hasValue() {\n        return this.getText() !== '';\n    }\n    get childOpts() {\n        return Array.from(this.el.querySelectorAll('ion-select-option'));\n    }\n    /**\n     * Returns any plaintext associated with\n     * the label (either prop or slot).\n     * Note: This will not return any custom\n     * HTML. Use the `hasLabel` getter if you\n     * want to know if any slotted label content\n     * was passed.\n     */\n    get labelText() {\n        const { label } = this;\n        if (label !== undefined) {\n            return label;\n        }\n        const { labelSlot } = this;\n        if (labelSlot !== null) {\n            return labelSlot.textContent;\n        }\n        return;\n    }\n    getText() {\n        const selectedText = this.selectedText;\n        if (selectedText != null && selectedText !== '') {\n            return selectedText;\n        }\n        return generateText(this.childOpts, this.value, this.compareWith);\n    }\n    setFocus() {\n        if (this.focusEl) {\n            this.focusEl.focus();\n        }\n    }\n    emitStyle() {\n        const { disabled } = this;\n        const style = {\n            'interactive-disabled': disabled,\n        };\n        this.ionStyle.emit(style);\n    }\n    renderLabel() {\n        const { label } = this;\n        return (h(\"div\", { class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !this.hasLabel,\n            }, part: \"label\" }, label === undefined ? h(\"slot\", { name: \"label\" }) : h(\"div\", { class: \"label-text\" }, label)));\n    }\n    componentDidRender() {\n        var _a;\n        (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n    }\n    /**\n     * Gets any content passed into the `label` slot,\n     * not the <slot> definition.\n     */\n    get labelSlot() {\n        return this.el.querySelector('[slot=\"label\"]');\n    }\n    /**\n     * Returns `true` if label content is provided\n     * either by a prop or a content. If you want\n     * to get the plaintext value of the label use\n     * the `labelText` getter instead.\n     */\n    get hasLabel() {\n        return this.label !== undefined || this.labelSlot !== null;\n    }\n    /**\n     * Renders the border container\n     * when fill=\"outline\".\n     */\n    renderLabelContainer() {\n        const mode = getIonMode(this);\n        const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n        if (hasOutlineFill) {\n            /**\n             * The outline fill has a special outline\n             * that appears around the select and the label.\n             * Certain stacked and floating label placements cause the\n             * label to translate up and create a \"cut out\"\n             * inside of that border by using the notch-spacer element.\n             */\n            return [\n                h(\"div\", { class: \"select-outline-container\" }, h(\"div\", { class: \"select-outline-start\" }), h(\"div\", { class: {\n                        'select-outline-notch': true,\n                        'select-outline-notch-hidden': !this.hasLabel,\n                    } }, h(\"div\", { class: \"notch-spacer\", \"aria-hidden\": \"true\", ref: (el) => (this.notchSpacerEl = el) }, this.label)), h(\"div\", { class: \"select-outline-end\" })),\n                this.renderLabel(),\n            ];\n        }\n        /**\n         * If not using the outline style,\n         * we can render just the label.\n         */\n        return this.renderLabel();\n    }\n    /**\n     * Renders either the placeholder\n     * or the selected values based on\n     * the state of the select.\n     */\n    renderSelectText() {\n        const { placeholder } = this;\n        const displayValue = this.getText();\n        let addPlaceholderClass = false;\n        let selectText = displayValue;\n        if (selectText === '' && placeholder !== undefined) {\n            selectText = placeholder;\n            addPlaceholderClass = true;\n        }\n        const selectTextClasses = {\n            'select-text': true,\n            'select-placeholder': addPlaceholderClass,\n        };\n        const textPart = addPlaceholderClass ? 'placeholder' : 'text';\n        return (h(\"div\", { \"aria-hidden\": \"true\", class: selectTextClasses, part: textPart }, selectText));\n    }\n    /**\n     * Renders the chevron icon\n     * next to the select text.\n     */\n    renderSelectIcon() {\n        const mode = getIonMode(this);\n        const { isExpanded, toggleIcon, expandedIcon } = this;\n        let icon;\n        if (isExpanded && expandedIcon !== undefined) {\n            icon = expandedIcon;\n        }\n        else {\n            const defaultIcon = mode === 'ios' ? chevronExpand : caretDownSharp;\n            icon = toggleIcon !== null && toggleIcon !== void 0 ? toggleIcon : defaultIcon;\n        }\n        return h(\"ion-icon\", { class: \"select-icon\", part: \"icon\", \"aria-hidden\": \"true\", icon: icon });\n    }\n    get ariaLabel() {\n        var _a;\n        const { placeholder, inheritedAttributes } = this;\n        const displayValue = this.getText();\n        // The aria label should be preferred over visible text if both are specified\n        const definedLabel = (_a = inheritedAttributes['aria-label']) !== null && _a !== void 0 ? _a : this.labelText;\n        /**\n         * If developer has specified a placeholder\n         * and there is nothing selected, the selectText\n         * should have the placeholder value.\n         */\n        let renderedLabel = displayValue;\n        if (renderedLabel === '' && placeholder !== undefined) {\n            renderedLabel = placeholder;\n        }\n        /**\n         * If there is a developer-defined label,\n         * then we need to concatenate the developer label\n         * string with the current current value.\n         * The label for the control should be read\n         * before the values of the control.\n         */\n        if (definedLabel !== undefined) {\n            renderedLabel = renderedLabel === '' ? definedLabel : `${definedLabel}, ${renderedLabel}`;\n        }\n        return renderedLabel;\n    }\n    renderListbox() {\n        const { disabled, inputId, isExpanded, required } = this;\n        return (h(\"button\", { disabled: disabled, id: inputId, \"aria-label\": this.ariaLabel, \"aria-haspopup\": \"dialog\", \"aria-expanded\": `${isExpanded}`, \"aria-describedby\": this.getHintTextID(), \"aria-invalid\": this.getHintTextID() === this.errorTextId, \"aria-required\": `${required}`, onFocus: this.onFocus, onBlur: this.onBlur, ref: (focusEl) => (this.focusEl = focusEl) }));\n    }\n    getHintTextID() {\n        const { el, helperText, errorText, helperTextId, errorTextId } = this;\n        if (el.classList.contains('ion-touched') && el.classList.contains('ion-invalid') && errorText) {\n            return errorTextId;\n        }\n        if (helperText) {\n            return helperTextId;\n        }\n        return undefined;\n    }\n    /**\n     * Renders the helper text or error text values\n     */\n    renderHintText() {\n        const { helperText, errorText, helperTextId, errorTextId } = this;\n        return [\n            h(\"div\", { id: helperTextId, class: \"helper-text\", part: \"supporting-text helper-text\" }, helperText),\n            h(\"div\", { id: errorTextId, class: \"error-text\", part: \"supporting-text error-text\" }, errorText),\n        ];\n    }\n    /**\n     * Responsible for rendering helper text, and error text. This element\n     * should only be rendered if hint text is set.\n     */\n    renderBottomContent() {\n        const { helperText, errorText } = this;\n        /**\n         * undefined and empty string values should\n         * be treated as not having helper/error text.\n         */\n        const hasHintText = !!helperText || !!errorText;\n        if (!hasHintText) {\n            return;\n        }\n        return h(\"div\", { class: \"select-bottom\" }, this.renderHintText());\n    }\n    render() {\n        const { disabled, el, isExpanded, expandedIcon, labelPlacement, justify, placeholder, fill, shape, name, value, hasFocus, } = this;\n        const mode = getIonMode(this);\n        const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n        const justifyEnabled = !hasFloatingOrStackedLabel && justify !== undefined;\n        const rtl = isRTL(el) ? 'rtl' : 'ltr';\n        const inItem = hostContext('ion-item', this.el);\n        const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n        const hasValue = this.hasValue();\n        const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n        renderHiddenInput(true, el, name, parseValue(value), disabled);\n        /**\n         * If the label is stacked, it should always sit above the select.\n         * For floating labels, the label should move above the select if\n         * the select has a value, is open, or has anything in either\n         * the start or end slot.\n         *\n         * If there is content in the start slot, the label would overlap\n         * it if not forced to float. This is also applied to the end slot\n         * because with the default or solid fills, the select is not\n         * vertically centered in the container, but the label is. This\n         * causes the slots and label to appear vertically offset from each\n         * other when the label isn't floating above the input. This doesn't\n         * apply to the outline fill, but this was not accounted for to keep\n         * things consistent.\n         *\n         * TODO(FW-5592): Remove hasStartEndSlots condition\n         */\n        const labelShouldFloat = labelPlacement === 'stacked' || (labelPlacement === 'floating' && (hasValue || isExpanded || hasStartEndSlots));\n        return (h(Host, { key: 'c03fb65e8fc9f9aab295e07b282377d57d910519', onClick: this.onClick, class: createColorClasses(this.color, {\n                [mode]: true,\n                'in-item': inItem,\n                'in-item-color': hostContext('ion-item.ion-color', el),\n                'select-disabled': disabled,\n                'select-expanded': isExpanded,\n                'has-expanded-icon': expandedIcon !== undefined,\n                'has-value': hasValue,\n                'label-floating': labelShouldFloat,\n                'has-placeholder': placeholder !== undefined,\n                'has-focus': hasFocus,\n                // TODO(FW-6451): Remove `ion-focusable` class in favor of `has-focus`.\n                'ion-focusable': true,\n                [`select-${rtl}`]: true,\n                [`select-fill-${fill}`]: fill !== undefined,\n                [`select-justify-${justify}`]: justifyEnabled,\n                [`select-shape-${shape}`]: shape !== undefined,\n                [`select-label-placement-${labelPlacement}`]: true,\n            }) }, h(\"label\", { key: '0d0c8ec55269adcac625f2899a547f4e7f3e3741', class: \"select-wrapper\", id: \"select-label\", onClick: this.onLabelClick }, this.renderLabelContainer(), h(\"div\", { key: 'f6dfc93c0e23cbe75a2947abde67d842db2dad78', class: \"select-wrapper-inner\" }, h(\"slot\", { key: '957bfadf9f101f519091419a362d3abdc2be66f6', name: \"start\" }), h(\"div\", { key: 'ca349202a484e7f2e884533fd330f0b136754f7d', class: \"native-wrapper\", ref: (el) => (this.nativeWrapperEl = el), part: \"container\" }, this.renderSelectText(), this.renderListbox()), h(\"slot\", { key: 'f0e62a6533ff1c8f62bd2d27f60b23385c4fa9ed', name: \"end\" }), !hasFloatingOrStackedLabel && this.renderSelectIcon()), hasFloatingOrStackedLabel && this.renderSelectIcon(), shouldRenderHighlight && h(\"div\", { key: 'fb840d46bafafb09898ebeebbe8c181906a3d8a2', class: \"select-highlight\" })), this.renderBottomContent()));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"disabled\": [\"styleChanged\"],\n        \"isExpanded\": [\"styleChanged\"],\n        \"placeholder\": [\"styleChanged\"],\n        \"value\": [\"styleChanged\"]\n    }; }\n};\nconst getOptionValue = (el) => {\n    const value = el.value;\n    return value === undefined ? el.textContent || '' : value;\n};\nconst parseValue = (value) => {\n    if (value == null) {\n        return undefined;\n    }\n    if (Array.isArray(value)) {\n        return value.join(',');\n    }\n    return value.toString();\n};\nconst generateText = (opts, value, compareWith) => {\n    if (value === undefined) {\n        return '';\n    }\n    if (Array.isArray(value)) {\n        return value\n            .map((v) => textForValue(opts, v, compareWith))\n            .filter((opt) => opt !== null)\n            .join(', ');\n    }\n    else {\n        return textForValue(opts, value, compareWith) || '';\n    }\n};\nconst textForValue = (opts, value, compareWith) => {\n    const selectOpt = opts.find((opt) => {\n        return compareOptions(value, getOptionValue(opt), compareWith);\n    });\n    return selectOpt ? selectOpt.textContent : null;\n};\nlet selectIds = 0;\nconst OPTION_CLASS = 'select-interface-option';\nSelect.style = {\n    ios: selectIosCss,\n    md: selectMdCss\n};\n\nconst selectOptionCss = \":host{display:none}\";\n\nconst SelectOption = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.inputId = `ion-selopt-${selectOptionIds++}`;\n        /**\n         * If `true`, the user cannot interact with the select option. This property does not apply when `interface=\"action-sheet\"` as `ion-action-sheet` does not allow for disabled buttons.\n         */\n        this.disabled = false;\n    }\n    render() {\n        return h(Host, { key: '3a70eea9fa03a9acba582180761d18347c72acee', role: \"option\", id: this.inputId, class: getIonMode(this) });\n    }\n    get el() { return getElement(this); }\n};\nlet selectOptionIds = 0;\nSelectOption.style = selectOptionCss;\n\nconst selectPopoverIosCss = \".sc-ion-select-popover-ios-h ion-list.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-ios,ion-label.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-ios-h{overflow-y:auto}\";\n\nconst selectPopoverMdCss = \".sc-ion-select-popover-md-h ion-list.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-md,ion-label.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-md-h{overflow-y:auto}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(container){display:none}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(label){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-item.sc-ion-select-popover-md{--inner-border-width:0}.item-radio-checked.sc-ion-select-popover-md{--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08);--background-focused:var(--ion-color-primary, #0054e9);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #0054e9);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-popover-md{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #0054e9)}\";\n\nconst SelectPopover = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        /**\n         * An array of options for the popover\n         */\n        this.options = [];\n    }\n    findOptionFromEvent(ev) {\n        const { options } = this;\n        return options.find((o) => o.value === ev.target.value);\n    }\n    /**\n     * When an option is selected we need to get the value(s)\n     * of the selected option(s) and return it in the option\n     * handler\n     */\n    callOptionHandler(ev) {\n        const option = this.findOptionFromEvent(ev);\n        const values = this.getValues(ev);\n        if (option === null || option === void 0 ? void 0 : option.handler) {\n            safeCall(option.handler, values);\n        }\n    }\n    /**\n     * Dismisses the host popover that the `ion-select-popover`\n     * is rendered within.\n     */\n    dismissParentPopover() {\n        const popover = this.el.closest('ion-popover');\n        if (popover) {\n            popover.dismiss();\n        }\n    }\n    setChecked(ev) {\n        const { multiple } = this;\n        const option = this.findOptionFromEvent(ev);\n        // this is a popover with checkboxes (multiple value select)\n        // we need to set the checked value for this option\n        if (multiple && option) {\n            option.checked = ev.detail.checked;\n        }\n    }\n    getValues(ev) {\n        const { multiple, options } = this;\n        if (multiple) {\n            // this is a popover with checkboxes (multiple value select)\n            // return an array of all the checked values\n            return options.filter((o) => o.checked).map((o) => o.value);\n        }\n        // this is a popover with radio buttons (single value select)\n        // return the value that was clicked, otherwise undefined\n        const option = this.findOptionFromEvent(ev);\n        return option ? option.value : undefined;\n    }\n    renderOptions(options) {\n        const { multiple } = this;\n        switch (multiple) {\n            case true:\n                return this.renderCheckboxOptions(options);\n            default:\n                return this.renderRadioOptions(options);\n        }\n    }\n    renderCheckboxOptions(options) {\n        return options.map((option) => (h(\"ion-item\", { class: Object.assign({\n                // TODO FW-4784\n                'item-checkbox-checked': option.checked\n            }, getClassMap(option.cssClass)) }, h(\"ion-checkbox\", { value: option.value, disabled: option.disabled, checked: option.checked, justify: \"start\", labelPlacement: \"end\", onIonChange: (ev) => {\n                this.setChecked(ev);\n                this.callOptionHandler(ev);\n                // TODO FW-4784\n                forceUpdate(this);\n            } }, option.text))));\n    }\n    renderRadioOptions(options) {\n        const checked = options.filter((o) => o.checked).map((o) => o.value)[0];\n        return (h(\"ion-radio-group\", { value: checked, onIonChange: (ev) => this.callOptionHandler(ev) }, options.map((option) => (h(\"ion-item\", { class: Object.assign({\n                // TODO FW-4784\n                'item-radio-checked': option.value === checked\n            }, getClassMap(option.cssClass)) }, h(\"ion-radio\", { value: option.value, disabled: option.disabled, onClick: () => this.dismissParentPopover(), onKeyUp: (ev) => {\n                if (ev.key === ' ') {\n                    /**\n                     * Selecting a radio option with keyboard navigation,\n                     * either through the Enter or Space keys, should\n                     * dismiss the popover.\n                     */\n                    this.dismissParentPopover();\n                }\n            } }, option.text))))));\n    }\n    render() {\n        const { header, message, options, subHeader } = this;\n        const hasSubHeaderOrMessage = subHeader !== undefined || message !== undefined;\n        return (h(Host, { key: 'ab931b49b59283825bd2afa3f7f995b0e6e05bef', class: getIonMode(this) }, h(\"ion-list\", { key: '3bd12b67832607596b912a73d5b3ae9b954b244d' }, header !== undefined && h(\"ion-list-header\", { key: '97da930246edf7423a039c030d40e3ff7a5148a3' }, header), hasSubHeaderOrMessage && (h(\"ion-item\", { key: 'c579df6ea8fac07bb0c59d34c69b149656863224' }, h(\"ion-label\", { key: 'af699c5f465710ccb13b8cf8e7be66f0e8acfad1', class: \"ion-text-wrap\" }, subHeader !== undefined && h(\"h3\", { key: 'df9a936d42064b134e843c7229f314a2a3ec7e80' }, subHeader), message !== undefined && h(\"p\", { key: '9c3ddad378df00f106afa94e9928cf68c17124dd' }, message)))), this.renderOptions(options))));\n    }\n    get el() { return getElement(this); }\n};\nSelectPopover.style = {\n    ios: selectPopoverIosCss,\n    md: selectPopoverMdCss\n};\n\nexport { Select as ion_select, SelectOption as ion_select_option, SelectPopover as ion_select_popover };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBA,IAAM,eAAe;AAErB,IAAM,cAAc;AAEpB,IAAM,SAAS,MAAM;AAAA,EACjB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,SAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,SAAK,aAAa,YAAY,MAAM,cAAc,CAAC;AACnD,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,UAAU,WAAW,WAAW;AACrC,SAAK,eAAe,GAAG,KAAK,OAAO;AACnC,SAAK,cAAc,GAAG,KAAK,OAAO;AAClC,SAAK,sBAAsB,CAAC;AAC5B,SAAK,aAAa;AASlB,SAAK,WAAW;AAIhB,SAAK,aAAa;AAIlB,SAAK,WAAW;AAIhB,SAAK,YAAY;AAUjB,SAAK,mBAAmB,CAAC;AAUzB,SAAK,iBAAiB;AAItB,SAAK,WAAW;AAIhB,SAAK,OAAO,KAAK;AAIjB,SAAK,SAAS;AAMd,SAAK,WAAW;AAChB,SAAK,UAAU,CAAC,OAAO;AACnB,YAAM,SAAS,GAAG;AAClB,YAAM,cAAc,OAAO,QAAQ,8BAA8B;AACjE,UAAI,WAAW,KAAK,MAAM,gBAAgB,MAAM;AAC5C,aAAK,SAAS;AACd,aAAK,KAAK,EAAE;AAAA,MAChB,OACK;AA2BD,WAAG,eAAe;AAAA,MACtB;AAAA,IACJ;AACA,SAAK,UAAU,MAAM;AACjB,WAAK,WAAW;AAChB,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,SAAK,SAAS,MAAM;AAChB,WAAK,WAAW;AAChB,WAAK,QAAQ,KAAK;AAAA,IACtB;AAKA,SAAK,eAAe,CAAC,OAAO;AAGxB,UAAI,GAAG,WAAW,GAAG,eAAe;AAChC,WAAG,gBAAgB;AAAA,MACvB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,eAAe;AACX,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,SAAS,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,UAAU,KAAK,EAAE,MAAM,CAAC;AAAA,EACjC;AAAA,EACM,oBAAoB;AAAA;AACtB,YAAM,EAAE,GAAG,IAAI;AACf,WAAK,kBAAkB,sBAAsB,IAAI,MAAM,KAAK,eAAe,MAAM,KAAK,SAAS;AAC/F,WAAK,qBAAqB;AAC1B,WAAK,UAAU;AACf,WAAK,YAAY,gBAAgB,KAAK,IAAI,qBAAqB,MAAY;AACvE,aAAK,qBAAqB;AAO1B,oBAAY,IAAI;AAAA,MACpB,EAAC;AAAA,IACL;AAAA;AAAA,EACA,oBAAoB;AAChB,SAAK,sBAAsB,kBAAkB,KAAK,IAAI,CAAC,YAAY,CAAC;AAAA,EACxE;AAAA,EACA,mBAAmB;AAWf,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,uBAAuB;AACnB,QAAI,KAAK,WAAW;AAChB,WAAK,UAAU,WAAW;AAC1B,WAAK,YAAY;AAAA,IACrB;AACA,QAAI,KAAK,iBAAiB;AACtB,WAAK,gBAAgB,QAAQ;AAC7B,WAAK,kBAAkB;AAAA,IAC3B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOM,KAAK,OAAO;AAAA;AACd,UAAI,KAAK,YAAY,KAAK,YAAY;AAClC,eAAO;AAAA,MACX;AACA,WAAK,aAAa;AAClB,YAAM,UAAW,KAAK,UAAU,MAAM,KAAK,cAAc,KAAK;AAE9D,YAAM,yBAAyB,MAAM;AACjC,cAAM,kBAAkB,KAAK,UAAU,UAAU,CAAC,MAAM,EAAE,UAAU,KAAK,KAAK;AAC9E,YAAI,kBAAkB,IAAI;AACtB,gBAAM,eAAe,QAAQ,cAAc,wCAAwC,kBAAkB,CAAC,GAAG;AACzG,cAAI,cAAc;AAYd,kBAAM,gBAAgB,aAAa,cAAc,yBAAyB;AAC1E,gBAAI,eAAe;AACf,2BAAa,eAAe,EAAE,OAAO,UAAU,CAAC;AAGhD,4BAAc,SAAS;AAAA,YAC3B;AACA,gCAAoB,YAAY;AAAA,UACpC;AAAA,QACJ,OACK;AAID,gBAAM,qBAAqB,QAAQ,cAAc,sEAAsE;AACvH,cAAI,oBAAoB;AAOpB,+BAAmB,SAAS;AAC5B,gCAAoB,mBAAmB,QAAQ,UAAU,CAAC;AAAA,UAC9D;AAAA,QACJ;AAAA,MACJ;AAEA,UAAI,KAAK,cAAc,SAAS;AAC5B,gBAAQ,iBAAiB,uBAAuB,wBAAwB,EAAE,MAAM,KAAK,CAAC;AAAA,MAC1F,WACS,KAAK,cAAc,WAAW;AACnC,gBAAQ,iBAAiB,yBAAyB,wBAAwB,EAAE,MAAM,KAAK,CAAC;AAAA,MAC5F,OACK;AAOD,cAAM,oBAAoB,MAAM;AAC5B,gCAAsB,MAAM;AACxB,mCAAuB;AAAA,UAC3B,CAAC;AAAA,QACL;AACA,YAAI,KAAK,cAAc,SAAS;AAC5B,kBAAQ,iBAAiB,uBAAuB,mBAAmB,EAAE,MAAM,KAAK,CAAC;AAAA,QACrF,WACS,KAAK,cAAc,gBAAgB;AACxC,kBAAQ,iBAAiB,6BAA6B,mBAAmB,EAAE,MAAM,KAAK,CAAC;AAAA,QAC3F;AAAA,MACJ;AACA,cAAQ,aAAa,EAAE,KAAK,MAAM;AAC9B,aAAK,UAAU;AACf,aAAK,aAAa;AAClB,aAAK,WAAW,KAAK;AACrB,aAAK,SAAS;AAAA,MAClB,CAAC;AACD,YAAM,QAAQ,QAAQ;AACtB,aAAO;AAAA,IACX;AAAA;AAAA,EACA,cAAc,IAAI;AACd,QAAI,kBAAkB,KAAK;AAC3B,QAAI,oBAAoB,kBAAkB,KAAK,UAAU;AACrD,sBAAgB,uCAAuC,eAAe,mEAAmE;AACzI,wBAAkB;AAAA,IACtB;AACA,QAAI,oBAAoB,aAAa,CAAC,IAAI;AACtC,sBAAgB,yCAAyC,eAAe,kEAAkE;AAC1I,wBAAkB;AAAA,IACtB;AACA,QAAI,oBAAoB,gBAAgB;AACpC,aAAO,KAAK,gBAAgB;AAAA,IAChC;AACA,QAAI,oBAAoB,WAAW;AAC/B,aAAO,KAAK,YAAY,EAAE;AAAA,IAC9B;AACA,QAAI,oBAAoB,SAAS;AAC7B,aAAO,KAAK,UAAU;AAAA,IAC1B;AACA,WAAO,KAAK,UAAU;AAAA,EAC1B;AAAA,EACA,uBAAuB;AACnB,UAAM,UAAU,KAAK;AACrB,QAAI,CAAC,SAAS;AACV;AAAA,IACJ;AACA,UAAM,YAAY,KAAK;AACvB,UAAM,QAAQ,KAAK;AACnB,YAAQ,KAAK,WAAW;AAAA,MACpB,KAAK;AACD,gBAAQ,UAAU,KAAK,yBAAyB,WAAW,KAAK;AAChE;AAAA,MACJ,KAAK;AACD,cAAM,UAAU,QAAQ,cAAc,oBAAoB;AAC1D,YAAI,SAAS;AACT,kBAAQ,UAAU,KAAK,2BAA2B,WAAW,KAAK;AAAA,QACtE;AACA;AAAA,MACJ,KAAK;AACD,cAAM,QAAQ,QAAQ,cAAc,kBAAkB;AACtD,YAAI,OAAO;AACP,gBAAM,UAAU,KAAK,2BAA2B,WAAW,KAAK;AAAA,QACpE;AACA;AAAA,MACJ,KAAK;AACD,cAAM,YAAY,KAAK,WAAW,aAAa;AAC/C,gBAAQ,SAAS,KAAK,kBAAkB,WAAW,WAAW,KAAK;AACnE;AAAA,IACR;AAAA,EACJ;AAAA,EACA,yBAAyB,MAAM,aAAa;AACxC,UAAM,qBAAqB,KAAK,IAAI,CAAC,WAAW;AAC5C,YAAM,QAAQ,eAAe,MAAM;AAEnC,YAAM,cAAc,MAAM,KAAK,OAAO,SAAS,EAC1C,OAAO,CAAC,QAAQ,QAAQ,UAAU,EAClC,KAAK,GAAG;AACb,YAAM,WAAW,GAAG,YAAY,IAAI,WAAW;AAC/C,aAAO;AAAA,QACH,MAAM,iBAAiB,aAAa,OAAO,KAAK,WAAW,IAAI,aAAa;AAAA,QAC5E,MAAM,OAAO;AAAA,QACb,UAAU;AAAA,QACV,SAAS,MAAM;AACX,eAAK,SAAS,KAAK;AAAA,QACvB;AAAA,MACJ;AAAA,IACJ,CAAC;AAED,uBAAmB,KAAK;AAAA,MACpB,MAAM,KAAK;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAM;AACX,aAAK,UAAU,KAAK;AAAA,MACxB;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AAAA,EACA,kBAAkB,MAAM,WAAW,aAAa;AAC5C,UAAM,cAAc,KAAK,IAAI,CAAC,WAAW;AACrC,YAAM,QAAQ,eAAe,MAAM;AAEnC,YAAM,cAAc,MAAM,KAAK,OAAO,SAAS,EAC1C,OAAO,CAAC,QAAQ,QAAQ,UAAU,EAClC,KAAK,GAAG;AACb,YAAM,WAAW,GAAG,YAAY,IAAI,WAAW;AAC/C,aAAO;AAAA,QACH,MAAM;AAAA,QACN,UAAU;AAAA,QACV,OAAO,OAAO,eAAe;AAAA,QAC7B;AAAA,QACA,SAAS,iBAAiB,aAAa,OAAO,KAAK,WAAW;AAAA,QAC9D,UAAU,OAAO;AAAA,MACrB;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AAAA,EACA,2BAA2B,MAAM,aAAa;AAC1C,UAAM,iBAAiB,KAAK,IAAI,CAAC,WAAW;AACxC,YAAM,QAAQ,eAAe,MAAM;AAEnC,YAAM,cAAc,MAAM,KAAK,OAAO,SAAS,EAC1C,OAAO,CAAC,QAAQ,QAAQ,UAAU,EAClC,KAAK,GAAG;AACb,YAAM,WAAW,GAAG,YAAY,IAAI,WAAW;AAC/C,aAAO;AAAA,QACH,MAAM,OAAO,eAAe;AAAA,QAC5B,UAAU;AAAA,QACV;AAAA,QACA,SAAS,iBAAiB,aAAa,OAAO,KAAK,WAAW;AAAA,QAC9D,UAAU,OAAO;AAAA,QACjB,SAAS,CAAC,aAAa;AACnB,eAAK,SAAS,QAAQ;AACtB,cAAI,CAAC,KAAK,UAAU;AAChB,iBAAK,MAAM;AAAA,UACf;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AAAA,EACM,YAAY,IAAI;AAAA;AAClB,YAAM,EAAE,MAAM,eAAe,IAAI;AACjC,YAAM,mBAAmB,KAAK;AAC9B,YAAM,OAAO,WAAW,IAAI;AAC5B,YAAM,eAAe,SAAS,OAAO,QAAQ;AAC7C,YAAM,WAAW,KAAK;AACtB,YAAM,QAAQ,KAAK;AACnB,UAAI,QAAQ;AACZ,UAAI,OAAO;AACX,YAAM,4BAA4B,mBAAmB,cAAc,mBAAmB;AAMtF,UAAI,6BAA8B,SAAS,QAAQ,SAAS,QAAY;AACpE,eAAO;AAAA,MAMX,OACK;AACD,gBAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,QAAQ;AAAA,UAC/C,iBAAiB,KAAK;AAAA,QAC1B,EAAE,CAAC;AAAA,MACX;AACA,YAAM,cAAc,OAAO,OAAO,OAAO,OAAO;AAAA,QAAE;AAAA,QAC9C;AAAA,QAAO,WAAW;AAAA,QAAU;AAAA,QAC5B;AAAA,MAAa,GAAG,gBAAgB,GAAG,EAAE,WAAW,sBAAsB,UAAU,CAAC,kBAAkB,iBAAiB,QAAQ,GAAG,gBAAgB;AAAA,QAC3I,QAAQ,iBAAiB;AAAA,QACzB,WAAW,iBAAiB;AAAA,QAC5B,SAAS,iBAAiB;AAAA,QAC1B;AAAA,QACA;AAAA,QACA,SAAS,KAAK,2BAA2B,KAAK,WAAW,KAAK;AAAA,MAClE,EAAE,CAAC;AACP,aAAO,kBAAkB,OAAO,WAAW;AAAA,IAC/C;AAAA;AAAA,EACM,kBAAkB;AAAA;AACpB,YAAM,OAAO,WAAW,IAAI;AAC5B,YAAM,mBAAmB,KAAK;AAC9B,YAAM,kBAAkB,OAAO,OAAO,OAAO,OAAO,EAAE,KAAK,GAAG,gBAAgB,GAAG,EAAE,SAAS,KAAK,yBAAyB,KAAK,WAAW,KAAK,KAAK,GAAG,UAAU,CAAC,uBAAuB,iBAAiB,QAAQ,EAAE,CAAC;AACrN,aAAO,sBAAsB,OAAO,eAAe;AAAA,IACvD;AAAA;AAAA,EACM,YAAY;AAAA;AACd,YAAM,mBAAmB,KAAK;AAC9B,YAAM,YAAY,KAAK,WAAW,aAAa;AAC/C,YAAM,OAAO,WAAW,IAAI;AAC5B,YAAM,YAAY,OAAO,OAAO,OAAO,OAAO,EAAE,KAAK,GAAG,gBAAgB,GAAG,EAAE,QAAQ,iBAAiB,SAAS,iBAAiB,SAAS,KAAK,WAAW,QAAQ,KAAK,kBAAkB,KAAK,WAAW,WAAW,KAAK,KAAK,GAAG,SAAS;AAAA,QACjO;AAAA,UACI,MAAM,KAAK;AAAA,UACX,MAAM;AAAA,UACN,SAAS,MAAM;AACX,iBAAK,UAAU,KAAK;AAAA,UACxB;AAAA,QACJ;AAAA,QACA;AAAA,UACI,MAAM,KAAK;AAAA,UACX,SAAS,CAAC,mBAAmB;AACzB,iBAAK,SAAS,cAAc;AAAA,UAChC;AAAA,QACJ;AAAA,MACJ,GAAG,UAAU;AAAA,QACT;AAAA,QACA,iBAAiB;AAAA,QACjB,KAAK,WAAW,0BAA0B;AAAA,MAC9C,EAAE,CAAC;AACP,aAAO,gBAAgB,OAAO,SAAS;AAAA,IAC3C;AAAA;AAAA,EACA,YAAY;AACR,UAAM,EAAE,UAAU,OAAO,iBAAiB,IAAI;AAC9C,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,YAAY,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,gBAAgB,GAAG,EAAE,MAAM,UAAU,CAAC,gBAAgB,iBAAiB,QAAQ,GAAG,WAAW,oBAAoB,gBAAgB;AAAA,MAC3K,QAAQ,iBAAiB;AAAA,MACzB;AAAA,MACA;AAAA,MACA,SAAS,KAAK,2BAA2B,KAAK,WAAW,KAAK;AAAA,IAClE,EAAE,CAAC;AACP,WAAO,gBAAgB,OAAO,SAAS;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACJ,QAAI,CAAC,KAAK,SAAS;AACf,aAAO,QAAQ,QAAQ,KAAK;AAAA,IAChC;AACA,WAAO,KAAK,QAAQ,QAAQ;AAAA,EAChC;AAAA,EACA,WAAW;AACP,WAAO,KAAK,QAAQ,MAAM;AAAA,EAC9B;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,MAAM,KAAK,KAAK,GAAG,iBAAiB,mBAAmB,CAAC;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,YAAY;AACZ,UAAM,EAAE,MAAM,IAAI;AAClB,QAAI,UAAU,QAAW;AACrB,aAAO;AAAA,IACX;AACA,UAAM,EAAE,UAAU,IAAI;AACtB,QAAI,cAAc,MAAM;AACpB,aAAO,UAAU;AAAA,IACrB;AACA;AAAA,EACJ;AAAA,EACA,UAAU;AACN,UAAM,eAAe,KAAK;AAC1B,QAAI,gBAAgB,QAAQ,iBAAiB,IAAI;AAC7C,aAAO;AAAA,IACX;AACA,WAAO,aAAa,KAAK,WAAW,KAAK,OAAO,KAAK,WAAW;AAAA,EACpE;AAAA,EACA,WAAW;AACP,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,MAAM;AAAA,IACvB;AAAA,EACJ;AAAA,EACA,YAAY;AACR,UAAM,EAAE,SAAS,IAAI;AACrB,UAAM,QAAQ;AAAA,MACV,wBAAwB;AAAA,IAC5B;AACA,SAAK,SAAS,KAAK,KAAK;AAAA,EAC5B;AAAA,EACA,cAAc;AACV,UAAM,EAAE,MAAM,IAAI;AAClB,WAAQ,EAAE,OAAO,EAAE,OAAO;AAAA,MAClB,sBAAsB;AAAA,MACtB,6BAA6B,CAAC,KAAK;AAAA,IACvC,GAAG,MAAM,QAAQ,GAAG,UAAU,SAAY,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,aAAa,GAAG,KAAK,CAAC;AAAA,EACzH;AAAA,EACA,qBAAqB;AACjB,QAAI;AACJ,KAAC,KAAK,KAAK,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,oBAAoB;AAAA,EAC5F;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACZ,WAAO,KAAK,GAAG,cAAc,gBAAgB;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,WAAW;AACX,WAAO,KAAK,UAAU,UAAa,KAAK,cAAc;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACnB,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,iBAAiB,SAAS,QAAQ,KAAK,SAAS;AACtD,QAAI,gBAAgB;AAQhB,aAAO;AAAA,QACH,EAAE,OAAO,EAAE,OAAO,2BAA2B,GAAG,EAAE,OAAO,EAAE,OAAO,uBAAuB,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO;AAAA,UACvG,wBAAwB;AAAA,UACxB,+BAA+B,CAAC,KAAK;AAAA,QACzC,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,gBAAgB,eAAe,QAAQ,KAAK,CAAC,OAAQ,KAAK,gBAAgB,GAAI,GAAG,KAAK,KAAK,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,qBAAqB,CAAC,CAAC;AAAA,QACnK,KAAK,YAAY;AAAA,MACrB;AAAA,IACJ;AAKA,WAAO,KAAK,YAAY;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB;AACf,UAAM,EAAE,YAAY,IAAI;AACxB,UAAM,eAAe,KAAK,QAAQ;AAClC,QAAI,sBAAsB;AAC1B,QAAI,aAAa;AACjB,QAAI,eAAe,MAAM,gBAAgB,QAAW;AAChD,mBAAa;AACb,4BAAsB;AAAA,IAC1B;AACA,UAAM,oBAAoB;AAAA,MACtB,eAAe;AAAA,MACf,sBAAsB;AAAA,IAC1B;AACA,UAAM,WAAW,sBAAsB,gBAAgB;AACvD,WAAQ,EAAE,OAAO,EAAE,eAAe,QAAQ,OAAO,mBAAmB,MAAM,SAAS,GAAG,UAAU;AAAA,EACpG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACf,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,EAAE,YAAY,YAAY,aAAa,IAAI;AACjD,QAAI;AACJ,QAAI,cAAc,iBAAiB,QAAW;AAC1C,aAAO;AAAA,IACX,OACK;AACD,YAAM,cAAc,SAAS,QAAQ,gBAAgB;AACrD,aAAO,eAAe,QAAQ,eAAe,SAAS,aAAa;AAAA,IACvE;AACA,WAAO,EAAE,YAAY,EAAE,OAAO,eAAe,MAAM,QAAQ,eAAe,QAAQ,KAAW,CAAC;AAAA,EAClG;AAAA,EACA,IAAI,YAAY;AACZ,QAAI;AACJ,UAAM,EAAE,aAAa,oBAAoB,IAAI;AAC7C,UAAM,eAAe,KAAK,QAAQ;AAElC,UAAM,gBAAgB,KAAK,oBAAoB,YAAY,OAAO,QAAQ,OAAO,SAAS,KAAK,KAAK;AAMpG,QAAI,gBAAgB;AACpB,QAAI,kBAAkB,MAAM,gBAAgB,QAAW;AACnD,sBAAgB;AAAA,IACpB;AAQA,QAAI,iBAAiB,QAAW;AAC5B,sBAAgB,kBAAkB,KAAK,eAAe,GAAG,YAAY,KAAK,aAAa;AAAA,IAC3F;AACA,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB;AACZ,UAAM,EAAE,UAAU,SAAS,YAAY,SAAS,IAAI;AACpD,WAAQ,EAAE,UAAU,EAAE,UAAoB,IAAI,SAAS,cAAc,KAAK,WAAW,iBAAiB,UAAU,iBAAiB,GAAG,UAAU,IAAI,oBAAoB,KAAK,cAAc,GAAG,gBAAgB,KAAK,cAAc,MAAM,KAAK,aAAa,iBAAiB,GAAG,QAAQ,IAAI,SAAS,KAAK,SAAS,QAAQ,KAAK,QAAQ,KAAK,CAAC,YAAa,KAAK,UAAU,QAAS,CAAC;AAAA,EACnX;AAAA,EACA,gBAAgB;AACZ,UAAM,EAAE,IAAI,YAAY,WAAW,cAAc,YAAY,IAAI;AACjE,QAAI,GAAG,UAAU,SAAS,aAAa,KAAK,GAAG,UAAU,SAAS,aAAa,KAAK,WAAW;AAC3F,aAAO;AAAA,IACX;AACA,QAAI,YAAY;AACZ,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACb,UAAM,EAAE,YAAY,WAAW,cAAc,YAAY,IAAI;AAC7D,WAAO;AAAA,MACH,EAAE,OAAO,EAAE,IAAI,cAAc,OAAO,eAAe,MAAM,8BAA8B,GAAG,UAAU;AAAA,MACpG,EAAE,OAAO,EAAE,IAAI,aAAa,OAAO,cAAc,MAAM,6BAA6B,GAAG,SAAS;AAAA,IACpG;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB;AAClB,UAAM,EAAE,YAAY,UAAU,IAAI;AAKlC,UAAM,cAAc,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,QAAI,CAAC,aAAa;AACd;AAAA,IACJ;AACA,WAAO,EAAE,OAAO,EAAE,OAAO,gBAAgB,GAAG,KAAK,eAAe,CAAC;AAAA,EACrE;AAAA,EACA,SAAS;AACL,UAAM,EAAE,UAAU,IAAI,YAAY,cAAc,gBAAgB,SAAS,aAAa,MAAM,OAAO,MAAM,OAAO,SAAU,IAAI;AAC9H,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,4BAA4B,mBAAmB,cAAc,mBAAmB;AACtF,UAAM,iBAAiB,CAAC,6BAA6B,YAAY;AACjE,UAAM,MAAM,MAAM,EAAE,IAAI,QAAQ;AAChC,UAAM,SAAS,YAAY,YAAY,KAAK,EAAE;AAC9C,UAAM,wBAAwB,SAAS,QAAQ,SAAS,aAAa,CAAC;AACtE,UAAM,WAAW,KAAK,SAAS;AAC/B,UAAM,mBAAmB,GAAG,cAAc,8BAA8B,MAAM;AAC9E,sBAAkB,MAAM,IAAI,MAAM,WAAW,KAAK,GAAG,QAAQ;AAkB7D,UAAM,mBAAmB,mBAAmB,aAAc,mBAAmB,eAAe,YAAY,cAAc;AACtH,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,SAAS,KAAK,SAAS,OAAO,mBAAmB,KAAK,OAAO;AAAA,MACxH,CAAC,IAAI,GAAG;AAAA,MACR,WAAW;AAAA,MACX,iBAAiB,YAAY,sBAAsB,EAAE;AAAA,MACrD,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,MACnB,qBAAqB,iBAAiB;AAAA,MACtC,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,mBAAmB,gBAAgB;AAAA,MACnC,aAAa;AAAA;AAAA,MAEb,iBAAiB;AAAA,MACjB,CAAC,UAAU,GAAG,EAAE,GAAG;AAAA,MACnB,CAAC,eAAe,IAAI,EAAE,GAAG,SAAS;AAAA,MAClC,CAAC,kBAAkB,OAAO,EAAE,GAAG;AAAA,MAC/B,CAAC,gBAAgB,KAAK,EAAE,GAAG,UAAU;AAAA,MACrC,CAAC,0BAA0B,cAAc,EAAE,GAAG;AAAA,IAClD,CAAC,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,4CAA4C,OAAO,kBAAkB,IAAI,gBAAgB,SAAS,KAAK,aAAa,GAAG,KAAK,qBAAqB,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,uBAAuB,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,MAAM,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,kBAAkB,KAAK,CAACA,QAAQ,KAAK,kBAAkBA,KAAK,MAAM,YAAY,GAAG,KAAK,iBAAiB,GAAG,KAAK,cAAc,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,MAAM,MAAM,CAAC,GAAG,CAAC,6BAA6B,KAAK,iBAAiB,CAAC,GAAG,6BAA6B,KAAK,iBAAiB,GAAG,yBAAyB,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,mBAAmB,CAAC,CAAC,GAAG,KAAK,oBAAoB,CAAC;AAAA,EAC72B;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,YAAY,CAAC,cAAc;AAAA,MAC3B,cAAc,CAAC,cAAc;AAAA,MAC7B,eAAe,CAAC,cAAc;AAAA,MAC9B,SAAS,CAAC,cAAc;AAAA,IAC5B;AAAA,EAAG;AACP;AACA,IAAM,iBAAiB,CAAC,OAAO;AAC3B,QAAM,QAAQ,GAAG;AACjB,SAAO,UAAU,SAAY,GAAG,eAAe,KAAK;AACxD;AACA,IAAM,aAAa,CAAC,UAAU;AAC1B,MAAI,SAAS,MAAM;AACf,WAAO;AAAA,EACX;AACA,MAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,WAAO,MAAM,KAAK,GAAG;AAAA,EACzB;AACA,SAAO,MAAM,SAAS;AAC1B;AACA,IAAM,eAAe,CAAC,MAAM,OAAO,gBAAgB;AAC/C,MAAI,UAAU,QAAW;AACrB,WAAO;AAAA,EACX;AACA,MAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,WAAO,MACF,IAAI,CAAC,MAAM,aAAa,MAAM,GAAG,WAAW,CAAC,EAC7C,OAAO,CAAC,QAAQ,QAAQ,IAAI,EAC5B,KAAK,IAAI;AAAA,EAClB,OACK;AACD,WAAO,aAAa,MAAM,OAAO,WAAW,KAAK;AAAA,EACrD;AACJ;AACA,IAAM,eAAe,CAAC,MAAM,OAAO,gBAAgB;AAC/C,QAAM,YAAY,KAAK,KAAK,CAAC,QAAQ;AACjC,WAAO,eAAe,OAAO,eAAe,GAAG,GAAG,WAAW;AAAA,EACjE,CAAC;AACD,SAAO,YAAY,UAAU,cAAc;AAC/C;AACA,IAAI,YAAY;AAChB,IAAM,eAAe;AACrB,OAAO,QAAQ;AAAA,EACX,KAAK;AAAA,EACL,IAAI;AACR;AAEA,IAAM,kBAAkB;AAExB,IAAM,eAAe,MAAM;AAAA,EACvB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,UAAU,cAAc,iBAAiB;AAI9C,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,SAAS;AACL,WAAO,EAAE,MAAM,EAAE,KAAK,4CAA4C,MAAM,UAAU,IAAI,KAAK,SAAS,OAAO,WAAW,IAAI,EAAE,CAAC;AAAA,EACjI;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AACxC;AACA,IAAI,kBAAkB;AACtB,aAAa,QAAQ;AAErB,IAAM,sBAAsB;AAE5B,IAAM,qBAAqB;AAE3B,IAAM,gBAAgB,MAAM;AAAA,EACxB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAI9B,SAAK,UAAU,CAAC;AAAA,EACpB;AAAA,EACA,oBAAoB,IAAI;AACpB,UAAM,EAAE,QAAQ,IAAI;AACpB,WAAO,QAAQ,KAAK,CAAC,MAAM,EAAE,UAAU,GAAG,OAAO,KAAK;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,IAAI;AAClB,UAAM,SAAS,KAAK,oBAAoB,EAAE;AAC1C,UAAM,SAAS,KAAK,UAAU,EAAE;AAChC,QAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS;AAChE,eAAS,OAAO,SAAS,MAAM;AAAA,IACnC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACnB,UAAM,UAAU,KAAK,GAAG,QAAQ,aAAa;AAC7C,QAAI,SAAS;AACT,cAAQ,QAAQ;AAAA,IACpB;AAAA,EACJ;AAAA,EACA,WAAW,IAAI;AACX,UAAM,EAAE,SAAS,IAAI;AACrB,UAAM,SAAS,KAAK,oBAAoB,EAAE;AAG1C,QAAI,YAAY,QAAQ;AACpB,aAAO,UAAU,GAAG,OAAO;AAAA,IAC/B;AAAA,EACJ;AAAA,EACA,UAAU,IAAI;AACV,UAAM,EAAE,UAAU,QAAQ,IAAI;AAC9B,QAAI,UAAU;AAGV,aAAO,QAAQ,OAAO,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK;AAAA,IAC9D;AAGA,UAAM,SAAS,KAAK,oBAAoB,EAAE;AAC1C,WAAO,SAAS,OAAO,QAAQ;AAAA,EACnC;AAAA,EACA,cAAc,SAAS;AACnB,UAAM,EAAE,SAAS,IAAI;AACrB,YAAQ,UAAU;AAAA,MACd,KAAK;AACD,eAAO,KAAK,sBAAsB,OAAO;AAAA,MAC7C;AACI,eAAO,KAAK,mBAAmB,OAAO;AAAA,IAC9C;AAAA,EACJ;AAAA,EACA,sBAAsB,SAAS;AAC3B,WAAO,QAAQ,IAAI,CAAC,WAAY,EAAE,YAAY,EAAE,OAAO,OAAO,OAAO;AAAA;AAAA,MAE7D,yBAAyB,OAAO;AAAA,IACpC,GAAG,YAAY,OAAO,QAAQ,CAAC,EAAE,GAAG,EAAE,gBAAgB,EAAE,OAAO,OAAO,OAAO,UAAU,OAAO,UAAU,SAAS,OAAO,SAAS,SAAS,SAAS,gBAAgB,OAAO,aAAa,CAAC,OAAO;AAC3L,WAAK,WAAW,EAAE;AAClB,WAAK,kBAAkB,EAAE;AAEzB,kBAAY,IAAI;AAAA,IACpB,EAAE,GAAG,OAAO,IAAI,CAAC,CAAE;AAAA,EAC3B;AAAA,EACA,mBAAmB,SAAS;AACxB,UAAM,UAAU,QAAQ,OAAO,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC;AACtE,WAAQ,EAAE,mBAAmB,EAAE,OAAO,SAAS,aAAa,CAAC,OAAO,KAAK,kBAAkB,EAAE,EAAE,GAAG,QAAQ,IAAI,CAAC,WAAY,EAAE,YAAY,EAAE,OAAO,OAAO,OAAO;AAAA;AAAA,MAExJ,sBAAsB,OAAO,UAAU;AAAA,IAC3C,GAAG,YAAY,OAAO,QAAQ,CAAC,EAAE,GAAG,EAAE,aAAa,EAAE,OAAO,OAAO,OAAO,UAAU,OAAO,UAAU,SAAS,MAAM,KAAK,qBAAqB,GAAG,SAAS,CAAC,OAAO;AAC9J,UAAI,GAAG,QAAQ,KAAK;AAMhB,aAAK,qBAAqB;AAAA,MAC9B;AAAA,IACJ,EAAE,GAAG,OAAO,IAAI,CAAC,CAAE,CAAC;AAAA,EAC5B;AAAA,EACA,SAAS;AACL,UAAM,EAAE,QAAQ,SAAS,SAAS,UAAU,IAAI;AAChD,UAAM,wBAAwB,cAAc,UAAa,YAAY;AACrE,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,OAAO,WAAW,IAAI,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,2CAA2C,GAAG,WAAW,UAAa,EAAE,mBAAmB,EAAE,KAAK,2CAA2C,GAAG,MAAM,GAAG,yBAA0B,EAAE,YAAY,EAAE,KAAK,2CAA2C,GAAG,EAAE,aAAa,EAAE,KAAK,4CAA4C,OAAO,gBAAgB,GAAG,cAAc,UAAa,EAAE,MAAM,EAAE,KAAK,2CAA2C,GAAG,SAAS,GAAG,YAAY,UAAa,EAAE,KAAK,EAAE,KAAK,2CAA2C,GAAG,OAAO,CAAC,CAAC,GAAI,KAAK,cAAc,OAAO,CAAC,CAAC;AAAA,EAC3qB;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AACxC;AACA,cAAc,QAAQ;AAAA,EAClB,KAAK;AAAA,EACL,IAAI;AACR;", "names": ["el"]}