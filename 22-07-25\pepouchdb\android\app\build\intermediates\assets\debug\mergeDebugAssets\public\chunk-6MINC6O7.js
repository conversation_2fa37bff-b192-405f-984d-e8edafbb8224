import{a as C,c as L}from"./chunk-JIT2VFLB.js";import{h as f,i as u,k as b,l as y,m as c,p as k,s as x,t as v}from"./chunk-PK2JKDOR.js";import{a as g}from"./chunk-3OLZKZOW.js";import"./chunk-B7F22SOJ.js";import{c as w}from"./chunk-BMJVVMK5.js";import{a as o}from"./chunk-753GXAUT.js";import{i as h}from"./chunk-XFXTD7QR.js";import"./chunk-WI5MSH4N.js";import"./chunk-JCOV4R6X.js";import"./chunk-CKP3SGE2.js";import{a as d,f as p,i as A,m as a,n as D,o as T,p as s}from"./chunk-EHNA26RN.js";import{g as l}from"./chunk-2R6CW7ES.js";var E=n=>{let i=o(),e=o(),t=o();return e.addElement(n.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),t.addElement(n.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.01,transform:"scale(1.1)"},{offset:1,opacity:1,transform:"scale(1)"}]),i.addElement(n).easing("ease-in-out").duration(200).addAnimation([e,t])},O=n=>{let i=o(),e=o(),t=o();return e.addElement(n.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),t.addElement(n.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.99,transform:"scale(1)"},{offset:1,opacity:0,transform:"scale(0.9)"}]),i.addElement(n).easing("ease-in-out").duration(200).addAnimation([e,t])},z=n=>{let i=o(),e=o(),t=o();return e.addElement(n.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),t.addElement(n.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.01,transform:"scale(1.1)"},{offset:1,opacity:1,transform:"scale(1)"}]),i.addElement(n).easing("ease-in-out").duration(200).addAnimation([e,t])},M=n=>{let i=o(),e=o(),t=o();return e.addElement(n.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),t.addElement(n.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.99,transform:"scale(1)"},{offset:1,opacity:0,transform:"scale(0.9)"}]),i.addElement(n).easing("ease-in-out").duration(200).addAnimation([e,t])},B=".sc-ion-loading-ios-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-ios-h{display:none}.loading-wrapper.sc-ion-loading-ios{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-ios{color:var(--spinner-color)}.sc-ion-loading-ios-h{--background:var(--ion-overlay-background-color, var(--ion-color-step-100, var(--ion-background-color-step-100, #f9f9f9)));--max-width:270px;--max-height:90%;--spinner-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));--backdrop-opacity:var(--ion-backdrop-opacity, 0.3);color:var(--ion-text-color, #000);font-size:0.875rem}.loading-wrapper.sc-ion-loading-ios{border-radius:8px;-webkit-padding-start:34px;padding-inline-start:34px;-webkit-padding-end:34px;padding-inline-end:34px;padding-top:24px;padding-bottom:24px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.loading-translucent.sc-ion-loading-ios-h .loading-wrapper.sc-ion-loading-ios{background-color:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}.loading-content.sc-ion-loading-ios{font-weight:bold}.loading-spinner.sc-ion-loading-ios+.loading-content.sc-ion-loading-ios{-webkit-margin-start:16px;margin-inline-start:16px}",I=".sc-ion-loading-md-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-md-h{display:none}.loading-wrapper.sc-ion-loading-md{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-md{color:var(--spinner-color)}.sc-ion-loading-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--max-width:280px;--max-height:90%;--spinner-color:var(--ion-color-primary, #0054e9);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32);color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));font-size:0.875rem}.loading-wrapper.sc-ion-loading-md{border-radius:2px;-webkit-padding-start:24px;padding-inline-start:24px;-webkit-padding-end:24px;padding-inline-end:24px;padding-top:24px;padding-bottom:24px;-webkit-box-shadow:0 16px 20px rgba(0, 0, 0, 0.4);box-shadow:0 16px 20px rgba(0, 0, 0, 0.4)}.loading-spinner.sc-ion-loading-md+.loading-content.sc-ion-loading-md{-webkit-margin-start:16px;margin-inline-start:16px}",G=(()=>{let n=class{constructor(i){A(this,i),this.didPresent=s(this,"ionLoadingDidPresent",7),this.willPresent=s(this,"ionLoadingWillPresent",7),this.willDismiss=s(this,"ionLoadingWillDismiss",7),this.didDismiss=s(this,"ionLoadingDidDismiss",7),this.didPresentShorthand=s(this,"didPresent",7),this.willPresentShorthand=s(this,"willPresent",7),this.willDismissShorthand=s(this,"willDismiss",7),this.didDismissShorthand=s(this,"didDismiss",7),this.delegateController=x(this),this.lockController=g(),this.triggerController=v(),this.customHTMLEnabled=d.get("innerHTMLTemplatesEnabled",L),this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.duration=0,this.backdropDismiss=!1,this.showBackdrop=!0,this.translucent=!1,this.animated=!0,this.isOpen=!1,this.onBackdropTap=()=>{this.dismiss(void 0,k)}}onIsOpenChange(i,e){i===!0&&e===!1?this.present():i===!1&&e===!0&&this.dismiss()}triggerChanged(){let{trigger:i,el:e,triggerController:t}=this;i&&t.addClickListener(e,i)}connectedCallback(){f(this.el),this.triggerChanged()}componentWillLoad(){var i;if(this.spinner===void 0){let e=p(this);this.spinner=d.get("loadingSpinner",d.get("spinner",e==="ios"?"lines":"crescent"))}!((i=this.htmlAttributes)===null||i===void 0)&&i.id||u(this.el)}componentDidLoad(){this.isOpen===!0&&h(()=>this.present()),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener()}present(){return l(this,null,function*(){let i=yield this.lockController.lock();yield this.delegateController.attachViewToDom(),yield b(this,"loadingEnter",E,z),this.duration>0&&(this.durationTimeout=setTimeout(()=>this.dismiss(),this.duration+10)),i()})}dismiss(i,e){return l(this,null,function*(){let t=yield this.lockController.lock();this.durationTimeout&&clearTimeout(this.durationTimeout);let r=yield y(this,i,e,"loadingLeave",O,M);return r&&this.delegateController.removeViewFromDom(),t(),r})}onDidDismiss(){return c(this.el,"ionLoadingDidDismiss")}onWillDismiss(){return c(this.el,"ionLoadingWillDismiss")}renderLoadingMessage(i){let{customHTMLEnabled:e,message:t}=this;return e?a("div",{class:"loading-content",id:i,innerHTML:C(t)}):a("div",{class:"loading-content",id:i},t)}render(){let{message:i,spinner:e,htmlAttributes:t,overlayIndex:r}=this,S=p(this),m=`loading-${r}-msg`;return a(D,Object.assign({key:"4497183ce220242abe19ae15f328f9a92ccafbbc",role:"dialog","aria-modal":"true","aria-labelledby":i!==void 0?m:null,tabindex:"-1"},t,{style:{zIndex:`${4e4+this.overlayIndex}`},onIonBackdropTap:this.onBackdropTap,class:Object.assign(Object.assign({},w(this.cssClass)),{[S]:!0,"overlay-hidden":!0,"loading-translucent":this.translucent})}),a("ion-backdrop",{key:"231dec84e424a2dc358ce95b84d6035cf43e4dea",visible:this.showBackdrop,tappable:this.backdropDismiss}),a("div",{key:"c9af29b6e6bb49a217396a5c874bbfb8835a926c",tabindex:"0","aria-hidden":"true"}),a("div",{key:"a8659863743cdeccbe1ba810eaabfd3ebfcb86f3",class:"loading-wrapper ion-overlay-wrapper"},e&&a("div",{key:"3b346f39bc71691bd8686556a1e142198a7b12fa",class:"loading-spinner"},a("ion-spinner",{key:"8dc2bf1556e5138e262827f1516c59ecd09f3520",name:e,"aria-hidden":"true"})),i!==void 0&&this.renderLoadingMessage(m)),a("div",{key:"054164c0dbae9a0e0973dd3c8e28f5b771820310",tabindex:"0","aria-hidden":"true"}))}get el(){return T(this)}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}};return n.style={ios:B,md:I},n})();export{G as ion_loading};
