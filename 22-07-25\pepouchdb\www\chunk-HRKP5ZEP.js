import{i as c,m as n,n as d,o as h,p as m}from"./chunk-EHNA26RN.js";import{g as a}from"./chunk-2R6CW7ES.js";var u=":host{display:-ms-flexbox;display:flex;height:100%;overflow-x:scroll;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;scrollbar-width:none;-ms-overflow-style:none}:host::-webkit-scrollbar{display:none}:host(.segment-view-disabled){-ms-touch-action:none;touch-action:none;overflow-x:hidden}:host(.segment-view-scroll-disabled){pointer-events:none}:host(.segment-view-disabled){opacity:0.3}",f=":host{display:-ms-flexbox;display:flex;height:100%;overflow-x:scroll;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;scrollbar-width:none;-ms-overflow-style:none}:host::-webkit-scrollbar{display:none}:host(.segment-view-disabled){-ms-touch-action:none;touch-action:none;overflow-x:hidden}:host(.segment-view-scroll-disabled){pointer-events:none}:host(.segment-view-disabled){opacity:0.3}",w=(()=>{let i=class{constructor(e){c(this,e),this.ionSegmentViewScroll=m(this,"ionSegmentViewScroll",7),this.scrollEndTimeout=null,this.isTouching=!1,this.disabled=!1}handleScroll(e){var t;let{scrollLeft:r,scrollWidth:s,clientWidth:o}=e.target,l=r/(s-o);this.ionSegmentViewScroll.emit({scrollRatio:l,isManualScroll:(t=this.isManualScroll)!==null&&t!==void 0?t:!0}),this.resetScrollEndTimeout()}handleScrollStart(){this.scrollEndTimeout&&(clearTimeout(this.scrollEndTimeout),this.scrollEndTimeout=null),this.isTouching=!0}handleTouchEnd(){this.isTouching=!1}resetScrollEndTimeout(){this.scrollEndTimeout&&(clearTimeout(this.scrollEndTimeout),this.scrollEndTimeout=null),this.scrollEndTimeout=setTimeout(()=>{this.checkForScrollEnd()},100)}checkForScrollEnd(){this.isTouching||(this.isManualScroll=void 0)}setContent(e,t=!0){return a(this,null,function*(){let s=this.getSegmentContents().findIndex(l=>l.id===e);if(s===-1)return;this.isManualScroll=!1,this.resetScrollEndTimeout();let o=this.el.offsetWidth;this.el.scrollTo({top:0,left:s*o,behavior:t?"smooth":"instant"})})}getSegmentContents(){return Array.from(this.el.querySelectorAll("ion-segment-content"))}render(){let{disabled:e,isManualScroll:t}=this;return n(d,{key:"754a374e89fd4dd682eb00497e717242a6f83357",class:{"segment-view-disabled":e,"segment-view-scroll-disabled":t===!1}},n("slot",{key:"77366044eb61f0d4bba305bd6f0ef8fd1e25194b"}))}get el(){return h(this)}};return i.style={ios:u,md:f},i})();export{w as ion_segment_view};
