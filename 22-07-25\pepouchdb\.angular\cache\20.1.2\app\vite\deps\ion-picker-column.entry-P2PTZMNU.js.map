{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-picker-column.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, a as isPlatform, h, e as getIonMode, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { d as doc } from './index-ZjP4CjeZ.js';\nimport { r as raf, g as getElementRoot } from './helpers-1O4D2b7y.js';\nimport { b as hapticSelectionStart, a as hapticSelectionChanged, h as hapticSelectionEnd } from './haptic-DzAMWJuk.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\nimport './capacitor-CFERIeaU.js';\n\nconst pickerColumnCss = \":host{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;max-width:100%;height:200px;font-size:22px;text-align:center}.assistive-focusable{left:0;right:0;top:0;bottom:0;position:absolute;z-index:1;pointer-events:none}.assistive-focusable:focus{outline:none}.picker-opts{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0px;padding-bottom:0px;min-width:26px;max-height:200px;outline:none;text-align:inherit;-webkit-scroll-snap-type:y mandatory;-ms-scroll-snap-type:y mandatory;scroll-snap-type:y mandatory;overflow-x:hidden;overflow-y:scroll;scrollbar-width:none}.picker-item-empty{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.picker-opts::-webkit-scrollbar{display:none}::slotted(ion-picker-column-option){display:block;scroll-snap-align:center}.picker-item-empty,:host(:not([disabled])) ::slotted(ion-picker-column-option.option-disabled){scroll-snap-align:none}::slotted([slot=prefix]),::slotted([slot=suffix]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}::slotted([slot=prefix]){-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0;padding-bottom:0;-ms-flex-pack:end;justify-content:end}::slotted([slot=suffix]){-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0;padding-bottom:0;-ms-flex-pack:start;justify-content:start}:host(.picker-column-disabled) .picker-opts{overflow-y:hidden}:host(.picker-column-disabled) ::slotted(ion-picker-column-option){cursor:default;opacity:0.4;pointer-events:none}@media (any-hover: hover){:host(:focus) .picker-opts{outline:none;background:rgba(var(--ion-color-base-rgb), 0.2)}}\";\n\nconst PickerColumn = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.isScrolling = false;\n        this.isColumnVisible = false;\n        this.canExitInputMode = true;\n        this.updateValueTextOnScroll = false;\n        this.ariaLabel = null;\n        this.isActive = false;\n        /**\n         * If `true`, the user cannot interact with the picker.\n         */\n        this.disabled = false;\n        /**\n         * The color to use from your application's color palette.\n         * Default options are: `\"primary\"`, `\"secondary\"`, `\"tertiary\"`, `\"success\"`, `\"warning\"`, `\"danger\"`, `\"light\"`, `\"medium\"`, and `\"dark\"`.\n         * For more information on colors, see [theming](/docs/theming/basics).\n         */\n        this.color = 'primary';\n        /**\n         * If `true`, tapping the picker will\n         * reveal a number input keyboard that lets\n         * the user type in values for each picker\n         * column. This is useful when working\n         * with time pickers.\n         *\n         * @internal\n         */\n        this.numericInput = false;\n        this.centerPickerItemInView = (target, smooth = true, canExitInputMode = true) => {\n            const { isColumnVisible, scrollEl } = this;\n            if (isColumnVisible && scrollEl) {\n                // (Vertical offset from parent) - (three empty picker rows) + (half the height of the target to ensure the scroll triggers)\n                const top = target.offsetTop - 3 * target.clientHeight + target.clientHeight / 2;\n                if (scrollEl.scrollTop !== top) {\n                    /**\n                     * Setting this flag prevents input\n                     * mode from exiting in the picker column's\n                     * scroll callback. This is useful when the user manually\n                     * taps an item or types on the keyboard as both\n                     * of these can cause a scroll to occur.\n                     */\n                    this.canExitInputMode = canExitInputMode;\n                    this.updateValueTextOnScroll = false;\n                    scrollEl.scroll({\n                        top,\n                        left: 0,\n                        behavior: smooth ? 'smooth' : undefined,\n                    });\n                }\n            }\n        };\n        this.setPickerItemActiveState = (item, isActive) => {\n            if (isActive) {\n                item.classList.add(PICKER_ITEM_ACTIVE_CLASS);\n            }\n            else {\n                item.classList.remove(PICKER_ITEM_ACTIVE_CLASS);\n            }\n        };\n        /**\n         * When ionInputModeChange is emitted, each column\n         * needs to check if it is the one being made available\n         * for text entry.\n         */\n        this.inputModeChange = (ev) => {\n            if (!this.numericInput) {\n                return;\n            }\n            const { useInputMode, inputModeColumn } = ev.detail;\n            /**\n             * If inputModeColumn is undefined then this means\n             * all numericInput columns are being selected.\n             */\n            const isColumnActive = inputModeColumn === undefined || inputModeColumn === this.el;\n            if (!useInputMode || !isColumnActive) {\n                this.setInputModeActive(false);\n                return;\n            }\n            this.setInputModeActive(true);\n        };\n        /**\n         * Setting isActive will cause a re-render.\n         * As a result, we do not want to cause the\n         * re-render mid scroll as this will cause\n         * the picker column to jump back to\n         * whatever value was selected at the\n         * start of the scroll interaction.\n         */\n        this.setInputModeActive = (state) => {\n            if (this.isScrolling) {\n                this.scrollEndCallback = () => {\n                    this.isActive = state;\n                };\n                return;\n            }\n            this.isActive = state;\n        };\n        /**\n         * When the column scrolls, the component\n         * needs to determine which item is centered\n         * in the view and will emit an ionChange with\n         * the item object.\n         */\n        this.initializeScrollListener = () => {\n            /**\n             * The haptics for the wheel picker are\n             * an iOS-only feature. As a result, they should\n             * be disabled on Android.\n             */\n            const enableHaptics = isPlatform('ios');\n            const { el, scrollEl } = this;\n            let timeout;\n            let activeEl = this.activeItem;\n            const scrollCallback = () => {\n                raf(() => {\n                    var _a;\n                    if (!scrollEl)\n                        return;\n                    if (timeout) {\n                        clearTimeout(timeout);\n                        timeout = undefined;\n                    }\n                    if (!this.isScrolling) {\n                        enableHaptics && hapticSelectionStart();\n                        this.isScrolling = true;\n                    }\n                    /**\n                     * Select item in the center of the column\n                     * which is the month/year that we want to select\n                     */\n                    const bbox = scrollEl.getBoundingClientRect();\n                    const centerX = bbox.x + bbox.width / 2;\n                    const centerY = bbox.y + bbox.height / 2;\n                    /**\n                     * elementFromPoint returns the top-most element.\n                     * This means that if an ion-backdrop is overlaying the\n                     * picker then the appropriate picker column option will\n                     * not be selected. To account for this, we use elementsFromPoint\n                     * and use an Array.find to find the appropriate column option\n                     * at that point.\n                     *\n                     * Additionally, the picker column could be used in the\n                     * Shadow DOM (i.e. in ion-datetime) so we need to make\n                     * sure we are choosing the correct host otherwise\n                     * the elements returns by elementsFromPoint will be\n                     * retargeted. To account for this, we check to see\n                     * if the picker column has a parent shadow root. If\n                     * so, we use that shadow root when doing elementsFromPoint.\n                     * Otherwise, we just use the document.\n                     */\n                    const rootNode = el.getRootNode();\n                    const hasParentShadow = rootNode instanceof ShadowRoot;\n                    const referenceNode = hasParentShadow ? rootNode : doc;\n                    /**\n                     * If the reference node is undefined\n                     * then it's likely that doc is undefined\n                     * due to being in an SSR environment.\n                     */\n                    if (referenceNode === undefined) {\n                        return;\n                    }\n                    const elementsAtPoint = referenceNode.elementsFromPoint(centerX, centerY);\n                    /**\n                     * elementsFromPoint can returns multiple elements\n                     * so find the relevant picker column option if one exists.\n                     */\n                    let newActiveElement = elementsAtPoint.find((el) => el.tagName === 'ION-PICKER-COLUMN-OPTION');\n                    /**\n                     * TODO(FW-6594): Remove this workaround when iOS 16 is no longer\n                     * supported.\n                     *\n                     * If `elementsFromPoint` failed to find the active element (a known\n                     * issue on iOS 16 when elements are in a Shadow DOM and the\n                     * referenceNode is the document), a fallback to `elementFromPoint`\n                     * is used. While `elementsFromPoint` returns all elements,\n                     * `elementFromPoint` returns only the top-most, which is sufficient\n                     * for this use case and appears to handle Shadow DOM retargeting\n                     * more reliably in this specific iOS bug.\n                     */\n                    if (newActiveElement === undefined) {\n                        const fallbackActiveElement = referenceNode.elementFromPoint(centerX, centerY);\n                        if ((fallbackActiveElement === null || fallbackActiveElement === void 0 ? void 0 : fallbackActiveElement.tagName) === 'ION-PICKER-COLUMN-OPTION') {\n                            newActiveElement = fallbackActiveElement;\n                        }\n                    }\n                    if (activeEl !== undefined) {\n                        this.setPickerItemActiveState(activeEl, false);\n                    }\n                    if (newActiveElement === undefined || newActiveElement.disabled) {\n                        return;\n                    }\n                    /**\n                     * If we are selecting a new value,\n                     * we need to run haptics again.\n                     */\n                    if (newActiveElement !== activeEl) {\n                        enableHaptics && hapticSelectionChanged();\n                        if (this.canExitInputMode) {\n                            /**\n                             * The native iOS wheel picker\n                             * only dismisses the keyboard\n                             * once the selected item has changed\n                             * as a result of a swipe\n                             * from the user. If `canExitInputMode` is\n                             * `false` then this means that the\n                             * scroll is happening as a result of\n                             * the `value` property programmatically changing\n                             * either by an application or by the user via the keyboard.\n                             */\n                            this.exitInputMode();\n                        }\n                    }\n                    activeEl = newActiveElement;\n                    this.setPickerItemActiveState(newActiveElement, true);\n                    /**\n                     * Set the aria-valuetext even though the value prop has not been updated yet.\n                     * This enables some screen readers to announce the value as the users drag\n                     * as opposed to when their release their pointer from the screen.\n                     *\n                     * When the value is programmatically updated, we will smoothly scroll\n                     * to the new option. However, we do not want to update aria-valuetext mid-scroll\n                     * as that can cause the old value to be briefly set before being set to the\n                     * correct option. This will cause some screen readers to announce the old value\n                     * again before announcing the new value. The correct valuetext will be set on render.\n                     */\n                    if (this.updateValueTextOnScroll) {\n                        (_a = this.assistiveFocusable) === null || _a === void 0 ? void 0 : _a.setAttribute('aria-valuetext', this.getOptionValueText(newActiveElement));\n                    }\n                    timeout = setTimeout(() => {\n                        this.isScrolling = false;\n                        this.updateValueTextOnScroll = true;\n                        enableHaptics && hapticSelectionEnd();\n                        /**\n                         * Certain tasks (such as those that\n                         * cause re-renders) should only be done\n                         * once scrolling has finished, otherwise\n                         * flickering may occur.\n                         */\n                        const { scrollEndCallback } = this;\n                        if (scrollEndCallback) {\n                            scrollEndCallback();\n                            this.scrollEndCallback = undefined;\n                        }\n                        /**\n                         * Reset this flag as the\n                         * next scroll interaction could\n                         * be a scroll from the user. In this\n                         * case, we should exit input mode.\n                         */\n                        this.canExitInputMode = true;\n                        this.setValue(newActiveElement.value);\n                    }, 250);\n                });\n            };\n            /**\n             * Wrap this in an raf so that the scroll callback\n             * does not fire when component is initially shown.\n             */\n            raf(() => {\n                if (!scrollEl)\n                    return;\n                scrollEl.addEventListener('scroll', scrollCallback);\n                this.destroyScrollListener = () => {\n                    scrollEl.removeEventListener('scroll', scrollCallback);\n                };\n            });\n        };\n        /**\n         * Tells the parent picker to\n         * exit text entry mode. This is only called\n         * when the selected item changes during scroll, so\n         * we know that the user likely wants to scroll\n         * instead of type.\n         */\n        this.exitInputMode = () => {\n            const { parentEl } = this;\n            if (parentEl == null)\n                return;\n            parentEl.exitInputMode();\n            /**\n             * setInputModeActive only takes\n             * effect once scrolling stops to avoid\n             * a component re-render while scrolling.\n             * However, we want the visual active\n             * indicator to go away immediately, so\n             * we call classList.remove here.\n             */\n            this.el.classList.remove('picker-column-active');\n        };\n        /**\n         * Find the next enabled option after the active option.\n         * @param stride - How many options to \"jump\" over in order to select the next option.\n         * This can be used to implement PageUp/PageDown behaviors where pressing these keys\n         * scrolls the picker by more than 1 option. For example, a stride of 5 means select\n         * the enabled option 5 options after the active one. Note that the actual option selected\n         * may be past the stride if the option at the stride is disabled.\n         */\n        this.findNextOption = (stride = 1) => {\n            const { activeItem } = this;\n            if (!activeItem)\n                return null;\n            let prevNode = activeItem;\n            let node = activeItem.nextElementSibling;\n            while (node != null) {\n                if (stride > 0) {\n                    stride--;\n                }\n                if (node.tagName === 'ION-PICKER-COLUMN-OPTION' && !node.disabled && stride === 0) {\n                    return node;\n                }\n                prevNode = node;\n                // Use nextElementSibling instead of nextSibling to avoid text/comment nodes\n                node = node.nextElementSibling;\n            }\n            return prevNode;\n        };\n        /**\n         * Find the next enabled option after the active option.\n         * @param stride - How many options to \"jump\" over in order to select the next option.\n         * This can be used to implement PageUp/PageDown behaviors where pressing these keys\n         * scrolls the picker by more than 1 option. For example, a stride of 5 means select\n         * the enabled option 5 options before the active one. Note that the actual option selected\n         *  may be past the stride if the option at the stride is disabled.\n         */\n        this.findPreviousOption = (stride = 1) => {\n            const { activeItem } = this;\n            if (!activeItem)\n                return null;\n            let nextNode = activeItem;\n            let node = activeItem.previousElementSibling;\n            while (node != null) {\n                if (stride > 0) {\n                    stride--;\n                }\n                if (node.tagName === 'ION-PICKER-COLUMN-OPTION' && !node.disabled && stride === 0) {\n                    return node;\n                }\n                nextNode = node;\n                // Use previousElementSibling instead of previousSibling to avoid text/comment nodes\n                node = node.previousElementSibling;\n            }\n            return nextNode;\n        };\n        this.onKeyDown = (ev) => {\n            /**\n             * The below operations should be inverted when running on a mobile device.\n             * For example, swiping up will dispatch an \"ArrowUp\" event. On desktop,\n             * this should cause the previous option to be selected. On mobile, swiping\n             * up causes a view to scroll down. As a result, swiping up on mobile should\n             * cause the next option to be selected. The Home/End operations remain\n             * unchanged because those always represent the first/last options, respectively.\n             */\n            const mobile = isPlatform('mobile');\n            let newOption = null;\n            switch (ev.key) {\n                case 'ArrowDown':\n                    newOption = mobile ? this.findPreviousOption() : this.findNextOption();\n                    break;\n                case 'ArrowUp':\n                    newOption = mobile ? this.findNextOption() : this.findPreviousOption();\n                    break;\n                case 'PageUp':\n                    newOption = mobile ? this.findNextOption(5) : this.findPreviousOption(5);\n                    break;\n                case 'PageDown':\n                    newOption = mobile ? this.findPreviousOption(5) : this.findNextOption(5);\n                    break;\n                case 'Home':\n                    /**\n                     * There is no guarantee that the first child will be an ion-picker-column-option,\n                     * so we do not use firstElementChild.\n                     */\n                    newOption = this.el.querySelector('ion-picker-column-option:first-of-type');\n                    break;\n                case 'End':\n                    /**\n                     * There is no guarantee that the last child will be an ion-picker-column-option,\n                     * so we do not use lastElementChild.\n                     */\n                    newOption = this.el.querySelector('ion-picker-column-option:last-of-type');\n                    break;\n            }\n            if (newOption !== null) {\n                this.setValue(newOption.value);\n                // This stops any default browser behavior such as scrolling\n                ev.preventDefault();\n            }\n        };\n        /**\n         * Utility to generate the correct text for aria-valuetext.\n         */\n        this.getOptionValueText = (el) => {\n            var _a;\n            return el ? (_a = el.getAttribute('aria-label')) !== null && _a !== void 0 ? _a : el.innerText : '';\n        };\n        /**\n         * Render an element that overlays the column. This element is for assistive\n         * tech to allow users to navigate the column up/down. This element should receive\n         * focus as it listens for synthesized keyboard events as required by the\n         * slider role: https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/slider_role\n         */\n        this.renderAssistiveFocusable = () => {\n            const { activeItem } = this;\n            const valueText = this.getOptionValueText(activeItem);\n            /**\n             * When using the picker, the valuetext provides important context that valuenow\n             * does not. Additionally, using non-zero valuemin/valuemax values can cause\n             * WebKit to incorrectly announce numeric valuetext values (such as a year\n             * like \"2024\") as percentages: https://bugs.webkit.org/show_bug.cgi?id=273126\n             */\n            return (h(\"div\", { ref: (el) => (this.assistiveFocusable = el), class: \"assistive-focusable\", role: \"slider\", tabindex: this.disabled ? undefined : 0, \"aria-label\": this.ariaLabel, \"aria-valuemin\": 0, \"aria-valuemax\": 0, \"aria-valuenow\": 0, \"aria-valuetext\": valueText, \"aria-orientation\": \"vertical\", onKeyDown: (ev) => this.onKeyDown(ev) }));\n        };\n    }\n    ariaLabelChanged(newValue) {\n        this.ariaLabel = newValue;\n    }\n    valueChange() {\n        if (this.isColumnVisible) {\n            /**\n             * Only scroll the active item into view when the picker column\n             * is actively visible to the user.\n             */\n            this.scrollActiveItemIntoView(true);\n        }\n    }\n    /**\n     * Only setup scroll listeners\n     * when the picker is visible, otherwise\n     * the container will have a scroll\n     * height of 0px.\n     */\n    componentWillLoad() {\n        /**\n         * We cache parentEl in a local variable\n         * so we don't need to keep accessing\n         * the class variable (which comes with\n         * a small performance hit)\n         */\n        const parentEl = (this.parentEl = this.el.closest('ion-picker'));\n        const visibleCallback = (entries) => {\n            /**\n             * Browsers will sometimes group multiple IO events into a single callback.\n             * As a result, we want to grab the last/most recent event in case there are multiple events.\n             */\n            const ev = entries[entries.length - 1];\n            if (ev.isIntersecting) {\n                const { activeItem, el } = this;\n                this.isColumnVisible = true;\n                /**\n                 * Because this initial call to scrollActiveItemIntoView has to fire before\n                 * the scroll listener is set up, we need to manage the active class manually.\n                 */\n                const oldActive = getElementRoot(el).querySelector(`.${PICKER_ITEM_ACTIVE_CLASS}`);\n                if (oldActive) {\n                    this.setPickerItemActiveState(oldActive, false);\n                }\n                this.scrollActiveItemIntoView();\n                if (activeItem) {\n                    this.setPickerItemActiveState(activeItem, true);\n                }\n                this.initializeScrollListener();\n            }\n            else {\n                this.isColumnVisible = false;\n                if (this.destroyScrollListener) {\n                    this.destroyScrollListener();\n                    this.destroyScrollListener = undefined;\n                }\n            }\n        };\n        /**\n         * Set the root to be the parent picker element\n         * This causes the IO callback\n         * to be fired in WebKit as soon as the element\n         * is visible. If we used the default root value\n         * then WebKit would only fire the IO callback\n         * after any animations (such as a modal transition)\n         * finished, and there would potentially be a flicker.\n         */\n        new IntersectionObserver(visibleCallback, { threshold: 0.001, root: this.parentEl }).observe(this.el);\n        if (parentEl !== null) {\n            // TODO(FW-2832): type\n            parentEl.addEventListener('ionInputModeChange', (ev) => this.inputModeChange(ev));\n        }\n    }\n    componentDidRender() {\n        const { el, activeItem, isColumnVisible, value } = this;\n        if (isColumnVisible && !activeItem) {\n            const firstOption = el.querySelector('ion-picker-column-option');\n            /**\n             * If the picker column does not have an active item and the current value\n             * does not match the first item in the picker column, that means\n             * the value is out of bounds. In this case, we assign the value to the\n             * first item to match the scroll position of the column.\n             *\n             */\n            if (firstOption !== null && firstOption.value !== value) {\n                this.setValue(firstOption.value);\n            }\n        }\n    }\n    /** @internal  */\n    async scrollActiveItemIntoView(smooth = false) {\n        const activeEl = this.activeItem;\n        if (activeEl) {\n            this.centerPickerItemInView(activeEl, smooth, false);\n        }\n    }\n    /**\n     * Sets the value prop and fires the ionChange event.\n     * This is used when we need to fire ionChange from\n     * user-generated events that cannot be caught with normal\n     * input/change event listeners.\n     * @internal\n     */\n    async setValue(value) {\n        if (this.disabled === true || this.value === value) {\n            return;\n        }\n        this.value = value;\n        this.ionChange.emit({ value });\n    }\n    /**\n     * Sets focus on the scrollable container within the picker column.\n     * Use this method instead of the global `pickerColumn.focus()`.\n     */\n    async setFocus() {\n        if (this.assistiveFocusable) {\n            this.assistiveFocusable.focus();\n        }\n    }\n    connectedCallback() {\n        var _a;\n        this.ariaLabel = (_a = this.el.getAttribute('aria-label')) !== null && _a !== void 0 ? _a : 'Select a value';\n    }\n    get activeItem() {\n        const { value } = this;\n        const options = Array.from(this.el.querySelectorAll('ion-picker-column-option'));\n        return options.find((option) => {\n            /**\n             * If the whole picker column is disabled, the current value should appear active\n             * If the current value item is specifically disabled, it should not appear active\n             */\n            if (!this.disabled && option.disabled) {\n                return false;\n            }\n            return option.value === value;\n        });\n    }\n    render() {\n        const { color, disabled, isActive, numericInput } = this;\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'ea0280355b2f87895bf7dddd289ccf473aa759f3', class: createColorClasses(color, {\n                [mode]: true,\n                ['picker-column-active']: isActive,\n                ['picker-column-numeric-input']: numericInput,\n                ['picker-column-disabled']: disabled,\n            }) }, this.renderAssistiveFocusable(), h(\"slot\", { key: '482992131cdeb85b1f61430d7fe1322a16345769', name: \"prefix\" }), h(\"div\", { key: '43f7f80d621d411ef366b3ca1396299e8c9a0c97', \"aria-hidden\": \"true\", class: \"picker-opts\", ref: (el) => {\n                this.scrollEl = el;\n            },\n            /**\n             * When an element has an overlay scroll style and\n             * a fixed height, Firefox will focus the scrollable\n             * container if the content exceeds the container's\n             * dimensions.\n             *\n             * This causes keyboard navigation to focus to this\n             * element instead of going to the next element in\n             * the tab order.\n             *\n             * The desired behavior is for the user to be able to\n             * focus the assistive focusable element and tab to\n             * the next element in the tab order. Instead of tabbing\n             * to this element.\n             *\n             * To prevent this, we set the tabIndex to -1. This\n             * will match the behavior of the other browsers.\n             */\n            tabIndex: -1 }, h(\"div\", { key: '13a9ee686132af32240710730765de4c0003a9e8', class: \"picker-item-empty\", \"aria-hidden\": \"true\" }, \"\\u00A0\"), h(\"div\", { key: 'dbccba4920833cfcebe9b0fc763458ec3053705a', class: \"picker-item-empty\", \"aria-hidden\": \"true\" }, \"\\u00A0\"), h(\"div\", { key: '682b43f83a5ea2e46067457f3af118535e111edb', class: \"picker-item-empty\", \"aria-hidden\": \"true\" }, \"\\u00A0\"), h(\"slot\", { key: 'd27e1e1dc0504b2f4627a29912a05bb91e8e413a' }), h(\"div\", { key: '61c948dbb9cf7469aed3018542bc0954211585ba', class: \"picker-item-empty\", \"aria-hidden\": \"true\" }, \"\\u00A0\"), h(\"div\", { key: 'cf46c277fbee65e35ff44ce0d53ce12aa9cbf9db', class: \"picker-item-empty\", \"aria-hidden\": \"true\" }, \"\\u00A0\"), h(\"div\", { key: 'bbc0e2d491d3f836ab849493ade2f7fa6ad9244e', class: \"picker-item-empty\", \"aria-hidden\": \"true\" }, \"\\u00A0\")), h(\"slot\", { key: 'd25cbbe14b2914fe7b878d43b4e3f4a8c8177d24', name: \"suffix\" })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"aria-label\": [\"ariaLabelChanged\"],\n        \"value\": [\"valueChange\"]\n    }; }\n};\nconst PICKER_ITEM_ACTIVE_CLASS = 'option-active';\nPickerColumn.style = pickerColumnCss;\n\nexport { PickerColumn as ion_picker_column };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,kBAAkB;AAExB,IAAM,eAAe,MAAM;AAAA,EACvB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,SAAK,cAAc;AACnB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AACxB,SAAK,0BAA0B;AAC/B,SAAK,YAAY;AACjB,SAAK,WAAW;AAIhB,SAAK,WAAW;AAMhB,SAAK,QAAQ;AAUb,SAAK,eAAe;AACpB,SAAK,yBAAyB,CAAC,QAAQ,SAAS,MAAM,mBAAmB,SAAS;AAC9E,YAAM,EAAE,iBAAiB,SAAS,IAAI;AACtC,UAAI,mBAAmB,UAAU;AAE7B,cAAM,MAAM,OAAO,YAAY,IAAI,OAAO,eAAe,OAAO,eAAe;AAC/E,YAAI,SAAS,cAAc,KAAK;AAQ5B,eAAK,mBAAmB;AACxB,eAAK,0BAA0B;AAC/B,mBAAS,OAAO;AAAA,YACZ;AAAA,YACA,MAAM;AAAA,YACN,UAAU,SAAS,WAAW;AAAA,UAClC,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,2BAA2B,CAAC,MAAM,aAAa;AAChD,UAAI,UAAU;AACV,aAAK,UAAU,IAAI,wBAAwB;AAAA,MAC/C,OACK;AACD,aAAK,UAAU,OAAO,wBAAwB;AAAA,MAClD;AAAA,IACJ;AAMA,SAAK,kBAAkB,CAAC,OAAO;AAC3B,UAAI,CAAC,KAAK,cAAc;AACpB;AAAA,MACJ;AACA,YAAM,EAAE,cAAc,gBAAgB,IAAI,GAAG;AAK7C,YAAM,iBAAiB,oBAAoB,UAAa,oBAAoB,KAAK;AACjF,UAAI,CAAC,gBAAgB,CAAC,gBAAgB;AAClC,aAAK,mBAAmB,KAAK;AAC7B;AAAA,MACJ;AACA,WAAK,mBAAmB,IAAI;AAAA,IAChC;AASA,SAAK,qBAAqB,CAAC,UAAU;AACjC,UAAI,KAAK,aAAa;AAClB,aAAK,oBAAoB,MAAM;AAC3B,eAAK,WAAW;AAAA,QACpB;AACA;AAAA,MACJ;AACA,WAAK,WAAW;AAAA,IACpB;AAOA,SAAK,2BAA2B,MAAM;AAMlC,YAAM,gBAAgB,WAAW,KAAK;AACtC,YAAM,EAAE,IAAI,SAAS,IAAI;AACzB,UAAI;AACJ,UAAI,WAAW,KAAK;AACpB,YAAM,iBAAiB,MAAM;AACzB,YAAI,MAAM;AACN,cAAI;AACJ,cAAI,CAAC;AACD;AACJ,cAAI,SAAS;AACT,yBAAa,OAAO;AACpB,sBAAU;AAAA,UACd;AACA,cAAI,CAAC,KAAK,aAAa;AACnB,6BAAiB,qBAAqB;AACtC,iBAAK,cAAc;AAAA,UACvB;AAKA,gBAAM,OAAO,SAAS,sBAAsB;AAC5C,gBAAM,UAAU,KAAK,IAAI,KAAK,QAAQ;AACtC,gBAAM,UAAU,KAAK,IAAI,KAAK,SAAS;AAkBvC,gBAAM,WAAW,GAAG,YAAY;AAChC,gBAAM,kBAAkB,oBAAoB;AAC5C,gBAAM,gBAAgB,kBAAkB,WAAW;AAMnD,cAAI,kBAAkB,QAAW;AAC7B;AAAA,UACJ;AACA,gBAAM,kBAAkB,cAAc,kBAAkB,SAAS,OAAO;AAKxE,cAAI,mBAAmB,gBAAgB,KAAK,CAACA,QAAOA,IAAG,YAAY,0BAA0B;AAa7F,cAAI,qBAAqB,QAAW;AAChC,kBAAM,wBAAwB,cAAc,iBAAiB,SAAS,OAAO;AAC7E,iBAAK,0BAA0B,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,aAAa,4BAA4B;AAC9I,iCAAmB;AAAA,YACvB;AAAA,UACJ;AACA,cAAI,aAAa,QAAW;AACxB,iBAAK,yBAAyB,UAAU,KAAK;AAAA,UACjD;AACA,cAAI,qBAAqB,UAAa,iBAAiB,UAAU;AAC7D;AAAA,UACJ;AAKA,cAAI,qBAAqB,UAAU;AAC/B,6BAAiB,uBAAuB;AACxC,gBAAI,KAAK,kBAAkB;AAYvB,mBAAK,cAAc;AAAA,YACvB;AAAA,UACJ;AACA,qBAAW;AACX,eAAK,yBAAyB,kBAAkB,IAAI;AAYpD,cAAI,KAAK,yBAAyB;AAC9B,aAAC,KAAK,KAAK,wBAAwB,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,kBAAkB,KAAK,mBAAmB,gBAAgB,CAAC;AAAA,UACnJ;AACA,oBAAU,WAAW,MAAM;AACvB,iBAAK,cAAc;AACnB,iBAAK,0BAA0B;AAC/B,6BAAiB,mBAAmB;AAOpC,kBAAM,EAAE,kBAAkB,IAAI;AAC9B,gBAAI,mBAAmB;AACnB,gCAAkB;AAClB,mBAAK,oBAAoB;AAAA,YAC7B;AAOA,iBAAK,mBAAmB;AACxB,iBAAK,SAAS,iBAAiB,KAAK;AAAA,UACxC,GAAG,GAAG;AAAA,QACV,CAAC;AAAA,MACL;AAKA,UAAI,MAAM;AACN,YAAI,CAAC;AACD;AACJ,iBAAS,iBAAiB,UAAU,cAAc;AAClD,aAAK,wBAAwB,MAAM;AAC/B,mBAAS,oBAAoB,UAAU,cAAc;AAAA,QACzD;AAAA,MACJ,CAAC;AAAA,IACL;AAQA,SAAK,gBAAgB,MAAM;AACvB,YAAM,EAAE,SAAS,IAAI;AACrB,UAAI,YAAY;AACZ;AACJ,eAAS,cAAc;AASvB,WAAK,GAAG,UAAU,OAAO,sBAAsB;AAAA,IACnD;AASA,SAAK,iBAAiB,CAAC,SAAS,MAAM;AAClC,YAAM,EAAE,WAAW,IAAI;AACvB,UAAI,CAAC;AACD,eAAO;AACX,UAAI,WAAW;AACf,UAAI,OAAO,WAAW;AACtB,aAAO,QAAQ,MAAM;AACjB,YAAI,SAAS,GAAG;AACZ;AAAA,QACJ;AACA,YAAI,KAAK,YAAY,8BAA8B,CAAC,KAAK,YAAY,WAAW,GAAG;AAC/E,iBAAO;AAAA,QACX;AACA,mBAAW;AAEX,eAAO,KAAK;AAAA,MAChB;AACA,aAAO;AAAA,IACX;AASA,SAAK,qBAAqB,CAAC,SAAS,MAAM;AACtC,YAAM,EAAE,WAAW,IAAI;AACvB,UAAI,CAAC;AACD,eAAO;AACX,UAAI,WAAW;AACf,UAAI,OAAO,WAAW;AACtB,aAAO,QAAQ,MAAM;AACjB,YAAI,SAAS,GAAG;AACZ;AAAA,QACJ;AACA,YAAI,KAAK,YAAY,8BAA8B,CAAC,KAAK,YAAY,WAAW,GAAG;AAC/E,iBAAO;AAAA,QACX;AACA,mBAAW;AAEX,eAAO,KAAK;AAAA,MAChB;AACA,aAAO;AAAA,IACX;AACA,SAAK,YAAY,CAAC,OAAO;AASrB,YAAM,SAAS,WAAW,QAAQ;AAClC,UAAI,YAAY;AAChB,cAAQ,GAAG,KAAK;AAAA,QACZ,KAAK;AACD,sBAAY,SAAS,KAAK,mBAAmB,IAAI,KAAK,eAAe;AACrE;AAAA,QACJ,KAAK;AACD,sBAAY,SAAS,KAAK,eAAe,IAAI,KAAK,mBAAmB;AACrE;AAAA,QACJ,KAAK;AACD,sBAAY,SAAS,KAAK,eAAe,CAAC,IAAI,KAAK,mBAAmB,CAAC;AACvE;AAAA,QACJ,KAAK;AACD,sBAAY,SAAS,KAAK,mBAAmB,CAAC,IAAI,KAAK,eAAe,CAAC;AACvE;AAAA,QACJ,KAAK;AAKD,sBAAY,KAAK,GAAG,cAAc,wCAAwC;AAC1E;AAAA,QACJ,KAAK;AAKD,sBAAY,KAAK,GAAG,cAAc,uCAAuC;AACzE;AAAA,MACR;AACA,UAAI,cAAc,MAAM;AACpB,aAAK,SAAS,UAAU,KAAK;AAE7B,WAAG,eAAe;AAAA,MACtB;AAAA,IACJ;AAIA,SAAK,qBAAqB,CAAC,OAAO;AAC9B,UAAI;AACJ,aAAO,MAAM,KAAK,GAAG,aAAa,YAAY,OAAO,QAAQ,OAAO,SAAS,KAAK,GAAG,YAAY;AAAA,IACrG;AAOA,SAAK,2BAA2B,MAAM;AAClC,YAAM,EAAE,WAAW,IAAI;AACvB,YAAM,YAAY,KAAK,mBAAmB,UAAU;AAOpD,aAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,OAAQ,KAAK,qBAAqB,IAAK,OAAO,uBAAuB,MAAM,UAAU,UAAU,KAAK,WAAW,SAAY,GAAG,cAAc,KAAK,WAAW,iBAAiB,GAAG,iBAAiB,GAAG,iBAAiB,GAAG,kBAAkB,WAAW,oBAAoB,YAAY,WAAW,CAAC,OAAO,KAAK,UAAU,EAAE,EAAE,CAAC;AAAA,IACzV;AAAA,EACJ;AAAA,EACA,iBAAiB,UAAU;AACvB,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,cAAc;AACV,QAAI,KAAK,iBAAiB;AAKtB,WAAK,yBAAyB,IAAI;AAAA,IACtC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB;AAOhB,UAAM,WAAY,KAAK,WAAW,KAAK,GAAG,QAAQ,YAAY;AAC9D,UAAM,kBAAkB,CAAC,YAAY;AAKjC,YAAM,KAAK,QAAQ,QAAQ,SAAS,CAAC;AACrC,UAAI,GAAG,gBAAgB;AACnB,cAAM,EAAE,YAAY,GAAG,IAAI;AAC3B,aAAK,kBAAkB;AAKvB,cAAM,YAAY,eAAe,EAAE,EAAE,cAAc,IAAI,wBAAwB,EAAE;AACjF,YAAI,WAAW;AACX,eAAK,yBAAyB,WAAW,KAAK;AAAA,QAClD;AACA,aAAK,yBAAyB;AAC9B,YAAI,YAAY;AACZ,eAAK,yBAAyB,YAAY,IAAI;AAAA,QAClD;AACA,aAAK,yBAAyB;AAAA,MAClC,OACK;AACD,aAAK,kBAAkB;AACvB,YAAI,KAAK,uBAAuB;AAC5B,eAAK,sBAAsB;AAC3B,eAAK,wBAAwB;AAAA,QACjC;AAAA,MACJ;AAAA,IACJ;AAUA,QAAI,qBAAqB,iBAAiB,EAAE,WAAW,MAAO,MAAM,KAAK,SAAS,CAAC,EAAE,QAAQ,KAAK,EAAE;AACpG,QAAI,aAAa,MAAM;AAEnB,eAAS,iBAAiB,sBAAsB,CAAC,OAAO,KAAK,gBAAgB,EAAE,CAAC;AAAA,IACpF;AAAA,EACJ;AAAA,EACA,qBAAqB;AACjB,UAAM,EAAE,IAAI,YAAY,iBAAiB,MAAM,IAAI;AACnD,QAAI,mBAAmB,CAAC,YAAY;AAChC,YAAM,cAAc,GAAG,cAAc,0BAA0B;AAQ/D,UAAI,gBAAgB,QAAQ,YAAY,UAAU,OAAO;AACrD,aAAK,SAAS,YAAY,KAAK;AAAA,MACnC;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA,EAEM,yBAAyB,SAAS,OAAO;AAAA;AAC3C,YAAM,WAAW,KAAK;AACtB,UAAI,UAAU;AACV,aAAK,uBAAuB,UAAU,QAAQ,KAAK;AAAA,MACvD;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQM,SAAS,OAAO;AAAA;AAClB,UAAI,KAAK,aAAa,QAAQ,KAAK,UAAU,OAAO;AAChD;AAAA,MACJ;AACA,WAAK,QAAQ;AACb,WAAK,UAAU,KAAK,EAAE,MAAM,CAAC;AAAA,IACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,WAAW;AAAA;AACb,UAAI,KAAK,oBAAoB;AACzB,aAAK,mBAAmB,MAAM;AAAA,MAClC;AAAA,IACJ;AAAA;AAAA,EACA,oBAAoB;AAChB,QAAI;AACJ,SAAK,aAAa,KAAK,KAAK,GAAG,aAAa,YAAY,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,EAChG;AAAA,EACA,IAAI,aAAa;AACb,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,UAAU,MAAM,KAAK,KAAK,GAAG,iBAAiB,0BAA0B,CAAC;AAC/E,WAAO,QAAQ,KAAK,CAAC,WAAW;AAK5B,UAAI,CAAC,KAAK,YAAY,OAAO,UAAU;AACnC,eAAO;AAAA,MACX;AACA,aAAO,OAAO,UAAU;AAAA,IAC5B,CAAC;AAAA,EACL;AAAA,EACA,SAAS;AACL,UAAM,EAAE,OAAO,UAAU,UAAU,aAAa,IAAI;AACpD,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,OAAO,mBAAmB,OAAO;AAAA,MAC5F,CAAC,IAAI,GAAG;AAAA,MACR,CAAC,sBAAsB,GAAG;AAAA,MAC1B,CAAC,6BAA6B,GAAG;AAAA,MACjC,CAAC,wBAAwB,GAAG;AAAA,IAChC,CAAC,EAAE,GAAG,KAAK,yBAAyB,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,MAAM,SAAS,CAAC,GAAG,EAAE,OAAO;AAAA,MAAE,KAAK;AAAA,MAA4C,eAAe;AAAA,MAAQ,OAAO;AAAA,MAAe,KAAK,CAAC,OAAO;AACzO,aAAK,WAAW;AAAA,MACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAmBA,UAAU;AAAA,IAAG,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,qBAAqB,eAAe,OAAO,GAAG,GAAQ,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,qBAAqB,eAAe,OAAO,GAAG,GAAQ,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,qBAAqB,eAAe,OAAO,GAAG,GAAQ,GAAG,EAAE,QAAQ,EAAE,KAAK,2CAA2C,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,qBAAqB,eAAe,OAAO,GAAG,GAAQ,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,qBAAqB,eAAe,OAAO,GAAG,GAAQ,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,qBAAqB,eAAe,OAAO,GAAG,GAAQ,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,MAAM,SAAS,CAAC,CAAC;AAAA,EAC/4B;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,cAAc,CAAC,kBAAkB;AAAA,MACjC,SAAS,CAAC,aAAa;AAAA,IAC3B;AAAA,EAAG;AACP;AACA,IAAM,2BAA2B;AACjC,aAAa,QAAQ;", "names": ["el"]}