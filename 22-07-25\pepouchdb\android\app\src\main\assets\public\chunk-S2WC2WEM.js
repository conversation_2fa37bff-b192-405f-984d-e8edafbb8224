import{b as _2}from"./chunk-JIT2VFLB.js";import{e as z2,f as y2,g as C2}from"./chunk-PK2JKDOR.js";import{g as M3,r as k3}from"./chunk-EHNA26RN.js";import{$ as $t,$a as v3,A as w2,B as t1,C as L,D as b,E as a1,F as Rt,G as qt,Ga as I0,H as f2,Ha as Kt,Ia as M2,Ja as Qt,K as E1,Ka as Xt,L as u1,La as Jt,M as v1,Ma as Yt,N as R,Na as e3,O as E,Oa as t3,P as q0,Pa as n3,Q as r0,Qa as o3,R as W1,Ra as i3,S as Z1,Sa as a3,T as q,Ta as s3,U as K1,Ua as r3,V as Q1,Va as l3,W as Ft,Wa as k2,X as l0,Xa as k0,Y as Nt,Z as F,Za as c3,_ as Ut,_a as d3,a as U,aa as M0,ab as g3,b as Vt,ba as Gt,bb as h3,c as u2,ca as x2,cb as p3,d as A0,da as F0,db as u3,e as Ht,eb as m3,f as s0,fa as Wt,fb as w3,gb as f3,h as j0,ha as Zt,hb as V0,i as y1,ib as x3,j as C1,k as n0,kb as N0,l as Dt,m as a0,n as m2,o as O,p as Ot,q as X,r as g,s as Et,t as D,u as Pt,v as o0,w as H1,x as k1,y as T,z as Tt}from"./chunk-EIQRL2CK.js";import{a as f0,b as x0,e as h2,f as p2,g as n1}from"./chunk-2R6CW7ES.js";var P3=h2((O3,E3)=>{"use strict";(function(e){if(typeof O3=="object")E3.exports=e();else if(typeof define=="function"&&define.amd)define(e);else{var o;try{o=window}catch{o=self}o.SparkMD5=e()}})(function(e){"use strict";var o=function(u,d){return u+d&4294967295},n=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"];function t(u,d,r,l,h,m){return d=o(o(d,u),o(l,m)),o(d<<h|d>>>32-h,r)}function i(u,d){var r=u[0],l=u[1],h=u[2],m=u[3];r+=(l&h|~l&m)+d[0]-680876936|0,r=(r<<7|r>>>25)+l|0,m+=(r&l|~r&h)+d[1]-389564586|0,m=(m<<12|m>>>20)+r|0,h+=(m&r|~m&l)+d[2]+606105819|0,h=(h<<17|h>>>15)+m|0,l+=(h&m|~h&r)+d[3]-1044525330|0,l=(l<<22|l>>>10)+h|0,r+=(l&h|~l&m)+d[4]-176418897|0,r=(r<<7|r>>>25)+l|0,m+=(r&l|~r&h)+d[5]+1200080426|0,m=(m<<12|m>>>20)+r|0,h+=(m&r|~m&l)+d[6]-1473231341|0,h=(h<<17|h>>>15)+m|0,l+=(h&m|~h&r)+d[7]-45705983|0,l=(l<<22|l>>>10)+h|0,r+=(l&h|~l&m)+d[8]+1770035416|0,r=(r<<7|r>>>25)+l|0,m+=(r&l|~r&h)+d[9]-1958414417|0,m=(m<<12|m>>>20)+r|0,h+=(m&r|~m&l)+d[10]-42063|0,h=(h<<17|h>>>15)+m|0,l+=(h&m|~h&r)+d[11]-1990404162|0,l=(l<<22|l>>>10)+h|0,r+=(l&h|~l&m)+d[12]+1804603682|0,r=(r<<7|r>>>25)+l|0,m+=(r&l|~r&h)+d[13]-40341101|0,m=(m<<12|m>>>20)+r|0,h+=(m&r|~m&l)+d[14]-1502002290|0,h=(h<<17|h>>>15)+m|0,l+=(h&m|~h&r)+d[15]+1236535329|0,l=(l<<22|l>>>10)+h|0,r+=(l&m|h&~m)+d[1]-165796510|0,r=(r<<5|r>>>27)+l|0,m+=(r&h|l&~h)+d[6]-1069501632|0,m=(m<<9|m>>>23)+r|0,h+=(m&l|r&~l)+d[11]+643717713|0,h=(h<<14|h>>>18)+m|0,l+=(h&r|m&~r)+d[0]-373897302|0,l=(l<<20|l>>>12)+h|0,r+=(l&m|h&~m)+d[5]-701558691|0,r=(r<<5|r>>>27)+l|0,m+=(r&h|l&~h)+d[10]+38016083|0,m=(m<<9|m>>>23)+r|0,h+=(m&l|r&~l)+d[15]-660478335|0,h=(h<<14|h>>>18)+m|0,l+=(h&r|m&~r)+d[4]-405537848|0,l=(l<<20|l>>>12)+h|0,r+=(l&m|h&~m)+d[9]+568446438|0,r=(r<<5|r>>>27)+l|0,m+=(r&h|l&~h)+d[14]-1019803690|0,m=(m<<9|m>>>23)+r|0,h+=(m&l|r&~l)+d[3]-187363961|0,h=(h<<14|h>>>18)+m|0,l+=(h&r|m&~r)+d[8]+1163531501|0,l=(l<<20|l>>>12)+h|0,r+=(l&m|h&~m)+d[13]-1444681467|0,r=(r<<5|r>>>27)+l|0,m+=(r&h|l&~h)+d[2]-51403784|0,m=(m<<9|m>>>23)+r|0,h+=(m&l|r&~l)+d[7]+1735328473|0,h=(h<<14|h>>>18)+m|0,l+=(h&r|m&~r)+d[12]-1926607734|0,l=(l<<20|l>>>12)+h|0,r+=(l^h^m)+d[5]-378558|0,r=(r<<4|r>>>28)+l|0,m+=(r^l^h)+d[8]-2022574463|0,m=(m<<11|m>>>21)+r|0,h+=(m^r^l)+d[11]+1839030562|0,h=(h<<16|h>>>16)+m|0,l+=(h^m^r)+d[14]-35309556|0,l=(l<<23|l>>>9)+h|0,r+=(l^h^m)+d[1]-1530992060|0,r=(r<<4|r>>>28)+l|0,m+=(r^l^h)+d[4]+1272893353|0,m=(m<<11|m>>>21)+r|0,h+=(m^r^l)+d[7]-155497632|0,h=(h<<16|h>>>16)+m|0,l+=(h^m^r)+d[10]-1094730640|0,l=(l<<23|l>>>9)+h|0,r+=(l^h^m)+d[13]+681279174|0,r=(r<<4|r>>>28)+l|0,m+=(r^l^h)+d[0]-358537222|0,m=(m<<11|m>>>21)+r|0,h+=(m^r^l)+d[3]-722521979|0,h=(h<<16|h>>>16)+m|0,l+=(h^m^r)+d[6]+76029189|0,l=(l<<23|l>>>9)+h|0,r+=(l^h^m)+d[9]-640364487|0,r=(r<<4|r>>>28)+l|0,m+=(r^l^h)+d[12]-421815835|0,m=(m<<11|m>>>21)+r|0,h+=(m^r^l)+d[15]+530742520|0,h=(h<<16|h>>>16)+m|0,l+=(h^m^r)+d[2]-995338651|0,l=(l<<23|l>>>9)+h|0,r+=(h^(l|~m))+d[0]-198630844|0,r=(r<<6|r>>>26)+l|0,m+=(l^(r|~h))+d[7]+1126891415|0,m=(m<<10|m>>>22)+r|0,h+=(r^(m|~l))+d[14]-1416354905|0,h=(h<<15|h>>>17)+m|0,l+=(m^(h|~r))+d[5]-57434055|0,l=(l<<21|l>>>11)+h|0,r+=(h^(l|~m))+d[12]+1700485571|0,r=(r<<6|r>>>26)+l|0,m+=(l^(r|~h))+d[3]-1894986606|0,m=(m<<10|m>>>22)+r|0,h+=(r^(m|~l))+d[10]-1051523|0,h=(h<<15|h>>>17)+m|0,l+=(m^(h|~r))+d[1]-2054922799|0,l=(l<<21|l>>>11)+h|0,r+=(h^(l|~m))+d[8]+1873313359|0,r=(r<<6|r>>>26)+l|0,m+=(l^(r|~h))+d[15]-30611744|0,m=(m<<10|m>>>22)+r|0,h+=(r^(m|~l))+d[6]-1560198380|0,h=(h<<15|h>>>17)+m|0,l+=(m^(h|~r))+d[13]+1309151649|0,l=(l<<21|l>>>11)+h|0,r+=(h^(l|~m))+d[4]-145523070|0,r=(r<<6|r>>>26)+l|0,m+=(l^(r|~h))+d[11]-1120210379|0,m=(m<<10|m>>>22)+r|0,h+=(r^(m|~l))+d[2]+718787259|0,h=(h<<15|h>>>17)+m|0,l+=(m^(h|~r))+d[9]-343485551|0,l=(l<<21|l>>>11)+h|0,u[0]=r+u[0]|0,u[1]=l+u[1]|0,u[2]=h+u[2]|0,u[3]=m+u[3]|0}function a(u){var d=[],r;for(r=0;r<64;r+=4)d[r>>2]=u.charCodeAt(r)+(u.charCodeAt(r+1)<<8)+(u.charCodeAt(r+2)<<16)+(u.charCodeAt(r+3)<<24);return d}function s(u){var d=[],r;for(r=0;r<64;r+=4)d[r>>2]=u[r]+(u[r+1]<<8)+(u[r+2]<<16)+(u[r+3]<<24);return d}function v(u){var d=u.length,r=[1732584193,-271733879,-1732584194,271733878],l,h,m,V,H,I;for(l=64;l<=d;l+=64)i(r,a(u.substring(l-64,l)));for(u=u.substring(l-64),h=u.length,m=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],l=0;l<h;l+=1)m[l>>2]|=u.charCodeAt(l)<<(l%4<<3);if(m[l>>2]|=128<<(l%4<<3),l>55)for(i(r,m),l=0;l<16;l+=1)m[l]=0;return V=d*8,V=V.toString(16).match(/(.*?)(.{0,8})$/),H=parseInt(V[2],16),I=parseInt(V[1],16)||0,m[14]=H,m[15]=I,i(r,m),r}function p(u){var d=u.length,r=[1732584193,-271733879,-1732584194,271733878],l,h,m,V,H,I;for(l=64;l<=d;l+=64)i(r,s(u.subarray(l-64,l)));for(u=l-64<d?u.subarray(l-64):new Uint8Array(0),h=u.length,m=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],l=0;l<h;l+=1)m[l>>2]|=u[l]<<(l%4<<3);if(m[l>>2]|=128<<(l%4<<3),l>55)for(i(r,m),l=0;l<16;l+=1)m[l]=0;return V=d*8,V=V.toString(16).match(/(.*?)(.{0,8})$/),H=parseInt(V[2],16),I=parseInt(V[1],16)||0,m[14]=H,m[15]=I,i(r,m),r}function f(u){var d="",r;for(r=0;r<4;r+=1)d+=n[u>>r*8+4&15]+n[u>>r*8&15];return d}function M(u){var d;for(d=0;d<u.length;d+=1)u[d]=f(u[d]);return u.join("")}M(v("hello"))!=="5d41402abc4b2a76b9719d911017c592"&&(o=function(u,d){var r=(u&65535)+(d&65535),l=(u>>16)+(d>>16)+(r>>16);return l<<16|r&65535}),typeof ArrayBuffer<"u"&&!ArrayBuffer.prototype.slice&&function(){function u(d,r){return d=d|0||0,d<0?Math.max(d+r,0):Math.min(d,r)}ArrayBuffer.prototype.slice=function(d,r){var l=this.byteLength,h=u(d,l),m=l,V,H,I,J;return r!==e&&(m=u(r,l)),h>m?new ArrayBuffer(0):(V=m-h,H=new ArrayBuffer(V),I=new Uint8Array(H),J=new Uint8Array(this,h,V),I.set(J),H)}}();function c(u){return/[\u0080-\uFFFF]/.test(u)&&(u=unescape(encodeURIComponent(u))),u}function w(u,d){var r=u.length,l=new ArrayBuffer(r),h=new Uint8Array(l),m;for(m=0;m<r;m+=1)h[m]=u.charCodeAt(m);return d?h:l}function k(u){return String.fromCharCode.apply(null,new Uint8Array(u))}function y(u,d,r){var l=new Uint8Array(u.byteLength+d.byteLength);return l.set(new Uint8Array(u)),l.set(new Uint8Array(d),u.byteLength),r?l:l.buffer}function _(u){var d=[],r=u.length,l;for(l=0;l<r-1;l+=2)d.push(parseInt(u.substr(l,2),16));return String.fromCharCode.apply(String,d)}function x(){this.reset()}return x.prototype.append=function(u){return this.appendBinary(c(u)),this},x.prototype.appendBinary=function(u){this._buff+=u,this._length+=u.length;var d=this._buff.length,r;for(r=64;r<=d;r+=64)i(this._hash,a(this._buff.substring(r-64,r)));return this._buff=this._buff.substring(r-64),this},x.prototype.end=function(u){var d=this._buff,r=d.length,l,h=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],m;for(l=0;l<r;l+=1)h[l>>2]|=d.charCodeAt(l)<<(l%4<<3);return this._finish(h,r),m=M(this._hash),u&&(m=_(m)),this.reset(),m},x.prototype.reset=function(){return this._buff="",this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},x.prototype.getState=function(){return{buff:this._buff,length:this._length,hash:this._hash.slice()}},x.prototype.setState=function(u){return this._buff=u.buff,this._length=u.length,this._hash=u.hash,this},x.prototype.destroy=function(){delete this._hash,delete this._buff,delete this._length},x.prototype._finish=function(u,d){var r=d,l,h,m;if(u[r>>2]|=128<<(r%4<<3),r>55)for(i(this._hash,u),r=0;r<16;r+=1)u[r]=0;l=this._length*8,l=l.toString(16).match(/(.*?)(.{0,8})$/),h=parseInt(l[2],16),m=parseInt(l[1],16)||0,u[14]=h,u[15]=m,i(this._hash,u)},x.hash=function(u,d){return x.hashBinary(c(u),d)},x.hashBinary=function(u,d){var r=v(u),l=M(r);return d?_(l):l},x.ArrayBuffer=function(){this.reset()},x.ArrayBuffer.prototype.append=function(u){var d=y(this._buff.buffer,u,!0),r=d.length,l;for(this._length+=u.byteLength,l=64;l<=r;l+=64)i(this._hash,s(d.subarray(l-64,l)));return this._buff=l-64<r?new Uint8Array(d.buffer.slice(l-64)):new Uint8Array(0),this},x.ArrayBuffer.prototype.end=function(u){var d=this._buff,r=d.length,l=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],h,m;for(h=0;h<r;h+=1)l[h>>2]|=d[h]<<(h%4<<3);return this._finish(l,r),m=M(this._hash),u&&(m=_(m)),this.reset(),m},x.ArrayBuffer.prototype.reset=function(){return this._buff=new Uint8Array(0),this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},x.ArrayBuffer.prototype.getState=function(){var u=x.prototype.getState.call(this);return u.buff=k(u.buff),u},x.ArrayBuffer.prototype.setState=function(u){return u.buff=w(u.buff,!0),x.prototype.setState.call(this,u)},x.ArrayBuffer.prototype.destroy=x.prototype.destroy,x.ArrayBuffer.prototype._finish=x.prototype._finish,x.ArrayBuffer.hash=function(u,d){var r=p(new Uint8Array(u)),l=M(r);return d?_(l):l},x})});var F3=h2(De=>{"use strict";De.stringify=function(o){var n=[];n.push({obj:o});for(var t="",i,a,s,v,p,f,M,c,w,k,y;i=n.pop();)if(a=i.obj,s=i.prefix||"",v=i.val||"",t+=s,v)t+=v;else if(typeof a!="object")t+=typeof a>"u"?null:JSON.stringify(a);else if(a===null)t+="null";else if(Array.isArray(a)){for(n.push({val:"]"}),p=a.length-1;p>=0;p--)f=p===0?"":",",n.push({obj:a[p],prefix:f});n.push({val:"["})}else{M=[];for(c in a)a.hasOwnProperty(c)&&M.push(c);for(n.push({val:"}"}),p=M.length-1;p>=0;p--)w=M[p],k=a[w],y=p>0?",":"",y+=JSON.stringify(w)+":",n.push({obj:k,prefix:y});n.push({val:"{"})}return t};function z0(e,o,n){var t=n[n.length-1];e===t.element&&(n.pop(),t=n[n.length-1]);var i=t.element,a=t.index;if(Array.isArray(i))i.push(e);else if(a===o.length-2){var s=o.pop();i[s]=e}else o.push(e)}De.parse=function(e){for(var o=[],n=[],t=0,i,a,s,v,p,f,M,c,w;;){if(i=e[t++],i==="}"||i==="]"||typeof i>"u"){if(o.length===1)return o.pop();z0(o.pop(),o,n);continue}switch(i){case" ":case"	":case`
`:case":":case",":break;case"n":t+=3,z0(null,o,n);break;case"t":t+=3,z0(!0,o,n);break;case"f":t+=4,z0(!1,o,n);break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":case"-":for(a="",t--;;)if(s=e[t++],/[\d\.\-e\+]/.test(s))a+=s;else{t--;break}z0(parseFloat(a),o,n);break;case'"':for(v="",p=void 0,f=0;M=e[t++],M!=='"'||p==="\\"&&f%2===1;)v+=M,p=M,p==="\\"?f++:f=0;z0(JSON.parse('"'+v+'"'),o,n);break;case"[":c={element:[],index:o.length},o.push(c.element),n.push(c);break;case"{":w={element:{},index:o.length},o.push(w.element),n.push(w);break;default:throw new Error("unexpectedly reached end of input: "+i)}}}});var Y3=h2((tr,Oe)=>{"use strict";var y0=typeof Reflect=="object"?Reflect:null,N3=y0&&typeof y0.apply=="function"?y0.apply:function(o,n,t){return Function.prototype.apply.call(o,n,t)},W0;y0&&typeof y0.ownKeys=="function"?W0=y0.ownKeys:Object.getOwnPropertySymbols?W0=function(o){return Object.getOwnPropertyNames(o).concat(Object.getOwnPropertySymbols(o))}:W0=function(o){return Object.getOwnPropertyNames(o)};function li(e){console&&console.warn&&console.warn(e)}var $3=Number.isNaN||function(o){return o!==o};function x1(){x1.init.call(this)}Oe.exports=x1;Oe.exports.once=gi;x1.EventEmitter=x1;x1.prototype._events=void 0;x1.prototype._eventsCount=0;x1.prototype._maxListeners=void 0;var U3=10;function Z0(e){if(typeof e!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}Object.defineProperty(x1,"defaultMaxListeners",{enumerable:!0,get:function(){return U3},set:function(e){if(typeof e!="number"||e<0||$3(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");U3=e}});x1.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0};x1.prototype.setMaxListeners=function(o){if(typeof o!="number"||o<0||$3(o))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+o+".");return this._maxListeners=o,this};function G3(e){return e._maxListeners===void 0?x1.defaultMaxListeners:e._maxListeners}x1.prototype.getMaxListeners=function(){return G3(this)};x1.prototype.emit=function(o){for(var n=[],t=1;t<arguments.length;t++)n.push(arguments[t]);var i=o==="error",a=this._events;if(a!==void 0)i=i&&a.error===void 0;else if(!i)return!1;if(i){var s;if(n.length>0&&(s=n[0]),s instanceof Error)throw s;var v=new Error("Unhandled error."+(s?" ("+s.message+")":""));throw v.context=s,v}var p=a[o];if(p===void 0)return!1;if(typeof p=="function")N3(p,this,n);else for(var f=p.length,M=X3(p,f),t=0;t<f;++t)N3(M[t],this,n);return!0};function W3(e,o,n,t){var i,a,s;if(Z0(n),a=e._events,a===void 0?(a=e._events=Object.create(null),e._eventsCount=0):(a.newListener!==void 0&&(e.emit("newListener",o,n.listener?n.listener:n),a=e._events),s=a[o]),s===void 0)s=a[o]=n,++e._eventsCount;else if(typeof s=="function"?s=a[o]=t?[n,s]:[s,n]:t?s.unshift(n):s.push(n),i=G3(e),i>0&&s.length>i&&!s.warned){s.warned=!0;var v=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(o)+" listeners added. Use emitter.setMaxListeners() to increase limit");v.name="MaxListenersExceededWarning",v.emitter=e,v.type=o,v.count=s.length,li(v)}return e}x1.prototype.addListener=function(o,n){return W3(this,o,n,!1)};x1.prototype.on=x1.prototype.addListener;x1.prototype.prependListener=function(o,n){return W3(this,o,n,!0)};function ci(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function Z3(e,o,n){var t={fired:!1,wrapFn:void 0,target:e,type:o,listener:n},i=ci.bind(t);return i.listener=n,t.wrapFn=i,i}x1.prototype.once=function(o,n){return Z0(n),this.on(o,Z3(this,o,n)),this};x1.prototype.prependOnceListener=function(o,n){return Z0(n),this.prependListener(o,Z3(this,o,n)),this};x1.prototype.removeListener=function(o,n){var t,i,a,s,v;if(Z0(n),i=this._events,i===void 0)return this;if(t=i[o],t===void 0)return this;if(t===n||t.listener===n)--this._eventsCount===0?this._events=Object.create(null):(delete i[o],i.removeListener&&this.emit("removeListener",o,t.listener||n));else if(typeof t!="function"){for(a=-1,s=t.length-1;s>=0;s--)if(t[s]===n||t[s].listener===n){v=t[s].listener,a=s;break}if(a<0)return this;a===0?t.shift():di(t,a),t.length===1&&(i[o]=t[0]),i.removeListener!==void 0&&this.emit("removeListener",o,v||n)}return this};x1.prototype.off=x1.prototype.removeListener;x1.prototype.removeAllListeners=function(o){var n,t,i;if(t=this._events,t===void 0)return this;if(t.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):t[o]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete t[o]),this;if(arguments.length===0){var a=Object.keys(t),s;for(i=0;i<a.length;++i)s=a[i],s!=="removeListener"&&this.removeAllListeners(s);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(n=t[o],typeof n=="function")this.removeListener(o,n);else if(n!==void 0)for(i=n.length-1;i>=0;i--)this.removeListener(o,n[i]);return this};function K3(e,o,n){var t=e._events;if(t===void 0)return[];var i=t[o];return i===void 0?[]:typeof i=="function"?n?[i.listener||i]:[i]:n?vi(i):X3(i,i.length)}x1.prototype.listeners=function(o){return K3(this,o,!0)};x1.prototype.rawListeners=function(o){return K3(this,o,!1)};x1.listenerCount=function(e,o){return typeof e.listenerCount=="function"?e.listenerCount(o):Q3.call(e,o)};x1.prototype.listenerCount=Q3;function Q3(e){var o=this._events;if(o!==void 0){var n=o[e];if(typeof n=="function")return 1;if(n!==void 0)return n.length}return 0}x1.prototype.eventNames=function(){return this._eventsCount>0?W0(this._events):[]};function X3(e,o){for(var n=new Array(o),t=0;t<o;++t)n[t]=e[t];return n}function di(e,o){for(;o+1<e.length;o++)e[o]=e[o+1];e.pop()}function vi(e){for(var o=new Array(e.length),n=0;n<o.length;++n)o[n]=e[n].listener||e[n];return o}function gi(e,o){return new Promise(function(n,t){function i(s){e.removeListener(o,a),t(s)}function a(){typeof e.removeListener=="function"&&e.removeListener("error",i),n([].slice.call(arguments))}J3(e,o,a,{once:!0}),o!=="error"&&hi(e,i,{once:!0})})}function hi(e,o,n){typeof e.on=="function"&&J3(e,"error",o,n)}function J3(e,o,n,t){if(typeof e.on=="function")t.once?e.once(o,n):e.on(o,n);else if(typeof e.addEventListener=="function")e.addEventListener(o,function i(a){t.once&&e.removeEventListener(o,i),n(a)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}});var _3=(e,o)=>n1(null,null,function*(){if(!(typeof window>"u"))return yield M3(),k3(JSON.parse('[["ion-menu_3",[[33,"ion-menu-button",{"color":[513],"disabled":[4],"menu":[1],"autoHide":[4,"auto-hide"],"type":[1],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]],[33,"ion-menu",{"contentId":[513,"content-id"],"menuId":[513,"menu-id"],"type":[1025],"disabled":[1028],"side":[513],"swipeGesture":[4,"swipe-gesture"],"maxEdgeStart":[2,"max-edge-start"],"isPaneVisible":[32],"isEndSide":[32],"isOpen":[64],"isActive":[64],"open":[64],"close":[64],"toggle":[64],"setOpen":[64]},[[16,"ionSplitPaneVisible","onSplitPaneChanged"],[2,"click","onBackdropClick"]],{"type":["typeChanged"],"disabled":["disabledChanged"],"side":["sideChanged"],"swipeGesture":["swipeGestureChanged"]}],[1,"ion-menu-toggle",{"menu":[1],"autoHide":[4,"auto-hide"],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]]]],["ion-input-password-toggle",[[33,"ion-input-password-toggle",{"color":[513],"showIcon":[1,"show-icon"],"hideIcon":[1,"hide-icon"],"type":[1025]},null,{"type":["onTypeChange"]}]]],["ion-fab_3",[[33,"ion-fab-button",{"color":[513],"activated":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1],"show":[4],"translucent":[4],"type":[1],"size":[1],"closeIcon":[1,"close-icon"]}],[1,"ion-fab",{"horizontal":[1],"vertical":[1],"edge":[4],"activated":[1028],"close":[64],"toggle":[64]},null,{"activated":["activatedChanged"]}],[1,"ion-fab-list",{"activated":[4],"side":[1]},null,{"activated":["activatedChanged"]}]]],["ion-refresher_2",[[0,"ion-refresher-content",{"pullingIcon":[1025,"pulling-icon"],"pullingText":[1,"pulling-text"],"refreshingSpinner":[1025,"refreshing-spinner"],"refreshingText":[1,"refreshing-text"]}],[32,"ion-refresher",{"pullMin":[2,"pull-min"],"pullMax":[2,"pull-max"],"closeDuration":[1,"close-duration"],"snapbackDuration":[1,"snapback-duration"],"pullFactor":[2,"pull-factor"],"disabled":[4],"nativeRefresher":[32],"state":[32],"complete":[64],"cancel":[64],"getProgress":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-back-button",[[33,"ion-back-button",{"color":[513],"defaultHref":[1025,"default-href"],"disabled":[516],"icon":[1],"text":[1],"type":[1],"routerAnimation":[16,"router-animation"]}]]],["ion-toast",[[33,"ion-toast",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"color":[513],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"cssClass":[1,"css-class"],"duration":[2],"header":[1],"layout":[1],"message":[1],"keyboardClose":[4,"keyboard-close"],"position":[1],"positionAnchor":[1,"position-anchor"],"buttons":[16],"translucent":[4],"animated":[4],"icon":[1],"htmlAttributes":[16,"html-attributes"],"swipeGesture":[1,"swipe-gesture"],"isOpen":[4,"is-open"],"trigger":[1],"revealContentToScreenReader":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"swipeGesture":["swipeGestureChanged"],"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-card_5",[[33,"ion-card",{"color":[513],"button":[4],"type":[1],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1]}],[32,"ion-card-content"],[33,"ion-card-header",{"color":[513],"translucent":[4]}],[33,"ion-card-subtitle",{"color":[513]}],[33,"ion-card-title",{"color":[513]}]]],["ion-item-option_3",[[33,"ion-item-option",{"color":[513],"disabled":[4],"download":[1],"expandable":[4],"href":[1],"rel":[1],"target":[1],"type":[1]}],[32,"ion-item-options",{"side":[1],"fireSwipeEvent":[64]}],[0,"ion-item-sliding",{"disabled":[4],"state":[32],"getOpenAmount":[64],"getSlidingRatio":[64],"open":[64],"close":[64],"closeOpened":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-accordion_2",[[49,"ion-accordion",{"value":[1],"disabled":[4],"readonly":[4],"toggleIcon":[1,"toggle-icon"],"toggleIconSlot":[1,"toggle-icon-slot"],"state":[32],"isNext":[32],"isPrevious":[32]},null,{"value":["valueChanged"]}],[33,"ion-accordion-group",{"animated":[4],"multiple":[4],"value":[1025],"disabled":[4],"readonly":[4],"expand":[1],"requestAccordionToggle":[64],"getAccordions":[64]},[[0,"keydown","onKeydown"]],{"value":["valueChanged"],"disabled":["disabledChanged"],"readonly":["readonlyChanged"]}]]],["ion-infinite-scroll_2",[[32,"ion-infinite-scroll-content",{"loadingSpinner":[1025,"loading-spinner"],"loadingText":[1,"loading-text"]}],[0,"ion-infinite-scroll",{"threshold":[1],"disabled":[4],"position":[1],"isLoading":[32],"complete":[64]},null,{"threshold":["thresholdChanged"],"disabled":["disabledChanged"]}]]],["ion-reorder_2",[[33,"ion-reorder",null,[[2,"click","onClick"]]],[0,"ion-reorder-group",{"disabled":[4],"state":[32],"complete":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-segment_2",[[33,"ion-segment-button",{"contentId":[513,"content-id"],"disabled":[1028],"layout":[1],"type":[1],"value":[8],"checked":[32],"setFocus":[64]},null,{"value":["valueChanged"]}],[33,"ion-segment",{"color":[513],"disabled":[4],"scrollable":[4],"swipeGesture":[4,"swipe-gesture"],"value":[1032],"selectOnFocus":[4,"select-on-focus"],"activated":[32]},[[16,"ionSegmentViewScroll","handleSegmentViewScroll"],[0,"keydown","onKeyDown"]],{"color":["colorChanged"],"swipeGesture":["swipeGestureChanged"],"value":["valueChanged"],"disabled":["disabledChanged"]}]]],["ion-chip",[[33,"ion-chip",{"color":[513],"outline":[4],"disabled":[4]}]]],["ion-input",[[38,"ion-input",{"color":[513],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"autofocus":[4],"clearInput":[4,"clear-input"],"clearInputIcon":[1,"clear-input-icon"],"clearOnEdit":[4,"clear-on-edit"],"counter":[4],"counterFormatter":[16,"counter-formatter"],"debounce":[2],"disabled":[516],"enterkeyhint":[1],"errorText":[1,"error-text"],"fill":[1],"inputmode":[1],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"max":[8],"maxlength":[2],"min":[8],"minlength":[2],"multiple":[4],"name":[1],"pattern":[1],"placeholder":[1],"readonly":[516],"required":[4],"shape":[1],"spellcheck":[4],"step":[1],"type":[1],"value":[1032],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},[[2,"click","onClickCapture"]],{"debounce":["debounceChanged"],"type":["onTypeChange"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-searchbar",[[34,"ion-searchbar",{"color":[513],"animated":[4],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"cancelButtonIcon":[1,"cancel-button-icon"],"cancelButtonText":[1,"cancel-button-text"],"clearIcon":[1,"clear-icon"],"debounce":[2],"disabled":[4],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"searchIcon":[1,"search-icon"],"showCancelButton":[1,"show-cancel-button"],"showClearButton":[1,"show-clear-button"],"spellcheck":[4],"type":[1],"value":[1025],"focused":[32],"noAnimate":[32],"setFocus":[64],"getInputElement":[64]},null,{"lang":["onLangChanged"],"dir":["onDirChanged"],"debounce":["debounceChanged"],"value":["valueChanged"],"showCancelButton":["showCancelButtonChanged"]}]]],["ion-toggle",[[33,"ion-toggle",{"color":[513],"name":[1],"checked":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[1],"enableOnOffLabels":[4,"enable-on-off-labels"],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"activated":[32]},null,{"disabled":["disabledChanged"]}]]],["ion-nav_2",[[1,"ion-nav",{"delegate":[16],"swipeGesture":[1028,"swipe-gesture"],"animated":[4],"animation":[16],"rootParams":[16,"root-params"],"root":[1],"push":[64],"insert":[64],"insertPages":[64],"pop":[64],"popTo":[64],"popToRoot":[64],"removeIndex":[64],"setRoot":[64],"setPages":[64],"setRouteId":[64],"getRouteId":[64],"getActive":[64],"getByIndex":[64],"canGoBack":[64],"getPrevious":[64],"getLength":[64]},null,{"swipeGesture":["swipeGestureChanged"],"root":["rootChanged"]}],[0,"ion-nav-link",{"component":[1],"componentProps":[16,"component-props"],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"]}]]],["ion-tab_2",[[1,"ion-tab",{"active":[1028],"delegate":[16],"tab":[1],"component":[1],"setActive":[64]},null,{"active":["changeActive"]}],[1,"ion-tabs",{"useRouter":[1028,"use-router"],"selectedTab":[32],"select":[64],"getTab":[64],"getSelected":[64],"setRouteId":[64],"getRouteId":[64]}]]],["ion-textarea",[[38,"ion-textarea",{"color":[513],"autocapitalize":[1],"autofocus":[4],"clearOnEdit":[4,"clear-on-edit"],"debounce":[2],"disabled":[4],"fill":[1],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"readonly":[4],"required":[4],"spellcheck":[4],"cols":[514],"rows":[2],"wrap":[1],"autoGrow":[516,"auto-grow"],"value":[1025],"counter":[4],"counterFormatter":[16,"counter-formatter"],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"shape":[1],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},[[2,"click","onClickCapture"]],{"debounce":["debounceChanged"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-backdrop",[[33,"ion-backdrop",{"visible":[4],"tappable":[4],"stopPropagation":[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]]],["ion-loading",[[34,"ion-loading",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"message":[1],"cssClass":[1,"css-class"],"duration":[2],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"spinner":[1025],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-breadcrumb_2",[[33,"ion-breadcrumb",{"collapsed":[4],"last":[4],"showCollapsedIndicator":[4,"show-collapsed-indicator"],"color":[1],"active":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"separator":[4],"target":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"]}],[33,"ion-breadcrumbs",{"color":[513],"maxItems":[2,"max-items"],"itemsBeforeCollapse":[2,"items-before-collapse"],"itemsAfterCollapse":[2,"items-after-collapse"],"collapsed":[32],"activeChanged":[32]},[[0,"collapsedClick","onCollapsedClick"]],{"maxItems":["maxItemsChanged"],"itemsBeforeCollapse":["maxItemsChanged"],"itemsAfterCollapse":["maxItemsChanged"]}]]],["ion-tab-bar_2",[[33,"ion-tab-button",{"disabled":[4],"download":[1],"href":[1],"rel":[1],"layout":[1025],"selected":[1028],"tab":[1],"target":[1]},[[8,"ionTabBarChanged","onTabBarChanged"]]],[33,"ion-tab-bar",{"color":[513],"selectedTab":[1,"selected-tab"],"translucent":[4],"keyboardVisible":[32]},null,{"selectedTab":["selectedTabChanged"]}]]],["ion-datetime-button",[[33,"ion-datetime-button",{"color":[513],"disabled":[516],"datetime":[1],"datetimePresentation":[32],"dateText":[32],"timeText":[32],"datetimeActive":[32],"selectedButton":[32]}]]],["ion-route_4",[[0,"ion-route",{"url":[1],"component":[1],"componentProps":[16,"component-props"],"beforeLeave":[16,"before-leave"],"beforeEnter":[16,"before-enter"]},null,{"url":["onUpdate"],"component":["onUpdate"],"componentProps":["onComponentProps"]}],[0,"ion-route-redirect",{"from":[1],"to":[1]},null,{"from":["propDidChange"],"to":["propDidChange"]}],[0,"ion-router",{"root":[1],"useHash":[4,"use-hash"],"canTransition":[64],"push":[64],"back":[64],"printDebug":[64],"navChanged":[64]},[[8,"popstate","onPopState"],[4,"ionBackButton","onBackButton"]]],[1,"ion-router-link",{"color":[513],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1]}]]],["ion-avatar_3",[[33,"ion-avatar"],[33,"ion-badge",{"color":[513]}],[1,"ion-thumbnail"]]],["ion-col_3",[[1,"ion-col",{"offset":[1],"offsetXs":[1,"offset-xs"],"offsetSm":[1,"offset-sm"],"offsetMd":[1,"offset-md"],"offsetLg":[1,"offset-lg"],"offsetXl":[1,"offset-xl"],"pull":[1],"pullXs":[1,"pull-xs"],"pullSm":[1,"pull-sm"],"pullMd":[1,"pull-md"],"pullLg":[1,"pull-lg"],"pullXl":[1,"pull-xl"],"push":[1],"pushXs":[1,"push-xs"],"pushSm":[1,"push-sm"],"pushMd":[1,"push-md"],"pushLg":[1,"push-lg"],"pushXl":[1,"push-xl"],"size":[1],"sizeXs":[1,"size-xs"],"sizeSm":[1,"size-sm"],"sizeMd":[1,"size-md"],"sizeLg":[1,"size-lg"],"sizeXl":[1,"size-xl"]},[[9,"resize","onResize"]]],[1,"ion-grid",{"fixed":[4]}],[1,"ion-row"]]],["ion-img",[[1,"ion-img",{"alt":[1],"src":[1],"loadSrc":[32],"loadError":[32]},null,{"src":["srcChanged"]}]]],["ion-input-otp",[[38,"ion-input-otp",{"autocapitalize":[1],"color":[513],"disabled":[516],"fill":[1],"inputmode":[1],"length":[2],"pattern":[1],"readonly":[516],"separators":[1],"shape":[1],"size":[1],"type":[1],"value":[1032],"inputValues":[32],"hasFocus":[32],"previousInputValues":[32],"setFocus":[64]},null,{"value":["valueChanged"],"separators":["processSeparators"],"length":["processSeparators"]}]]],["ion-progress-bar",[[33,"ion-progress-bar",{"type":[1],"reversed":[4],"value":[2],"buffer":[2],"color":[513]}]]],["ion-range",[[33,"ion-range",{"color":[513],"debounce":[2],"name":[1],"label":[1],"dualKnobs":[4,"dual-knobs"],"min":[2],"max":[2],"pin":[4],"pinFormatter":[16,"pin-formatter"],"snaps":[4],"step":[2],"ticks":[4],"activeBarStart":[1026,"active-bar-start"],"disabled":[4],"value":[1026],"labelPlacement":[1,"label-placement"],"ratioA":[32],"ratioB":[32],"pressedKnob":[32]},null,{"debounce":["debounceChanged"],"min":["minChanged"],"max":["maxChanged"],"step":["stepChanged"],"activeBarStart":["activeBarStartChanged"],"disabled":["disabledChanged"],"value":["valueChanged"]}]]],["ion-segment-content",[[1,"ion-segment-content"]]],["ion-segment-view",[[33,"ion-segment-view",{"disabled":[4],"isManualScroll":[32],"setContent":[64]},[[1,"scroll","handleScroll"],[1,"touchstart","handleScrollStart"],[1,"touchend","handleTouchEnd"]]]]],["ion-split-pane",[[33,"ion-split-pane",{"contentId":[513,"content-id"],"disabled":[4],"when":[8],"visible":[32],"isVisible":[64]},null,{"visible":["visibleChanged"],"disabled":["updateState"],"when":["updateState"]}]]],["ion-text",[[1,"ion-text",{"color":[513]}]]],["ion-select-modal",[[34,"ion-select-modal",{"header":[1],"multiple":[4],"options":[16]}]]],["ion-datetime_3",[[33,"ion-datetime",{"color":[1],"name":[1],"disabled":[4],"formatOptions":[16,"format-options"],"readonly":[4],"isDateEnabled":[16,"is-date-enabled"],"showAdjacentDays":[4,"show-adjacent-days"],"min":[1025],"max":[1025],"presentation":[1],"cancelText":[1,"cancel-text"],"doneText":[1,"done-text"],"clearText":[1,"clear-text"],"yearValues":[8,"year-values"],"monthValues":[8,"month-values"],"dayValues":[8,"day-values"],"hourValues":[8,"hour-values"],"minuteValues":[8,"minute-values"],"locale":[1],"firstDayOfWeek":[2,"first-day-of-week"],"titleSelectedDatesFormatter":[16,"title-selected-dates-formatter"],"multiple":[4],"highlightedDates":[16,"highlighted-dates"],"value":[1025],"showDefaultTitle":[4,"show-default-title"],"showDefaultButtons":[4,"show-default-buttons"],"showClearButton":[4,"show-clear-button"],"showDefaultTimeLabel":[4,"show-default-time-label"],"hourCycle":[1,"hour-cycle"],"size":[1],"preferWheel":[4,"prefer-wheel"],"showMonthAndYear":[32],"activeParts":[32],"workingParts":[32],"isTimePopoverOpen":[32],"forceRenderDate":[32],"confirm":[64],"reset":[64],"cancel":[64]},null,{"formatOptions":["formatOptionsChanged"],"disabled":["disabledChanged"],"min":["minChanged"],"max":["maxChanged"],"presentation":["presentationChanged"],"yearValues":["yearValuesChanged"],"monthValues":["monthValuesChanged"],"dayValues":["dayValuesChanged"],"hourValues":["hourValuesChanged"],"minuteValues":["minuteValuesChanged"],"value":["valueChanged"]}],[34,"ion-picker-legacy",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"buttons":[16],"columns":[16],"cssClass":[1,"css-class"],"duration":[2],"showBackdrop":[4,"show-backdrop"],"backdropDismiss":[4,"backdrop-dismiss"],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"getColumn":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}],[32,"ion-picker-legacy-column",{"col":[16]},null,{"col":["colChanged"]}]]],["ion-action-sheet",[[34,"ion-action-sheet",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"buttons":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"header":[1],"subHeader":[1,"sub-header"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-alert",[[34,"ion-alert",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"cssClass":[1,"css-class"],"header":[1],"subHeader":[1,"sub-header"],"message":[1],"buttons":[16],"inputs":[1040],"backdropDismiss":[4,"backdrop-dismiss"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},[[4,"keydown","onKeydown"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"],"buttons":["buttonsChanged"],"inputs":["inputsChanged"]}]]],["ion-modal",[[33,"ion-modal",{"hasController":[4,"has-controller"],"overlayIndex":[2,"overlay-index"],"delegate":[16],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"breakpoints":[16],"expandToScroll":[4,"expand-to-scroll"],"initialBreakpoint":[2,"initial-breakpoint"],"backdropBreakpoint":[2,"backdrop-breakpoint"],"handle":[4],"handleBehavior":[1,"handle-behavior"],"component":[1],"componentProps":[16,"component-props"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"animated":[4],"presentingElement":[16,"presenting-element"],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"keepContentsMounted":[4,"keep-contents-mounted"],"focusTrap":[4,"focus-trap"],"canDismiss":[4,"can-dismiss"],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"setCurrentBreakpoint":[64],"getCurrentBreakpoint":[64]},[[9,"resize","onWindowResize"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-picker",[[33,"ion-picker",{"exitInputMode":[64]},[[1,"touchstart","preventTouchStartPropagation"]]]]],["ion-picker-column",[[1,"ion-picker-column",{"disabled":[4],"value":[1032],"color":[513],"numericInput":[4,"numeric-input"],"ariaLabel":[32],"isActive":[32],"scrollActiveItemIntoView":[64],"setValue":[64],"setFocus":[64]},null,{"aria-label":["ariaLabelChanged"],"value":["valueChange"]}]]],["ion-picker-column-option",[[33,"ion-picker-column-option",{"disabled":[4],"value":[8],"color":[513],"ariaLabel":[32]},null,{"aria-label":["onAriaLabelChange"]}]]],["ion-popover",[[33,"ion-popover",{"hasController":[4,"has-controller"],"delegate":[16],"overlayIndex":[2,"overlay-index"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"component":[1],"componentProps":[16,"component-props"],"keyboardClose":[4,"keyboard-close"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"event":[8],"showBackdrop":[4,"show-backdrop"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"triggerAction":[1,"trigger-action"],"trigger":[1],"size":[1],"dismissOnSelect":[4,"dismiss-on-select"],"reference":[1],"side":[1],"alignment":[1025],"arrow":[4],"isOpen":[4,"is-open"],"keyboardEvents":[4,"keyboard-events"],"focusTrap":[4,"focus-trap"],"keepContentsMounted":[4,"keep-contents-mounted"],"presented":[32],"presentFromTrigger":[64],"present":[64],"dismiss":[64],"getParentPopover":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"trigger":["onTriggerChange"],"triggerAction":["onTriggerChange"],"isOpen":["onIsOpenChange"]}]]],["ion-checkbox",[[33,"ion-checkbox",{"color":[513],"name":[1],"checked":[1028],"indeterminate":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"setFocus":[64]}]]],["ion-item_8",[[33,"ion-item-divider",{"color":[513],"sticky":[4]}],[32,"ion-item-group"],[33,"ion-note",{"color":[513]}],[1,"ion-skeleton-text",{"animated":[4]}],[38,"ion-label",{"color":[513],"position":[1],"noAnimate":[32]},null,{"color":["colorChanged"],"position":["positionChanged"]}],[33,"ion-list-header",{"color":[513],"lines":[1]}],[33,"ion-item",{"color":[513],"button":[4],"detail":[4],"detailIcon":[1,"detail-icon"],"disabled":[516],"download":[1],"href":[1],"rel":[1],"lines":[1],"routerAnimation":[16,"router-animation"],"routerDirection":[1,"router-direction"],"target":[1],"type":[1],"multipleInputs":[32],"focusable":[32],"isInteractive":[32]},[[0,"ionColor","labelColorChanged"],[0,"ionStyle","itemStyle"]],{"button":["buttonChanged"]}],[32,"ion-list",{"lines":[1],"inset":[4],"closeSlidingItems":[64]}]]],["ion-app_8",[[0,"ion-app",{"setFocus":[64]}],[36,"ion-footer",{"collapse":[1],"translucent":[4],"keyboardVisible":[32]}],[1,"ion-router-outlet",{"mode":[1025],"delegate":[16],"animated":[4],"animation":[16],"swipeHandler":[16,"swipe-handler"],"commit":[64],"setRouteId":[64],"getRouteId":[64]},null,{"swipeHandler":["swipeHandlerChanged"]}],[1,"ion-content",{"color":[513],"fullscreen":[4],"fixedSlotPlacement":[1,"fixed-slot-placement"],"forceOverscroll":[1028,"force-overscroll"],"scrollX":[4,"scroll-x"],"scrollY":[4,"scroll-y"],"scrollEvents":[4,"scroll-events"],"getScrollElement":[64],"getBackgroundElement":[64],"scrollToTop":[64],"scrollToBottom":[64],"scrollByPoint":[64],"scrollToPoint":[64]},[[9,"resize","onResize"]]],[36,"ion-header",{"collapse":[1],"translucent":[4]}],[33,"ion-title",{"color":[513],"size":[1]},null,{"size":["sizeChanged"]}],[33,"ion-toolbar",{"color":[513]},[[0,"ionStyle","childrenStyle"]]],[38,"ion-buttons",{"collapse":[4]}]]],["ion-select_3",[[33,"ion-select",{"cancelText":[1,"cancel-text"],"color":[513],"compareWith":[1,"compare-with"],"disabled":[4],"fill":[1],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"interface":[1],"interfaceOptions":[8,"interface-options"],"justify":[1],"label":[1],"labelPlacement":[1,"label-placement"],"multiple":[4],"name":[1],"okText":[1,"ok-text"],"placeholder":[1],"selectedText":[1,"selected-text"],"toggleIcon":[1,"toggle-icon"],"expandedIcon":[1,"expanded-icon"],"shape":[1],"value":[1032],"required":[4],"isExpanded":[32],"hasFocus":[32],"open":[64]},null,{"disabled":["styleChanged"],"isExpanded":["styleChanged"],"placeholder":["styleChanged"],"value":["styleChanged"]}],[1,"ion-select-option",{"disabled":[4],"value":[8]}],[34,"ion-select-popover",{"header":[1],"subHeader":[1,"sub-header"],"message":[1],"multiple":[4],"options":[16]}]]],["ion-spinner",[[1,"ion-spinner",{"color":[513],"duration":[2],"name":[1],"paused":[4]}]]],["ion-radio_2",[[33,"ion-radio",{"color":[513],"name":[1],"disabled":[4],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"checked":[32],"buttonTabindex":[32],"setFocus":[64],"setButtonTabindex":[64]},null,{"value":["valueChanged"]}],[36,"ion-radio-group",{"allowEmptySelection":[4,"allow-empty-selection"],"compareWith":[1,"compare-with"],"name":[1],"value":[1032],"helperText":[1,"helper-text"],"errorText":[1,"error-text"],"setFocus":[64]},[[4,"keydown","onKeydown"]],{"value":["valueChanged"]}]]],["ion-ripple-effect",[[1,"ion-ripple-effect",{"type":[1],"addRipple":[64]}]]],["ion-button_2",[[33,"ion-button",{"color":[513],"buttonType":[1025,"button-type"],"disabled":[516],"expand":[513],"fill":[1537],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"download":[1],"href":[1],"rel":[1],"shape":[513],"size":[513],"strong":[4],"target":[1],"type":[1],"form":[1],"isCircle":[32]},null,{"disabled":["disabledChanged"],"aria-checked":["onAriaChanged"],"aria-label":["onAriaChanged"]}],[1,"ion-icon",{"mode":[1025],"color":[1],"ios":[1],"md":[1],"flipRtl":[4,"flip-rtl"],"name":[513],"src":[1],"icon":[8],"size":[1],"lazy":[4],"sanitize":[4],"svgContent":[32],"isVisible":[32]},null,{"name":["loadIcon"],"src":["loadIcon"],"icon":["loadIcon"],"ios":["loadIcon"],"md":["loadIcon"]}]]]]'),o)});(function(){if(typeof window<"u"&&window.Reflect!==void 0&&window.customElements!==void 0){var e=HTMLElement;window.HTMLElement=function(){return Reflect.construct(e,[],this.constructor)},HTMLElement.prototype=e.prototype,HTMLElement.prototype.constructor=HTMLElement,Object.setPrototypeOf(HTMLElement,e)}})();var N=["*"],_n=["outletContent"],Bn=["outlet"],bn=[[["","slot","top"]],"*",[["ion-tab"]]],Ln=["[slot=top]","*","ion-tab"];function Sn(e,o){if(e&1){let n=E1();L(0,"ion-router-outlet",5,1),u1("stackWillChange",function(i){y1(n);let a=v1();return C1(a.onStackWillChange(i))})("stackDidChange",function(i){y1(n);let a=v1();return C1(a.onStackDidChange(i))}),b()}}function An(e,o){e&1&&E(0,2,["*ngIf","tabs.length > 0"])}function jn(e,o){if(e&1&&(L(0,"div",1),f2(1,2),b()),e&2){let n=v1();X(),t1("ngTemplateOutlet",n.template)}}function In(e,o){if(e&1&&f2(0,1),e&2){let n=v1();t1("ngTemplateOutlet",n.template)}}var Vn=(()=>{class e extends V0{constructor(n,t){super(n,t)}writeValue(n){this.elementRef.nativeElement.checked=this.lastValue=n,x3(this.elementRef)}_handleIonChange(n){this.handleValueChange(n,n.checked)}static \u0275fac=function(t){return new(t||e)(g(n0),g(O))};static \u0275dir=o0({type:e,selectors:[["ion-checkbox"],["ion-toggle"]],hostBindings:function(t,i){t&1&&u1("ionChange",function(s){return i._handleIonChange(s.target)})},standalone:!1,features:[l0([{provide:I0,useExisting:e,multi:!0}]),H1]})}return e})(),se=(()=>{class e extends V0{el;constructor(n,t){super(n,t),this.el=t}handleInputEvent(n){this.handleValueChange(n,n.value)}registerOnChange(n){this.el.nativeElement.tagName==="ION-INPUT"||this.el.nativeElement.tagName==="ION-INPUT-OTP"?super.registerOnChange(t=>{n(t===""?null:parseFloat(t))}):super.registerOnChange(n)}static \u0275fac=function(t){return new(t||e)(g(n0),g(O))};static \u0275dir=o0({type:e,selectors:[["ion-input","type","number"],["ion-input-otp",3,"type","text"],["ion-range"]],hostBindings:function(t,i){t&1&&u1("ionInput",function(s){return i.handleInputEvent(s.target)})},standalone:!1,features:[l0([{provide:I0,useExisting:e,multi:!0}]),H1]})}return e})(),re=(()=>{class e extends V0{constructor(n,t){super(n,t)}_handleChangeEvent(n){this.handleValueChange(n,n.value)}static \u0275fac=function(t){return new(t||e)(g(n0),g(O))};static \u0275dir=o0({type:e,selectors:[["ion-select"],["ion-radio-group"],["ion-segment"],["ion-datetime"]],hostBindings:function(t,i){t&1&&u1("ionChange",function(s){return i._handleChangeEvent(s.target)})},standalone:!1,features:[l0([{provide:I0,useExisting:e,multi:!0}]),H1]})}return e})(),le=(()=>{class e extends V0{constructor(n,t){super(n,t)}_handleInputEvent(n){this.handleValueChange(n,n.value)}static \u0275fac=function(t){return new(t||e)(g(n0),g(O))};static \u0275dir=o0({type:e,selectors:[["ion-input",3,"type","number"],["ion-input-otp","type","text"],["ion-textarea"],["ion-searchbar"]],hostBindings:function(t,i){t&1&&u1("ionInput",function(s){return i._handleInputEvent(s.target)})},standalone:!1,features:[l0([{provide:I0,useExisting:e,multi:!0}]),H1]})}return e})(),Hn=(e,o)=>{let n=e.prototype;o.forEach(t=>{Object.defineProperty(n,t,{get(){return this.el[t]},set(i){this.z.runOutsideAngular(()=>this.el[t]=i)},configurable:!0})})},Dn=(e,o)=>{let n=e.prototype;o.forEach(t=>{n[t]=function(){let i=arguments;return this.z.runOutsideAngular(()=>this.el[t].apply(this.el,i))}})},h1=(e,o,n)=>{n.forEach(t=>e[t]=Vt(o,t))};function $(e){return function(n){let{defineCustomElementFn:t,inputs:i,methods:a}=e;return t!==void 0&&t(),i&&Hn(n,i),a&&Dn(n,a),n}}var On=(()=>{let e=class B2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||B2)(g(F),g(O),g(T))};static \u0275cmp=D({type:B2,selectors:[["ion-accordion"]],inputs:{disabled:"disabled",mode:"mode",readonly:"readonly",toggleIcon:"toggleIcon",toggleIconSlot:"toggleIconSlot",value:"value"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["disabled","mode","readonly","toggleIcon","toggleIconSlot","value"]})],e),e})(),En=(()=>{let e=class b2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionChange"])}static \u0275fac=function(t){return new(t||b2)(g(F),g(O),g(T))};static \u0275cmp=D({type:b2,selectors:[["ion-accordion-group"]],inputs:{animated:"animated",disabled:"disabled",expand:"expand",mode:"mode",multiple:"multiple",readonly:"readonly",value:"value"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["animated","disabled","expand","mode","multiple","readonly","value"]})],e),e})(),Pn=(()=>{let e=class L2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionActionSheetDidPresent","ionActionSheetWillPresent","ionActionSheetWillDismiss","ionActionSheetDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(t){return new(t||L2)(g(F),g(O),g(T))};static \u0275cmp=D({type:L2,selectors:[["ion-action-sheet"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),Tn=(()=>{let e=class S2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionAlertDidPresent","ionAlertWillPresent","ionAlertWillDismiss","ionAlertDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(t){return new(t||S2)(g(F),g(O),g(T))};static \u0275cmp=D({type:S2,selectors:[["ion-alert"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",inputs:"inputs",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","inputs","isOpen","keyboardClose","leaveAnimation","message","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),Rn=(()=>{let e=class A2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||A2)(g(F),g(O),g(T))};static \u0275cmp=D({type:A2,selectors:[["ion-app"]],standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({methods:["setFocus"]})],e),e})(),qn=(()=>{let e=class j2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||j2)(g(F),g(O),g(T))};static \u0275cmp=D({type:j2,selectors:[["ion-avatar"]],standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({})],e),e})(),Fn=(()=>{let e=class I2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionBackdropTap"])}static \u0275fac=function(t){return new(t||I2)(g(F),g(O),g(T))};static \u0275cmp=D({type:I2,selectors:[["ion-backdrop"]],inputs:{stopPropagation:"stopPropagation",tappable:"tappable",visible:"visible"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["stopPropagation","tappable","visible"]})],e),e})(),Nn=(()=>{let e=class V2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||V2)(g(F),g(O),g(T))};static \u0275cmp=D({type:V2,selectors:[["ion-badge"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","mode"]})],e),e})(),Un=(()=>{let e=class H2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||H2)(g(F),g(O),g(T))};static \u0275cmp=D({type:H2,selectors:[["ion-breadcrumb"]],inputs:{active:"active",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",separator:"separator",target:"target"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["active","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","separator","target"]})],e),e})(),$n=(()=>{let e=class D2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionCollapsedClick"])}static \u0275fac=function(t){return new(t||D2)(g(F),g(O),g(T))};static \u0275cmp=D({type:D2,selectors:[["ion-breadcrumbs"]],inputs:{color:"color",itemsAfterCollapse:"itemsAfterCollapse",itemsBeforeCollapse:"itemsBeforeCollapse",maxItems:"maxItems",mode:"mode"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","itemsAfterCollapse","itemsBeforeCollapse","maxItems","mode"]})],e),e})(),ce=(()=>{let e=class O2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||O2)(g(F),g(O),g(T))};static \u0275cmp=D({type:O2,selectors:[["ion-button"]],inputs:{buttonType:"buttonType",color:"color",disabled:"disabled",download:"download",expand:"expand",fill:"fill",form:"form",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",size:"size",strong:"strong",target:"target",type:"type"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["buttonType","color","disabled","download","expand","fill","form","href","mode","rel","routerAnimation","routerDirection","shape","size","strong","target","type"]})],e),e})(),Gn=(()=>{let e=class E2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||E2)(g(F),g(O),g(T))};static \u0275cmp=D({type:E2,selectors:[["ion-buttons"]],inputs:{collapse:"collapse"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["collapse"]})],e),e})(),de=(()=>{let e=class P2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||P2)(g(F),g(O),g(T))};static \u0275cmp=D({type:P2,selectors:[["ion-card"]],inputs:{button:"button",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["button","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","target","type"]})],e),e})(),ve=(()=>{let e=class T2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||T2)(g(F),g(O),g(T))};static \u0275cmp=D({type:T2,selectors:[["ion-card-content"]],inputs:{mode:"mode"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["mode"]})],e),e})(),ge=(()=>{let e=class R2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||R2)(g(F),g(O),g(T))};static \u0275cmp=D({type:R2,selectors:[["ion-card-header"]],inputs:{color:"color",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","mode","translucent"]})],e),e})(),Wn=(()=>{let e=class q2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||q2)(g(F),g(O),g(T))};static \u0275cmp=D({type:q2,selectors:[["ion-card-subtitle"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","mode"]})],e),e})(),he=(()=>{let e=class F2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||F2)(g(F),g(O),g(T))};static \u0275cmp=D({type:F2,selectors:[["ion-card-title"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","mode"]})],e),e})(),Zn=(()=>{let e=class N2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||N2)(g(F),g(O),g(T))};static \u0275cmp=D({type:N2,selectors:[["ion-checkbox"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",errorText:"errorText",helperText:"helperText",indeterminate:"indeterminate",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["alignment","checked","color","disabled","errorText","helperText","indeterminate","justify","labelPlacement","mode","name","required","value"]})],e),e})(),Kn=(()=>{let e=class U2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||U2)(g(F),g(O),g(T))};static \u0275cmp=D({type:U2,selectors:[["ion-chip"]],inputs:{color:"color",disabled:"disabled",mode:"mode",outline:"outline"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","disabled","mode","outline"]})],e),e})(),pe=(()=>{let e=class $2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||$2)(g(F),g(O),g(T))};static \u0275cmp=D({type:$2,selectors:[["ion-col"]],inputs:{offset:"offset",offsetLg:"offsetLg",offsetMd:"offsetMd",offsetSm:"offsetSm",offsetXl:"offsetXl",offsetXs:"offsetXs",pull:"pull",pullLg:"pullLg",pullMd:"pullMd",pullSm:"pullSm",pullXl:"pullXl",pullXs:"pullXs",push:"push",pushLg:"pushLg",pushMd:"pushMd",pushSm:"pushSm",pushXl:"pushXl",pushXs:"pushXs",size:"size",sizeLg:"sizeLg",sizeMd:"sizeMd",sizeSm:"sizeSm",sizeXl:"sizeXl",sizeXs:"sizeXs"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["offset","offsetLg","offsetMd","offsetSm","offsetXl","offsetXs","pull","pullLg","pullMd","pullSm","pullXl","pullXs","push","pushLg","pushMd","pushSm","pushXl","pushXs","size","sizeLg","sizeMd","sizeSm","sizeXl","sizeXs"]})],e),e})(),ue=(()=>{let e=class G2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionScrollStart","ionScroll","ionScrollEnd"])}static \u0275fac=function(t){return new(t||G2)(g(F),g(O),g(T))};static \u0275cmp=D({type:G2,selectors:[["ion-content"]],inputs:{color:"color",fixedSlotPlacement:"fixedSlotPlacement",forceOverscroll:"forceOverscroll",fullscreen:"fullscreen",scrollEvents:"scrollEvents",scrollX:"scrollX",scrollY:"scrollY"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","fixedSlotPlacement","forceOverscroll","fullscreen","scrollEvents","scrollX","scrollY"],methods:["getScrollElement","scrollToTop","scrollToBottom","scrollByPoint","scrollToPoint"]})],e),e})(),Qn=(()=>{let e=class W2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionCancel","ionChange","ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||W2)(g(F),g(O),g(T))};static \u0275cmp=D({type:W2,selectors:[["ion-datetime"]],inputs:{cancelText:"cancelText",clearText:"clearText",color:"color",dayValues:"dayValues",disabled:"disabled",doneText:"doneText",firstDayOfWeek:"firstDayOfWeek",formatOptions:"formatOptions",highlightedDates:"highlightedDates",hourCycle:"hourCycle",hourValues:"hourValues",isDateEnabled:"isDateEnabled",locale:"locale",max:"max",min:"min",minuteValues:"minuteValues",mode:"mode",monthValues:"monthValues",multiple:"multiple",name:"name",preferWheel:"preferWheel",presentation:"presentation",readonly:"readonly",showAdjacentDays:"showAdjacentDays",showClearButton:"showClearButton",showDefaultButtons:"showDefaultButtons",showDefaultTimeLabel:"showDefaultTimeLabel",showDefaultTitle:"showDefaultTitle",size:"size",titleSelectedDatesFormatter:"titleSelectedDatesFormatter",value:"value",yearValues:"yearValues"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["cancelText","clearText","color","dayValues","disabled","doneText","firstDayOfWeek","formatOptions","highlightedDates","hourCycle","hourValues","isDateEnabled","locale","max","min","minuteValues","mode","monthValues","multiple","name","preferWheel","presentation","readonly","showAdjacentDays","showClearButton","showDefaultButtons","showDefaultTimeLabel","showDefaultTitle","size","titleSelectedDatesFormatter","value","yearValues"],methods:["confirm","reset","cancel"]})],e),e})(),Xn=(()=>{let e=class Z2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Z2)(g(F),g(O),g(T))};static \u0275cmp=D({type:Z2,selectors:[["ion-datetime-button"]],inputs:{color:"color",datetime:"datetime",disabled:"disabled",mode:"mode"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","datetime","disabled","mode"]})],e),e})(),Jn=(()=>{let e=class K2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||K2)(g(F),g(O),g(T))};static \u0275cmp=D({type:K2,selectors:[["ion-fab"]],inputs:{activated:"activated",edge:"edge",horizontal:"horizontal",vertical:"vertical"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["activated","edge","horizontal","vertical"],methods:["close"]})],e),e})(),Yn=(()=>{let e=class Q2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||Q2)(g(F),g(O),g(T))};static \u0275cmp=D({type:Q2,selectors:[["ion-fab-button"]],inputs:{activated:"activated",closeIcon:"closeIcon",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",show:"show",size:"size",target:"target",translucent:"translucent",type:"type"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["activated","closeIcon","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","show","size","target","translucent","type"]})],e),e})(),eo=(()=>{let e=class X2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||X2)(g(F),g(O),g(T))};static \u0275cmp=D({type:X2,selectors:[["ion-fab-list"]],inputs:{activated:"activated",side:"side"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["activated","side"]})],e),e})(),to=(()=>{let e=class J2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||J2)(g(F),g(O),g(T))};static \u0275cmp=D({type:J2,selectors:[["ion-footer"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["collapse","mode","translucent"]})],e),e})(),me=(()=>{let e=class Y2{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Y2)(g(F),g(O),g(T))};static \u0275cmp=D({type:Y2,selectors:[["ion-grid"]],inputs:{fixed:"fixed"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["fixed"]})],e),e})(),we=(()=>{let e=class e4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||e4)(g(F),g(O),g(T))};static \u0275cmp=D({type:e4,selectors:[["ion-header"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["collapse","mode","translucent"]})],e),e})(),fe=(()=>{let e=class t4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||t4)(g(F),g(O),g(T))};static \u0275cmp=D({type:t4,selectors:[["ion-icon"]],inputs:{color:"color",flipRtl:"flipRtl",icon:"icon",ios:"ios",lazy:"lazy",md:"md",mode:"mode",name:"name",sanitize:"sanitize",size:"size",src:"src"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","flipRtl","icon","ios","lazy","md","mode","name","sanitize","size","src"]})],e),e})(),xe=(()=>{let e=class n4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionImgWillLoad","ionImgDidLoad","ionError"])}static \u0275fac=function(t){return new(t||n4)(g(F),g(O),g(T))};static \u0275cmp=D({type:n4,selectors:[["ion-img"]],inputs:{alt:"alt",src:"src"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["alt","src"]})],e),e})(),no=(()=>{let e=class o4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionInfinite"])}static \u0275fac=function(t){return new(t||o4)(g(F),g(O),g(T))};static \u0275cmp=D({type:o4,selectors:[["ion-infinite-scroll"]],inputs:{disabled:"disabled",position:"position",threshold:"threshold"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["disabled","position","threshold"],methods:["complete"]})],e),e})(),oo=(()=>{let e=class i4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||i4)(g(F),g(O),g(T))};static \u0275cmp=D({type:i4,selectors:[["ion-infinite-scroll-content"]],inputs:{loadingSpinner:"loadingSpinner",loadingText:"loadingText"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["loadingSpinner","loadingText"]})],e),e})(),Me=(()=>{let e=class a4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionInput","ionChange","ionBlur","ionFocus"])}static \u0275fac=function(t){return new(t||a4)(g(F),g(O),g(T))};static \u0275cmp=D({type:a4,selectors:[["ion-input"]],inputs:{autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",autofocus:"autofocus",clearInput:"clearInput",clearInputIcon:"clearInputIcon",clearOnEdit:"clearOnEdit",color:"color",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",max:"max",maxlength:"maxlength",min:"min",minlength:"minlength",mode:"mode",multiple:"multiple",name:"name",pattern:"pattern",placeholder:"placeholder",readonly:"readonly",required:"required",shape:"shape",spellcheck:"spellcheck",step:"step",type:"type",value:"value"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["autocapitalize","autocomplete","autocorrect","autofocus","clearInput","clearInputIcon","clearOnEdit","color","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","max","maxlength","min","minlength","mode","multiple","name","pattern","placeholder","readonly","required","shape","spellcheck","step","type","value"],methods:["setFocus","getInputElement"]})],e),e})(),io=(()=>{let e=class s4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionInput","ionChange","ionComplete","ionBlur","ionFocus"])}static \u0275fac=function(t){return new(t||s4)(g(F),g(O),g(T))};static \u0275cmp=D({type:s4,selectors:[["ion-input-otp"]],inputs:{autocapitalize:"autocapitalize",color:"color",disabled:"disabled",fill:"fill",inputmode:"inputmode",length:"length",pattern:"pattern",readonly:"readonly",separators:"separators",shape:"shape",size:"size",type:"type",value:"value"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["autocapitalize","color","disabled","fill","inputmode","length","pattern","readonly","separators","shape","size","type","value"],methods:["setFocus"]})],e),e})(),ao=(()=>{let e=class r4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||r4)(g(F),g(O),g(T))};static \u0275cmp=D({type:r4,selectors:[["ion-input-password-toggle"]],inputs:{color:"color",hideIcon:"hideIcon",mode:"mode",showIcon:"showIcon"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","hideIcon","mode","showIcon"]})],e),e})(),ke=(()=>{let e=class l4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||l4)(g(F),g(O),g(T))};static \u0275cmp=D({type:l4,selectors:[["ion-item"]],inputs:{button:"button",color:"color",detail:"detail",detailIcon:"detailIcon",disabled:"disabled",download:"download",href:"href",lines:"lines",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["button","color","detail","detailIcon","disabled","download","href","lines","mode","rel","routerAnimation","routerDirection","target","type"]})],e),e})(),so=(()=>{let e=class c4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||c4)(g(F),g(O),g(T))};static \u0275cmp=D({type:c4,selectors:[["ion-item-divider"]],inputs:{color:"color",mode:"mode",sticky:"sticky"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","mode","sticky"]})],e),e})(),ro=(()=>{let e=class d4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||d4)(g(F),g(O),g(T))};static \u0275cmp=D({type:d4,selectors:[["ion-item-group"]],standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({})],e),e})(),lo=(()=>{let e=class v4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||v4)(g(F),g(O),g(T))};static \u0275cmp=D({type:v4,selectors:[["ion-item-option"]],inputs:{color:"color",disabled:"disabled",download:"download",expandable:"expandable",href:"href",mode:"mode",rel:"rel",target:"target",type:"type"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","disabled","download","expandable","href","mode","rel","target","type"]})],e),e})(),co=(()=>{let e=class g4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionSwipe"])}static \u0275fac=function(t){return new(t||g4)(g(F),g(O),g(T))};static \u0275cmp=D({type:g4,selectors:[["ion-item-options"]],inputs:{side:"side"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["side"]})],e),e})(),vo=(()=>{let e=class h4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionDrag"])}static \u0275fac=function(t){return new(t||h4)(g(F),g(O),g(T))};static \u0275cmp=D({type:h4,selectors:[["ion-item-sliding"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["disabled"],methods:["getOpenAmount","getSlidingRatio","open","close","closeOpened"]})],e),e})(),ze=(()=>{let e=class p4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||p4)(g(F),g(O),g(T))};static \u0275cmp=D({type:p4,selectors:[["ion-label"]],inputs:{color:"color",mode:"mode",position:"position"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","mode","position"]})],e),e})(),ye=(()=>{let e=class u4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||u4)(g(F),g(O),g(T))};static \u0275cmp=D({type:u4,selectors:[["ion-list"]],inputs:{inset:"inset",lines:"lines",mode:"mode"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["inset","lines","mode"],methods:["closeSlidingItems"]})],e),e})(),go=(()=>{let e=class m4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||m4)(g(F),g(O),g(T))};static \u0275cmp=D({type:m4,selectors:[["ion-list-header"]],inputs:{color:"color",lines:"lines",mode:"mode"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","lines","mode"]})],e),e})(),ho=(()=>{let e=class w4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionLoadingDidPresent","ionLoadingWillPresent","ionLoadingWillDismiss","ionLoadingDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(t){return new(t||w4)(g(F),g(O),g(T))};static \u0275cmp=D({type:w4,selectors:[["ion-loading"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",showBackdrop:"showBackdrop",spinner:"spinner",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["animated","backdropDismiss","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","message","mode","showBackdrop","spinner","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),po=(()=>{let e=class f4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionWillOpen","ionWillClose","ionDidOpen","ionDidClose"])}static \u0275fac=function(t){return new(t||f4)(g(F),g(O),g(T))};static \u0275cmp=D({type:f4,selectors:[["ion-menu"]],inputs:{contentId:"contentId",disabled:"disabled",maxEdgeStart:"maxEdgeStart",menuId:"menuId",side:"side",swipeGesture:"swipeGesture",type:"type"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["contentId","disabled","maxEdgeStart","menuId","side","swipeGesture","type"],methods:["isOpen","isActive","open","close","toggle","setOpen"]})],e),e})(),uo=(()=>{let e=class x4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||x4)(g(F),g(O),g(T))};static \u0275cmp=D({type:x4,selectors:[["ion-menu-button"]],inputs:{autoHide:"autoHide",color:"color",disabled:"disabled",menu:"menu",mode:"mode",type:"type"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["autoHide","color","disabled","menu","mode","type"]})],e),e})(),mo=(()=>{let e=class M4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||M4)(g(F),g(O),g(T))};static \u0275cmp=D({type:M4,selectors:[["ion-menu-toggle"]],inputs:{autoHide:"autoHide",menu:"menu"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["autoHide","menu"]})],e),e})(),wo=(()=>{let e=class k4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||k4)(g(F),g(O),g(T))};static \u0275cmp=D({type:k4,selectors:[["ion-nav-link"]],inputs:{component:"component",componentProps:"componentProps",routerAnimation:"routerAnimation",routerDirection:"routerDirection"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["component","componentProps","routerAnimation","routerDirection"]})],e),e})(),fo=(()=>{let e=class z4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||z4)(g(F),g(O),g(T))};static \u0275cmp=D({type:z4,selectors:[["ion-note"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","mode"]})],e),e})(),xo=(()=>{let e=class y4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||y4)(g(F),g(O),g(T))};static \u0275cmp=D({type:y4,selectors:[["ion-picker"]],inputs:{mode:"mode"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["mode"]})],e),e})(),Mo=(()=>{let e=class C4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionChange"])}static \u0275fac=function(t){return new(t||C4)(g(F),g(O),g(T))};static \u0275cmp=D({type:C4,selectors:[["ion-picker-column"]],inputs:{color:"color",disabled:"disabled",mode:"mode",value:"value"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","disabled","mode","value"],methods:["setFocus"]})],e),e})(),ko=(()=>{let e=class _4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||_4)(g(F),g(O),g(T))};static \u0275cmp=D({type:_4,selectors:[["ion-picker-column-option"]],inputs:{color:"color",disabled:"disabled",value:"value"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","disabled","value"]})],e),e})(),zo=(()=>{let e=class B4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionPickerDidPresent","ionPickerWillPresent","ionPickerWillDismiss","ionPickerDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(t){return new(t||B4)(g(F),g(O),g(T))};static \u0275cmp=D({type:B4,selectors:[["ion-picker-legacy"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",columns:"columns",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",trigger:"trigger"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["animated","backdropDismiss","buttons","columns","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss","getColumn"]})],e),e})(),yo=(()=>{let e=class b4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||b4)(g(F),g(O),g(T))};static \u0275cmp=D({type:b4,selectors:[["ion-progress-bar"]],inputs:{buffer:"buffer",color:"color",mode:"mode",reversed:"reversed",type:"type",value:"value"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["buffer","color","mode","reversed","type","value"]})],e),e})(),Ce=(()=>{let e=class L4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||L4)(g(F),g(O),g(T))};static \u0275cmp=D({type:L4,selectors:[["ion-radio"]],inputs:{alignment:"alignment",color:"color",disabled:"disabled",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",value:"value"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["alignment","color","disabled","justify","labelPlacement","mode","name","value"]})],e),e})(),_e=(()=>{let e=class S4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionChange"])}static \u0275fac=function(t){return new(t||S4)(g(F),g(O),g(T))};static \u0275cmp=D({type:S4,selectors:[["ion-radio-group"]],inputs:{allowEmptySelection:"allowEmptySelection",compareWith:"compareWith",errorText:"errorText",helperText:"helperText",name:"name",value:"value"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["allowEmptySelection","compareWith","errorText","helperText","name","value"]})],e),e})(),Co=(()=>{let e=class A4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionChange","ionInput","ionFocus","ionBlur","ionKnobMoveStart","ionKnobMoveEnd"])}static \u0275fac=function(t){return new(t||A4)(g(F),g(O),g(T))};static \u0275cmp=D({type:A4,selectors:[["ion-range"]],inputs:{activeBarStart:"activeBarStart",color:"color",debounce:"debounce",disabled:"disabled",dualKnobs:"dualKnobs",label:"label",labelPlacement:"labelPlacement",max:"max",min:"min",mode:"mode",name:"name",pin:"pin",pinFormatter:"pinFormatter",snaps:"snaps",step:"step",ticks:"ticks",value:"value"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["activeBarStart","color","debounce","disabled","dualKnobs","label","labelPlacement","max","min","mode","name","pin","pinFormatter","snaps","step","ticks","value"]})],e),e})(),_o=(()=>{let e=class j4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionRefresh","ionPull","ionStart"])}static \u0275fac=function(t){return new(t||j4)(g(F),g(O),g(T))};static \u0275cmp=D({type:j4,selectors:[["ion-refresher"]],inputs:{closeDuration:"closeDuration",disabled:"disabled",mode:"mode",pullFactor:"pullFactor",pullMax:"pullMax",pullMin:"pullMin",snapbackDuration:"snapbackDuration"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["closeDuration","disabled","mode","pullFactor","pullMax","pullMin","snapbackDuration"],methods:["complete","cancel","getProgress"]})],e),e})(),Bo=(()=>{let e=class I4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||I4)(g(F),g(O),g(T))};static \u0275cmp=D({type:I4,selectors:[["ion-refresher-content"]],inputs:{pullingIcon:"pullingIcon",pullingText:"pullingText",refreshingSpinner:"refreshingSpinner",refreshingText:"refreshingText"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["pullingIcon","pullingText","refreshingSpinner","refreshingText"]})],e),e})(),bo=(()=>{let e=class V4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||V4)(g(F),g(O),g(T))};static \u0275cmp=D({type:V4,selectors:[["ion-reorder"]],standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({})],e),e})(),Lo=(()=>{let e=class H4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionItemReorder"])}static \u0275fac=function(t){return new(t||H4)(g(F),g(O),g(T))};static \u0275cmp=D({type:H4,selectors:[["ion-reorder-group"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["disabled"],methods:["complete"]})],e),e})(),So=(()=>{let e=class D4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||D4)(g(F),g(O),g(T))};static \u0275cmp=D({type:D4,selectors:[["ion-ripple-effect"]],inputs:{type:"type"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["type"],methods:["addRipple"]})],e),e})(),Be=(()=>{let e=class O4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||O4)(g(F),g(O),g(T))};static \u0275cmp=D({type:O4,selectors:[["ion-row"]],standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({})],e),e})(),Ao=(()=>{let e=class E4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionInput","ionChange","ionCancel","ionClear","ionBlur","ionFocus"])}static \u0275fac=function(t){return new(t||E4)(g(F),g(O),g(T))};static \u0275cmp=D({type:E4,selectors:[["ion-searchbar"]],inputs:{animated:"animated",autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",cancelButtonIcon:"cancelButtonIcon",cancelButtonText:"cancelButtonText",clearIcon:"clearIcon",color:"color",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",inputmode:"inputmode",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",searchIcon:"searchIcon",showCancelButton:"showCancelButton",showClearButton:"showClearButton",spellcheck:"spellcheck",type:"type",value:"value"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["animated","autocapitalize","autocomplete","autocorrect","cancelButtonIcon","cancelButtonText","clearIcon","color","debounce","disabled","enterkeyhint","inputmode","maxlength","minlength","mode","name","placeholder","searchIcon","showCancelButton","showClearButton","spellcheck","type","value"],methods:["setFocus","getInputElement"]})],e),e})(),jo=(()=>{let e=class P4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionChange"])}static \u0275fac=function(t){return new(t||P4)(g(F),g(O),g(T))};static \u0275cmp=D({type:P4,selectors:[["ion-segment"]],inputs:{color:"color",disabled:"disabled",mode:"mode",scrollable:"scrollable",selectOnFocus:"selectOnFocus",swipeGesture:"swipeGesture",value:"value"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","disabled","mode","scrollable","selectOnFocus","swipeGesture","value"]})],e),e})(),Io=(()=>{let e=class T4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||T4)(g(F),g(O),g(T))};static \u0275cmp=D({type:T4,selectors:[["ion-segment-button"]],inputs:{contentId:"contentId",disabled:"disabled",layout:"layout",mode:"mode",type:"type",value:"value"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["contentId","disabled","layout","mode","type","value"]})],e),e})(),Vo=(()=>{let e=class R4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||R4)(g(F),g(O),g(T))};static \u0275cmp=D({type:R4,selectors:[["ion-segment-content"]],standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({})],e),e})(),Ho=(()=>{let e=class q4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionSegmentViewScroll"])}static \u0275fac=function(t){return new(t||q4)(g(F),g(O),g(T))};static \u0275cmp=D({type:q4,selectors:[["ion-segment-view"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["disabled"]})],e),e})(),be=(()=>{let e=class F4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionChange","ionCancel","ionDismiss","ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||F4)(g(F),g(O),g(T))};static \u0275cmp=D({type:F4,selectors:[["ion-select"]],inputs:{cancelText:"cancelText",color:"color",compareWith:"compareWith",disabled:"disabled",errorText:"errorText",expandedIcon:"expandedIcon",fill:"fill",helperText:"helperText",interface:"interface",interfaceOptions:"interfaceOptions",justify:"justify",label:"label",labelPlacement:"labelPlacement",mode:"mode",multiple:"multiple",name:"name",okText:"okText",placeholder:"placeholder",required:"required",selectedText:"selectedText",shape:"shape",toggleIcon:"toggleIcon",value:"value"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["cancelText","color","compareWith","disabled","errorText","expandedIcon","fill","helperText","interface","interfaceOptions","justify","label","labelPlacement","mode","multiple","name","okText","placeholder","required","selectedText","shape","toggleIcon","value"],methods:["open"]})],e),e})(),Do=(()=>{let e=class N4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||N4)(g(F),g(O),g(T))};static \u0275cmp=D({type:N4,selectors:[["ion-select-modal"]],inputs:{header:"header",multiple:"multiple",options:"options"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["header","multiple","options"]})],e),e})(),Le=(()=>{let e=class U4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||U4)(g(F),g(O),g(T))};static \u0275cmp=D({type:U4,selectors:[["ion-select-option"]],inputs:{disabled:"disabled",value:"value"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["disabled","value"]})],e),e})(),Oo=(()=>{let e=class $4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||$4)(g(F),g(O),g(T))};static \u0275cmp=D({type:$4,selectors:[["ion-skeleton-text"]],inputs:{animated:"animated"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["animated"]})],e),e})(),Eo=(()=>{let e=class G4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||G4)(g(F),g(O),g(T))};static \u0275cmp=D({type:G4,selectors:[["ion-spinner"]],inputs:{color:"color",duration:"duration",name:"name",paused:"paused"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","duration","name","paused"]})],e),e})(),Po=(()=>{let e=class W4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionSplitPaneVisible"])}static \u0275fac=function(t){return new(t||W4)(g(F),g(O),g(T))};static \u0275cmp=D({type:W4,selectors:[["ion-split-pane"]],inputs:{contentId:"contentId",disabled:"disabled",when:"when"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["contentId","disabled","when"]})],e),e})(),b3=(()=>{let e=class Z4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Z4)(g(F),g(O),g(T))};static \u0275cmp=D({type:Z4,selectors:[["ion-tab"]],inputs:{component:"component",tab:"tab"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["component","tab"],methods:["setActive"]})],e),e})(),K4=(()=>{let e=class Q4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Q4)(g(F),g(O),g(T))};static \u0275cmp=D({type:Q4,selectors:[["ion-tab-bar"]],inputs:{color:"color",mode:"mode",selectedTab:"selectedTab",translucent:"translucent"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","mode","selectedTab","translucent"]})],e),e})(),To=(()=>{let e=class X4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||X4)(g(F),g(O),g(T))};static \u0275cmp=D({type:X4,selectors:[["ion-tab-button"]],inputs:{disabled:"disabled",download:"download",href:"href",layout:"layout",mode:"mode",rel:"rel",selected:"selected",tab:"tab",target:"target"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["disabled","download","href","layout","mode","rel","selected","tab","target"]})],e),e})(),Ro=(()=>{let e=class J4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||J4)(g(F),g(O),g(T))};static \u0275cmp=D({type:J4,selectors:[["ion-text"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","mode"]})],e),e})(),qo=(()=>{let e=class Y4{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionChange","ionInput","ionBlur","ionFocus"])}static \u0275fac=function(t){return new(t||Y4)(g(F),g(O),g(T))};static \u0275cmp=D({type:Y4,selectors:[["ion-textarea"]],inputs:{autoGrow:"autoGrow",autocapitalize:"autocapitalize",autofocus:"autofocus",clearOnEdit:"clearOnEdit",color:"color",cols:"cols",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",readonly:"readonly",required:"required",rows:"rows",shape:"shape",spellcheck:"spellcheck",value:"value",wrap:"wrap"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["autoGrow","autocapitalize","autofocus","clearOnEdit","color","cols","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","maxlength","minlength","mode","name","placeholder","readonly","required","rows","shape","spellcheck","value","wrap"],methods:["setFocus","getInputElement"]})],e),e})(),Fo=(()=>{let e=class ee{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||ee)(g(F),g(O),g(T))};static \u0275cmp=D({type:ee,selectors:[["ion-thumbnail"]],standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({})],e),e})(),Se=(()=>{let e=class te{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||te)(g(F),g(O),g(T))};static \u0275cmp=D({type:te,selectors:[["ion-title"]],inputs:{color:"color",size:"size"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","size"]})],e),e})(),No=(()=>{let e=class ne{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionToastDidPresent","ionToastWillPresent","ionToastWillDismiss","ionToastDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(t){return new(t||ne)(g(F),g(O),g(T))};static \u0275cmp=D({type:ne,selectors:[["ion-toast"]],inputs:{animated:"animated",buttons:"buttons",color:"color",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",icon:"icon",isOpen:"isOpen",keyboardClose:"keyboardClose",layout:"layout",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",position:"position",positionAnchor:"positionAnchor",swipeGesture:"swipeGesture",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["animated","buttons","color","cssClass","duration","enterAnimation","header","htmlAttributes","icon","isOpen","keyboardClose","layout","leaveAnimation","message","mode","position","positionAnchor","swipeGesture","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),Uo=(()=>{let e=class oe{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,h1(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||oe)(g(F),g(O),g(T))};static \u0275cmp=D({type:oe,selectors:[["ion-toggle"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",enableOnOffLabels:"enableOnOffLabels",errorText:"errorText",helperText:"helperText",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["alignment","checked","color","disabled","enableOnOffLabels","errorText","helperText","justify","labelPlacement","mode","name","required","value"]})],e),e})(),Ae=(()=>{let e=class ie{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||ie)(g(F),g(O),g(T))};static \u0275cmp=D({type:ie,selectors:[["ion-toolbar"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})};return e=U([$({inputs:["color","mode"]})],e),e})(),U0=(()=>{class e extends v3{parentOutlet;outletContent;constructor(n,t,i,a,s,v,p,f){super(n,t,i,a,s,v,p,f),this.parentOutlet=f}static \u0275fac=function(t){return new(t||e)(m2("name"),m2("tabs"),g(Ut),g(O),g(Zt),g(T),g(Wt),g(e,12))};static \u0275cmp=D({type:e,selectors:[["ion-router-outlet"]],viewQuery:function(t,i){if(t&1&&r0(_n,7,Et),t&2){let a;W1(a=Z1())&&(i.outletContent=a.first)}},standalone:!1,features:[H1],ngContentSelectors:N,decls:3,vars:0,consts:[["outletContent",""]],template:function(t,i){t&1&&(R(),Rt(0,null,0),E(2),qt())},encapsulation:2})}return e})(),$o=(()=>{class e extends w3{outlet;tabBar;tabBars;tabs;static \u0275fac=(()=>{let n;return function(i){return(n||(n=a0(e)))(i||e)}})();static \u0275cmp=D({type:e,selectors:[["ion-tabs"]],contentQueries:function(t,i,a){if(t&1&&(q0(a,K4,5),q0(a,K4,4),q0(a,b3,4)),t&2){let s;W1(s=Z1())&&(i.tabBar=s.first),W1(s=Z1())&&(i.tabBars=s),W1(s=Z1())&&(i.tabs=s)}},viewQuery:function(t,i){if(t&1&&r0(Bn,5,U0),t&2){let a;W1(a=Z1())&&(i.outlet=a.first)}},standalone:!1,features:[H1],ngContentSelectors:Ln,decls:6,vars:2,consts:[["tabsInner",""],["outlet",""],[1,"tabs-inner"],["tabs","true",3,"stackWillChange","stackDidChange",4,"ngIf"],[4,"ngIf"],["tabs","true",3,"stackWillChange","stackDidChange"]],template:function(t,i){t&1&&(R(bn),E(0),L(1,"div",2,0),k1(3,Sn,2,0,"ion-router-outlet",3)(4,An,1,0,"ng-content",4),b(),E(5,1)),t&2&&(X(3),t1("ngIf",i.tabs.length===0),X(),t1("ngIf",i.tabs.length>0))},dependencies:[M0,U0],styles:["[_nghost-%COMP%]{display:flex;position:absolute;inset:0;flex-direction:column;width:100%;height:100%;contain:layout size style}.tabs-inner[_ngcontent-%COMP%]{position:relative;flex:1;contain:layout size style}"]})}return e})(),Go=(()=>{class e extends h3{constructor(n,t,i,a,s,v){super(n,t,i,a,s,v)}static \u0275fac=function(t){return new(t||e)(g(U0,8),g(r3),g(l3),g(O),g(T),g(F))};static \u0275cmp=D({type:e,selectors:[["ion-back-button"]],standalone:!1,features:[H1],ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})}return e})(),Wo=(()=>{class e extends m3{constructor(n,t,i,a,s,v){super(n,t,i,a,s,v)}static \u0275fac=function(t){return new(t||e)(g(O),g(j0),g(n0),g(k0),g(T),g(F))};static \u0275cmp=D({type:e,selectors:[["ion-nav"]],standalone:!1,features:[H1],ngContentSelectors:N,decls:1,vars:0,template:function(t,i){t&1&&(R(),E(0))},encapsulation:2,changeDetection:0})}return e})(),Zo=(()=>{class e extends p3{static \u0275fac=(()=>{let n;return function(i){return(n||(n=a0(e)))(i||e)}})();static \u0275dir=o0({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],standalone:!1,features:[H1]})}return e})(),Ko=(()=>{class e extends u3{static \u0275fac=(()=>{let n;return function(i){return(n||(n=a0(e)))(i||e)}})();static \u0275dir=o0({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],standalone:!1,features:[H1]})}return e})(),je=(()=>{class e extends d3{static \u0275fac=(()=>{let n;return function(i){return(n||(n=a0(e)))(i||e)}})();static \u0275cmp=D({type:e,selectors:[["ion-modal"]],standalone:!1,features:[H1],decls:1,vars:1,consts:[["class","ion-delegate-host ion-page",4,"ngIf"],[1,"ion-delegate-host","ion-page"],[3,"ngTemplateOutlet"]],template:function(t,i){t&1&&k1(0,jn,2,1,"div",0),t&2&&t1("ngIf",i.isCmpOpen||i.keepContentsMounted)},dependencies:[M0,x2],encapsulation:2,changeDetection:0})}return e})(),Qo=(()=>{class e extends c3{static \u0275fac=(()=>{let n;return function(i){return(n||(n=a0(e)))(i||e)}})();static \u0275cmp=D({type:e,selectors:[["ion-popover"]],standalone:!1,features:[H1],decls:1,vars:1,consts:[[3,"ngTemplateOutlet",4,"ngIf"],[3,"ngTemplateOutlet"]],template:function(t,i){t&1&&k1(0,In,1,1,"ng-container",0),t&2&&t1("ngIf",i.isCmpOpen||i.keepContentsMounted)},dependencies:[M0,x2],encapsulation:2,changeDetection:0})}return e})(),Xo={provide:M2,useExisting:u2(()=>L3),multi:!0},L3=(()=>{class e extends o3{static \u0275fac=(()=>{let n;return function(i){return(n||(n=a0(e)))(i||e)}})();static \u0275dir=o0({type:e,selectors:[["ion-input","type","number","max","","formControlName",""],["ion-input","type","number","max","","formControl",""],["ion-input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(t,i){t&2&&w2("max",i._enabled?i.max:null)},standalone:!1,features:[l0([Xo]),H1]})}return e})(),Jo={provide:M2,useExisting:u2(()=>S3),multi:!0},S3=(()=>{class e extends i3{static \u0275fac=(()=>{let n;return function(i){return(n||(n=a0(e)))(i||e)}})();static \u0275dir=o0({type:e,selectors:[["ion-input","type","number","min","","formControlName",""],["ion-input","type","number","min","","formControl",""],["ion-input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(t,i){t&2&&w2("min",i._enabled?i.min:null)},standalone:!1,features:[l0([Jo]),H1]})}return e})();var Yo=(()=>{class e extends N0{angularDelegate=s0(k0);injector=s0(n0);environmentInjector=s0(j0);constructor(){super(z2)}create(n){return super.create(x0(f0({},n),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")}))}static \u0275fac=function(t){return new(t||e)};static \u0275prov=A0({token:e,factory:e.\u0275fac})}return e})();var ae=class extends N0{angularDelegate=s0(k0);injector=s0(n0);environmentInjector=s0(j0);constructor(){super(y2)}create(o){return super.create(x0(f0({},o),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")}))}},A3=(()=>{class e extends N0{constructor(){super(C2)}static \u0275fac=function(t){return new(t||e)};static \u0275prov=A0({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ei=(e,o,n)=>()=>{let t=o.defaultView;if(t&&typeof window<"u"){_2(x0(f0({},e),{_zoneGate:a=>n.run(a)}));let i="__zone_symbol__addEventListener"in o.body?"__zone_symbol__addEventListener":"addEventListener";return _3(t,{exclude:["ion-tabs"],syncQueue:!0,raf:f3,jmp:a=>n.runOutsideAngular(a),ael(a,s,v,p){a[i](s,v,p)},rel(a,s,v,p){a.removeEventListener(s,v,p)}})}},ti=[On,En,Pn,Tn,Rn,qn,Fn,Nn,Un,$n,ce,Gn,de,ve,ge,Wn,he,Zn,Kn,pe,ue,Qn,Xn,Jn,Yn,eo,to,me,we,fe,xe,no,oo,Me,io,ao,ke,so,ro,lo,co,vo,ze,ye,go,ho,po,uo,mo,wo,fo,xo,Mo,ko,zo,yo,Ce,_e,Co,_o,Bo,bo,Lo,So,Be,Ao,jo,Io,Vo,Ho,be,Do,Le,Oo,Eo,Po,b3,K4,To,Ro,qo,Fo,Se,No,Uo,Ae],Ds=[...ti,je,Qo,Vn,se,re,le,$o,U0,Go,Wo,Zo,Ko,S3,L3],j3=(()=>{class e{static forRoot(n={}){return{ngModule:e,providers:[{provide:k2,useValue:n},{provide:Tt,useFactory:ei,multi:!0,deps:[k2,Dt,T]},k0,g3()]}}static \u0275fac=function(t){return new(t||e)};static \u0275mod=Pt({type:e});static \u0275inj=Ht({providers:[Yo,ae],imports:[F0]})}return e})();var Ie,oi=function(){if(typeof window>"u")return new Map;if(!Ie){var e=window;e.Ionicons=e.Ionicons||{},Ie=e.Ionicons.map=e.Ionicons.map||new Map}return Ie},Ve=function(e){Object.keys(e).forEach(function(o){I3(o,e[o]);var n=o.replace(/([a-z0-9]|(?=[A-Z]))([A-Z0-9])/g,"$1-$2").toLowerCase();o!==n&&I3(n,e[o])})},I3=function(e,o){var n=oi(),t=n.get(e);t===void 0?n.set(e,o):t!==o&&console.warn('[Ionicons Warning]: Multiple icons were mapped to name "'.concat(e,'". Ensure that multiple icons are not mapped to the same icon name.'))};var V3="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M384 224v184a40 40 0 01-40 40H104a40 40 0 01-40-40V168a40 40 0 0140-40h167.48' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M459.94 53.25a16.06 16.06 0 00-23.22-.56L424.35 65a8 8 0 000 11.31l11.34 11.32a8 8 0 0011.34 0l12.06-12c6.1-6.09 6.67-16.01.85-22.38zM399.34 90L218.82 270.2a9 9 0 00-2.31 3.93L208.16 299a3.91 3.91 0 004.86 4.86l24.85-8.35a9 9 0 003.93-2.31L422 112.66a9 9 0 000-12.66l-9.95-10a9 9 0 00-12.71 0z'/></svg>";var H3="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M112 112l20 320c.95 18.49 14.4 32 32 32h184c17.67 0 30.87-13.51 32-32l20-320' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-miterlimit='10' d='M80 112h352' class='ionicon-stroke-width'/><path d='M192 112V72h0a23.93 23.93 0 0124-24h80a23.93 23.93 0 0124 24h0v40M256 176v224M184 176l8 224M328 176l-8 224' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";function D3(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let o=Math.random()*16|0;return(e==="x"?o:o&3|8).toString(16)})}var Y0=p2(P3());var $0,ii=new Uint8Array(16);function He(){if(!$0&&($0=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||typeof msCrypto<"u"&&typeof msCrypto.getRandomValues=="function"&&msCrypto.getRandomValues.bind(msCrypto),!$0))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return $0(ii)}var T3=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function ai(e){return typeof e=="string"&&T3.test(e)}var R3=ai;var I1=[];for(G0=0;G0<256;++G0)I1.push((G0+256).toString(16).substr(1));var G0;function si(e){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=(I1[e[o+0]]+I1[e[o+1]]+I1[e[o+2]]+I1[e[o+3]]+"-"+I1[e[o+4]]+I1[e[o+5]]+"-"+I1[e[o+6]]+I1[e[o+7]]+"-"+I1[e[o+8]]+I1[e[o+9]]+"-"+I1[e[o+10]]+I1[e[o+11]]+I1[e[o+12]]+I1[e[o+13]]+I1[e[o+14]]+I1[e[o+15]]).toLowerCase();if(!R3(n))throw TypeError("Stringified UUID is invalid");return n}var q3=si;function ri(e,o,n){e=e||{};var t=e.random||(e.rng||He)();if(t[6]=t[6]&15|64,t[8]=t[8]&63|128,o){n=n||0;for(var i=0;i<16;++i)o[n+i]=t[i];return o}return q3(t)}var H0=ri;var ft=p2(F3()),e0=p2(Y3());function pi(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer||typeof Blob<"u"&&e instanceof Blob}function ui(e){return e instanceof ArrayBuffer?e.slice(0):e.slice(0,e.size,e.type)}var b6=Function.prototype.toString,mi=b6.call(Object);function wi(e){var o=Object.getPrototypeOf(e);if(o===null)return!0;var n=o.constructor;return typeof n=="function"&&n instanceof n&&b6.call(n)==mi}function A1(e){var o,n,t;if(!e||typeof e!="object")return e;if(Array.isArray(e)){for(o=[],n=0,t=e.length;n<t;n++)o[n]=A1(e[n]);return o}if(e instanceof Date&&isFinite(e))return e.toISOString();if(pi(e))return ui(e);if(!wi(e))return e;o={};for(n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var i=A1(e[n]);typeof i<"u"&&(o[n]=i)}return o}function L6(e){var o=!1;return function(...n){if(o)throw new Error("once called more than once");o=!0,e.apply(this,n)}}function S6(e){return function(...o){o=A1(o);var n=this,t=typeof o[o.length-1]=="function"?o.pop():!1,i=new Promise(function(a,s){var v;try{var p=L6(function(f,M){f?s(f):a(M)});o.push(p),v=e.apply(n,o),v&&typeof v.then=="function"&&a(v)}catch(f){s(f)}});return t&&i.then(function(a){t(null,a)},t),i}}function fi(e,o,n){if(e.constructor.listeners("debug").length){for(var t=["api",e.name,o],i=0;i<n.length-1;i++)t.push(n[i]);e.constructor.emit("debug",t);var a=n[n.length-1];n[n.length-1]=function(s,v){var p=["api",e.name,o];p=p.concat(s?["error",s]:["success",v]),e.constructor.emit("debug",p),a(s,v)}}}function _1(e,o){return S6(function(...n){if(this._closed)return Promise.reject(new Error("database is closed"));if(this._destroyed)return Promise.reject(new Error("database is destroyed"));var t=this;return fi(t,e,n),this.taskqueue.isReady?o.apply(this,n):new Promise(function(i,a){t.taskqueue.addTask(function(s){s?a(s):i(t[e].apply(t,n))})})})}function T0(e,o){for(var n={},t=0,i=o.length;t<i;t++){var a=o[t];a in e&&(n[a]=e[a])}return n}var xi=6;function e6(e){return e}function Mi(e){return[{ok:e}]}function A6(e,o,n){var t=o.docs,i=new Map;t.forEach(function(_){i.has(_.id)?i.get(_.id).push(_):i.set(_.id,[_])});var a=i.size,s=0,v=new Array(a);function p(){var _=[];v.forEach(function(x){x.docs.forEach(function(u){_.push({id:x.id,docs:[u]})})}),n(null,{results:_})}function f(){++s===a&&p()}function M(_,x,u){v[_]={id:x,docs:u},f()}var c=[];i.forEach(function(_,x){c.push(x)});var w=0;function k(){if(!(w>=c.length)){var _=Math.min(w+xi,c.length),x=c.slice(w,_);y(x,w),w+=x.length}}function y(_,x){_.forEach(function(u,d){var r=x+d,l=i.get(u),h=T0(l[0],["atts_since","attachments"]);h.open_revs=l.map(function(V){return V.rev}),h.open_revs=h.open_revs.filter(e6);var m=e6;h.open_revs.length===0&&(delete h.open_revs,m=Mi),["revs","attachments","binary","ajax","latest"].forEach(function(V){V in o&&(h[V]=o[V])}),e.get(u,h,function(V,H){var I;V?I=[{error:V}]:I=m(H),M(r,u,I),k()})})}k()}var Qe;try{localStorage.setItem("_pouch_check_localstorage",1),Qe=!!localStorage.getItem("_pouch_check_localstorage")}catch{Qe=!1}function e2(){return Qe}var t0=typeof queueMicrotask=="function"?queueMicrotask:function(o){Promise.resolve().then(o)},Xe=class extends e0.default{constructor(){super(),this._listeners={},e2()&&addEventListener("storage",o=>{this.emit(o.key)})}addListener(o,n,t,i){if(this._listeners[n])return;var a=!1,s=this;function v(){if(!s._listeners[n])return;if(a){a="waiting";return}a=!0;var p=T0(i,["style","include_docs","attachments","conflicts","filter","doc_ids","view","since","query_params","binary","return_docs"]);function f(){a=!1}t.changes(p).on("change",function(M){M.seq>i.since&&!i.cancelled&&(i.since=M.seq,i.onChange(M))}).on("complete",function(){a==="waiting"&&t0(v),a=!1}).on("error",f)}this._listeners[n]=v,this.on(o,v)}removeListener(o,n){n in this._listeners&&(super.removeListener(o,this._listeners[n]),delete this._listeners[n])}notifyLocalWindows(o){e2()&&(localStorage[o]=localStorage[o]==="a"?"b":"a")}notify(o){this.emit(o),this.notifyLocalWindows(o)}};function q1(e){if(typeof console<"u"&&typeof console[e]=="function"){var o=Array.prototype.slice.call(arguments,1);console[e].apply(console,o)}}function ki(e,o){var n=6e5;e=parseInt(e,10)||0,o=parseInt(o,10),o!==o||o<=e?o=(e||1)<<1:o=o+1,o>n&&(e=n>>1,o=n);var t=Math.random(),i=o-e;return~~(i*t+e)}function zi(e){var o=0;return e||(o=2e3),ki(e,o)}function Je(e,o){q1("info","The above "+e+" is totally normal. "+o)}var M1=class extends Error{constructor(o,n,t){super(),this.status=o,this.name=n,this.message=t,this.error=!0}toString(){return JSON.stringify({status:this.status,name:this.name,message:this.message,reason:this.reason})}},or=new M1(401,"unauthorized","Name or password is incorrect."),yi=new M1(400,"bad_request","Missing JSON list of 'docs'"),T1=new M1(404,"not_found","missing"),b0=new M1(409,"conflict","Document update conflict"),j6=new M1(400,"bad_request","_id field must contain a string"),Ci=new M1(412,"missing_id","_id is required for puts"),_i=new M1(400,"bad_request","Only reserved document ids may start with underscore."),ir=new M1(412,"precondition_failed","Database not open"),xt=new M1(500,"unknown_error","Database encountered an unknown error"),I6=new M1(500,"badarg","Some query argument is invalid"),ar=new M1(400,"invalid_request","Request was invalid"),Bi=new M1(400,"query_parse_error","Some query parameter is invalid"),t6=new M1(500,"doc_validation","Bad special document member"),d2=new M1(400,"bad_request","Something wrong with the request"),Ee=new M1(400,"bad_request","Document must be a JSON object"),sr=new M1(404,"not_found","Database not found"),Mt=new M1(500,"indexed_db_went_bad","unknown"),rr=new M1(500,"web_sql_went_bad","unknown"),lr=new M1(500,"levelDB_went_went_bad","unknown"),cr=new M1(403,"forbidden","Forbidden by design doc validate_doc_update function"),X0=new M1(400,"bad_request","Invalid rev format"),dr=new M1(412,"file_exists","The database could not be created, the file already exists."),bi=new M1(412,"missing_stub","A pre-existing attachment stub wasn't found"),vr=new M1(413,"invalid_url","Provided URL is invalid");function s1(e,o){function n(t){for(var i=Object.getOwnPropertyNames(e),a=0,s=i.length;a<s;a++)typeof e[i[a]]!="function"&&(this[i[a]]=e[i[a]]);this.stack===void 0&&(this.stack=new Error().stack),t!==void 0&&(this.reason=t)}return n.prototype=M1.prototype,new n(o)}function L0(e){if(typeof e!="object"){var o=e;e=xt,e.data=o}return"error"in e&&e.error==="conflict"&&(e.name="conflict",e.status=409),"name"in e||(e.name=e.error||"unknown"),"status"in e||(e.status=500),"message"in e||(e.message=e.message||e.reason),"stack"in e||(e.stack=new Error().stack),e}function Li(e,o,n){try{return!e(o,n)}catch(i){var t="Filter function threw: "+i.toString();return s1(d2,t)}}function kt(e){var o={},n=e.filter&&typeof e.filter=="function";return o.query=e.query_params,function(i){i.doc||(i.doc={});var a=n&&Li(e.filter,i.doc,o);if(typeof a=="object")return a;if(a)return!1;if(!e.include_docs)delete i.doc;else if(!e.attachments)for(var s in i.doc._attachments)Object.prototype.hasOwnProperty.call(i.doc._attachments,s)&&(i.doc._attachments[s].stub=!0);return!0}}function V6(e){var o;if(e?typeof e!="string"?o=s1(j6):/^_/.test(e)&&!/^_(design|local)/.test(e)&&(o=s1(_i)):o=s1(Ci),o)throw o}function Y1(e){return typeof e._remote=="boolean"?e._remote:typeof e.type=="function"?(q1("warn","db.type() is deprecated and will be removed in a future version of PouchDB"),e.type()==="http"):!1}function Si(e,o){return"listenerCount"in e?e.listenerCount(o):e0.default.listenerCount(e,o)}function Ye(e){if(!e)return null;var o=e.split("/");return o.length===2?o:o.length===1?[e,e]:null}function n6(e){var o=Ye(e);return o?o.join("/"):null}var o6=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"],i6="queryKey",Ai=/(?:^|&)([^&=]*)=?([^&]*)/g,ji=/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/;function H6(e){for(var o=ji.exec(e),n={},t=14;t--;){var i=o6[t],a=o[t]||"",s=["user","password"].indexOf(i)!==-1;n[i]=s?decodeURIComponent(a):a}return n[i6]={},n[o6[12]].replace(Ai,function(v,p,f){p&&(n[i6][p]=f)}),n}function zt(e,o){var n=[],t=[];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(n.push(i),t.push(o[i]));return n.push(e),Function.apply(null,n).apply(null,t)}function t2(e,o,n){return e.get(o).catch(function(t){if(t.status!==404)throw t;return{}}).then(function(t){var i=t._rev,a=n(t);return a?(a._id=o,a._rev=i,Ii(e,a,n)):{updated:!1,rev:i}})}function Ii(e,o,n){return e.put(o).then(function(t){return{updated:!0,rev:t.rev}},function(t){if(t.status!==409)throw t;return t2(e,o._id,n)})}var yt=function(e){return atob(e)},R0=function(e){return btoa(e)};function Ct(e,o){e=e||[],o=o||{};try{return new Blob(e,o)}catch(a){if(a.name!=="TypeError")throw a;for(var n=typeof BlobBuilder<"u"?BlobBuilder:typeof MSBlobBuilder<"u"?MSBlobBuilder:typeof MozBlobBuilder<"u"?MozBlobBuilder:WebKitBlobBuilder,t=new n,i=0;i<e.length;i+=1)t.append(e[i]);return t.getBlob(o.type)}}function Vi(e){for(var o=e.length,n=new ArrayBuffer(o),t=new Uint8Array(n),i=0;i<o;i++)t[i]=e.charCodeAt(i);return n}function _t(e,o){return Ct([Vi(e)],{type:o})}function Bt(e,o){return _t(yt(e),o)}function Hi(e){for(var o="",n=new Uint8Array(e),t=n.byteLength,i=0;i<t;i++)o+=String.fromCharCode(n[i]);return o}function D6(e,o){var n=new FileReader,t=typeof n.readAsBinaryString=="function";n.onloadend=function(i){var a=i.target.result||"";if(t)return o(a);o(Hi(a))},t?n.readAsBinaryString(e):n.readAsArrayBuffer(e)}function O6(e,o){D6(e,function(n){o(n)})}function bt(e,o){O6(e,function(n){o(R0(n))})}function Di(e,o){var n=new FileReader;n.onloadend=function(t){var i=t.target.result||new ArrayBuffer(0);o(i)},n.readAsArrayBuffer(e)}var Oi=self.setImmediate||self.setTimeout,Ei=32768;function Pi(e){return R0(e)}function Ti(e,o,n,t,i){(n>0||t<o.size)&&(o=o.slice(n,t)),Di(o,function(a){e.append(a),i()})}function Ri(e,o,n,t,i){(n>0||t<o.length)&&(o=o.substring(n,t)),e.appendBinary(o),i()}function Lt(e,o){var n=typeof e=="string",t=n?e.length:e.size,i=Math.min(Ei,t),a=Math.ceil(t/i),s=0,v=n?new Y0.default:new Y0.default.ArrayBuffer,p=n?Ri:Ti;function f(){Oi(c)}function M(){var w=v.end(!0),k=Pi(w);o(k),v.destroy()}function c(){var w=s*i,k=w+i;s++,s<a?p(v,e,w,k,f):p(v,e,w,k,M)}c()}function E6(e){return Y0.default.hash(e)}function P6(e,o){if(!o)return H0().replace(/-/g,"").toLowerCase();var n=Object.assign({},e);return delete n._rev_tree,E6(JSON.stringify(n))}var v2=H0;function u0(e){for(var o,n,t,i=e.rev_tree.slice(),a;a=i.pop();){var s=a.ids,v=s[2],p=a.pos;if(v.length){for(var f=0,M=v.length;f<M;f++)i.push({pos:p+1,ids:v[f]});continue}var c=!!s[1].deleted,w=s[0];(!o||(t!==c?t:n!==p?n<p:o<w))&&(o=w,n=p,t=c)}return n+"-"+o}function m0(e,o){for(var n=e.slice(),t;t=n.pop();)for(var i=t.pos,a=t.ids,s=a[2],v=o(s.length===0,i,a[0],t.ctx,a[1]),p=0,f=s.length;p<f;p++)n.push({pos:i+1,ids:s[p],ctx:v})}function qi(e,o){return e.pos-o.pos}function St(e){var o=[];m0(e,function(i,a,s,v,p){i&&o.push({rev:a+"-"+s,pos:a,opts:p})}),o.sort(qi).reverse();for(var n=0,t=o.length;n<t;n++)delete o[n].pos;return o}function At(e){for(var o=u0(e),n=St(e.rev_tree),t=[],i=0,a=n.length;i<a;i++){var s=n[i];s.rev!==o&&!s.opts.deleted&&t.push(s.rev)}return t}function Fi(e){var o=[];return m0(e.rev_tree,function(n,t,i,a,s){s.status==="available"&&!n&&(o.push(t+"-"+i),s.status="missing")}),o}function Ni(e,o){let n=[],t=e.slice(),i;for(;i=t.pop();){let{pos:a,ids:s}=i,v=`${a}-${s[0]}`,p=s[2];if(n.push(v),v===o){if(p.length!==0)throw new Error("The requested revision is not a leaf");return n.reverse()}(p.length===0||p.length>1)&&(n=[]);for(let f=0,M=p.length;f<M;f++)t.push({pos:a+1,ids:p[f]})}if(n.length===0)throw new Error("The requested revision does not exist");return n.reverse()}function T6(e){for(var o=[],n=e.slice(),t;t=n.pop();){var i=t.pos,a=t.ids,s=a[0],v=a[1],p=a[2],f=p.length===0,M=t.history?t.history.slice():[];M.push({id:s,opts:v}),f&&o.push({pos:i+1-M.length,ids:M});for(var c=0,w=p.length;c<w;c++)n.push({pos:i+1,ids:p[c],history:M})}return o.reverse()}function Ui(e,o){return e.pos-o.pos}function $i(e,o,n){for(var t=0,i=e.length,a;t<i;)a=t+i>>>1,n(e[a],o)<0?t=a+1:i=a;return t}function Gi(e,o,n){var t=$i(e,o,n);e.splice(t,0,o)}function a6(e,o){for(var n,t,i=o,a=e.length;i<a;i++){var s=e[i],v=[s.id,s.opts,[]];t?(t[2].push(v),t=v):n=t=v}return n}function Wi(e,o){return e[0]<o[0]?-1:1}function s6(e,o){for(var n=[{tree1:e,tree2:o}],t=!1;n.length>0;){var i=n.pop(),a=i.tree1,s=i.tree2;(a[1].status||s[1].status)&&(a[1].status=a[1].status==="available"||s[1].status==="available"?"available":"missing");for(var v=0;v<s[2].length;v++){if(!a[2][0]){t="new_leaf",a[2][0]=s[2][v];continue}for(var p=!1,f=0;f<a[2].length;f++)a[2][f][0]===s[2][v][0]&&(n.push({tree1:a[2][f],tree2:s[2][v]}),p=!0);p||(t="new_branch",Gi(a[2],s[2][v],Wi))}}return{conflicts:t,tree:e}}function R6(e,o,n){var t=[],i=!1,a=!1,s;if(!e.length)return{tree:[o],conflicts:"new_leaf"};for(var v=0,p=e.length;v<p;v++){var f=e[v];if(f.pos===o.pos&&f.ids[0]===o.ids[0])s=s6(f.ids,o.ids),t.push({pos:f.pos,ids:s.tree}),i=i||s.conflicts,a=!0;else if(n!==!0){var M=f.pos<o.pos?f:o,c=f.pos<o.pos?o:f,w=c.pos-M.pos,k=[],y=[];for(y.push({ids:M.ids,diff:w,parent:null,parentIdx:null});y.length>0;){var _=y.pop();if(_.diff===0){_.ids[0]===c.ids[0]&&k.push(_);continue}for(var x=_.ids[2],u=0,d=x.length;u<d;u++)y.push({ids:x[u],diff:_.diff-1,parent:_.ids,parentIdx:u})}var r=k[0];r?(s=s6(r.ids,c.ids),r.parent[2][r.parentIdx]=s.tree,t.push({pos:M.pos,ids:M.ids}),i=i||s.conflicts,a=!0):t.push(f)}else t.push(f)}return a||t.push(o),t.sort(Ui),{tree:t,conflicts:i||"internal_node"}}function Zi(e,o){for(var n=T6(e),t,i,a=0,s=n.length;a<s;a++){var v=n[a],p=v.ids,f;if(p.length>o){t||(t={});var M=p.length-o;f={pos:v.pos+M,ids:a6(p,M)};for(var c=0;c<M;c++){var w=v.pos+c+"-"+p[c].id;t[w]=!0}}else f={pos:v.pos,ids:a6(p,0)};i?i=R6(i,f,!0).tree:i=[f]}return t&&m0(i,function(k,y,_){delete t[y+"-"+_]}),{tree:i,revs:t?Object.keys(t):[]}}function q6(e,o,n){var t=R6(e,o),i=Zi(t.tree,n);return{tree:i.tree,stemmedRevs:i.revs,conflicts:t.conflicts}}function Ki(e,o){for(var n=e.slice(),t=o.split("-"),i=parseInt(t[0],10),a=t[1],s;s=n.pop();){if(s.pos===i&&s.ids[0]===a)return!0;for(var v=s.ids[2],p=0,f=v.length;p<f;p++)n.push({pos:s.pos+1,ids:v[p]})}return!1}function Qi(e){return e.ids}function i0(e,o){o||(o=u0(e));for(var n=o.substring(o.indexOf("-")+1),t=e.rev_tree.map(Qi),i;i=t.pop();){if(i[0]===n)return!!i[1].deleted;t=t.concat(i[2])}}function p0(e){return typeof e=="string"&&e.startsWith("_local/")}function Xi(e,o){for(var n=o.rev_tree.slice(),t;t=n.pop();){var i=t.pos,a=t.ids,s=a[0],v=a[1],p=a[2],f=p.length===0,M=t.history?t.history.slice():[];if(M.push({id:s,pos:i,opts:v}),f)for(var c=0,w=M.length;c<w;c++){var k=M[c],y=k.pos+"-"+k.id;if(y===e)return i+"-"+s}for(var _=0,x=p.length;_<x;_++)n.push({pos:i+1,ids:p[_],history:M})}throw new Error("Unable to resolve latest revision for id "+o.id+", rev "+e)}function Ji(e,o,n,t){try{e.emit("change",o,n,t)}catch(i){q1("error",'Error in .on("change", function):',i)}}function Yi(e,o,n){var t=[{rev:e._rev}];n.style==="all_docs"&&(t=St(o.rev_tree).map(function(a){return{rev:a.rev}}));var i={id:o.id,changes:t,doc:e};return i0(o,e._rev)&&(i.deleted=!0),n.conflicts&&(i.doc._conflicts=At(o),i.doc._conflicts.length||delete i.doc._conflicts),i}var et=class extends e0.default{constructor(o,n,t){super(),this.db=o,n=n?A1(n):{};var i=n.complete=L6((v,p)=>{v?Si(this,"error")>0&&this.emit("error",v):this.emit("complete",p),this.removeAllListeners(),o.removeListener("destroyed",a)});t&&(this.on("complete",function(v){t(null,v)}),this.on("error",t));let a=()=>{this.cancel()};o.once("destroyed",a),n.onChange=(v,p,f)=>{this.isCancelled||Ji(this,v,p,f)};var s=new Promise(function(v,p){n.complete=function(f,M){f?p(f):v(M)}});this.once("cancel",function(){o.removeListener("destroyed",a),n.complete(null,{status:"cancelled"})}),this.then=s.then.bind(s),this.catch=s.catch.bind(s),this.then(function(v){i(null,v)},i),o.taskqueue.isReady?this.validateChanges(n):o.taskqueue.addTask(v=>{v?n.complete(v):this.isCancelled?this.emit("cancel"):this.validateChanges(n)})}cancel(){this.isCancelled=!0,this.db.taskqueue.isReady&&this.emit("cancel")}validateChanges(o){var n=o.complete;l1._changesFilterPlugin?l1._changesFilterPlugin.validate(o,t=>{if(t)return n(t);this.doChanges(o)}):this.doChanges(o)}doChanges(o){var n=o.complete;if(o=A1(o),"live"in o&&!("continuous"in o)&&(o.continuous=o.live),o.processChange=Yi,o.since==="latest"&&(o.since="now"),o.since||(o.since=0),o.since==="now"){this.db.info().then(i=>{if(this.isCancelled){n(null,{status:"cancelled"});return}o.since=i.update_seq,this.doChanges(o)},n);return}if(l1._changesFilterPlugin){if(l1._changesFilterPlugin.normalize(o),l1._changesFilterPlugin.shouldFilter(this,o))return l1._changesFilterPlugin.filter(this,o)}else["doc_ids","filter","selector","view"].forEach(function(i){i in o&&q1("warn",'The "'+i+'" option was passed in to changes/replicate, but pouchdb-changes-filter plugin is not installed, so it was ignored. Please install the plugin to enable filtering.')});"descending"in o||(o.descending=!1),o.limit=o.limit===0?1:o.limit,o.complete=n;var t=this.db._changes(o);if(t&&typeof t.cancel=="function"){let i=this.cancel;this.cancel=(...a)=>{t.cancel(),i.apply(this,a)}}}};function Pe(e,o){return function(n,t){n||t[0]&&t[0].error?(n=n||t[0],n.docId=o,e(n)):e(null,t.length?t[0]:t)}}function e8(e){for(var o=0;o<e.length;o++){var n=e[o];if(n._deleted)delete n._attachments;else if(n._attachments)for(var t=Object.keys(n._attachments),i=0;i<t.length;i++){var a=t[i];n._attachments[a]=T0(n._attachments[a],["data","digest","content_type","length","revpos","stub"])}}}function t8(e,o){if(e._id===o._id){let n=e._revisions?e._revisions.start:0,t=o._revisions?o._revisions.start:0;return n-t}return e._id<o._id?-1:1}function n8(e){var o={},n=[];return m0(e,function(t,i,a,s){var v=i+"-"+a;return t&&(o[v]=0),s!==void 0&&n.push({from:s,to:v}),v}),n.reverse(),n.forEach(function(t){o[t.from]===void 0?o[t.from]=1+o[t.to]:o[t.from]=Math.min(o[t.from],1+o[t.to])}),o}function o8(e){var o="limit"in e?e.keys.slice(e.skip,e.limit+e.skip):e.skip>0?e.keys.slice(e.skip):e.keys;e.keys=o,e.skip=0,delete e.limit,e.descending&&(o.reverse(),e.descending=!1)}function F6(e){var o=e._compactionQueue[0],n=o.opts,t=o.callback;e.get("_local/compaction").catch(function(){return!1}).then(function(i){i&&i.last_seq&&(n.last_seq=i.last_seq),e._compact(n,function(a,s){a?t(a):t(null,s),t0(function(){e._compactionQueue.shift(),e._compactionQueue.length&&F6(e)})})})}function i8(e,o,n){return e.get("_local/purges").then(function(t){let i=t.purgeSeq+1;return t.purges.push({docId:o,rev:n,purgeSeq:i}),t.purges.length>self.purged_infos_limit&&t.purges.splice(0,t.purges.length-self.purged_infos_limit),t.purgeSeq=i,t}).catch(function(t){if(t.status!==404)throw t;return{_id:"_local/purges",purges:[{docId:o,rev:n,purgeSeq:0}],purgeSeq:0}}).then(function(t){return e.put(t)})}function a8(e){return e.charAt(0)==="_"?e+" is not a valid attachment name, attachment names cannot start with '_'":!1}function Te(e){return e===null||typeof e!="object"||Array.isArray(e)}var s8=/^\d+-[^-]*$/;function Re(e){return typeof e=="string"&&s8.test(e)}var n2=class extends e0.default{_setup(){this.post=_1("post",function(o,n,t){if(typeof n=="function"&&(t=n,n={}),Te(o))return t(s1(Ee));this.bulkDocs({docs:[o]},n,Pe(t,o._id))}).bind(this),this.put=_1("put",function(o,n,t){if(typeof n=="function"&&(t=n,n={}),Te(o))return t(s1(Ee));if(V6(o._id),"_rev"in o&&!Re(o._rev))return t(s1(X0));if(p0(o._id)&&typeof this._putLocal=="function")return o._deleted?this._removeLocal(o,t):this._putLocal(o,t);let i=s=>{typeof this._put=="function"&&n.new_edits!==!1?this._put(o,n,s):this.bulkDocs({docs:[o]},n,Pe(s,o._id))};n.force&&o._rev?(a(),i(function(s){var v=s?null:{ok:!0,id:o._id,rev:o._rev};t(s,v)})):i(t);function a(){var s=o._rev.split("-"),v=s[1],p=parseInt(s[0],10),f=p+1,M=P6();o._revisions={start:f,ids:[M,v]},o._rev=f+"-"+M,n.new_edits=!1}}).bind(this),this.putAttachment=_1("putAttachment",function(o,n,t,i,a){var s=this;typeof a=="function"&&(a=i,i=t,t=null),typeof a>"u"&&(a=i,i=t,t=null),a||q1("warn","Attachment",n,"on document",o,"is missing content_type");function v(p){var f="_rev"in p?parseInt(p._rev,10):0;return p._attachments=p._attachments||{},p._attachments[n]={content_type:a,data:i,revpos:++f},s.put(p)}return s.get(o).then(function(p){if(p._rev!==t)throw s1(b0);return v(p)},function(p){if(p.reason===T1.message)return v({_id:o});throw p})}).bind(this),this.removeAttachment=_1("removeAttachment",function(o,n,t,i){this.get(o,(a,s)=>{if(a){i(a);return}if(s._rev!==t){i(s1(b0));return}if(!s._attachments)return i();delete s._attachments[n],Object.keys(s._attachments).length===0&&delete s._attachments,this.put(s,i)})}).bind(this),this.remove=_1("remove",function(o,n,t,i){var a;typeof n=="string"?(a={_id:o,_rev:n},typeof t=="function"&&(i=t,t={})):(a=o,typeof n=="function"?(i=n,t={}):(i=t,t=n)),t=t||{},t.was_delete=!0;var s={_id:a._id,_rev:a._rev||t.rev};if(s._deleted=!0,p0(s._id)&&typeof this._removeLocal=="function")return this._removeLocal(a,i);this.bulkDocs({docs:[s]},t,Pe(i,s._id))}).bind(this),this.revsDiff=_1("revsDiff",function(o,n,t){typeof n=="function"&&(t=n,n={});var i=Object.keys(o);if(!i.length)return t(null,{});var a=0,s=new Map;function v(f,M){s.has(f)||s.set(f,{missing:[]}),s.get(f).missing.push(M)}function p(f,M){var c=o[f].slice(0);m0(M,function(w,k,y,_,x){var u=k+"-"+y,d=c.indexOf(u);d!==-1&&(c.splice(d,1),x.status!=="available"&&v(f,u))}),c.forEach(function(w){v(f,w)})}i.forEach(function(f){this._getRevisionTree(f,function(M,c){if(M&&M.status===404&&M.message==="missing")s.set(f,{missing:o[f]});else{if(M)return t(M);p(f,c)}if(++a===i.length){var w={};return s.forEach(function(k,y){w[y]=k}),t(null,w)}})},this)}).bind(this),this.bulkGet=_1("bulkGet",function(o,n){A6(this,o,n)}).bind(this),this.compactDocument=_1("compactDocument",function(o,n,t){this._getRevisionTree(o,(i,a)=>{if(i)return t(i);var s=n8(a),v=[],p=[];Object.keys(s).forEach(function(f){s[f]>n&&v.push(f)}),m0(a,function(f,M,c,w,k){var y=M+"-"+c;k.status==="available"&&v.indexOf(y)!==-1&&p.push(y)}),this._doCompaction(o,p,t)})}).bind(this),this.compact=_1("compact",function(o,n){typeof o=="function"&&(n=o,o={}),o=o||{},this._compactionQueue=this._compactionQueue||[],this._compactionQueue.push({opts:o,callback:n}),this._compactionQueue.length===1&&F6(this)}).bind(this),this.get=_1("get",function(o,n,t){if(typeof n=="function"&&(t=n,n={}),n=n||{},typeof o!="string")return t(s1(j6));if(p0(o)&&typeof this._getLocal=="function")return this._getLocal(o,t);var i=[];let a=()=>{var p=[],f=i.length;if(!f)return t(null,p);i.forEach(M=>{this.get(o,{rev:M,revs:n.revs,latest:n.latest,attachments:n.attachments,binary:n.binary},function(c,w){if(c)p.push({missing:M});else{for(var k,y=0,_=p.length;y<_;y++)if(p[y].ok&&p[y].ok._rev===w._rev){k=!0;break}k||p.push({ok:w})}f--,f||t(null,p)})})};if(n.open_revs){if(n.open_revs==="all")this._getRevisionTree(o,function(p,f){if(p)return t(p);i=St(f).map(function(M){return M.rev}),a()});else if(Array.isArray(n.open_revs)){i=n.open_revs;for(var s=0;s<i.length;s++){var v=i[s];if(!Re(v))return t(s1(X0))}a()}else return t(s1(xt,"function_clause"));return}return this._get(o,n,(p,f)=>{if(p)return p.docId=o,t(p);var M=f.doc,c=f.metadata,w=f.ctx;if(n.conflicts){var k=At(c);k.length&&(M._conflicts=k)}if(i0(c,M._rev)&&(M._deleted=!0),n.revs||n.revs_info){for(var y=M._rev.split("-"),_=parseInt(y[0],10),x=y[1],u=T6(c.rev_tree),d=null,r=0;r<u.length;r++){var l=u[r];let Q=l.ids.findIndex(e1=>e1.id===x);var h=Q===_-1;(h||!d&&Q!==-1)&&(d=l)}if(!d)return p=new Error("invalid rev tree"),p.docId=o,t(p);let K=M._rev.split("-")[1],r1=d.ids.findIndex(Q=>Q.id===K)+1;var m=d.ids.length-r1;if(d.ids.splice(r1,m),d.ids.reverse(),n.revs&&(M._revisions={start:d.pos+d.ids.length-1,ids:d.ids.map(function(Q){return Q.id})}),n.revs_info){var V=d.pos+d.ids.length;M._revs_info=d.ids.map(function(Q){return V--,{rev:V+"-"+Q.id,status:Q.opts.status}})}}if(n.attachments&&M._attachments){var H=M._attachments,I=Object.keys(H).length;if(I===0)return t(null,M);Object.keys(H).forEach(K=>{this._getAttachment(M._id,K,H[K],{binary:n.binary,metadata:c,ctx:w},function(r1,Q){var e1=M._attachments[K];e1.data=Q,delete e1.stub,delete e1.length,--I||t(null,M)})})}else{if(M._attachments)for(var J in M._attachments)Object.prototype.hasOwnProperty.call(M._attachments,J)&&(M._attachments[J].stub=!0);t(null,M)}})}).bind(this),this.getAttachment=_1("getAttachment",function(o,n,t,i){t instanceof Function&&(i=t,t={}),this._get(o,t,(a,s)=>{if(a)return i(a);if(s.doc._attachments&&s.doc._attachments[n])t.ctx=s.ctx,t.binary=!0,t.metadata=s.metadata,this._getAttachment(o,n,s.doc._attachments[n],t,i);else return i(s1(T1))})}).bind(this),this.allDocs=_1("allDocs",function(o,n){if(typeof o=="function"&&(n=o,o={}),o.skip=typeof o.skip<"u"?o.skip:0,o.start_key&&(o.startkey=o.start_key),o.end_key&&(o.endkey=o.end_key),"keys"in o){if(!Array.isArray(o.keys))return n(new TypeError("options.keys must be an array"));var t=["startkey","endkey","key"].filter(function(i){return i in o})[0];if(t){n(s1(Bi,"Query parameter `"+t+"` is not compatible with multi-get"));return}if(!Y1(this)&&(o8(o),o.keys.length===0))return this._allDocs({limit:0},n)}return this._allDocs(o,n)}).bind(this),this.close=_1("close",function(o){return this._closed=!0,this.emit("closed"),this._close(o)}).bind(this),this.info=_1("info",function(o){this._info((n,t)=>{if(n)return o(n);t.db_name=t.db_name||this.name,t.auto_compaction=!!(this.auto_compaction&&!Y1(this)),t.adapter=this.adapter,o(null,t)})}).bind(this),this.id=_1("id",function(o){return this._id(o)}).bind(this),this.bulkDocs=_1("bulkDocs",function(o,n,t){if(typeof n=="function"&&(t=n,n={}),n=n||{},Array.isArray(o)&&(o={docs:o}),!o||!o.docs||!Array.isArray(o.docs))return t(s1(yi));for(var i=0;i<o.docs.length;++i){let p=o.docs[i];if(Te(p))return t(s1(Ee));if("_rev"in p&&!Re(p._rev))return t(s1(X0))}var a;if(o.docs.forEach(function(p){p._attachments&&Object.keys(p._attachments).forEach(function(f){a=a||a8(f),p._attachments[f].content_type||q1("warn","Attachment",f,"on document",p._id,"is missing content_type")})}),a)return t(s1(d2,a));"new_edits"in n||("new_edits"in o?n.new_edits=o.new_edits:n.new_edits=!0);var s=this;!n.new_edits&&!Y1(s)&&o.docs.sort(t8),e8(o.docs);var v=o.docs.map(function(p){return p._id});this._bulkDocs(o,n,function(p,f){if(p)return t(p);if(n.new_edits||(f=f.filter(function(w){return w.error})),!Y1(s))for(var M=0,c=f.length;M<c;M++)f[M].id=f[M].id||v[M];t(null,f)})}).bind(this),this.registerDependentDatabase=_1("registerDependentDatabase",function(o,n){var t=A1(this.__opts);this.__opts.view_adapter&&(t.adapter=this.__opts.view_adapter);var i=new this.constructor(o,t);function a(s){return s.dependentDbs=s.dependentDbs||{},s.dependentDbs[o]?!1:(s.dependentDbs[o]=!0,s)}t2(this,"_local/_pouch_dependentDbs",a).then(function(){n(null,{db:i})}).catch(n)}).bind(this),this.destroy=_1("destroy",function(o,n){typeof o=="function"&&(n=o,o={});var t="use_prefix"in this?this.use_prefix:!0;let i=()=>{this._destroy(o,(a,s)=>{if(a)return n(a);this._destroyed=!0,this.emit("destroyed"),n(null,s||{ok:!0})})};if(Y1(this))return i();this.get("_local/_pouch_dependentDbs",(a,s)=>{if(a)return a.status!==404?n(a):i();var v=s.dependentDbs,p=this.constructor,f=Object.keys(v).map(M=>{var c=t?M.replace(new RegExp("^"+p.prefix),""):M;return new p(c,this.__opts).destroy()});Promise.all(f).then(i,n)})}).bind(this)}_compact(o,n){var t={return_docs:!1,last_seq:o.last_seq||0,since:o.last_seq||0},i=[],a,s=0;let v=M=>{this.activeTasks.update(a,{completed_items:++s}),i.push(this.compactDocument(M.id,0))},p=M=>{this.activeTasks.remove(a,M),n(M)},f=M=>{var c=M.last_seq;Promise.all(i).then(()=>t2(this,"_local/compaction",w=>!w.last_seq||w.last_seq<c?(w.last_seq=c,w):!1)).then(()=>{this.activeTasks.remove(a),n(null,{ok:!0})}).catch(p)};this.info().then(M=>{a=this.activeTasks.add({name:"database_compaction",total_items:M.update_seq-t.last_seq}),this.changes(t).on("change",v).on("complete",f).on("error",p)})}changes(o,n){return typeof o=="function"&&(n=o,o={}),o=o||{},o.return_docs="return_docs"in o?o.return_docs:!o.live,new et(this,o,n)}type(){return typeof this._type=="function"?this._type():this.adapter}};n2.prototype.purge=_1("_purge",function(e,o,n){if(typeof this._purge>"u")return n(s1(xt,"Purge is not implemented in the "+this.adapter+" adapter."));var t=this;t._getRevisionTree(e,(i,a)=>{if(i)return n(i);if(!a)return n(s1(T1));let s;try{s=Ni(a,o)}catch(v){return n(v.message||v)}t._purge(e,s,(v,p)=>{if(v)return n(v);i8(t,e,o).then(function(){return n(null,p)})})})});var tt=class{constructor(){this.isReady=!1,this.failed=!1,this.queue=[]}execute(){var o;if(this.failed)for(;o=this.queue.shift();)o(this.failed);else for(;o=this.queue.shift();)o()}fail(o){this.failed=o,this.execute()}ready(o){this.isReady=!0,this.db=o,this.execute()}addTask(o){this.queue.push(o),this.failed&&this.execute()}};function r8(e,o){var n=e.match(/([a-z-]*):\/\/(.*)/);if(n)return{name:/https?/.test(n[1])?n[1]+"://"+n[2]:n[2],adapter:n[1]};var t=l1.adapters,i=l1.preferredAdapters,a=l1.prefix,s=o.adapter;if(!s)for(var v=0;v<i.length;++v){if(s=i[v],s==="idb"&&"websql"in t&&e2()&&localStorage["_pouch__websqldb_"+a+e]){q1("log",'PouchDB is downgrading "'+e+'" to WebSQL to avoid data loss, because it was already opened with WebSQL.');continue}break}var p=t[s],f=p&&"use_prefix"in p?p.use_prefix:!0;return{name:f?a+e:e,adapter:s}}function l8(e,o){e.prototype=Object.create(o.prototype,{constructor:{value:e}})}function N6(e,o){let n=function(...t){if(!(this instanceof n))return new n(...t);o.apply(this,t)};return l8(n,e),n}function c8(e){function o(t){e.removeListener("closed",n),t||e.constructor.emit("destroyed",e.name)}function n(){e.removeListener("destroyed",o),e.constructor.emit("unref",e)}e.once("destroyed",o),e.once("closed",n),e.constructor.emit("ref",e)}var o2=class extends n2{constructor(o,n){super(),this._setup(o,n)}_setup(o,n){if(super._setup(),n=n||{},o&&typeof o=="object"&&(n=o,o=n.name,delete n.name),n.deterministic_revs===void 0&&(n.deterministic_revs=!0),this.__opts=n=A1(n),this.auto_compaction=n.auto_compaction,this.purged_infos_limit=n.purged_infos_limit||1e3,this.prefix=l1.prefix,typeof o!="string")throw new Error("Missing/invalid DB name");var t=(n.prefix||"")+o,i=r8(t,n);if(n.name=i.name,n.adapter=n.adapter||i.adapter,this.name=o,this._adapter=n.adapter,l1.emit("debug",["adapter","Picked adapter: ",n.adapter]),!l1.adapters[n.adapter]||!l1.adapters[n.adapter].valid())throw new Error("Invalid Adapter: "+n.adapter);if(n.view_adapter&&(!l1.adapters[n.view_adapter]||!l1.adapters[n.view_adapter].valid()))throw new Error("Invalid View Adapter: "+n.view_adapter);this.taskqueue=new tt,this.adapter=n.adapter,l1.adapters[n.adapter].call(this,n,a=>{if(a)return this.taskqueue.fail(a);c8(this),this.emit("created",this),l1.emit("created",this.name),this.taskqueue.ready(this)})}},l1=N6(o2,function(e,o){o2.prototype._setup.call(this,e,o)}),U6=fetch,_0=Headers,nt=class{constructor(){this.tasks={}}list(){return Object.values(this.tasks)}add(o){let n=H0();return this.tasks[n]={id:n,name:o.name,total_items:o.total_items,created_at:new Date().toJSON()},n}get(o){return this.tasks[o]}remove(o,n){return delete this.tasks[o],this.tasks}update(o,n){let t=this.tasks[o];if(typeof t<"u"){let i={id:t.id,name:t.name,created_at:t.created_at,total_items:n.total_items||t.total_items,completed_items:n.completed_items||t.completed_items,updated_at:new Date().toJSON()};this.tasks[o]=i}return this.tasks}};l1.adapters={};l1.preferredAdapters=[];l1.prefix="_pouch_";var r6=new e0.default;function d8(e){Object.keys(e0.default.prototype).forEach(function(n){typeof e0.default.prototype[n]=="function"&&(e[n]=r6[n].bind(r6))});var o=e._destructionListeners=new Map;e.on("ref",function(t){o.has(t.name)||o.set(t.name,[]),o.get(t.name).push(t)}),e.on("unref",function(t){if(o.has(t.name)){var i=o.get(t.name),a=i.indexOf(t);a<0||(i.splice(a,1),i.length>1?o.set(t.name,i):o.delete(t.name))}}),e.on("destroyed",function(t){if(o.has(t)){var i=o.get(t);o.delete(t),i.forEach(function(a){a.emit("destroyed",!0)})}})}d8(l1);l1.adapter=function(e,o,n){o.valid()&&(l1.adapters[e]=o,n&&l1.preferredAdapters.push(e))};l1.plugin=function(e){if(typeof e=="function")e(l1);else{if(typeof e!="object"||Object.keys(e).length===0)throw new Error('Invalid plugin: got "'+e+'", expected an object or a function');Object.keys(e).forEach(function(o){l1.prototype[o]=e[o]})}return this.__defaults&&(l1.__defaults=Object.assign({},this.__defaults)),l1};l1.defaults=function(e){let o=N6(l1,function(n,t){t=t||{},n&&typeof n=="object"&&(t=n,n=t.name,delete t.name),t=Object.assign({},o.__defaults,t),l1.call(this,n,t)});return o.preferredAdapters=l1.preferredAdapters.slice(),Object.keys(l1).forEach(function(n){n in o||(o[n]=l1[n])}),o.__defaults=Object.assign({},this.__defaults,e),o};l1.fetch=function(e,o){return U6(e,o)};l1.prototype.activeTasks=l1.activeTasks=new nt;var v8="9.0.0";function jt(e,o){for(var n=e,t=0,i=o.length;t<i;t++){var a=o[t];if(n=n[a],!n)break}return n}function g8(e,o){return e<o?-1:e>o?1:0}function It(e){for(var o=[],n="",t=0,i=e.length;t<i;t++){var a=e[t];t>0&&e[t-1]==="\\"&&(a==="$"||a===".")?n=n.substring(0,n.length-1)+a:a==="."?(o.push(n),n=""):n+=a}return o.push(n),o}var h8=["$or","$nor","$not"];function $6(e){return h8.indexOf(e)>-1}function G6(e){return Object.keys(e)[0]}function p8(e){return e[G6(e)]}function E0(e){var o={},n={$or:!0,$nor:!0};return e.forEach(function(t){Object.keys(t).forEach(function(i){var a=t[i];if(typeof a!="object"&&(a={$eq:a}),$6(i))if(a instanceof Array){if(n[i]){n[i]=!1,o[i]=a;return}var s=[];o[i].forEach(function(p){Object.keys(a).forEach(function(f){var M=a[f],c=Math.max(Object.keys(p).length,Object.keys(M).length),w=E0([p,M]);Object.keys(w).length<=c||s.push(w)})}),o[i]=s}else o[i]=E0([a]);else{var v=o[i]=o[i]||{};Object.keys(a).forEach(function(p){var f=a[p];if(p==="$gt"||p==="$gte")return u8(p,f,v);if(p==="$lt"||p==="$lte")return m8(p,f,v);if(p==="$ne")return w8(f,v);if(p==="$eq")return f8(f,v);if(p==="$regex")return x8(f,v);v[p]=f})}})}),o}function u8(e,o,n){typeof n.$eq<"u"||(typeof n.$gte<"u"?e==="$gte"?o>n.$gte&&(n.$gte=o):o>=n.$gte&&(delete n.$gte,n.$gt=o):typeof n.$gt<"u"?e==="$gte"?o>n.$gt&&(delete n.$gt,n.$gte=o):o>n.$gt&&(n.$gt=o):n[e]=o)}function m8(e,o,n){typeof n.$eq<"u"||(typeof n.$lte<"u"?e==="$lte"?o<n.$lte&&(n.$lte=o):o<=n.$lte&&(delete n.$lte,n.$lt=o):typeof n.$lt<"u"?e==="$lte"?o<n.$lt&&(delete n.$lt,n.$lte=o):o<n.$lt&&(n.$lt=o):n[e]=o)}function w8(e,o){"$ne"in o?o.$ne.push(e):o.$ne=[e]}function f8(e,o){delete o.$gt,delete o.$gte,delete o.$lt,delete o.$lte,delete o.$ne,o.$eq=e}function x8(e,o){"$regex"in o?o.$regex.push(e):o.$regex=[e]}function W6(e){for(var o in e){if(Array.isArray(e))for(var n in e)e[n].$and&&(e[n]=E0(e[n].$and));var t=e[o];typeof t=="object"&&W6(t)}return e}function Z6(e,o){for(var n in e){n==="$and"&&(o=!0);var t=e[n];typeof t=="object"&&(o=Z6(t,o))}return o}function M8(e){var o=A1(e);Z6(o,!1)&&(o=W6(o),"$and"in o&&(o=E0(o.$and))),["$or","$nor"].forEach(function(s){s in o&&o[s].forEach(function(v){for(var p=Object.keys(v),f=0;f<p.length;f++){var M=p[f],c=v[M];(typeof c!="object"||c===null)&&(v[M]={$eq:c})}})}),"$not"in o&&(o.$not=E0([o.$not]));for(var n=Object.keys(o),t=0;t<n.length;t++){var i=n[t],a=o[i];(typeof a!="object"||a===null)&&(a={$eq:a}),o[i]=a}return ot(o),o}function ot(e){Object.keys(e).forEach(function(o){var n=e[o];Array.isArray(n)?n.forEach(function(t){t&&typeof t=="object"&&ot(t)}):o==="$ne"?e.$ne=[n]:o==="$regex"?e.$regex=[n]:n&&typeof n=="object"&&ot(n)})}function k8(e,o,n){for(var t="",i=n-e.length;t.length<i;)t+=o;return t}function z8(e,o,n){var t=k8(e,o,n);return t+e}var K6=-324,it=3,at="";function B1(e,o){if(e===o)return 0;e=w0(e),o=w0(o);var n=st(e),t=st(o);if(n-t!==0)return n-t;switch(typeof e){case"number":return e-o;case"boolean":return e<o?-1:1;case"string":return L8(e,o)}return Array.isArray(e)?b8(e,o):S8(e,o)}function w0(e){switch(typeof e){case"undefined":return null;case"number":return e===1/0||e===-1/0||isNaN(e)?null:e;case"object":var o=e;if(Array.isArray(e)){var n=e.length;e=new Array(n);for(var t=0;t<n;t++)e[t]=w0(o[t])}else{if(e instanceof Date)return e.toJSON();if(e!==null){e={};for(var i in o)if(Object.prototype.hasOwnProperty.call(o,i)){var a=o[i];typeof a<"u"&&(e[i]=w0(a))}}}}return e}function y8(e){if(e!==null)switch(typeof e){case"boolean":return e?1:0;case"number":return A8(e);case"string":return e.replace(/\u0002/g,"").replace(/\u0001/g,"").replace(/\u0000/g,"");case"object":var o=Array.isArray(e),n=o?e:Object.keys(e),t=-1,i=n.length,a="";if(o)for(;++t<i;)a+=U1(n[t]);else for(;++t<i;){var s=n[t];a+=U1(s)+U1(e[s])}return a}return""}function U1(e){var o="\0";return e=w0(e),st(e)+at+y8(e)+o}function C8(e,o){var n=o,t,i=e[o]==="1";if(i)t=0,o++;else{var a=e[o]==="0";o++;var s="",v=e.substring(o,o+it),p=parseInt(v,10)+K6;for(a&&(p=-p),o+=it;;){var f=e[o];if(f==="\0")break;s+=f,o++}s=s.split("."),s.length===1?t=parseInt(s,10):t=parseFloat(s[0]+"."+s[1]),a&&(t=t-10),p!==0&&(t=parseFloat(t+"e"+p))}return{num:t,length:o-n}}function _8(e,o){var n=e.pop();if(o.length){var t=o[o.length-1];n===t.element&&(o.pop(),t=o[o.length-1]);var i=t.element,a=t.index;if(Array.isArray(i))i.push(n);else if(a===e.length-2){var s=e.pop();i[s]=n}else e.push(n)}}function B8(e){for(var o=[],n=[],t=0;;){var i=e[t++];if(i==="\0"){if(o.length===1)return o.pop();_8(o,n);continue}switch(i){case"1":o.push(null);break;case"2":o.push(e[t]==="1"),t++;break;case"3":var a=C8(e,t);o.push(a.num),t+=a.length;break;case"4":for(var s="";;){var v=e[t];if(v==="\0")break;s+=v,t++}s=s.replace(/\u0001\u0001/g,"\0").replace(/\u0001\u0002/g,"").replace(/\u0002\u0002/g,""),o.push(s);break;case"5":var p={element:[],index:o.length};o.push(p.element),n.push(p);break;case"6":var f={element:{},index:o.length};o.push(f.element),n.push(f);break;default:throw new Error("bad collationIndex or unexpectedly reached end of input: "+i)}}}function b8(e,o){for(var n=Math.min(e.length,o.length),t=0;t<n;t++){var i=B1(e[t],o[t]);if(i!==0)return i}return e.length===o.length?0:e.length>o.length?1:-1}function L8(e,o){return e===o?0:e>o?1:-1}function S8(e,o){for(var n=Object.keys(e),t=Object.keys(o),i=Math.min(n.length,t.length),a=0;a<i;a++){var s=B1(n[a],t[a]);if(s!==0||(s=B1(e[n[a]],o[t[a]]),s!==0))return s}return n.length===t.length?0:n.length>t.length?1:-1}function st(e){var o=["boolean","number","string","object"],n=o.indexOf(typeof e);if(~n)return e===null?1:Array.isArray(e)?5:n<3?n+2:n+3;if(Array.isArray(e))return 5}function A8(e){if(e===0)return"1";var o=e.toExponential().split(/e\+?/),n=parseInt(o[1],10),t=e<0,i=t?"0":"2",a=(t?-n:n)-K6,s=z8(a.toString(),"0",it);i+=at+s;var v=Math.abs(parseFloat(o[0]));t&&(v=10-v);var p=v.toFixed(20);return p=p.replace(/\.?0+$/,""),i+=at+p,i}function j8(e){function o(n){return e.map(function(t){var i=G6(t),a=It(i),s=jt(n,a);return s})}return function(n,t){var i=o(n.doc),a=o(t.doc),s=B1(i,a);return s!==0?s:g8(n.doc._id,t.doc._id)}}function I8(e,o,n){if(e=e.filter(function(s){return B0(s.doc,o.selector,n)}),o.sort){var t=j8(o.sort);e=e.sort(t),typeof o.sort[0]!="string"&&p8(o.sort[0])==="desc"&&(e=e.reverse())}if("limit"in o||"skip"in o){var i=o.skip||0,a=("limit"in o?o.limit:e.length)+i;e=e.slice(i,a)}return e}function B0(e,o,n){return n.every(function(t){var i=o[t],a=It(t),s=jt(e,a);return $6(t)?V8(t,i,e):i2(i,e,a,s)})}function i2(e,o,n,t){return e?typeof e=="object"?Object.keys(e).every(function(i){var a=e[i];if(i.indexOf("$")===0)return l6(i,o,a,n,t);var s=It(i);if(t===void 0&&typeof a!="object"&&s.length>0)return!1;var v=jt(t,s);return typeof a=="object"?i2(a,o,n,v):l6("$eq",o,a,s,v)}):e===t:!0}function V8(e,o,n){return e==="$or"?o.some(function(t){return B0(n,t,Object.keys(t))}):e==="$not"?!B0(n,o,Object.keys(o)):!o.find(function(t){return B0(n,t,Object.keys(t))})}function l6(e,o,n,t,i){if(!d6[e])throw new Error('unknown operator "'+e+'" - should be one of $eq, $lte, $lt, $gt, $gte, $exists, $ne, $in, $nin, $size, $mod, $regex, $elemMatch, $type, $allMatch or $all');return d6[e](o,n,t,i)}function D0(e){return typeof e<"u"&&e!==null}function c0(e){return typeof e<"u"}function H8(e,o){if(typeof e!="number"||parseInt(e,10)!==e)return!1;var n=o[0],t=o[1];return e%n===t}function c6(e,o){return o.some(function(n){return e instanceof Array?e.some(function(t){return B1(n,t)===0}):B1(n,e)===0})}function D8(e,o){return o.every(function(n){return e.some(function(t){return B1(n,t)===0})})}function O8(e,o){return e.length===o}function E8(e,o){var n=new RegExp(o);return n.test(e)}function P8(e,o){switch(o){case"null":return e===null;case"boolean":return typeof e=="boolean";case"number":return typeof e=="number";case"string":return typeof e=="string";case"array":return e instanceof Array;case"object":return{}.toString.call(e)==="[object Object]"}}var d6={$elemMatch:function(e,o,n,t){return!Array.isArray(t)||t.length===0?!1:typeof t[0]=="object"&&t[0]!==null?t.some(function(i){return B0(i,o,Object.keys(o))}):t.some(function(i){return i2(o,e,n,i)})},$allMatch:function(e,o,n,t){return!Array.isArray(t)||t.length===0?!1:typeof t[0]=="object"&&t[0]!==null?t.every(function(i){return B0(i,o,Object.keys(o))}):t.every(function(i){return i2(o,e,n,i)})},$eq:function(e,o,n,t){return c0(t)&&B1(t,o)===0},$gte:function(e,o,n,t){return c0(t)&&B1(t,o)>=0},$gt:function(e,o,n,t){return c0(t)&&B1(t,o)>0},$lte:function(e,o,n,t){return c0(t)&&B1(t,o)<=0},$lt:function(e,o,n,t){return c0(t)&&B1(t,o)<0},$exists:function(e,o,n,t){return o?c0(t):!c0(t)},$mod:function(e,o,n,t){return D0(t)&&H8(t,o)},$ne:function(e,o,n,t){return o.every(function(i){return B1(t,i)!==0})},$in:function(e,o,n,t){return D0(t)&&c6(t,o)},$nin:function(e,o,n,t){return D0(t)&&!c6(t,o)},$size:function(e,o,n,t){return D0(t)&&Array.isArray(t)&&O8(t,o)},$all:function(e,o,n,t){return Array.isArray(t)&&D8(t,o)},$regex:function(e,o,n,t){return D0(t)&&typeof t=="string"&&o.every(function(i){return E8(t,i)})},$type:function(e,o,n,t){return P8(t,o)}};function T8(e,o){if(typeof o!="object")throw new Error("Selector error: expected a JSON object");o=M8(o);var n={doc:e},t=I8([n],{selector:o},Object.keys(o));return t&&t.length===1}function R8(e){return zt(`"use strict";
return `+e+";",{})}function q8(e){var o=["return function(doc) {",'  "use strict";',"  var emitted = false;","  var emit = function (a, b) {","    emitted = true;","  };","  var view = "+e+";","  view(doc);","  if (emitted) {","    return true;","  }","};"].join(`
`);return zt(o,{})}function F8(e,o){if(e.selector&&e.filter&&e.filter!=="_selector"){var n=typeof e.filter=="string"?e.filter:"function";return o(new Error('selector invalid for filter "'+n+'"'))}o()}function N8(e){e.view&&!e.filter&&(e.filter="_view"),e.selector&&!e.filter&&(e.filter="_selector"),e.filter&&typeof e.filter=="string"&&(e.filter==="_view"?e.view=n6(e.view):e.filter=n6(e.filter))}function U8(e,o){return o.filter&&typeof o.filter=="string"&&!o.doc_ids&&!Y1(e.db)}function $8(e,o){var n=o.complete;if(o.filter==="_view"){if(!o.view||typeof o.view!="string"){var t=s1(d2,"`view` filter parameter not found or invalid.");return n(t)}var i=Ye(o.view);e.db.get("_design/"+i[0],function(s,v){if(e.isCancelled)return n(null,{status:"cancelled"});if(s)return n(L0(s));var p=v&&v.views&&v.views[i[1]]&&v.views[i[1]].map;if(!p)return n(s1(T1,v.views?"missing json key: "+i[1]:"missing json key: views"));o.filter=q8(p),e.doChanges(o)})}else if(o.selector)o.filter=function(s){return T8(s,o.selector)},e.doChanges(o);else{var a=Ye(o.filter);e.db.get("_design/"+a[0],function(s,v){if(e.isCancelled)return n(null,{status:"cancelled"});if(s)return n(L0(s));var p=v&&v.filters&&v.filters[a[1]];if(!p)return n(s1(T1,v&&v.filters?"missing json key: "+a[1]:"missing json key: filters"));o.filter=R8(p),e.doChanges(o)})}}function G8(e){e._changesFilterPlugin={validate:F8,normalize:N8,shouldFilter:U8,filter:$8}}l1.plugin(G8);l1.version=v8;function W8(e,o,n){return new Promise(function(t){var i=Ct([""]);let a;if(typeof n=="function"){let v=n(i);a=e.objectStore(o).put(v)}else{let s=n;a=e.objectStore(o).put(i,s)}a.onsuccess=function(){var s=navigator.userAgent.match(/Chrome\/(\d+)/),v=navigator.userAgent.match(/Edge\//);t(v||!s||parseInt(s[1],10)>=43)},a.onerror=e.onabort=function(s){s.preventDefault(),s.stopPropagation(),t(!1)}}).catch(function(){return!1})}function Q6(e){return e.reduce(function(o,n){return o[n]=!0,o},{})}var Z8=Q6(["_id","_rev","_access","_attachments","_deleted","_revisions","_revs_info","_conflicts","_deleted_conflicts","_local_seq","_rev_tree","_replication_id","_replication_state","_replication_state_time","_replication_state_reason","_replication_stats","_removed"]),K8=Q6(["_access","_attachments","_replication_id","_replication_state","_replication_state_time","_replication_state_reason","_replication_stats"]);function v6(e){if(!/^\d+-/.test(e))return s1(X0);var o=e.indexOf("-"),n=e.substring(0,o),t=e.substring(o+1);return{prefix:parseInt(n,10),id:t}}function Q8(e,o){for(var n=e.start-e.ids.length+1,t=e.ids,i=[t[0],o,[]],a=1,s=t.length;a<s;a++)i=[t[a],{status:"missing"},[i]];return[{pos:n,ids:i}]}function X6(e,o,n){n||(n={deterministic_revs:!0});var t,i,a,s={status:"available"};if(e._deleted&&(s.deleted=!0),o)if(e._id||(e._id=v2()),i=P6(e,n.deterministic_revs),e._rev){if(a=v6(e._rev),a.error)return a;e._rev_tree=[{pos:a.prefix,ids:[a.id,{status:"missing"},[[i,s,[]]]]}],t=a.prefix+1}else e._rev_tree=[{pos:1,ids:[i,s,[]]}],t=1;else if(e._revisions&&(e._rev_tree=Q8(e._revisions,s),t=e._revisions.start,i=e._revisions.ids[0]),!e._rev_tree){if(a=v6(e._rev),a.error)return a;t=a.prefix,i=a.id,e._rev_tree=[{pos:t,ids:[i,s,[]]}]}V6(e._id),e._rev=t+"-"+i;var v={metadata:{},data:{}};for(var p in e)if(Object.prototype.hasOwnProperty.call(e,p)){var f=p[0]==="_";if(f&&!Z8[p]){var M=s1(t6,p);throw M.message=t6.message+": "+p,M}else f&&!K8[p]?v.metadata[p.slice(1)]=e[p]:v.data[p]=e[p]}return v}function X8(e){try{return yt(e)}catch{var o=s1(I6,"Attachment is not a valid base64 string");return{error:o}}}function J8(e,o,n){var t=X8(e.data);if(t.error)return n(t.error);e.length=t.length,o==="blob"?e.data=_t(t,e.content_type):o==="base64"?e.data=R0(t):e.data=t,Lt(t,function(i){e.digest="md5-"+i,n()})}function Y8(e,o,n){Lt(e.data,function(t){e.digest="md5-"+t,e.length=e.data.size||e.data.length||0,o==="binary"?O6(e.data,function(i){e.data=i,n()}):o==="base64"?bt(e.data,function(i){e.data=i,n()}):n()})}function e5(e,o,n){if(e.stub)return n();typeof e.data=="string"?J8(e,o,n):Y8(e,o,n)}function t5(e,o,n){if(!e.length)return n();var t=0,i;e.forEach(function(s){var v=s.data&&s.data._attachments?Object.keys(s.data._attachments):[],p=0;if(!v.length)return a();function f(c){i=c,p++,p===v.length&&a()}for(var M in s.data._attachments)Object.prototype.hasOwnProperty.call(s.data._attachments,M)&&e5(s.data._attachments[M],o,f)});function a(){t++,e.length===t&&(i?n(i):n())}}function n5(e,o,n,t,i,a,s,v){if(Ki(o.rev_tree,n.metadata.rev)&&!v)return t[i]=n,a();var p=o.winningRev||u0(o),f="deleted"in o?o.deleted:i0(o,p),M="deleted"in n.metadata?n.metadata.deleted:i0(n.metadata),c=/^1-/.test(n.metadata.rev);if(f&&!M&&v&&c){var w=n.data;w._rev=p,w._id=n.metadata.id,n=X6(w,v)}var k=q6(o.rev_tree,n.metadata.rev_tree[0],e),y=v&&(f&&M&&k.conflicts!=="new_leaf"||!f&&k.conflicts!=="new_leaf"||f&&!M&&k.conflicts==="new_branch");if(y){var _=s1(b0);return t[i]=_,a()}var x=n.metadata.rev;n.metadata.rev_tree=k.tree,n.stemmedRevs=k.stemmedRevs||[],o.rev_map&&(n.metadata.rev_map=o.rev_map);var u=u0(n.metadata),d=i0(n.metadata,u),r=f===d?0:f<d?-1:1,l;x===u?l=d:l=i0(n.metadata,x),s(n,u,d,l,!0,r,i,a)}function o5(e){return e.metadata.rev_tree[0].ids[1].status==="missing"}function i5(e,o,n,t,i,a,s,v,p){e=e||1e3;function f(_,x,u){var d=u0(_.metadata),r=i0(_.metadata,d);if("was_delete"in v&&r)return a[x]=s1(T1,"deleted"),u();var l=M&&o5(_);if(l){var h=s1(b0);return a[x]=h,u()}var m=r?0:1;s(_,d,r,r,!1,m,x,u)}var M=v.new_edits,c=new Map,w=0,k=o.length;function y(){++w===k&&p&&p()}o.forEach(function(_,x){if(_._id&&p0(_._id)){var u=_._deleted?"_removeLocal":"_putLocal";n[u](_,{ctx:i},function(r,l){a[x]=r||l,y()});return}var d=_.metadata.id;c.has(d)?(k--,c.get(d).push([_,x])):c.set(d,[[_,x]])}),c.forEach(function(_,x){var u=0;function d(){++u<_.length?r():y()}function r(){var l=_[u],h=l[0],m=l[1];if(t.has(x))n5(e,t.get(x),h,a,m,d,s,M);else{var V=q6([],h.metadata.rev_tree[0],e);h.metadata.rev_tree=V.tree,h.stemmedRevs=V.stemmedRevs||[],f(h,m,d)}}r()})}var a5=5,L1="document-store",V1="by-sequence",R1="attach-store",g0="attach-seq-store",D1="meta-store",X1="local-store",qe="detect-blob-support";function s5(e){try{return JSON.parse(e)}catch{return ft.default.parse(e)}}function r5(e){try{return JSON.stringify(e)}catch{return ft.default.stringify(e)}}function J1(e){return function(o){var n="unknown_error";o.target&&o.target.error&&(n=o.target.error.name||o.target.error.message),e(s1(Mt,n,o.type))}}function rt(e,o,n){return{data:r5(e),winningRev:o,deletedOrLocal:n?"1":"0",seq:e.seq,id:e.id}}function h0(e){if(!e)return null;var o=s5(e.data);return o.winningRev=e.winningRev,o.deleted=e.deletedOrLocal==="1",o.seq=e.seq,o}function a2(e){if(!e)return e;var o=e._doc_id_rev.lastIndexOf(":");return e._id=e._doc_id_rev.substring(0,o-1),e._rev=e._doc_id_rev.substring(o+1),delete e._doc_id_rev,e}function J6(e,o,n,t){n?t(e?typeof e!="string"?e:Bt(e,o):Ct([""],{type:o})):e?typeof e!="string"?D6(e,function(i){t(R0(i))}):t(e):t("")}function Y6(e,o,n,t){var i=Object.keys(e._attachments||{});if(!i.length)return t&&t();var a=0;function s(){++a===i.length&&t&&t()}function v(p,f){var M=p._attachments[f],c=M.digest,w=n.objectStore(R1).get(c);w.onsuccess=function(k){M.body=k.target.result.body,s()}}i.forEach(function(p){o.attachments&&o.include_docs?v(e,p):(e._attachments[p].stub=!0,s())})}function lt(e,o){return Promise.all(e.map(function(n){if(n.doc&&n.doc._attachments){var t=Object.keys(n.doc._attachments);return Promise.all(t.map(function(i){var a=n.doc._attachments[i];if("body"in a){var s=a.body,v=a.content_type;return new Promise(function(p){J6(s,v,o,function(f){n.doc._attachments[i]=Object.assign(T0(a,["digest","content_type"]),{data:f}),p()})})}}))}}))}function en(e,o,n){var t=[],i=n.objectStore(V1),a=n.objectStore(R1),s=n.objectStore(g0),v=e.length;function p(){v--,v||f()}function f(){t.length&&t.forEach(function(M){var c=s.index("digestSeq").count(IDBKeyRange.bound(M+"::",M+"::\uFFFF",!1,!1));c.onsuccess=function(w){var k=w.target.result;k||a.delete(M)}})}e.forEach(function(M){var c=i.index("_doc_id_rev"),w=o+"::"+M;c.getKey(w).onsuccess=function(k){var y=k.target.result;if(typeof y!="number")return p();i.delete(y);var _=s.index("seq").openCursor(IDBKeyRange.only(y));_.onsuccess=function(x){var u=x.target.result;if(u){var d=u.value.digestSeq.split("::")[0];t.push(d),s.delete(u.primaryKey),u.continue()}else p()}}})}function $1(e,o,n){try{return{txn:e.transaction(o,n)}}catch(t){return{error:t}}}var O0=new Xe;function l5(e,o,n,t,i,a){for(var s=o.docs,v,p,f,M,c,w,k,y,_=0,x=s.length;_<x;_++){var u=s[_];u._id&&p0(u._id)||(u=s[_]=X6(u,n.new_edits,e),u.error&&!k&&(k=u))}if(k)return a(k);var d=!1,r=0,l=new Array(s.length),h=new Map,m=!1,V=t._meta.blobSupport?"blob":"base64";t5(s,V,function(S){if(S)return a(S);H()});function H(){var S=[L1,V1,R1,X1,g0,D1],P=$1(i,S,"readwrite");if(P.error)return a(P.error);v=P.txn,v.onabort=J1(a),v.ontimeout=J1(a),v.oncomplete=Q,p=v.objectStore(L1),f=v.objectStore(V1),M=v.objectStore(R1),c=v.objectStore(g0),w=v.objectStore(D1),w.get(D1).onsuccess=function(Z){y=Z.target.result,K()},c1(function(Z){if(Z)return m=!0,a(Z);r1()})}function I(){d=!0,K()}function J(){i5(e.revs_limit,s,t,h,v,l,d1,n,I)}function K(){!y||!d||(y.docCount+=r,w.put(y))}function r1(){if(!s.length)return;var S=0;function P(){++S===s.length&&J()}function Z(W){var Y=h0(W.target.result);Y&&h.set(Y.id,Y),P()}for(var i1=0,G=s.length;i1<G;i1++){var B=s[i1];if(B._id&&p0(B._id)){P();continue}var A=p.get(B.metadata.id);A.onsuccess=Z}}function Q(){m||(O0.notify(t._meta.name),a(null,l))}function e1(S,P){var Z=M.get(S);Z.onsuccess=function(i1){if(i1.target.result)P();else{var G=s1(bi,"unknown stub attachment with digest "+S);G.status=412,P(G)}}}function c1(S){var P=[];if(s.forEach(function(B){B.data&&B.data._attachments&&Object.keys(B.data._attachments).forEach(function(A){var W=B.data._attachments[A];W.stub&&P.push(W.digest)})}),!P.length)return S();var Z=0,i1;function G(){++Z===P.length&&S(i1)}P.forEach(function(B){e1(B,function(A){A&&!i1&&(i1=A),G()})})}function d1(S,P,Z,i1,G,B,A,W){S.metadata.winningRev=P,S.metadata.deleted=Z;var Y=S.data;Y._id=S.metadata.id,Y._rev=S.metadata.rev,i1&&(Y._deleted=!0);var o1=Y._attachments&&Object.keys(Y._attachments).length;if(o1)return z(S,P,Z,G,A,W);r+=B,K(),g1(S,P,Z,G,A,W)}function g1(S,P,Z,i1,G,B){var A=S.data,W=S.metadata;A._doc_id_rev=W.id+"::"+W.rev,delete A._id,delete A._rev;function Y(b1){var O1=S.stemmedRevs||[];i1&&t.auto_compaction&&(O1=O1.concat(Fi(S.metadata))),O1&&O1.length&&en(O1,S.metadata.id,v),W.seq=b1.target.result;var G1=rt(W,P,Z),m1=p.put(G1);m1.onsuccess=w1}function o1(b1){b1.preventDefault(),b1.stopPropagation();var O1=f.index("_doc_id_rev"),G1=O1.getKey(A._doc_id_rev);G1.onsuccess=function(m1){var p1=f.put(A,m1.target.result);p1.onsuccess=Y}}function w1(){l[G]={ok:!0,id:W.id,rev:W.rev},h.set(S.metadata.id,S.metadata),C(S,W.seq,B)}var z1=f.put(A);z1.onsuccess=Y,z1.onerror=o1}function z(S,P,Z,i1,G,B){var A=S.data,W=0,Y=Object.keys(A._attachments);function o1(){W===Y.length&&g1(S,P,Z,i1,G,B)}function w1(){W++,o1()}Y.forEach(function(z1){var b1=S.data._attachments[z1];if(b1.stub)W++,o1();else{var O1=b1.data;delete b1.data,b1.revpos=parseInt(P,10);var G1=b1.digest;j(G1,O1,w1)}})}function C(S,P,Z){var i1=0,G=Object.keys(S.data._attachments||{});if(!G.length)return Z();function B(){++i1===G.length&&Z()}function A(Y){var o1=S.data._attachments[Y].digest,w1=c.put({seq:P,digestSeq:o1+"::"+P});w1.onsuccess=B,w1.onerror=function(z1){z1.preventDefault(),z1.stopPropagation(),B()}}for(var W=0;W<G.length;W++)A(G[W])}function j(S,P,Z){var i1=M.count(S);i1.onsuccess=function(G){var B=G.target.result;if(B)return Z();var A={digest:S,body:P},W=M.put(A);W.onsuccess=Z}}}function tn(e,o,n,t,i){t===-1&&(t=1e3);var a=typeof e.getAll=="function"&&typeof e.getAllKeys=="function"&&t>1&&!n,s,v,p;function f(k){v=k.target.result,s&&i(s,v,p)}function M(k){s=k.target.result,v&&i(s,v,p)}function c(){if(!s.length)return i();var k=s[s.length-1],y;if(o&&o.upper)try{y=IDBKeyRange.bound(k,o.upper,!0,o.upperOpen)}catch(_){if(_.name==="DataError"&&_.code===0)return i()}else y=IDBKeyRange.lowerBound(k,!0);o=y,s=null,v=null,e.getAll(o,t).onsuccess=f,e.getAllKeys(o,t).onsuccess=M}function w(k){var y=k.target.result;if(!y)return i();i([y.key],[y.value],y)}a?(p={continue:c},e.getAll(o,t).onsuccess=f,e.getAllKeys(o,t).onsuccess=M):n?e.openCursor(o,"prev").onsuccess=w:e.openCursor(o).onsuccess=w}function c5(e,o,n){if(typeof e.getAll=="function"){e.getAll(o).onsuccess=n;return}var t=[];function i(a){var s=a.target.result;s?(t.push(s.value),s.continue()):n({target:{result:t}})}e.openCursor(o).onsuccess=i}function d5(e,o,n){var t=new Array(e.length),i=0;e.forEach(function(a,s){o.get(a).onsuccess=function(v){v.target.result?t[s]=v.target.result:t[s]={key:a,error:"not_found"},i++,i===e.length&&n(e,t,{})}})}function v5(e,o,n,t,i){try{if(e&&o)return i?IDBKeyRange.bound(o,e,!n,!1):IDBKeyRange.bound(e,o,!1,!n);if(e)return i?IDBKeyRange.upperBound(e):IDBKeyRange.lowerBound(e);if(o)return i?IDBKeyRange.lowerBound(o,!n):IDBKeyRange.upperBound(o,!n);if(t)return IDBKeyRange.only(t)}catch(a){return{error:a}}return null}function g5(e,o,n){var t="startkey"in e?e.startkey:!1,i="endkey"in e?e.endkey:!1,a="key"in e?e.key:!1,s="keys"in e?e.keys:!1,v=e.skip||0,p=typeof e.limit=="number"?e.limit:-1,f=e.inclusive_end!==!1,M,c;if(!s&&(M=v5(t,i,f,a,e.descending),c=M&&M.error,c&&!(c.name==="DataError"&&c.code===0)))return n(s1(Mt,c.name,c.message));var w=[L1,V1,D1];e.attachments&&w.push(R1);var k=$1(o,w,"readonly");if(k.error)return n(k.error);var y=k.txn;y.oncomplete=r1,y.onabort=J1(n);var _=y.objectStore(L1),x=y.objectStore(V1),u=y.objectStore(D1),d=x.index("_doc_id_rev"),r=[],l,h;u.get(D1).onsuccess=function(Q){l=Q.target.result.docCount},e.update_seq&&(x.openKeyCursor(null,"prev").onsuccess=Q=>{var e1=Q.target.result;e1&&e1.key&&(h=e1.key)});function m(Q,e1,c1){var d1=Q.id+"::"+c1;d.get(d1).onsuccess=function(z){if(e1.doc=a2(z.target.result)||{},e.conflicts){var C=At(Q);C.length&&(e1.doc._conflicts=C)}Y6(e1.doc,e,y)}}function V(Q,e1){var c1={id:e1.id,key:e1.id,value:{rev:Q}},d1=e1.deleted;d1?s&&(r.push(c1),c1.value.deleted=!0,c1.doc=null):v--<=0&&(r.push(c1),e.include_docs&&m(e1,c1,Q))}function H(Q){for(var e1=0,c1=Q.length;e1<c1&&r.length!==p;e1++){var d1=Q[e1];if(d1.error&&s){r.push(d1);continue}var g1=h0(d1),z=g1.winningRev;V(z,g1)}}function I(Q,e1,c1){c1&&(H(e1),r.length<p&&c1.continue())}function J(Q){var e1=Q.target.result;e.descending&&(e1=e1.reverse()),H(e1)}function K(){var Q={total_rows:l,offset:e.skip,rows:r};e.update_seq&&h!==void 0&&(Q.update_seq=h),n(null,Q)}function r1(){e.attachments?lt(r,e.binary).then(K):K()}if(!(c||p===0)){if(s)return d5(s,_,I);if(p===-1)return c5(_,M,J);tn(_,M,e.descending,p+v,I)}}function h5(e,o){var n=e.objectStore(L1).index("deletedOrLocal");n.count(IDBKeyRange.only("0")).onsuccess=function(t){o(t.target.result)}}var ct=!1,dt=[];function p5(e,o,n,t){try{e(o,n)}catch(i){t.emit("error",i)}}function g6(){ct||!dt.length||(ct=!0,dt.shift()())}function u5(e,o,n){dt.push(function(){e(function(a,s){p5(o,a,s,n),ct=!1,t0(function(){g6(n)})})}),g6()}function m5(e,o,n,t){if(e=A1(e),e.continuous){var i=n+":"+v2();return O0.addListener(n,i,o,e),O0.notify(n),{cancel:function(){O0.removeListener(n,i)}}}var a=e.doc_ids&&new Set(e.doc_ids);e.since=e.since||0;var s=e.since,v="limit"in e?e.limit:-1;v===0&&(v=1);var p=[],f=0,M=kt(e),c=new Map,w,k,y,_;function x(H,I,J){if(!J||!H.length)return;var K=new Array(H.length),r1=new Array(H.length);function Q(d1,g1){var z=e.processChange(g1,d1,e);s=z.seq=d1.seq;var C=M(z);return typeof C=="object"?Promise.reject(C):C?(f++,e.return_docs&&p.push(z),e.attachments&&e.include_docs?new Promise(function(j){Y6(g1,e,w,function(){lt([z],e.binary).then(function(){j(z)})})}):Promise.resolve(z)):Promise.resolve()}function e1(){for(var d1=[],g1=0,z=K.length;g1<z&&f!==v;g1++){var C=K[g1];if(C){var j=r1[g1];d1.push(Q(j,C))}}Promise.all(d1).then(function(S){for(var P=0,Z=S.length;P<Z;P++)S[P]&&e.onChange(S[P])}).catch(e.complete),f!==v&&J.continue()}var c1=0;I.forEach(function(d1,g1){var z=a2(d1),C=H[g1];d(z,C,function(j,S){r1[g1]=j,K[g1]=S,++c1===H.length&&e1()})})}function u(H,I,J,K){if(J.seq!==I)return K();if(J.winningRev===H._rev)return K(J,H);var r1=H._id+"::"+J.winningRev,Q=_.get(r1);Q.onsuccess=function(e1){K(J,a2(e1.target.result))}}function d(H,I,J){if(a&&!a.has(H._id))return J();var K=c.get(H._id);if(K)return u(H,I,K,J);y.get(H._id).onsuccess=function(r1){K=h0(r1.target.result),c.set(H._id,K),u(H,I,K,J)}}function r(){e.complete(null,{results:p,last_seq:s})}function l(){!e.continuous&&e.attachments?lt(p).then(r):r()}var h=[L1,V1];e.attachments&&h.push(R1);var m=$1(t,h,"readonly");if(m.error)return e.complete(m.error);w=m.txn,w.onabort=J1(e.complete),w.oncomplete=l,k=w.objectStore(V1),y=w.objectStore(L1),_=k.index("_doc_id_rev");var V=e.since&&!e.descending?IDBKeyRange.lowerBound(e.since,!0):null;tn(k,V,e.descending,v,x)}var C0=new Map,Fe,Ne=new Map;function nn(e,o){var n=this;u5(function(t){w5(n,e,t)},o,n.constructor)}function w5(e,o,n){var t=o.name,i=null,a=null;e._meta=null;function s(x){return function(u,d){u&&u instanceof Error&&!u.reason&&a&&(u.reason=a),x(u,d)}}function v(x){var u=x.createObjectStore(L1,{keyPath:"id"});x.createObjectStore(V1,{autoIncrement:!0}).createIndex("_doc_id_rev","_doc_id_rev",{unique:!0}),x.createObjectStore(R1,{keyPath:"digest"}),x.createObjectStore(D1,{keyPath:"id",autoIncrement:!1}),x.createObjectStore(qe),u.createIndex("deletedOrLocal","deletedOrLocal",{unique:!1}),x.createObjectStore(X1,{keyPath:"_id"});var d=x.createObjectStore(g0,{autoIncrement:!0});d.createIndex("seq","seq"),d.createIndex("digestSeq","digestSeq",{unique:!0})}function p(x,u){var d=x.objectStore(L1);d.createIndex("deletedOrLocal","deletedOrLocal",{unique:!1}),d.openCursor().onsuccess=function(r){var l=r.target.result;if(l){var h=l.value,m=i0(h);h.deletedOrLocal=m?"1":"0",d.put(h),l.continue()}else u()}}function f(x){x.createObjectStore(X1,{keyPath:"_id"}).createIndex("_doc_id_rev","_doc_id_rev",{unique:!0})}function M(x,u){var d=x.objectStore(X1),r=x.objectStore(L1),l=x.objectStore(V1),h=r.openCursor();h.onsuccess=function(m){var V=m.target.result;if(V){var H=V.value,I=H.id,J=p0(I),K=u0(H);if(J){var r1=I+"::"+K,Q=I+"::",e1=I+"::~",c1=l.index("_doc_id_rev"),d1=IDBKeyRange.bound(Q,e1,!1,!1),g1=c1.openCursor(d1);g1.onsuccess=function(z){if(g1=z.target.result,!g1)r.delete(V.primaryKey),V.continue();else{var C=g1.value;C._doc_id_rev===r1&&d.put(C),l.delete(g1.primaryKey),g1.continue()}}}else V.continue()}else u&&u()}}function c(x){var u=x.createObjectStore(g0,{autoIncrement:!0});u.createIndex("seq","seq"),u.createIndex("digestSeq","digestSeq",{unique:!0})}function w(x,u){var d=x.objectStore(V1),r=x.objectStore(R1),l=x.objectStore(g0),h=r.count();h.onsuccess=function(m){var V=m.target.result;if(!V)return u();d.openCursor().onsuccess=function(H){var I=H.target.result;if(!I)return u();for(var J=I.value,K=I.primaryKey,r1=Object.keys(J._attachments||{}),Q={},e1=0;e1<r1.length;e1++){var c1=J._attachments[r1[e1]];Q[c1.digest]=!0}var d1=Object.keys(Q);for(e1=0;e1<d1.length;e1++){var g1=d1[e1];l.put({seq:K,digestSeq:g1+"::"+K})}I.continue()}}}function k(x){function u(h){return h.data?h0(h):(h.deleted=h.deletedOrLocal==="1",h)}var d=x.objectStore(V1),r=x.objectStore(L1),l=r.openCursor();l.onsuccess=function(h){var m=h.target.result;if(!m)return;var V=u(m.value);V.winningRev=V.winningRev||u0(V);function H(){var J=V.id+"::",K=V.id+"::\uFFFF",r1=d.index("_doc_id_rev").openCursor(IDBKeyRange.bound(J,K)),Q=0;r1.onsuccess=function(e1){var c1=e1.target.result;if(!c1)return V.seq=Q,I();var d1=c1.primaryKey;d1>Q&&(Q=d1),c1.continue()}}function I(){var J=rt(V,V.winningRev,V.deleted),K=r.put(J);K.onsuccess=function(){m.continue()}}if(V.seq)return I();H()}}e._remote=!1,e.type=function(){return"idb"},e._id=S6(function(x){x(null,e._meta.instanceId)}),e._bulkDocs=function(u,d,r){l5(o,u,d,e,i,s(r))},e._get=function(u,d,r){var l,h,m,V=d.ctx;if(!V){var H=$1(i,[L1,V1,R1],"readonly");if(H.error)return r(H.error);V=H.txn}function I(){r(m,{doc:l,metadata:h,ctx:V})}V.objectStore(L1).get(u).onsuccess=function(J){if(h=h0(J.target.result),!h)return m=s1(T1,"missing"),I();var K;if(d.rev)K=d.latest?Xi(d.rev,h):d.rev;else{K=h.winningRev;var r1=i0(h);if(r1)return m=s1(T1,"deleted"),I()}var Q=V.objectStore(V1),e1=h.id+"::"+K;Q.index("_doc_id_rev").get(e1).onsuccess=function(c1){if(l=c1.target.result,l&&(l=a2(l)),!l)return m=s1(T1,"missing"),I();I()}}},e._getAttachment=function(x,u,d,r,l){var h;if(r.ctx)h=r.ctx;else{var m=$1(i,[L1,V1,R1],"readonly");if(m.error)return l(m.error);h=m.txn}var V=d.digest,H=d.content_type;h.objectStore(R1).get(V).onsuccess=function(I){var J=I.target.result.body;J6(J,H,r.binary,function(K){l(null,K)})}},e._info=function(u){var d,r,l=$1(i,[D1,V1],"readonly");if(l.error)return u(l.error);var h=l.txn;h.objectStore(D1).get(D1).onsuccess=function(m){r=m.target.result.docCount},h.objectStore(V1).openKeyCursor(null,"prev").onsuccess=function(m){var V=m.target.result;d=V?V.key:0},h.oncomplete=function(){u(null,{doc_count:r,update_seq:d,idb_attachment_format:e._meta.blobSupport?"binary":"base64"})}},e._allDocs=function(u,d){g5(u,i,s(d))},e._changes=function(u){return m5(u,e,t,i)},e._close=function(x){i.close(),C0.delete(t),x()},e._getRevisionTree=function(x,u){var d=$1(i,[L1],"readonly");if(d.error)return u(d.error);var r=d.txn,l=r.objectStore(L1).get(x);l.onsuccess=function(h){var m=h0(h.target.result);m?u(null,m.rev_tree):u(s1(T1))}},e._doCompaction=function(x,u,d){var r=[L1,V1,R1,g0],l=$1(i,r,"readwrite");if(l.error)return d(l.error);var h=l.txn,m=h.objectStore(L1);m.get(x).onsuccess=function(V){var H=h0(V.target.result);m0(H.rev_tree,function(K,r1,Q,e1,c1){var d1=r1+"-"+Q;u.indexOf(d1)!==-1&&(c1.status="missing")}),en(u,x,h);var I=H.winningRev,J=H.deleted;h.objectStore(L1).put(rt(H,I,J))},h.onabort=J1(d),h.oncomplete=function(){d()}},e._getLocal=function(x,u){var d=$1(i,[X1],"readonly");if(d.error)return u(d.error);var r=d.txn,l=r.objectStore(X1).get(x);l.onerror=J1(u),l.onsuccess=function(h){var m=h.target.result;m?(delete m._doc_id_rev,u(null,m)):u(s1(T1))}},e._putLocal=function(x,u,d){typeof u=="function"&&(d=u,u={}),delete x._revisions;var r=x._rev,l=x._id;r?x._rev="0-"+(parseInt(r.split("-")[1],10)+1):x._rev="0-1";var h=u.ctx,m;if(!h){var V=$1(i,[X1],"readwrite");if(V.error)return d(V.error);h=V.txn,h.onerror=J1(d),h.oncomplete=function(){m&&d(null,m)}}var H=h.objectStore(X1),I;r?(I=H.get(l),I.onsuccess=function(J){var K=J.target.result;if(!K||K._rev!==r)d(s1(b0));else{var r1=H.put(x);r1.onsuccess=function(){m={ok:!0,id:x._id,rev:x._rev},u.ctx&&d(null,m)}}}):(I=H.add(x),I.onerror=function(J){d(s1(b0)),J.preventDefault(),J.stopPropagation()},I.onsuccess=function(){m={ok:!0,id:x._id,rev:x._rev},u.ctx&&d(null,m)})},e._removeLocal=function(x,u,d){typeof u=="function"&&(d=u,u={});var r=u.ctx;if(!r){var l=$1(i,[X1],"readwrite");if(l.error)return d(l.error);r=l.txn,r.oncomplete=function(){h&&d(null,h)}}var h,m=x._id,V=r.objectStore(X1),H=V.get(m);H.onerror=J1(d),H.onsuccess=function(I){var J=I.target.result;!J||J._rev!==x._rev?d(s1(T1)):(V.delete(m),h={ok:!0,id:m,rev:"0-0"},u.ctx&&d(null,h))}},e._destroy=function(x,u){O0.removeAllListeners(t);var d=Ne.get(t);d&&d.result&&(d.result.close(),C0.delete(t));var r=indexedDB.deleteDatabase(t);r.onsuccess=function(){Ne.delete(t),e2()&&t in localStorage&&delete localStorage[t],u(null,{ok:!0})},r.onerror=J1(u)};var y=C0.get(t);if(y)return i=y.idb,e._meta=y.global,t0(function(){n(null,e)});var _=indexedDB.open(t,a5);Ne.set(t,_),_.onupgradeneeded=function(x){var u=x.target.result;if(x.oldVersion<1)return v(u);var d=x.currentTarget.transaction;x.oldVersion<3&&f(u),x.oldVersion<4&&c(u);var r=[p,M,w,k],l=x.oldVersion;function h(){var m=r[l-1];l++,m&&m(d,h)}h()},_.onsuccess=function(x){i=x.target.result,i.onversionchange=function(){i.close(),C0.delete(t)},i.onabort=function(I){q1("error","Database has a global failure",I.target.error),a=I.target.error,i.close(),C0.delete(t)};var u=i.transaction([D1,qe,L1],"readwrite"),d=!1,r,l,h,m;function V(){typeof h>"u"||!d||(e._meta={name:t,instanceId:m,blobSupport:h},C0.set(t,{idb:i,global:e._meta}),n(null,e))}function H(){if(!(typeof l>"u"||typeof r>"u")){var I=t+"_id";I in r?m=r[I]:r[I]=m=v2(),r.docCount=l,u.objectStore(D1).put(r)}}u.objectStore(D1).get(D1).onsuccess=function(I){r=I.target.result||{id:D1},H()},h5(u,function(I){l=I,H()}),Fe||(Fe=W8(u,qe,"key")),Fe.then(function(I){h=I,V()}),u.oncomplete=function(){d=!0,V()},u.onabort=J1(n)},_.onerror=function(x){var u=x.target.error&&x.target.error.message;u?u.indexOf("stored database is a higher version")!==-1&&(u=new Error('This DB was created with the newer "indexeddb" adapter, but you are trying to open it with the older "idb" adapter')):u="Failed to open indexedDB, are you in private browsing mode?",q1("error",u),n(s1(Mt,u))}}nn.valid=function(){try{return typeof indexedDB<"u"&&typeof IDBKeyRange<"u"}catch{return!1}};function f5(e){e.adapter("idb",nn,!0)}function x5(e,o){return new Promise(function(n,t){var i=0,a=0,s=0,v=e.length,p;function f(){i++,e[a++]().then(c,w)}function M(){++s===v?p?t(p):n():k()}function c(){i--,M()}function w(y){i--,p=p||y,M()}function k(){for(;i<o&&a<v;)f()}k()})}var M5=25,k5=50,K0=5e3,z5=1e4,Ue={};function $e(e){let o=e.doc||e.ok,n=o&&o._attachments;n&&Object.keys(n).forEach(function(t){let i=n[t];i.data=Bt(i.data,i.content_type)})}function d0(e){return/^_design/.test(e)?"_design/"+encodeURIComponent(e.slice(8)):e.startsWith("_local/")?"_local/"+encodeURIComponent(e.slice(7)):encodeURIComponent(e)}function h6(e){return!e._attachments||!Object.keys(e._attachments)?Promise.resolve():Promise.all(Object.keys(e._attachments).map(function(o){let n=e._attachments[o];if(n.data&&typeof n.data!="string")return new Promise(function(t){bt(n.data,t)}).then(function(t){n.data=t})}))}function y5(e){if(!e.prefix)return!1;let o=H6(e.prefix).protocol;return o==="http"||o==="https"}function C5(e,o){if(y5(o)){let i=o.name.substr(o.prefix.length);e=o.prefix.replace(/\/?$/,"/")+encodeURIComponent(i)}let n=H6(e);(n.user||n.password)&&(n.auth={username:n.user,password:n.password});let t=n.path.replace(/(^\/|\/$)/g,"").split("/");return n.db=t.pop(),n.db.indexOf("%")===-1&&(n.db=encodeURIComponent(n.db)),n.path=t.join("/"),n}function S1(e,o){return J0(e,e.db+"/"+o)}function J0(e,o){let n=e.path?"/":"";return e.protocol+"://"+e.host+(e.port?":"+e.port:"")+"/"+e.path+n+o}function Q0(e){let o=Object.keys(e);return o.length===0?"":"?"+o.map(n=>n+"="+encodeURIComponent(e[n])).join("&")}function _5(e){let o=typeof navigator<"u"&&navigator.userAgent?navigator.userAgent.toLowerCase():"",n=o.indexOf("msie")!==-1,t=o.indexOf("trident")!==-1,i=o.indexOf("edge")!==-1,a=!("method"in e)||e.method==="GET";return(n||t||i)&&a}function vt(e,o){let n=this,t=C5(e.name,e),i=S1(t,"");e=A1(e);let a=function(c,w){return n1(this,null,function*(){if(w=w||{},w.headers=w.headers||new _0,w.credentials="include",e.auth||t.auth){let _=e.auth||t.auth,x=_.username+":"+_.password,u=R0(unescape(encodeURIComponent(x)));w.headers.set("Authorization","Basic "+u)}let k=e.headers||{};return Object.keys(k).forEach(function(_){w.headers.append(_,k[_])}),_5(w)&&(c+=(c.indexOf("?")===-1?"?":"&")+"_nonce="+Date.now()),yield(e.fetch||U6)(c,w)})};function s(c,w){return _1(c,function(...k){f().then(function(){return w.apply(this,k)}).catch(function(y){k.pop()(y)})}).bind(n)}function v(c,w){return n1(this,null,function*(){let k={};w=w||{},w.headers=w.headers||new _0,w.headers.get("Content-Type")||w.headers.set("Content-Type","application/json"),w.headers.get("Accept")||w.headers.set("Accept","application/json");let y=yield a(c,w);k.ok=y.ok,k.status=y.status;let _=yield y.json();if(k.data=_,!k.ok)throw k.data.status=k.status,L0(k.data);return Array.isArray(k.data)&&(k.data=k.data.map(function(x){return x.error||x.missing?L0(x):x})),k})}let p;function f(){return n1(this,null,function*(){return e.skip_setup?Promise.resolve():p||(p=v(i).catch(function(c){return c&&c.status&&c.status===404?(Je(404,"PouchDB is just detecting if the remote exists."),v(i,{method:"PUT"})):Promise.reject(c)}).catch(function(c){return c&&c.status&&c.status===412?!0:Promise.reject(c)}),p.catch(function(){p=null}),p)})}t0(function(){o(null,n)}),n._remote=!0,n.type=function(){return"http"},n.id=s("id",function(c){return n1(this,null,function*(){let w;try{w=yield(yield a(J0(t,""))).json()}catch{w={}}let k=w&&w.uuid?w.uuid+t.db:S1(t,"");c(null,k)})}),n.compact=s("compact",function(c,w){return n1(this,null,function*(){typeof c=="function"&&(w=c,c={}),c=A1(c),yield v(S1(t,"_compact"),{method:"POST"});function k(){n.info(function(y,_){_&&!_.compact_running?w(null,{ok:!0}):setTimeout(k,c.interval||200)})}k()})}),n.bulkGet=_1("bulkGet",function(c,w){let k=this;function y(d){return n1(this,null,function*(){let r={};c.revs&&(r.revs=!0),c.attachments&&(r.attachments=!0),c.latest&&(r.latest=!0);try{let l=yield v(S1(t,"_bulk_get"+Q0(r)),{method:"POST",body:JSON.stringify({docs:c.docs})});c.attachments&&c.binary&&l.data.results.forEach(function(h){h.docs.forEach($e)}),d(null,l.data)}catch(l){d(l)}})}function _(){let d=k5,r=Math.ceil(c.docs.length/d),l=0,h=new Array(r);function m(V){return function(H,I){h[V]=I.results,++l===r&&w(null,{results:h.flat()})}}for(let V=0;V<r;V++){let H=T0(c,["revs","attachments","binary","latest"]);H.docs=c.docs.slice(V*d,Math.min(c.docs.length,(V+1)*d)),A6(k,H,m(V))}}let x=J0(t,""),u=Ue[x];typeof u!="boolean"?y(function(d,r){d?(Ue[x]=!1,Je(d.status,"PouchDB is just detecting if the remote supports the _bulk_get API."),_()):(Ue[x]=!0,w(null,r))}):u?y(w):_()}),n._info=function(c){return n1(this,null,function*(){try{yield f();let k=yield(yield a(S1(t,""))).json();k.host=S1(t,""),c(null,k)}catch(w){c(w)}})},n.fetch=function(c,w){return n1(this,null,function*(){yield f();let k=c.substring(0,1)==="/"?J0(t,c.substring(1)):S1(t,c);return a(k,w)})},n.get=s("get",function(c,w,k){return n1(this,null,function*(){typeof w=="function"&&(k=w,w={}),w=A1(w);let y={};w.revs&&(y.revs=!0),w.revs_info&&(y.revs_info=!0),w.latest&&(y.latest=!0),w.open_revs&&(w.open_revs!=="all"&&(w.open_revs=JSON.stringify(w.open_revs)),y.open_revs=w.open_revs),w.rev&&(y.rev=w.rev),w.conflicts&&(y.conflicts=w.conflicts),w.update_seq&&(y.update_seq=w.update_seq),c=d0(c);function _(d){let r=d._attachments,l=r&&Object.keys(r);if(!r||!l.length)return;function h(V){return n1(this,null,function*(){let H=r[V],I=d0(d._id)+"/"+M(V)+"?rev="+d._rev,J=yield a(S1(t,I)),K;"buffer"in J?K=yield J.buffer():K=yield J.blob();let r1;if(w.binary){let Q=Object.getOwnPropertyDescriptor(K.__proto__,"type");(!Q||Q.set)&&(K.type=H.content_type),r1=K}else r1=yield new Promise(function(Q){bt(K,Q)});delete H.stub,delete H.length,H.data=r1})}let m=l.map(function(V){return function(){return h(V)}});return x5(m,5)}function x(d){return Array.isArray(d)?Promise.all(d.map(function(r){if(r.ok)return _(r.ok)})):_(d)}let u=S1(t,c+Q0(y));try{let d=yield v(u);w.attachments&&(yield x(d.data)),k(null,d.data)}catch(d){d.docId=c,k(d)}})}),n.remove=s("remove",function(c,w,k,y){return n1(this,null,function*(){let _;typeof w=="string"?(_={_id:c,_rev:w},typeof k=="function"&&(y=k,k={})):(_=c,typeof w=="function"?(y=w,k={}):(y=k,k=w));let x=_._rev||k.rev,u=S1(t,d0(_._id))+"?rev="+x;try{let d=yield v(u,{method:"DELETE"});y(null,d.data)}catch(d){y(d)}})});function M(c){return c.split("/").map(encodeURIComponent).join("/")}n.getAttachment=s("getAttachment",function(c,w,k,y){return n1(this,null,function*(){typeof k=="function"&&(y=k,k={});let _=k.rev?"?rev="+k.rev:"",x=S1(t,d0(c))+"/"+M(w)+_,u;try{let d=yield a(x,{method:"GET"});if(!d.ok)throw d;u=d.headers.get("content-type");let r;if(typeof process<"u"&&!process.browser&&typeof d.buffer=="function"?r=yield d.buffer():r=yield d.blob(),typeof process<"u"&&!process.browser){let l=Object.getOwnPropertyDescriptor(r.__proto__,"type");(!l||l.set)&&(r.type=u)}y(null,r)}catch(d){y(d)}})}),n.removeAttachment=s("removeAttachment",function(c,w,k,y){return n1(this,null,function*(){let _=S1(t,d0(c)+"/"+M(w))+"?rev="+k;try{let x=yield v(_,{method:"DELETE"});y(null,x.data)}catch(x){y(x)}})}),n.putAttachment=s("putAttachment",function(c,w,k,y,_,x){return n1(this,null,function*(){typeof _=="function"&&(x=_,_=y,y=k,k=null);let u=d0(c)+"/"+M(w),d=S1(t,u);if(k&&(d+="?rev="+k),typeof y=="string"){let r;try{r=yt(y)}catch{return x(s1(I6,"Attachment is not a valid base64 string"))}y=r?_t(r,_):""}try{let r=yield v(d,{headers:new _0({"Content-Type":_}),method:"PUT",body:y});x(null,r.data)}catch(r){x(r)}})}),n._bulkDocs=function(c,w,k){return n1(this,null,function*(){c.new_edits=w.new_edits;try{yield f(),yield Promise.all(c.docs.map(h6));let y=yield v(S1(t,"_bulk_docs"),{method:"POST",body:JSON.stringify(c)});k(null,y.data)}catch(y){k(y)}})},n._put=function(c,w,k){return n1(this,null,function*(){try{yield f(),yield h6(c);let y=yield v(S1(t,d0(c._id)),{method:"PUT",body:JSON.stringify(c)});k(null,y.data)}catch(y){y.docId=c&&c._id,k(y)}})},n.allDocs=s("allDocs",function(c,w){return n1(this,null,function*(){typeof c=="function"&&(w=c,c={}),c=A1(c);let k={},y,_="GET";c.conflicts&&(k.conflicts=!0),c.update_seq&&(k.update_seq=!0),c.descending&&(k.descending=!0),c.include_docs&&(k.include_docs=!0),c.attachments&&(k.attachments=!0),c.key&&(k.key=JSON.stringify(c.key)),c.start_key&&(c.startkey=c.start_key),c.startkey&&(k.startkey=JSON.stringify(c.startkey)),c.end_key&&(c.endkey=c.end_key),c.endkey&&(k.endkey=JSON.stringify(c.endkey)),typeof c.inclusive_end<"u"&&(k.inclusive_end=!!c.inclusive_end),typeof c.limit<"u"&&(k.limit=c.limit),typeof c.skip<"u"&&(k.skip=c.skip);let x=Q0(k);typeof c.keys<"u"&&(_="POST",y={keys:c.keys});try{let u=yield v(S1(t,"_all_docs"+x),{method:_,body:JSON.stringify(y)});c.include_docs&&c.attachments&&c.binary&&u.data.rows.forEach($e),w(null,u.data)}catch(u){w(u)}})}),n._changes=function(c){let w="batch_size"in c?c.batch_size:M5;c=A1(c),c.continuous&&!("heartbeat"in c)&&(c.heartbeat=z5);let k="timeout"in c?c.timeout:30*1e3;"timeout"in c&&c.timeout&&k-c.timeout<K0&&(k=c.timeout+K0),"heartbeat"in c&&c.heartbeat&&k-c.heartbeat<K0&&(k=c.heartbeat+K0);let y={};"timeout"in c&&c.timeout&&(y.timeout=c.timeout);let _=typeof c.limit<"u"?c.limit:!1,x=_;if(c.style&&(y.style=c.style),(c.include_docs||c.filter&&typeof c.filter=="function")&&(y.include_docs=!0),c.attachments&&(y.attachments=!0),c.continuous&&(y.feed="longpoll"),c.seq_interval&&(y.seq_interval=c.seq_interval),c.conflicts&&(y.conflicts=!0),c.descending&&(y.descending=!0),c.update_seq&&(y.update_seq=!0),"heartbeat"in c&&c.heartbeat&&(y.heartbeat=c.heartbeat),c.filter&&typeof c.filter=="string"&&(y.filter=c.filter),c.view&&typeof c.view=="string"&&(y.filter="_view",y.view=c.view),c.query_params&&typeof c.query_params=="object")for(let H in c.query_params)Object.prototype.hasOwnProperty.call(c.query_params,H)&&(y[H]=c.query_params[H]);let u="GET",d;c.doc_ids?(y.filter="_doc_ids",u="POST",d={doc_ids:c.doc_ids}):c.selector&&(y.filter="_selector",u="POST",d={selector:c.selector});let r=new AbortController,l,h=function(H,I){return n1(this,null,function*(){if(c.aborted)return;y.since=H,typeof y.since=="object"&&(y.since=JSON.stringify(y.since)),c.descending?_&&(y.limit=x):y.limit=!_||x>w?w:x;let J=S1(t,"_changes"+Q0(y)),K={signal:r.signal,method:u,body:JSON.stringify(d)};if(l=H,!c.aborted)try{yield f();let r1=yield v(J,K);I(null,r1.data)}catch(r1){I(r1)}})},m={results:[]},V=function(H,I){if(c.aborted)return;let J=0;if(I&&I.results){J=I.results.length,m.last_seq=I.last_seq;let r1=null,Q=null;typeof I.pending=="number"&&(r1=I.pending),(typeof m.last_seq=="string"||typeof m.last_seq=="number")&&(Q=m.last_seq);let e1={};e1.query=c.query_params,I.results=I.results.filter(function(c1){x--;let d1=kt(c)(c1);return d1&&(c.include_docs&&c.attachments&&c.binary&&$e(c1),c.return_docs&&m.results.push(c1),c.onChange(c1,r1,Q)),d1})}else if(H){c.aborted=!0,c.complete(H);return}I&&I.last_seq&&(l=I.last_seq);let K=_&&x<=0||I&&J<w||c.descending;c.continuous&&!(_&&x<=0)||!K?t0(function(){h(l,V)}):c.complete(null,m)};return h(c.since||0,V),{cancel:function(){c.aborted=!0,r.abort()}}},n.revsDiff=s("revsDiff",function(c,w,k){return n1(this,null,function*(){typeof w=="function"&&(k=w,w={});try{let y=yield v(S1(t,"_revs_diff"),{method:"POST",body:JSON.stringify(c)});k(null,y.data)}catch(y){k(y)}})}),n._close=function(c){c()},n._destroy=function(c,w){return n1(this,null,function*(){try{let k=yield v(S1(t,""),{method:"DELETE"});w(null,k)}catch(k){k.status===404?w(null,{ok:!0}):w(k)}})}}vt.valid=function(){return!0};function B5(e){e.adapter("http",vt,!1),e.adapter("https",vt,!1)}var v0=class e extends Error{constructor(o){super(),this.status=400,this.name="query_parse_error",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}},s2=class e extends Error{constructor(o){super(),this.status=404,this.name="not_found",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}},r2=class e extends Error{constructor(o){super(),this.status=500,this.name="invalid_value",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}};function on(e,o){return o&&e.then(function(n){t0(function(){o(null,n)})},function(n){t0(function(){o(n)})}),e}function b5(e){return function(...o){var n=o.pop(),t=e.apply(this,o);return typeof n=="function"&&on(t,n),t}}function L5(e,o){return e.then(function(n){return o().then(function(){return n})},function(n){return o().then(function(){throw n})})}function Ge(e,o){return function(){var n=arguments,t=this;return e.add(function(){return o.apply(t,n)})}}function p6(e){var o=new Set(e),n=new Array(o.size),t=-1;return o.forEach(function(i){n[++t]=i}),n}function We(e){var o=new Array(e.size),n=-1;return e.forEach(function(t,i){o[++n]=i}),o}function u6(e){var o="builtin "+e+" function requires map values to be numbers or number arrays";return new r2(o)}function gt(e){for(var o=0,n=0,t=e.length;n<t;n++){var i=e[n];if(typeof i!="number")if(Array.isArray(i)){o=typeof o=="number"?[o]:o;for(var a=0,s=i.length;a<s;a++){var v=i[a];if(typeof v!="number")throw u6("_sum");typeof o[a]>"u"?o.push(v):o[a]+=v}}else throw u6("_sum");else typeof o=="number"?o+=i:o[0]+=i}return o}var S5=q1.bind(null,"log"),A5=Array.isArray,j5=JSON.parse;function an(e,o){return zt("return ("+e.replace(/;\s*$/,"")+");",{emit:o,sum:gt,log:S5,isArray:A5,toJSON:j5})}var P0=class{constructor(){this.promise=Promise.resolve()}add(o){return this.promise=this.promise.catch(()=>{}).then(()=>o()),this.promise}finish(){return this.promise}};function m6(e){if(!e)return"undefined";switch(typeof e){case"function":return e.toString();case"string":return e.toString();default:return JSON.stringify(e)}}function I5(e,o){return m6(e)+m6(o)+"undefined"}function w6(e,o,n,t,i,a){return n1(this,null,function*(){let s=I5(n,t),v;if(!i&&(v=e._cachedViews=e._cachedViews||{},v[s]))return v[s];let p=e.info().then(function(f){return n1(this,null,function*(){let M=f.db_name+"-mrview-"+(i?"temp":E6(s));function c(x){x.views=x.views||{};let u=o;u.indexOf("/")===-1&&(u=o+"/"+o);let d=x.views[u]=x.views[u]||{};if(!d[M])return d[M]=!0,x}yield t2(e,"_local/"+a,c);let k=(yield e.registerDependentDatabase(M)).db;k.auto_compaction=!0;let y={name:M,db:k,sourceDB:e,adapter:e.adapter,mapFun:n,reduceFun:t},_;try{_=yield y.db.get("_local/lastSeq")}catch(x){if(x.status!==404)throw x}return y.seq=_?_.seq:0,v&&y.db.once("destroyed",function(){delete v[s]}),y})});return v&&(v[s]=p),p})}var f6={},x6=new P0,V5=50;function Ze(e){return e.indexOf("/")===-1?[e,e]:e.split("/")}function H5(e){return e.length===1&&/^1-/.test(e[0].rev)}function M6(e,o,n){try{e.emit("error",o)}catch{q1("error",`The user's map/reduce function threw an uncaught error.
You can debug this error by doing:
myDatabase.on('error', function (err) { debugger; });
Please double-check your map/reduce function.`),q1("error",o,n)}}function D5(e,o,n,t){function i(z,C,j){try{C(j)}catch(S){M6(z,S,{fun:C,doc:j})}}function a(z,C,j,S,P){try{return{output:C(j,S,P)}}catch(Z){return M6(z,Z,{fun:C,keys:j,values:S,rereduce:P}),{error:Z}}}function s(z,C){let j=B1(z.key,C.key);return j!==0?j:B1(z.value,C.value)}function v(z,C,j){return j=j||0,typeof C=="number"?z.slice(j,C+j):j>0?z.slice(j):z}function p(z){let C=z.value;return C&&typeof C=="object"&&C._id||z.id}function f(z){for(let C of z.rows){let j=C.doc&&C.doc._attachments;if(j)for(let S of Object.keys(j)){let P=j[S];j[S].data=Bt(P.data,P.content_type)}}}function M(z){return function(C){return z.include_docs&&z.attachments&&z.binary&&f(C),C}}function c(z,C,j,S){let P=C[z];typeof P<"u"&&(S&&(P=encodeURIComponent(JSON.stringify(P))),j.push(z+"="+P))}function w(z){if(typeof z<"u"){let C=Number(z);return!isNaN(C)&&C===parseInt(z,10)?C:z}}function k(z){return z.group_level=w(z.group_level),z.limit=w(z.limit),z.skip=w(z.skip),z}function y(z){if(z){if(typeof z!="number")return new v0(`Invalid value for integer: "${z}"`);if(z<0)return new v0(`Invalid value for positive integer: "${z}"`)}}function _(z,C){let j=z.descending?"endkey":"startkey",S=z.descending?"startkey":"endkey";if(typeof z[j]<"u"&&typeof z[S]<"u"&&B1(z[j],z[S])>0)throw new v0("No rows can match your key range, reverse your start_key and end_key or set {descending : true}");if(C.reduce&&z.reduce!==!1){if(z.include_docs)throw new v0("{include_docs:true} is invalid for reduce");if(z.keys&&z.keys.length>1&&!z.group&&!z.group_level)throw new v0("Multi-key fetches for reduce views must use {group: true}")}for(let P of["group_level","limit","skip"]){let Z=y(z[P]);if(Z)throw Z}}function x(z,C,j){return n1(this,null,function*(){let S=[],P,Z="GET",i1;if(c("reduce",j,S),c("include_docs",j,S),c("attachments",j,S),c("limit",j,S),c("descending",j,S),c("group",j,S),c("group_level",j,S),c("skip",j,S),c("stale",j,S),c("conflicts",j,S),c("startkey",j,S,!0),c("start_key",j,S,!0),c("endkey",j,S,!0),c("end_key",j,S,!0),c("inclusive_end",j,S),c("key",j,S,!0),c("update_seq",j,S),S=S.join("&"),S=S===""?"":"?"+S,typeof j.keys<"u"){let W=`keys=${encodeURIComponent(JSON.stringify(j.keys))}`;W.length+S.length+1<=2e3?S+=(S[0]==="?"?"&":"?")+W:(Z="POST",typeof C=="string"?P={keys:j.keys}:C.keys=j.keys)}if(typeof C=="string"){let A=Ze(C),W=yield z.fetch("_design/"+A[0]+"/_view/"+A[1]+S,{headers:new _0({"Content-Type":"application/json"}),method:Z,body:JSON.stringify(P)});i1=W.ok;let Y=yield W.json();if(!i1)throw Y.status=W.status,L0(Y);for(let o1 of Y.rows)if(o1.value&&o1.value.error&&o1.value.error==="builtin_reduce_error")throw new Error(o1.reason);return new Promise(function(o1){o1(Y)}).then(M(j))}P=P||{};for(let A of Object.keys(C))Array.isArray(C[A])?P[A]=C[A]:P[A]=C[A].toString();let G=yield z.fetch("_temp_view"+S,{headers:new _0({"Content-Type":"application/json"}),method:"POST",body:JSON.stringify(P)});i1=G.ok;let B=yield G.json();if(!i1)throw B.status=G.status,L0(B);return new Promise(function(A){A(B)}).then(M(j))})}function u(z,C,j){return new Promise(function(S,P){z._query(C,j,function(Z,i1){if(Z)return P(Z);S(i1)})})}function d(z){return new Promise(function(C,j){z._viewCleanup(function(S,P){if(S)return j(S);C(P)})})}function r(z){return function(C){if(C.status===404)return z;throw C}}function l(z,C,j){return n1(this,null,function*(){let S="_local/doc_"+z,P={_id:S,keys:[]},Z=j.get(z),i1=Z[0],G=Z[1];function B(){return H5(G)?Promise.resolve(P):C.db.get(S).catch(r(P))}function A(w1){return w1.keys.length?C.db.allDocs({keys:w1.keys,include_docs:!0}):Promise.resolve({rows:[]})}function W(w1,z1){let b1=[],O1=new Set;for(let m1 of z1.rows){let p1=m1.doc;if(p1&&(b1.push(p1),O1.add(p1._id),p1._deleted=!i1.has(p1._id),!p1._deleted)){let f1=i1.get(p1._id);"value"in f1&&(p1.value=f1.value)}}let G1=We(i1);for(let m1 of G1)if(!O1.has(m1)){let p1={_id:m1},f1=i1.get(m1);"value"in f1&&(p1.value=f1.value),b1.push(p1)}return w1.keys=p6(G1.concat(w1.keys)),b1.push(w1),b1}let Y=yield B(),o1=yield A(Y);return W(Y,o1)})}function h(z){return z.sourceDB.get("_local/purges").then(function(C){let j=C.purgeSeq;return z.db.get("_local/purgeSeq").then(function(S){return S._rev}).catch(r(void 0)).then(function(S){return z.db.put({_id:"_local/purgeSeq",_rev:S,purgeSeq:j})})}).catch(function(C){if(C.status!==404)throw C})}function m(z,C,j){var S="_local/lastSeq";return z.db.get(S).catch(r({_id:S,seq:0})).then(function(P){var Z=We(C);return Promise.all(Z.map(function(i1){return l(i1,z,C)})).then(function(i1){var G=i1.flat();return P.seq=j,G.push(P),z.db.bulkDocs({docs:G})}).then(()=>h(z))})}function V(z){let C=typeof z=="string"?z:z.name,j=f6[C];return j||(j=f6[C]=new P0),j}function H(z,C){return n1(this,null,function*(){return Ge(V(z),function(){return I(z,C)})()})}function I(z,C){return n1(this,null,function*(){let j,S,P;function Z(m1,p1){let f1={id:S._id,key:w0(m1)};typeof p1<"u"&&p1!==null&&(f1.value=w0(p1)),j.push(f1)}let i1=o(z.mapFun,Z),G=z.seq||0;function B(){return z.sourceDB.info().then(function(m1){P=z.sourceDB.activeTasks.add({name:"view_indexing",total_items:m1.update_seq-G})})}function A(m1,p1){return function(){return m(z,m1,p1)}}let W=0,Y={view:z.name,indexed_docs:W};z.sourceDB.emit("indexing",Y);let o1=new P0;function w1(){return n1(this,null,function*(){let m1=yield z.sourceDB.changes({return_docs:!0,conflicts:!0,include_docs:!0,style:"all_docs",since:G,limit:C.changes_batch_size}),p1=yield z1();return b1(m1,p1)})}function z1(){return z.db.get("_local/purgeSeq").then(function(m1){return m1.purgeSeq}).catch(r(-1)).then(function(m1){return z.sourceDB.get("_local/purges").then(function(p1){let f1=p1.purges.filter(function(P1,j1){return j1>m1}).map(P1=>P1.docId),F1=f1.filter(function(P1,j1){return f1.indexOf(P1)===j1});return Promise.all(F1.map(function(P1){return z.sourceDB.get(P1).then(function(j1){return{docId:P1,doc:j1}}).catch(r({docId:P1}))}))}).catch(r([]))})}function b1(m1,p1){let f1=m1.results;if(!f1.length&&!p1.length)return;for(let j1 of p1)if(f1.findIndex(function(S0){return S0.id===j1.docId})<0){let S0={_id:j1.docId,doc:{_id:j1.docId,_deleted:1},changes:[]};j1.doc&&(S0.doc=j1.doc,S0.changes.push({rev:j1.doc._rev})),f1.push(S0)}let F1=O1(f1);o1.add(A(F1,G)),W=W+f1.length;let P1={view:z.name,last_seq:m1.last_seq,results_count:f1.length,indexed_docs:W};if(z.sourceDB.emit("indexing",P1),z.sourceDB.activeTasks.update(P,{completed_items:W}),!(f1.length<C.changes_batch_size))return w1()}function O1(m1){let p1=new Map;for(let f1 of m1){if(f1.doc._id[0]!=="_"){j=[],S=f1.doc,S._deleted||i(z.sourceDB,i1,S),j.sort(s);let F1=G1(j);p1.set(f1.doc._id,[F1,f1.changes])}G=f1.seq}return p1}function G1(m1){let p1=new Map,f1;for(let F1=0,P1=m1.length;F1<P1;F1++){let j1=m1[F1],g2=[j1.key,j1.id];F1>0&&B1(j1.key,f1)===0&&g2.push(F1),p1.set(U1(g2),j1),f1=j1.key}return p1}try{yield B(),yield w1(),yield o1.finish(),z.seq=G,z.sourceDB.activeTasks.remove(P)}catch(m1){z.sourceDB.activeTasks.remove(P,m1)}})}function J(z,C,j){j.group_level===0&&delete j.group_level;let S=j.group||j.group_level,P=n(z.reduceFun),Z=[],i1=isNaN(j.group_level)?Number.POSITIVE_INFINITY:j.group_level;for(let G of C){let B=Z[Z.length-1],A=S?G.key:null;if(S&&Array.isArray(A)&&(A=A.slice(0,i1)),B&&B1(B.groupKey,A)===0){B.keys.push([G.key,G.id]),B.values.push(G.value);continue}Z.push({keys:[[G.key,G.id]],values:[G.value],groupKey:A})}C=[];for(let G of Z){let B=a(z.sourceDB,P,G.keys,G.values,!1);if(B.error&&B.error instanceof r2)throw B.error;C.push({value:B.error?null:B.output,key:G.groupKey})}return{rows:v(C,j.limit,j.skip)}}function K(z,C){return Ge(V(z),function(){return r1(z,C)})()}function r1(z,C){return n1(this,null,function*(){let j,S=z.reduceFun&&C.reduce!==!1,P=C.skip||0;typeof C.keys<"u"&&!C.keys.length&&(C.limit=0,delete C.keys);function Z(G){return n1(this,null,function*(){G.include_docs=!0;let B=yield z.db.allDocs(G);return j=B.total_rows,B.rows.map(function(A){if("value"in A.doc&&typeof A.doc.value=="object"&&A.doc.value!==null){let Y=Object.keys(A.doc.value).sort(),o1=["id","key","value"];if(!(Y<o1||Y>o1))return A.doc.value}let W=B8(A.doc._id);return{key:W[0],id:W[1],value:"value"in A.doc?A.doc.value:null}})})}function i1(G){return n1(this,null,function*(){let B;if(S?B=J(z,G,C):typeof C.keys>"u"?B={total_rows:j,offset:P,rows:G}:B={total_rows:j,offset:P,rows:v(G,C.limit,C.skip)},C.update_seq&&(B.update_seq=z.seq),C.include_docs){let A=p6(G.map(p)),W=yield z.sourceDB.allDocs({keys:A,include_docs:!0,conflicts:C.conflicts,attachments:C.attachments,binary:C.binary}),Y=new Map;for(let o1 of W.rows)Y.set(o1.id,o1.doc);for(let o1 of G){let w1=p(o1),z1=Y.get(w1);z1&&(o1.doc=z1)}}return B})}if(typeof C.keys<"u"){let B=C.keys.map(function(Y){let o1={startkey:U1([Y]),endkey:U1([Y,{}])};return C.update_seq&&(o1.update_seq=!0),Z(o1)}),W=(yield Promise.all(B)).flat();return i1(W)}else{let G={descending:C.descending};C.update_seq&&(G.update_seq=!0);let B,A;if("start_key"in C&&(B=C.start_key),"startkey"in C&&(B=C.startkey),"end_key"in C&&(A=C.end_key),"endkey"in C&&(A=C.endkey),typeof B<"u"&&(G.startkey=C.descending?U1([B,{}]):U1([B])),typeof A<"u"){let Y=C.inclusive_end!==!1;C.descending&&(Y=!Y),G.endkey=U1(Y?[A,{}]:[A])}if(typeof C.key<"u"){let Y=U1([C.key]),o1=U1([C.key,{}]);G.descending?(G.endkey=Y,G.startkey=o1):(G.startkey=Y,G.endkey=o1)}S||(typeof C.limit=="number"&&(G.limit=C.limit),G.skip=P);let W=yield Z(G);return i1(W)}})}function Q(z){return n1(this,null,function*(){return(yield z.fetch("_view_cleanup",{headers:new _0({"Content-Type":"application/json"}),method:"POST"})).json()})}function e1(z){return n1(this,null,function*(){try{let C=yield z.get("_local/"+e),j=new Map;for(let B of Object.keys(C.views)){let A=Ze(B),W="_design/"+A[0],Y=A[1],o1=j.get(W);o1||(o1=new Set,j.set(W,o1)),o1.add(Y)}let S={keys:We(j),include_docs:!0},P=yield z.allDocs(S),Z={};for(let B of P.rows){let A=B.key.substring(8);for(let W of j.get(B.key)){let Y=A+"/"+W;C.views[Y]||(Y=W);let o1=Object.keys(C.views[Y]),w1=B.doc&&B.doc.views&&B.doc.views[W];for(let z1 of o1)Z[z1]=Z[z1]||w1}}let G=Object.keys(Z).filter(function(B){return!Z[B]}).map(function(B){return Ge(V(B),function(){return new z.constructor(B,z.__opts).destroy()})()});return Promise.all(G).then(function(){return{ok:!0}})}catch(C){if(C.status===404)return{ok:!0};throw C}})}function c1(z,C,j){return n1(this,null,function*(){if(typeof z._query=="function")return u(z,C,j);if(Y1(z))return x(z,C,j);let S={changes_batch_size:z.__opts.view_update_changes_batch_size||V5};if(typeof C!="string")return _(j,C),x6.add(function(){return n1(this,null,function*(){let P=yield w6(z,"temp_view/temp_view",C.map,C.reduce,!0,e);return L5(H(P,S).then(function(){return K(P,j)}),function(){return P.db.destroy()})})}),x6.finish();{let P=C,Z=Ze(P),i1=Z[0],G=Z[1],B=yield z.get("_design/"+i1);if(C=B.views&&B.views[G],!C)throw new s2(`ddoc ${B._id} has no view named ${G}`);t(B,G),_(j,C);let A=yield w6(z,P,C.map,C.reduce,!1,e);return j.stale==="ok"||j.stale==="update_after"?(j.stale==="update_after"&&t0(function(){H(A,S)}),K(A,j)):(yield H(A,S),K(A,j))}})}function d1(z,C,j){let S=this;typeof C=="function"&&(j=C,C={}),C=C?k(C):{},typeof z=="function"&&(z={map:z});let P=Promise.resolve().then(function(){return c1(S,z,C)});return on(P,j),P}let g1=b5(function(){let z=this;return typeof z._viewCleanup=="function"?d(z):Y1(z)?Q(z):e1(z)});return{query:d1,viewCleanup:g1}}var Ke={_sum:function(e,o){return gt(o)},_count:function(e,o){return o.length},_stats:function(e,o){function n(t){for(var i=0,a=0,s=t.length;a<s;a++){var v=t[a];i+=v*v}return i}return{sum:gt(o),min:Math.min.apply(null,o),max:Math.max.apply(null,o),count:o.length,sumsqr:n(o)}}};function O5(e){if(/^_sum/.test(e))return Ke._sum;if(/^_count/.test(e))return Ke._count;if(/^_stats/.test(e))return Ke._stats;if(/^_/.test(e))throw new Error(e+" is not a supported reduce function.")}function E5(e,o){if(typeof e=="function"&&e.length===2){var n=e;return function(t){return n(t,o)}}else return an(e.toString(),o)}function P5(e){var o=e.toString(),n=O5(o);return n||an(o)}function T5(e,o){var n=e.views&&e.views[o];if(typeof n.map!="string")throw new s2("ddoc "+e._id+" has no string view named "+o+", instead found object of type: "+typeof n.map)}var R5="mrviews",sn=D5(R5,E5,P5,T5);function q5(e,o,n){return sn.query.call(this,e,o,n)}function F5(e){return sn.viewCleanup.call(this,e)}var N5={query:q5,viewCleanup:F5};function U5(e,o,n){return!e._attachments||!e._attachments[n]||e._attachments[n].digest!==o._attachments[n].digest}function k6(e,o){var n=Object.keys(o._attachments);return Promise.all(n.map(function(t){return e.getAttachment(o._id,t,{rev:o._rev})}))}function $5(e,o,n){var t=Y1(o)&&!Y1(e),i=Object.keys(n._attachments);return t?e.get(n._id).then(function(a){return Promise.all(i.map(function(s){return U5(a,n,s)?o.getAttachment(n._id,s):e.getAttachment(a._id,s)}))}).catch(function(a){if(a.status!==404)throw a;return k6(o,n)}):k6(o,n)}function G5(e){var o=[];return Object.keys(e).forEach(function(n){var t=e[n].missing;t.forEach(function(i){o.push({id:n,rev:i})})}),{docs:o,revs:!0,latest:!0}}function W5(e,o,n,t){n=A1(n);var i=[],a=!0;function s(){var p=G5(n);if(p.docs.length)return e.bulkGet(p).then(function(f){if(t.cancelled)throw new Error("cancelled");return Promise.all(f.results.map(function(M){return Promise.all(M.docs.map(function(c){var w=c.ok;return c.error&&(a=!1),!w||!w._attachments?w:$5(o,e,w).then(k=>{var y=Object.keys(w._attachments);return k.forEach(function(_,x){var u=w._attachments[y[x]];delete u.stub,delete u.length,u.data=_}),w})}))})).then(function(M){i=i.concat(M.flat().filter(Boolean))})})}function v(){return{ok:a,docs:i}}return Promise.resolve().then(s).then(v)}var z6=1,y6="pouchdb",Z5=5,N1=0;function ht(e,o,n,t,i){return e.get(o).catch(function(a){if(a.status===404)return(e.adapter==="http"||e.adapter==="https")&&Je(404,"PouchDB is just checking if a remote checkpoint exists."),{session_id:t,_id:o,history:[],replicator:y6,version:z6};throw a}).then(function(a){if(!i.cancelled&&a.last_seq!==n)return a.history=(a.history||[]).filter(function(s){return s.session_id!==t}),a.history.unshift({last_seq:n,session_id:t}),a.history=a.history.slice(0,Z5),a.version=z6,a.replicator=y6,a.session_id=t,a.last_seq=n,e.put(a).catch(function(s){if(s.status===409)return ht(e,o,n,t,i);throw s})})}var l2=class{constructor(o,n,t,i,a={writeSourceCheckpoint:!0,writeTargetCheckpoint:!0}){this.src=o,this.target=n,this.id=t,this.returnValue=i,this.opts=a,typeof a.writeSourceCheckpoint>"u"&&(a.writeSourceCheckpoint=!0),typeof a.writeTargetCheckpoint>"u"&&(a.writeTargetCheckpoint=!0)}writeCheckpoint(o,n){var t=this;return this.updateTarget(o,n).then(function(){return t.updateSource(o,n)})}updateTarget(o,n){return this.opts.writeTargetCheckpoint?ht(this.target,this.id,o,n,this.returnValue):Promise.resolve(!0)}updateSource(o,n){if(this.opts.writeSourceCheckpoint){var t=this;return ht(this.src,this.id,o,n,this.returnValue).catch(function(i){if(_6(i))return t.opts.writeSourceCheckpoint=!1,!0;throw i})}else return Promise.resolve(!0)}getCheckpoint(){var o=this;return!o.opts.writeSourceCheckpoint&&!o.opts.writeTargetCheckpoint?Promise.resolve(N1):o.opts&&o.opts.writeSourceCheckpoint&&!o.opts.writeTargetCheckpoint?o.src.get(o.id).then(function(n){return n.last_seq||N1}).catch(function(n){if(n.status!==404)throw n;return N1}):o.target.get(o.id).then(function(n){return o.opts&&o.opts.writeTargetCheckpoint&&!o.opts.writeSourceCheckpoint?n.last_seq||N1:o.src.get(o.id).then(function(t){if(n.version!==t.version)return N1;var i;return n.version?i=n.version.toString():i="undefined",i in C6?C6[i](n,t):N1},function(t){if(t.status===404&&n.last_seq)return o.src.put({_id:o.id,last_seq:N1}).then(function(){return N1},function(i){return _6(i)?(o.opts.writeSourceCheckpoint=!1,n.last_seq):N1});throw t})}).catch(function(n){if(n.status!==404)throw n;return N1})}},C6={undefined:function(e,o){return B1(e.last_seq,o.last_seq)===0?o.last_seq:0},1:function(e,o){return K5(o,e).last_seq}};function K5(e,o){return e.session_id===o.session_id?{last_seq:e.last_seq,history:e.history}:rn(e.history,o.history)}function rn(e,o){var n=e[0],t=e.slice(1),i=o[0],a=o.slice(1);if(!n||o.length===0)return{last_seq:N1,history:[]};var s=n.session_id;if(pt(s,o))return{last_seq:n.last_seq,history:e};var v=i.session_id;return pt(v,t)?{last_seq:i.last_seq,history:a}:rn(t,a)}function pt(e,o){var n=o[0],t=o.slice(1);return!e||o.length===0?!1:e===n.session_id?!0:pt(e,t)}function _6(e){return typeof e.status=="number"&&Math.floor(e.status/100)===4}function ln(e,o,n,t,i){return this instanceof l2?ln:new l2(e,o,n,t,i)}var B6=0;function Q5(e,o,n,t){if(e.retry===!1){o.emit("error",n),o.removeAllListeners();return}if(typeof e.back_off_function!="function"&&(e.back_off_function=zi),o.emit("requestError",n),o.state==="active"||o.state==="pending"){o.emit("paused",n),o.state="stopped";var i=function(){e.current_back_off=B6},a=function(){o.removeListener("active",i)};o.once("paused",a),o.once("active",i)}e.current_back_off=e.current_back_off||B6,e.current_back_off=e.back_off_function(e.current_back_off),setTimeout(t,e.current_back_off)}function X5(e){return Object.keys(e).sort(B1).reduce(function(o,n){return o[n]=e[n],o},{})}function J5(e,o,n){var t=n.doc_ids?n.doc_ids.sort(B1):"",i=n.filter?n.filter.toString():"",a="",s="",v="";return n.selector&&(v=JSON.stringify(n.selector)),n.filter&&n.query_params&&(a=JSON.stringify(X5(n.query_params))),n.filter&&n.filter==="_view"&&(s=n.view.toString()),Promise.all([e.id(),o.id()]).then(function(p){var f=p[0]+p[1]+i+s+a+t+v;return new Promise(function(M){Lt(f,M)})}).then(function(p){return p=p.replace(/\//g,".").replace(/\+/g,"_"),"_local/"+p})}function cn(e,o,n,t,i){var a=[],s,v={seq:0,changes:[],docs:[]},p=!1,f=!1,M=!1,c=0,w=0,k=n.continuous||n.live||!1,y=n.batch_size||100,_=n.batches_limit||10,x=n.style||"all_docs",u=!1,d=n.doc_ids,r=n.selector,l,h,m=[],V=v2(),H;i=i||{ok:!0,start_time:new Date().toISOString(),docs_read:0,docs_written:0,doc_write_failures:0,errors:[]};var I={};t.ready(e,o);function J(){return h?Promise.resolve():J5(e,o,n).then(function(B){l=B;var A={};n.checkpoint===!1?A={writeSourceCheckpoint:!1,writeTargetCheckpoint:!1}:n.checkpoint==="source"?A={writeSourceCheckpoint:!0,writeTargetCheckpoint:!1}:n.checkpoint==="target"?A={writeSourceCheckpoint:!1,writeTargetCheckpoint:!0}:A={writeSourceCheckpoint:!0,writeTargetCheckpoint:!0},h=new ln(e,o,l,t,A)})}function K(){if(m=[],s.docs.length!==0){var B=s.docs,A={timeout:n.timeout};return o.bulkDocs({docs:B,new_edits:!1},A).then(function(W){if(t.cancelled)throw z(),new Error("cancelled");var Y=Object.create(null);W.forEach(function(w1){w1.error&&(Y[w1.id]=w1)});var o1=Object.keys(Y).length;i.doc_write_failures+=o1,i.docs_written+=B.length-o1,B.forEach(function(w1){var z1=Y[w1._id];if(z1){i.errors.push(z1);var b1=(z1.name||"").toLowerCase();if(b1==="unauthorized"||b1==="forbidden")t.emit("denied",A1(z1));else throw z1}else m.push(w1)})},function(W){throw i.doc_write_failures+=B.length,W})}}function r1(){if(s.error)throw new Error("There was a problem getting docs.");i.last_seq=w=s.seq;var B=A1(i);return m.length&&(B.docs=m,typeof s.pending=="number"&&(B.pending=s.pending,delete s.pending),t.emit("change",B)),p=!0,e.info().then(function(A){var W=e.activeTasks.get(H);if(!(!s||!W)){var Y=W.completed_items||0,o1=parseInt(A.update_seq,10)-parseInt(c,10);e.activeTasks.update(H,{completed_items:Y+s.changes.length,total_items:o1})}}),h.writeCheckpoint(s.seq,V).then(function(){if(t.emit("checkpoint",{checkpoint:s.seq}),p=!1,t.cancelled)throw z(),new Error("cancelled");s=void 0,P()}).catch(function(A){throw G(A),A})}function Q(){var B={};return s.changes.forEach(function(A){t.emit("checkpoint",{revs_diff:A}),A.id!=="_user/"&&(B[A.id]=A.changes.map(function(W){return W.rev}))}),o.revsDiff(B).then(function(A){if(t.cancelled)throw z(),new Error("cancelled");s.diffs=A})}function e1(){return W5(e,o,s.diffs,t).then(function(B){s.error=!B.ok,B.docs.forEach(function(A){delete s.diffs[A._id],i.docs_read++,s.docs.push(A)})})}function c1(){if(!(t.cancelled||s)){if(a.length===0){d1(!0);return}s=a.shift(),t.emit("checkpoint",{start_next_batch:s.seq}),Q().then(e1).then(K).then(r1).then(c1).catch(function(B){g1("batch processing terminated with error",B)})}}function d1(B){if(v.changes.length===0){a.length===0&&!s&&((k&&I.live||f)&&(t.state="pending",t.emit("paused")),f&&z());return}(B||f||v.changes.length>=y)&&(a.push(v),v={seq:0,changes:[],docs:[]},(t.state==="pending"||t.state==="stopped")&&(t.state="active",t.emit("active")),c1())}function g1(B,A){M||(A.message||(A.message=B),i.ok=!1,i.status="aborting",a=[],v={seq:0,changes:[],docs:[]},z(A))}function z(B){if(!M&&!(t.cancelled&&(i.status="cancelled",p)))if(i.status=i.status||"complete",i.end_time=new Date().toISOString(),i.last_seq=w,M=!0,e.activeTasks.remove(H,B),B){B=s1(B),B.result=i;var A=(B.name||"").toLowerCase();A==="unauthorized"||A==="forbidden"?(t.emit("error",B),t.removeAllListeners()):Q5(n,t,B,function(){cn(e,o,n,t)})}else t.emit("complete",i),t.removeAllListeners()}function C(B,A,W){if(t.cancelled)return z();typeof A=="number"&&(v.pending=A);var Y=kt(n)(B);if(!Y){var o1=e.activeTasks.get(H);if(o1){var w1=o1.completed_items||0;e.activeTasks.update(H,{completed_items:++w1})}return}v.seq=B.seq||W,v.changes.push(B),t.emit("checkpoint",{pending_batch:v.seq}),t0(function(){d1(a.length===0&&I.live)})}function j(B){if(u=!1,t.cancelled)return z();if(B.results.length>0)I.since=B.results[B.results.length-1].seq,P(),d1(!0);else{var A=function(){k?(I.live=!0,P()):f=!0,d1(!0)};!s&&B.results.length===0?(p=!0,h.writeCheckpoint(B.last_seq,V).then(function(){if(p=!1,i.last_seq=w=B.last_seq,t.cancelled)throw z(),new Error("cancelled");A()}).catch(G)):A()}}function S(B){if(u=!1,t.cancelled)return z();g1("changes rejected",B)}function P(){if(!(!u&&!f&&a.length<_))return;u=!0;function B(){W.cancel()}function A(){t.removeListener("cancel",B)}t._changes&&(t.removeListener("cancel",t._abortChanges),t._changes.cancel()),t.once("cancel",B);var W=e.changes(I).on("change",C);W.then(A,A),W.then(j).catch(S),n.retry&&(t._changes=W,t._abortChanges=B)}function Z(B){return e.info().then(function(A){var W=typeof n.since>"u"?parseInt(A.update_seq,10)-parseInt(B,10):parseInt(A.update_seq,10);return H=e.activeTasks.add({name:`${k?"continuous ":""}replication from ${A.db_name}`,total_items:W}),B})}function i1(){J().then(function(){if(t.cancelled){z();return}return h.getCheckpoint().then(Z).then(function(B){w=B,c=B,I={since:w,limit:y,batch_size:y,style:x,doc_ids:d,selector:r,return_docs:!0},n.filter&&(typeof n.filter!="string"?I.include_docs=!0:I.filter=n.filter),"heartbeat"in n&&(I.heartbeat=n.heartbeat),"timeout"in n&&(I.timeout=n.timeout),n.query_params&&(I.query_params=n.query_params),n.view&&(I.view=n.view),P()})}).catch(function(B){g1("getCheckpoint rejected with ",B)})}function G(B){p=!1,g1("writeCheckpoint completed with error",B)}if(t.cancelled){z();return}t._addedListeners||(t.once("cancel",z),typeof n.complete=="function"&&(t.once("error",n.complete),t.once("complete",function(B){n.complete(null,B)})),t._addedListeners=!0),typeof n.since>"u"?i1():J().then(function(){return p=!0,h.writeCheckpoint(n.since,V)}).then(function(){if(p=!1,t.cancelled){z();return}w=n.since,i1()}).catch(G)}var ut=class extends e0.default{constructor(){super(),this.cancelled=!1,this.state="pending";let o=new Promise((n,t)=>{this.once("complete",n),this.once("error",t)});this.then=function(n,t){return o.then(n,t)},this.catch=function(n){return o.catch(n)},this.catch(function(){})}cancel(){this.cancelled=!0,this.state="cancelled",this.emit("cancel")}ready(o,n){if(this._readyCalled)return;this._readyCalled=!0;let t=()=>{this.cancel()};o.once("destroyed",t),n.once("destroyed",t);function i(){o.removeListener("destroyed",t),n.removeListener("destroyed",t)}this.once("complete",i),this.once("error",i)}};function c2(e,o){var n=o.PouchConstructor;return typeof e=="string"?new n(e,o):e}function mt(e,o,n,t){if(typeof n=="function"&&(t=n,n={}),typeof n>"u"&&(n={}),n.doc_ids&&!Array.isArray(n.doc_ids))throw s1(d2,"`doc_ids` filter parameter is not a list.");n.complete=t,n=A1(n),n.continuous=n.continuous||n.live,n.retry="retry"in n?n.retry:!1,n.PouchConstructor=n.PouchConstructor||this;var i=new ut(n),a=c2(e,n),s=c2(o,n);return cn(a,s,n,i),i}function Y5(e,o,n,t){return typeof n=="function"&&(t=n,n={}),typeof n>"u"&&(n={}),n=A1(n),n.PouchConstructor=n.PouchConstructor||this,e=c2(e,n),o=c2(o,n),new wt(e,o,n,t)}var wt=class extends e0.default{constructor(o,n,t,i){super(),this.canceled=!1;let a=t.push?Object.assign({},t,t.push):t,s=t.pull?Object.assign({},t,t.pull):t;this.push=mt(o,n,a),this.pull=mt(n,o,s),this.pushPaused=!0,this.pullPaused=!0;let v=r=>{this.emit("change",{direction:"pull",change:r})},p=r=>{this.emit("change",{direction:"push",change:r})},f=r=>{this.emit("denied",{direction:"push",doc:r})},M=r=>{this.emit("denied",{direction:"pull",doc:r})},c=()=>{this.pushPaused=!0,this.pullPaused&&this.emit("paused")},w=()=>{this.pullPaused=!0,this.pushPaused&&this.emit("paused")},k=()=>{this.pushPaused=!1,this.pullPaused&&this.emit("active",{direction:"push"})},y=()=>{this.pullPaused=!1,this.pushPaused&&this.emit("active",{direction:"pull"})},_={},x=r=>(l,h)=>{(l==="change"&&(h===v||h===p)||l==="denied"&&(h===M||h===f)||l==="paused"&&(h===w||h===c)||l==="active"&&(h===y||h===k))&&(l in _||(_[l]={}),_[l][r]=!0,Object.keys(_[l]).length===2&&this.removeAllListeners(l))};t.live&&(this.push.on("complete",this.pull.cancel.bind(this.pull)),this.pull.on("complete",this.push.cancel.bind(this.push)));function u(r,l,h){r.listeners(l).indexOf(h)==-1&&r.on(l,h)}this.on("newListener",function(r){r==="change"?(u(this.pull,"change",v),u(this.push,"change",p)):r==="denied"?(u(this.pull,"denied",M),u(this.push,"denied",f)):r==="active"?(u(this.pull,"active",y),u(this.push,"active",k)):r==="paused"&&(u(this.pull,"paused",w),u(this.push,"paused",c))}),this.on("removeListener",function(r){r==="change"?(this.pull.removeListener("change",v),this.push.removeListener("change",p)):r==="denied"?(this.pull.removeListener("denied",M),this.push.removeListener("denied",f)):r==="active"?(this.pull.removeListener("active",y),this.push.removeListener("active",k)):r==="paused"&&(this.pull.removeListener("paused",w),this.push.removeListener("paused",c))}),this.pull.on("removeListener",x("pull")),this.push.on("removeListener",x("push"));let d=Promise.all([this.push,this.pull]).then(r=>{let l={push:r[0],pull:r[1]};return this.emit("complete",l),i&&i(null,l),this.removeAllListeners(),l},r=>{if(this.cancel(),i?i(r):this.emit("error",r),this.removeAllListeners(),i)throw r});this.then=function(r,l){return d.then(r,l)},this.catch=function(r){return d.catch(r)}}cancel(){this.canceled||(this.canceled=!0,this.push.cancel(),this.pull.cancel())}};function ea(e){e.replicate=mt,e.sync=Y5,Object.defineProperty(e.prototype,"replicate",{get:function(){var o=this;return typeof this.replicateMethods>"u"&&(this.replicateMethods={from:function(n,t,i){return o.constructor.replicate(n,o,t,i)},to:function(n,t,i){return o.constructor.replicate(o,n,t,i)}}),this.replicateMethods}}),e.prototype.sync=function(o,n,t){return this.constructor.sync(this,o,n,t)}}l1.plugin(f5).plugin(B5).plugin(N5).plugin(ea);var dn=l1;var vn=(()=>{let o=class o{constructor(){this.newpatient=null,this.db=new dn("register_patient")}getAllPatient(){return n1(this,null,function*(){return(yield this.db.allDocs({include_docs:!0})).rows.map(i=>i.doc)})}addPatient(t){return n1(this,null,function*(){return t._id=D3(),yield this.db.put(t)})}updatePatient(t){return n1(this,null,function*(){return yield this.db.put(t)})}deletePatient(t){return n1(this,null,function*(){if(!t._id||!t._rev)throw new Error("Patient must have _id and _rev to be deleted");return this.db.remove(t._id,t._rev)})}getPatient(t){return n1(this,null,function*(){return yield this.db.get(t)})}syncWithServer(){return n1(this,null,function*(){try{let t=yield fetch("https://jsonplaceholder.typicode.com/posts");if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);let i=yield t.json();console.log("Data synced from server:",i)}catch(t){console.error("Failed to sync with server:",t)}})}};o.\u0275fac=function(i){return new(i||o)},o.\u0275prov=A0({token:o,factory:o.\u0275fac,providedIn:"root"});let e=o;return e})();var na=["video"],oa=["canvas"],ia=["uploadVideo"],aa=e=>({background:e});function sa(e,o){e&1&&(L(0,"ion-card-content"),a1(1,"input",18)(2,"input",19),L(3,"div",20)(4,"ion-item")(5,"ion-label",21),q(6,"First Name"),b(),a1(7,"ion-input",22),b(),L(8,"ion-item")(9,"ion-label",21),q(10,"Last Name"),b(),a1(11,"ion-input",23),b(),L(12,"ion-item")(13,"ion-label",21),q(14,"Age"),b(),a1(15,"ion-input",24),b(),L(16,"ion-item")(17,"ion-label",21),q(18,"Age (Years)"),b(),a1(19,"ion-input",25),b(),L(20,"ion-item",26)(21,"ion-label",27),q(22,"Gender"),b(),L(23,"ion-radio-group",28)(24,"div",29)(25,"label"),a1(26,"ion-radio",30),L(27,"span"),q(28,"Male"),b()(),L(29,"label"),a1(30,"ion-radio",31),L(31,"span"),q(32,"Female"),b()(),L(33,"label"),a1(34,"ion-radio",32),L(35,"span"),q(36,"Other"),b()()()()()(),L(37,"ion-label",27),q(38,"Address:"),b(),L(39,"ion-item"),a1(40,"ion-input",33),b()())}function ra(e,o){if(e&1&&(L(0,"ion-select-option",51),q(1),b()),e&2){let n=o.$implicit;t1("value",n.name),X(),K1(n.name)}}function la(e,o){if(e&1&&(L(0,"ion-select-option",51),q(1),b()),e&2){let n=o.$implicit;t1("value",n.name),X(),K1(n.name)}}function ca(e,o){if(e&1){let n=E1();L(0,"ion-item")(1,"ion-label"),q(2,"State"),b(),L(3,"ion-select",52),u1("ionChange",function(i){y1(n);let a=v1(2);return C1(a.onStateChange(i))}),k1(4,la,2,2,"ion-select-option",43),b()()}if(e&2){let n=v1(2);X(4),t1("ngForOf",n.stateList)}}function da(e,o){if(e&1&&(L(0,"ion-select-option",51),q(1),b()),e&2){let n=o.$implicit;t1("value",n),X(),K1(n)}}function va(e,o){if(e&1&&(L(0,"ion-item")(1,"ion-label"),q(2,"District"),b(),L(3,"ion-select",53),k1(4,da,2,2,"ion-select-option",43),b()()),e&2){let n=v1(2);X(4),t1("ngForOf",n.districtList)}}function ga(e,o){if(e&1){let n=E1();L(0,"ion-card-content")(1,"div",20)(2,"ion-item")(3,"ion-label",21),q(4,"Date of Birth"),b(),a1(5,"ion-input",34),b(),L(6,"ion-item",26)(7,"ion-label",27),q(8,"Marital Status"),b(),L(9,"ion-radio-group",35)(10,"div",29)(11,"label"),a1(12,"ion-radio",36),L(13,"span"),q(14,"Single"),b()(),L(15,"label"),a1(16,"ion-radio",37),L(17,"span"),q(18,"Married"),b()(),L(19,"label"),a1(20,"ion-radio",38),L(21,"span"),q(22,"Separated"),b()(),L(23,"label"),a1(24,"ion-radio",39),L(25,"span"),q(26,"Widow"),b()()()()(),L(27,"ion-item")(28,"ion-label",21),q(29,"Weight (kg)"),b(),a1(30,"ion-input",40),b(),L(31,"ion-item")(32,"ion-label",21),q(33,"Height (cm)"),b(),a1(34,"ion-input",41),b(),L(35,"ion-item")(36,"ion-label"),q(37,"Country"),b(),L(38,"ion-select",42),u1("ionChange",function(i){y1(n);let a=v1();return C1(a.onCountryChange(i))}),k1(39,ra,2,2,"ion-select-option",43),b()(),k1(40,ca,5,1,"ion-item",8)(41,va,5,1,"ion-item",8),L(42,"ion-item")(43,"ion-label",21),q(44,"Block"),b(),a1(45,"ion-input",44),b(),L(46,"ion-item")(47,"ion-label",21),q(48,"Village"),b(),a1(49,"ion-input",45),b(),L(50,"ion-item")(51,"ion-label",21),q(52,"head of household First Name"),b(),a1(53,"ion-input",46),b(),L(54,"ion-item")(55,"ion-label",21),q(56,"head of household Last Name"),b(),a1(57,"ion-input",47),b(),L(58,"ion-item")(59,"ion-label",21),q(60,"Mobile"),b(),a1(61,"ion-input",48),b(),L(62,"ion-item")(63,"ion-label",21),q(64,"Email"),b(),a1(65,"ion-input",49),b(),L(66,"ion-item")(67,"ion-label",21),q(68,"UID"),b(),a1(69,"ion-input",50),b()()()}if(e&2){let n=v1();X(39),t1("ngForOf",n.countryList),X(),t1("ngIf",n.stateList.length),X(),t1("ngIf",n.districtList.length)}}function ha(e,o){if(e&1&&(L(0,"option",51),q(1),b()),e&2){let n=o.$implicit,t=o.index;t1("value",n.deviceId),X(),Q1(" ",n.label||"Camera "+(t+1)," ")}}function pa(e,o){if(e&1&&(L(0,"div",66),a1(1,"ion-img",67),b()),e&2){let n=v1(2);X(),t1("src",n.photoPreviewUrl)}}function ua(e,o){if(e&1){let n=E1();L(0,"ion-card-content")(1,"div",54)(2,"label",55)(3,"strong"),q(4,"Camera:"),b()(),L(5,"select",56),u1("change",function(i){y1(n);let a=v1();return C1(a.switchCamera(i))}),k1(6,ha,2,2,"option",43),b()(),L(7,"div",57)(8,"ion-button",58),u1("click",function(){y1(n);let i=v1();return C1(i.startCamera())}),a1(9,"ion-icon",59),q(10," Start Camera "),b(),L(11,"ion-button",60),u1("click",function(){y1(n);let i=v1();return C1(i.captureImage())}),a1(12,"ion-icon",61),q(13," Take Snapshot "),b()(),L(14,"div",62),a1(15,"video",63,0),b(),a1(17,"canvas",64,1),k1(19,pa,2,1,"div",65),b()}if(e&2){let n=v1();X(6),t1("ngForOf",n.videoDevices),X(13),t1("ngIf",n.photoPreviewUrl)}}function ma(e,o){e&1&&(L(0,"div",79)(1,"ion-item")(2,"ion-label"),q(3,"Document Type"),b(),L(4,"ion-select",80)(5,"ion-select-option",81),q(6,"X-ray"),b(),L(7,"ion-select-option",82),q(8,"CT Scan"),b(),L(9,"ion-select-option",83),q(10,"MRI Scan"),b(),L(11,"ion-select-option",84),q(12,"Dermatoscope"),b(),L(13,"ion-select-option",85),q(14,"ECG"),b(),L(15,"ion-select-option",86),q(16,"CAT Scan"),b(),L(17,"ion-select-option",87),q(18,"Breast Cancer Screening"),b(),L(19,"ion-select-option",88),q(20,"General Image"),b(),L(21,"ion-select-option",89),q(22,"Otoscope"),b(),L(23,"ion-select-option",90),q(24,"Chest Image"),b(),L(25,"ion-select-option",91),q(26,"General Examination"),b()()()())}function wa(e,o){if(e&1&&(L(0,"option",51),q(1),b()),e&2){let n=o.$implicit,t=o.index;t1("value",n.deviceId),X(),Q1(" ",n.label||"Camera "+(t+1)," ")}}function fa(e,o){if(e&1){let n=E1();L(0,"div",54)(1,"label",92)(2,"strong"),q(3,"Camera:"),b()(),L(4,"select",93),u1("change",function(i){y1(n);let a=v1(2);return C1(a.switchUploadCamera(i))}),k1(5,wa,2,2,"option",43),b()()}if(e&2){let n=v1(2);X(5),t1("ngForOf",n.videoDevices)}}function xa(e,o){e&1&&(L(0,"div",94),a1(1,"video",95,2),b())}function Ma(e,o){if(e&1){let n=E1();L(0,"div",79)(1,"ion-button",60),u1("click",function(){y1(n);let i=v1(2);return C1(i.captureUploadImage())}),a1(2,"ion-icon",61),q(3," Take Snapshot "),b()()}}function ka(e,o){if(e&1){let n=E1();L(0,"div",79)(1,"ion-item")(2,"ion-label",27),q(3,"Choose File"),b(),L(4,"input",96),u1("change",function(i){y1(n);let a=v1(2);return C1(a.onFileSelected(i))}),b()()()}}function za(e,o){if(e&1){let n=E1();L(0,"ion-item")(1,"ion-label")(2,"p"),q(3),b(),L(4,"small"),q(5),b()(),L(6,"ion-button",97),u1("click",function(){let i=y1(n).index,a=v1(2);return C1(a.removeDocument(i))}),a1(7,"ion-icon",98),b()()}if(e&2){let n=o.$implicit;X(3),K1(n.fileName),X(2),Q1("Type: ",n.type)}}function ya(e,o){if(e&1){let n=E1();L(0,"ion-card-content")(1,"div",68)(2,"ion-label",69),a1(3,"ion-icon",70),q(4," Upload Document/Image "),b(),L(5,"div",57)(6,"ion-button",71),u1("click",function(){y1(n);let i=v1();return C1(i.onBrowseClick())}),a1(7,"ion-icon",72),q(8," Browse Files "),b(),L(9,"ion-button",73),u1("click",function(){y1(n);let i=v1();return C1(i.onCaptureClick())}),a1(10,"ion-icon",61),q(11," Capture Image "),b()(),k1(12,ma,27,0,"div",74)(13,fa,6,1,"div",75)(14,xa,3,0,"div",76),a1(15,"canvas",64,1),k1(17,Ma,4,0,"div",74)(18,ka,5,0,"div",74),b(),L(19,"ion-list",77),k1(20,za,8,2,"ion-item",78),b()()}if(e&2){let n,t=v1();X(12),t1("ngIf",t.showUploadControls),X(),t1("ngIf",t.showUploadControls&&t.isCaptureMode),X(),t1("ngIf",t.showUploadControls&&t.isCaptureMode),X(3),t1("ngIf",t.showUploadControls&&t.isCaptureMode),X(),t1("ngIf",t.showUploadControls&&!t.isCaptureMode),X(2),t1("ngForOf",(n=t.patientForm.get("documents"))==null?null:n.value)}}function Ca(e,o){if(e&1){let n=E1();L(0,"img",121),u1("click",function(){y1(n);let i=v1().$implicit,a=v1(2);return C1(a.openPhotoPopup(i.profile.imagepath))}),b()}if(e&2){let n=v1().$implicit;t1("src",n.profile.imagepath,Ot)}}function _a(e,o){if(e&1){let n=E1();L(0,"li",124),u1("click",function(){let i=y1(n).$implicit,a=v1(4);return C1(a.openDocPopup(i))}),q(1),b()}if(e&2){let n=o.$implicit;X(),Q1(" \u{1F4C4} ",n.fileName," ")}}function Ba(e,o){if(e&1&&(L(0,"ul",122),k1(1,_a,2,1,"li",123),b()),e&2){let n=v1().$implicit;X(),t1("ngForOf",n.documents)}}function ba(e,o){if(e&1){let n=E1();L(0,"ion-row",113),u1("mouseenter",function(){let i=y1(n).index,a=v1(2);return C1(a.hoverIndex=i)})("mouseleave",function(){y1(n);let i=v1(2);return C1(i.hoverIndex=null)}),L(1,"ion-col",106),q(2),b(),L(3,"ion-col",107),q(4),b(),L(5,"ion-col",108),q(6),b(),L(7,"ion-col",107),q(8),b(),L(9,"ion-col",109),q(10),b(),L(11,"ion-col",107),q(12),b(),L(13,"ion-col",107),q(14),b(),L(15,"ion-col",106),q(16),b(),L(17,"ion-col",110),q(18),b(),L(19,"ion-col",107),k1(20,Ca,1,1,"img",114),b(),L(21,"ion-col",106),k1(22,Ba,2,1,"ul",115),b(),L(23,"ion-col",116)(24,"ion-button",117),u1("click",function(){let i=y1(n),a=i.$implicit,s=i.index,v=v1(2);return C1(v.editPatients(a,s))}),a1(25,"ion-icon",118),q(26,"Edit "),b(),L(27,"ion-button",119),u1("click",function(){let i=y1(n).$implicit,a=v1(2);return C1(a.delete(i))}),a1(28,"ion-icon",120),q(29,"Del "),b()()()}if(e&2){let n=o.$implicit,t=o.index,i=v1(2);t1("ngStyle",Nt(13,aa,i.hoverIndex===t?"#f9f9f9":"white")),X(2),Ft("",n.first_name," ",n.last_name),X(2),K1(n.date_of_birth),X(2),Q1("",n.ageYears,"y"),X(2),K1(n.gender),X(2),K1(n.country),X(2),Q1("",n.height," cm"),X(2),Q1("",n.weight," kg"),X(2),K1(n.mobile),X(2),K1(n.email),X(2),t1("ngIf",n.profile==null?null:n.profile.imagepath),X(2),t1("ngIf",n.documents&&n.documents.length>0)}}function La(e,o){if(e&1&&(L(0,"ion-card",99)(1,"ion-card-header",100)(2,"ion-card-title",101),q(3," \u{1F4CB} Submitted Patient Records "),b(),L(4,"div",102),q(5),b()(),L(6,"ion-card-content",103)(7,"ion-grid",104)(8,"ion-row",105)(9,"ion-col",106),q(10,"Name"),b(),L(11,"ion-col",107),q(12,"DOB"),b(),L(13,"ion-col",108),q(14,"Age"),b(),L(15,"ion-col",107),q(16,"Gender"),b(),L(17,"ion-col",109),q(18,"Country"),b(),L(19,"ion-col",107),q(20,"Height"),b(),L(21,"ion-col",107),q(22,"Weight"),b(),L(23,"ion-col",106),q(24,"Contact"),b(),L(25,"ion-col",110),q(26,"Email"),b(),L(27,"ion-col",107),q(28,"Photo"),b(),L(29,"ion-col",106),q(30,"Docs"),b(),L(31,"ion-col",111),q(32,"Actions"),b()(),k1(33,ba,30,15,"ion-row",112),b()()()),e&2){let n=v1();X(5),Q1(" Total Entries: ",n.submittedPatients.length," "),X(28),t1("ngForOf",n.submittedPatients)}}function Sa(e,o){if(e&1&&a1(0,"ion-img",14),e&2){let n=v1();t1("src",n.selectedDoc==null?null:n.selectedDoc.data)}}function Aa(e,o){e&1&&(L(0,"p",125),q(1," \u{1F4C4} Document preview not available. "),b())}var yr=(()=>{let o=class o{constructor(t,i,a){this.objPouchdbService=t,this.fb=i,this.toastController=a,this.activeSection="section0",this.videoDevices=[],this.selectedCameraId="",this.submittedPatients=[],this.photoPreviewUrl=null,this.editIndex=null,this.photoPopupOpen=!1,this.selectedPhoto=null,this.docPopupOpen=!1,this.selectedDoc=null,this.hoverIndex=null,this.showUploadControls=!1,this.isCaptureMode=!1,this.countryList=[{name:"India",states:[{name:"Maharashtra",districts:["Mumbai","Pune","Nagpur"]},{name:"Karnataka",districts:["Bengaluru","Mysuru","Hubli"]}]},{name:"USA",states:[{name:"California",districts:["Los Angeles","San Francisco"]},{name:"Texas",districts:["Dallas","Austin"]}]}],this.stateList=[],this.districtList=[],Ve({"create-outline":V3,"trash-outline":H3})}openPhotoPopup(t){this.selectedPhoto=t,this.photoPopupOpen=!0}closePhotoPopup(){this.photoPopupOpen=!1,this.selectedPhoto=null}openDocPopup(t){this.selectedDoc=t,this.docPopupOpen=!0}closeDocPopup(){this.docPopupOpen=!1,this.selectedDoc=null}ngOnInit(){return n1(this,null,function*(){if(this.patientForm=this.fb.group({_id:[""],_rev:[null],domainwisepid:[0],patientid:[0],first_name:[""],last_name:[""],date_of_birth:[""],age:[""],ageYears:[""],gender:[""],maritalstatus:[""],height:[""],weight:[""],mobile:[""],email:[""],head_of_household_fname:[""],head_of_household_lname:[""],country:[""],state:[""],district:[""],block:[""],village:[""],address:[""],projid:[""],head_of_household_mobile:[""],isAbhaPatient:[!1],profile:this.fb.group({patientid:[0],imagepath:[""],S3URL:[""]}),pastrecord:[null],createdat:[""],createdby:[""],domain:[0],uid:[""],prefix:[null],EhealthId:[""],MRN:[""],password:[""],consentformcheckstatus:[0],fingerPrintTemplate:[""],health_number:[""],health_address:[""],unique_id:[null],nationalId:[null],ethnicity:[null],subscriptionDetails:this.fb.group({subscribedId:[0],familycardid:[null],freeSubcriptionAllocated:[0],completedFreeSubcrition:[0],remainingSubcription:[0],isActive:[null],subcriptionName:[null],subscriptionPlanActivatedOn:[null],subscriptionExpiredOn:[null],isExpaired:[0]}),localId:[""],patient_status:[null],patient_title:[null],postCode:[null],centerName:[null],status:[null],isSync:[!1],imageType:["select"],documents:this.fb.array([]),patientImage:[""]}),yield this.loadPatients(),navigator.mediaDevices&&navigator.mediaDevices.enumerateDevices)try{let t=yield navigator.mediaDevices.enumerateDevices();this.videoDevices=t.filter(i=>i.kind==="videoinput"),this.videoDevices.length>0&&(this.selectedCameraId=this.videoDevices[0].deviceId)}catch(t){console.error("Error enumerating devices:",t)}else console.warn("Media Devices API not supported.")})}loadPatients(){return n1(this,null,function*(){try{this.submittedPatients=yield this.objPouchdbService.getAllPatient()}catch(t){console.error("Failed to load patients:",t)}})}get documents(){return this.patientForm.get("documents")}toggleSection(t){this.activeSection=this.activeSection===t?"":t}onCountryChange(t){let i=t.detail.value,a=this.countryList.find(s=>s.name===i);this.stateList=a?.states||[],this.districtList=[],this.patientForm.patchValue({state:"",district:""})}onStateChange(t){let i=t.detail.value,a=this.stateList.find(s=>s.name===i);this.districtList=a?.districts||[],this.patientForm.patchValue({district:""})}startCamera(t){return n1(this,null,function*(){if(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia){alert("Camera API not supported");return}try{let i=yield navigator.mediaDevices.getUserMedia({video:t?{deviceId:{exact:t}}:!0});this.mediaStream=i,this.video.nativeElement.srcObject=i,yield this.video.nativeElement.play()}catch(i){console.error("Error starting camera:",i)}})}switchCamera(t){this.selectedCameraId=t.target.value,this.selectedCameraId&&this.startCamera(this.selectedCameraId)}switchUploadCamera(t){this.selectedCameraId=t.target.value,this.selectedCameraId&&this.startUploadCamera(this.selectedCameraId)}startUploadCamera(t){return n1(this,null,function*(){if(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia){alert("Camera API not supported");return}try{let i=yield navigator.mediaDevices.getUserMedia({video:t?{deviceId:{exact:t}}:!0});this.mediaStream=i,this.uploadVideo&&this.uploadVideo.nativeElement&&(this.uploadVideo.nativeElement.srcObject=i,yield this.uploadVideo.nativeElement.play())}catch(i){console.error("Error starting upload camera:",i)}})}captureImage(){let t=this.video.nativeElement,i=this.canvas.nativeElement;i.width=t.videoWidth,i.height=t.videoHeight;let a=i.getContext("2d");if(!a){console.error("Failed to get canvas context.");return}a.drawImage(t,0,0,i.width,i.height);let s=i.toDataURL("image/png");this.documents.push(this.fb.group({fileName:`Capture-${Date.now()}.png`,fileType:"image/png",data:s,type:this.patientForm.value.imageType||"Front"})),this.patientForm.patchValue({patientImage:s}),this.photoPreviewUrl=s,this.stopCamera(),this.video&&this.video.nativeElement&&(this.video.nativeElement.srcObject=null)}stopCamera(){this.mediaStream&&(this.mediaStream.getTracks().forEach(t=>t.stop()),this.mediaStream=null)}onFileSelected(t){let i=t.target.files;!i||i.length===0||Array.from(i).forEach(a=>{let s=new FileReader;s.onload=()=>{this.documents.push(this.fb.group({fileName:a.name,fileType:a.type,data:s.result,type:this.patientForm.value.imageType||"Document"}))},s.readAsDataURL(a)})}removeDocument(t){this.documents.removeAt(t)}savePatients(){return n1(this,null,function*(){try{this.patientForm.patchValue({patientImage:this.photoPreviewUrl,documents:this.documents.value});let t=this.patientForm.value;if(console.log("Saving patient data:",t),this.editIndex===null)t._rev||delete t._rev,(!t._id||t._id.trim()==="")&&delete t._id,yield this.objPouchdbService.addPatient(t),yield this.loadPatients(),yield(yield this.toastController.create({message:`Patient saved successfully. Total entries: ${this.submittedPatients.length}`,duration:3e3,color:"success"})).present();else{if(!t._id||t._id.trim()==="")throw new Error("Invalid patient _id for update");let i=yield this.objPouchdbService.getPatient(t._id);t._rev=i._rev,yield this.objPouchdbService.updatePatient(t),this.editIndex=null,yield this.loadPatients(),yield(yield this.toastController.create({message:`Patient edited successfully. Total entries: ${this.submittedPatients.length}`,duration:3e3,color:"success"})).present()}console.log(`Total patients in DB: ${this.submittedPatients.length}`),this.patientForm.reset(),this.photoPreviewUrl=null}catch(t){console.error("Failed to save patient:",t),yield(yield this.toastController.create({message:"Failed to save patient.",duration:3e3,color:"danger"})).present()}})}editPatients(t,i){this.editIndex=i,this.patientForm.patchValue(x0(f0({},t),{_id:t._id||"",_rev:t._rev||""})),this.photoPreviewUrl=t.profile?.imagepath||null}delete(t){return n1(this,null,function*(){try{yield this.objPouchdbService.deletePatient(t),yield this.loadPatients(),yield(yield this.toastController.create({message:`Patient deleted successfully. Total entries: ${this.submittedPatients.length}`,duration:3e3,color:"success"})).present()}catch(i){console.error("Failed to delete patient:",i),yield(yield this.toastController.create({message:"Failed to delete patient.",duration:3e3,color:"danger"})).present()}})}downloadFullPatientRecord(t){let i=JSON.stringify(t,null,2),a=new Blob([i],{type:"application/json"}),s=URL.createObjectURL(a),v=document.createElement("a"),p=new Date().toISOString().replace(/[:.]/g,"-");v.href=s,v.download=`patient-${p}.json`,document.body.appendChild(v),v.click(),document.body.removeChild(v),console.log(`\u2B07 Patient record downloaded: patient-${p}.json`)}onBrowseClick(){this.showUploadControls=!0,this.isCaptureMode=!1}onCaptureClick(){return n1(this,null,function*(){this.showUploadControls=!0,this.isCaptureMode=!0;try{let t=yield navigator.mediaDevices.getUserMedia({video:!0});this.mediaStream=t,this.uploadVideo&&this.uploadVideo.nativeElement&&(this.uploadVideo.nativeElement.srcObject=t,yield this.uploadVideo.nativeElement.play())}catch(t){console.error("Error starting upload capture camera:",t)}})}captureUploadImage(){if(!this.uploadVideo||!this.canvas||!this.canvas.nativeElement){console.error("Upload video or canvas element not found.");return}let t=this.uploadVideo.nativeElement,i=this.canvas.nativeElement;i.width=t.videoWidth,i.height=t.videoHeight;let a=i.getContext("2d");if(!a){console.error("Failed to get canvas context.");return}a.drawImage(t,0,0,i.width,i.height);let s=i.toDataURL("image/png");this.documents.push(this.fb.group({fileName:`UploadCapture-${Date.now()}.png`,fileType:"image/png",data:s,type:this.patientForm.value.imageType||"select"})),this.patientForm.patchValue({patientImage:s}),this.photoPreviewUrl=s,this.stopUploadCamera()}stopUploadCamera(){this.mediaStream&&(this.mediaStream.getTracks().forEach(t=>t.stop()),this.mediaStream=null)}};o.\u0275fac=function(i){return new(i||o)(g(vn),g(a3),g(A3))},o.\u0275cmp=D({type:o,selectors:[["app-patient-entry"]],viewQuery:function(i,a){if(i&1&&(r0(na,5),r0(oa,5),r0(ia,5)),i&2){let s;W1(s=Z1())&&(a.video=s.first),W1(s=Z1())&&(a.canvas=s.first),W1(s=Z1())&&(a.uploadVideo=s.first)}},decls:44,vars:19,consts:[["video",""],["canvas",""],["uploadVideo",""],[3,"translucent"],[1,"ion-padding",3,"fullscreen"],[3,"ngSubmit","formGroup"],[1,"card-header-clickable",3,"click"],["slot","end",3,"name"],[4,"ngIf"],[3,"click"],["expand","block","type","submit",3,"disabled"],["style","margin-top: 20px; overflow-x: auto; display: block; max-width: 100%; box-shadow: 0 4px 10px rgba(0,0,0,0.1); border-radius: 12px;",4,"ngIf"],[3,"didDismiss","isOpen"],[2,"text-align","center","padding","20px"],[2,"max-width","95%","max-height","75vh","border-radius","10px","box-shadow","0 4px 12px rgba(0,0,0,0.3)",3,"src"],["expand","block","color","primary",2,"margin-top","15px",3,"click"],["style","max-width: 95%; max-height: 75vh; border-radius: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);",3,"src",4,"ngIf"],["style","font-weight: 500; color: #555;",4,"ngIf"],["type","hidden","formControlName","_id"],["type","hidden","formControlName","_rev"],[1,"gridp"],["position","floating"],["formControlName","first_name"],["formControlName","last_name"],["type","number","formControlName","age"],["type","text","formControlName","ageYears"],[1,"full-width"],["position","stacked"],["formControlName","gender"],[1,"radio-group"],["value","Male"],["value","Female"],["value","Other"],["formControlName","address"],["type","date","formControlName","date_of_birth"],["formControlName","maritalstatus"],["value","Single"],["value","Married"],["value","Separated"],["value","Widow"],["type","number","formControlName","weight"],["type","number","formControlName","height"],["formControlName","country","placeholder","Select Country",3,"ionChange"],[3,"value",4,"ngFor","ngForOf"],["formControlName","block"],["formControlName","village"],["formControlName","head_of_household_fname"],["formControlName","head_of_household_lname"],["type","tel","formControlName","mobile"],["type","email","formControlName","email"],["formControlName","uid"],[3,"value"],["formControlName","state","placeholder","Select State",3,"ionChange"],["formControlName","district","placeholder","Select District"],[1,"camera-controls"],["for","cameraSelect"],["id","cameraSelect",3,"change"],[1,"upload-buttons"],["size","small","fill","outline",3,"click"],["name","videocam-outline","slot","start"],["expand","block","color","primary",3,"click"],["name","camera-outline","slot","start"],[1,"video-preview"],["autoplay","","muted","","playsinline",""],["hidden",""],["class","photo-preview",4,"ngIf"],[1,"photo-preview"],[3,"src"],[1,"upload-controls"],["color","success",2,"font-weight","bold","margin-bottom","12px","display","block"],["name","cloud-upload-outline"],["fill","outline","color","primary",3,"click"],["name","folder-open-outline","slot","start"],["fill","outline","color","secondary",3,"click"],["style","margin-bottom: 16px;",4,"ngIf"],["class","camera-controls",4,"ngIf"],["style","display: flex; justify-content: center; margin: 16px 0;",4,"ngIf"],[2,"margin-top","15px"],[4,"ngFor","ngForOf"],[2,"margin-bottom","16px"],["formControlName","imageType","placeholder","Select Type","interface","popover"],["value","73"],["value","74"],["value","97"],["value","117"],["value","118"],["value","136"],["value","140"],["value","148"],["value","156"],["value","163"],["value","168"],["for","uploadCameraSelect"],["id","uploadCameraSelect",3,"change"],[2,"display","flex","justify-content","center","margin","16px 0"],["autoplay","","muted","","playsinline","",2,"border-radius","8px"],["type","file","accept","image/*,application/pdf",2,"width","100%","padding","8px",3,"change"],["fill","clear","color","danger",3,"click"],["name","trash-outline"],[2,"margin-top","20px","overflow-x","auto","display","block","max-width","100%","box-shadow","0 4px 10px rgba(0,0,0,0.1)","border-radius","12px"],[2,"background","#0077b6","color","white","padding","12px","border-top-left-radius","12px","border-top-right-radius","12px"],[2,"font-size","18px","font-weight","600"],[2,"font-size","14px","font-weight","500","margin-top","4px"],[2,"padding","0","overflow-x","auto"],[2,"min-width","1200px","width","100%","border-collapse","collapse","font-size","14px"],[2,"background","#f4f4f4","font-weight","600","border-bottom","2px solid #ccc","text-align","center"],[2,"min-width","120px","padding","10px"],[2,"min-width","90px","padding","10px"],[2,"min-width","70px","padding","10px"],[2,"min-width","100px","padding","10px"],[2,"min-width","150px","padding","10px"],[2,"min-width","160px","padding","10px"],["style","border-bottom: 1px solid #ddd; text-align: center; transition: background 0.3s;",3,"ngStyle","mouseenter","mouseleave",4,"ngFor","ngForOf"],[2,"border-bottom","1px solid #ddd","text-align","center","transition","background 0.3s",3,"mouseenter","mouseleave","ngStyle"],["alt","Photo","style","width: 45px; height: 45px; border-radius: 50%; cursor: pointer; border: 2px solid #0077b6;",3,"src","click",4,"ngIf"],["style","list-style: none; padding: 0; margin: 0;",4,"ngIf"],[2,"min-width","160px","padding","10px","display","flex","justify-content","center","gap","6px","max-width","222px"],["fill","outline","size","small","color","primary",2,"--border-radius","6px","--padding-start","8px","--padding-end","8px","min-width","60px",3,"click"],["name","create-outline","slot","start"],["fill","outline","size","small","color","danger",2,"--border-radius","6px","--padding-start","8px","--padding-end","8px","min-width","60px",3,"click"],["name","trash-outline","slot","start"],["alt","Photo",2,"width","45px","height","45px","border-radius","50%","cursor","pointer","border","2px solid #0077b6",3,"click","src"],[2,"list-style","none","padding","0","margin","0"],["style","cursor: pointer; color: #0077b6; text-decoration: underline; font-weight: 500; font-size: 13px;",3,"click",4,"ngFor","ngForOf"],[2,"cursor","pointer","color","#0077b6","text-decoration","underline","font-weight","500","font-size","13px",3,"click"],[2,"font-weight","500","color","#555"]],template:function(i,a){i&1&&(L(0,"ion-header",3)(1,"ion-toolbar")(2,"ion-title"),q(3,"Register Patient"),b()()(),L(4,"ion-content",4)(5,"form",5),u1("ngSubmit",function(){return a.savePatients()}),L(6,"ion-card")(7,"ion-card-header",6),u1("click",function(){return a.toggleSection("section0")}),L(8,"ion-card-title"),q(9," Create ABHA Number "),a1(10,"ion-icon",7),b()(),k1(11,sa,41,0,"ion-card-content",8),b(),L(12,"ion-card")(13,"ion-card-header",6),u1("click",function(){return a.toggleSection("section1")}),L(14,"ion-card-title"),q(15," Additional Information "),a1(16,"ion-icon",7),b()(),k1(17,ga,70,3,"ion-card-content",8),b(),L(18,"ion-card")(19,"ion-card-header",6),u1("click",function(){return a.toggleSection("captureImage")}),L(20,"ion-card-title"),q(21," Capture Patient Image "),a1(22,"ion-icon",7),b()(),k1(23,ua,20,2,"ion-card-content",8),b(),L(24,"ion-card")(25,"ion-card-header",9),u1("click",function(){return a.toggleSection("uploadDocument")}),L(26,"ion-card-title"),q(27," Upload Documents "),a1(28,"ion-icon",7),b()(),k1(29,ya,21,6,"ion-card-content",8),b(),L(30,"ion-button",10),q(31),b()(),k1(32,La,34,2,"ion-card",11),L(33,"ion-modal",12),u1("didDismiss",function(){return a.closePhotoPopup()}),L(34,"ion-content",13),a1(35,"ion-img",14),L(36,"ion-button",15),u1("click",function(){return a.closePhotoPopup()}),q(37,"Close"),b()()(),L(38,"ion-modal",12),u1("didDismiss",function(){return a.closeDocPopup()}),L(39,"ion-content",13),k1(40,Sa,1,1,"ion-img",16)(41,Aa,2,0,"p",17),L(42,"ion-button",15),u1("click",function(){return a.closeDocPopup()}),q(43,"Close"),b()()()()),i&2&&(t1("translucent",!0),X(4),t1("fullscreen",!0),X(),t1("formGroup",a.patientForm),X(5),t1("name",a.activeSection==="section0"?"chevron-up-outline":"chevron-down-outline"),X(),t1("ngIf",a.activeSection==="section0"),X(5),t1("name",a.activeSection==="section1"?"chevron-up-outline":"chevron-down-outline"),X(),t1("ngIf",a.activeSection==="section1"),X(5),t1("name",a.activeSection==="captureImage"?"chevron-up-outline":"chevron-down-outline"),X(),t1("ngIf",a.activeSection==="captureImage"),X(5),t1("name",a.activeSection==="uploadDocument"?"chevron-up-outline":"chevron-down-outline"),X(),t1("ngIf",a.activeSection==="uploadDocument"),X(),t1("disabled",a.patientForm.invalid),X(),Q1(" ",a.editIndex===null?"Save Patient":"Update Patient"," "),X(),t1("ngIf",a.submittedPatients.length>0),X(),t1("isOpen",a.photoPopupOpen),X(2),t1("src",a.selectedPhoto),X(3),t1("isOpen",a.docPopupOpen),X(2),t1("ngIf",a.selectedDoc==null||a.selectedDoc.fileType==null?null:a.selectedDoc.fileType.startsWith("image/")),X(),t1("ngIf",!(!(a.selectedDoc==null||a.selectedDoc.fileType==null)&&a.selectedDoc.fileType.startsWith("image/"))))},dependencies:[s3,Jt,t3,n3,Kt,Qt,Xt,Yt,e3,j3,ce,de,ve,ge,he,pe,ue,me,we,fe,xe,Me,ke,ze,ye,Ce,_e,Be,be,Le,Se,Ae,je,se,re,le,F0,$t,M0,Gt],styles:["ion-content[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:flex-start;padding:8px;background:#f8f9fa;min-height:100vh}form[_ngcontent-%COMP%]{width:100%;max-width:1200px;border:1px solid #dcdcdc;border-radius:12px;box-shadow:0 4px 20px #0000001a;background:#fff;padding:12px;margin:0 auto}ion-card[_ngcontent-%COMP%]{margin-bottom:16px;border-radius:12px;box-shadow:0 2px 8px #00000014;overflow:hidden}ion-card-header[_ngcontent-%COMP%]{background-color:#d4edda;border-bottom:1px solid #c3e6cb;border-radius:12px 12px 0 0;padding:12px 16px;cursor:pointer;transition:background-color .3s ease}ion-card-header[_ngcontent-%COMP%]:hover{background-color:#c3e6cb}.gridp[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr;gap:12px;margin-top:16px;--background: #f0faff}.section0[_ngcontent-%COMP%] > ion-item[_ngcontent-%COMP%]{--background: #f0faff}ion-item.full-width[_ngcontent-%COMP%]{grid-column:span 1}ion-card-title[_ngcontent-%COMP%]{font-weight:600;color:#155724;display:flex;justify-content:space-between;align-items:center;font-size:16px}ion-button[_ngcontent-%COMP%]{margin-top:16px;width:100%;border-radius:8px;font-weight:500}ion-item[_ngcontent-%COMP%]{--inner-padding-end: 8px;--inner-padding-start: 8px;--highlight-height: 0px;--border-radius: 8px;margin-bottom:8px}@media (max-width: 480px){ion-content[_ngcontent-%COMP%]{padding:4px}form[_ngcontent-%COMP%]{padding:8px;border-radius:8px}ion-card[_ngcontent-%COMP%]{margin-bottom:12px;border-radius:8px}ion-card-header[_ngcontent-%COMP%]{padding:8px 12px;border-radius:8px 8px 0 0}ion-card-title[_ngcontent-%COMP%]{font-size:14px}.gridp[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:8px;margin-top:12px}ion-item[_ngcontent-%COMP%]{--inner-padding-end: 4px;--inner-padding-start: 4px;margin-bottom:6px}ion-button[_ngcontent-%COMP%]{margin-top:12px;font-size:14px}}@media (min-width: 481px) and (max-width: 768px){ion-content[_ngcontent-%COMP%]{padding:8px}form[_ngcontent-%COMP%]{padding:12px}.gridp[_ngcontent-%COMP%]{grid-template-columns:1fr 1fr;gap:12px}ion-item.full-width[_ngcontent-%COMP%]{grid-column:span 2}}@media (min-width: 769px) and (max-width: 1024px){ion-content[_ngcontent-%COMP%]{padding:16px}form[_ngcontent-%COMP%]{padding:20px;max-width:900px}.gridp[_ngcontent-%COMP%]{grid-template-columns:1fr 1fr;gap:16px}ion-item.full-width[_ngcontent-%COMP%]{grid-column:span 2}ion-card-title[_ngcontent-%COMP%]{font-size:18px}}@media (min-width: 1025px){ion-content[_ngcontent-%COMP%]{padding:24px}form[_ngcontent-%COMP%]{padding:24px;max-width:1200px}.gridp[_ngcontent-%COMP%]{grid-template-columns:1fr 1fr 1fr;gap:20px}ion-item.full-width[_ngcontent-%COMP%]{grid-column:span 3}ion-card-title[_ngcontent-%COMP%]{font-size:20px}}.data-table[_ngcontent-%COMP%]{width:100%;border-collapse:collapse;font-family:Arial,sans-serif;font-size:14px;color:#333;overflow-x:auto}.data-table[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]{width:100%;min-width:800px}.data-table[_ngcontent-%COMP%]   ion-row.table-header[_ngcontent-%COMP%]{background-color:#007bff;color:#fff;font-weight:700;text-transform:uppercase;font-size:13px;position:sticky;top:0;z-index:10}.data-table[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]{border-bottom:1px solid #ddd;transition:background-color .3s ease}.data-table[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]:hover{background-color:#f1f1f1;cursor:pointer}.data-table[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{padding:8px 6px;border-right:1px solid #ddd;display:flex;align-items:center;justify-content:center;text-align:center;word-wrap:break-word;overflow-wrap:break-word}.data-table[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]:last-child{border-right:none}.data-table[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-radius:50%;width:40px;height:40px;object-fit:cover;cursor:pointer;transition:transform .2s ease}.data-table[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover{transform:scale(1.1)}.data-table[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none;padding:0;margin:0;max-width:120px}.data-table[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{color:#007bff;text-decoration:underline;cursor:pointer;margin-bottom:4px;font-size:12px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.data-table[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:hover{color:#0056b3}video[_ngcontent-%COMP%]{width:100%;max-width:400px;height:auto;border:2px solid #ddd;border-radius:8px;background:#000}.camera-controls[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:8px;margin-bottom:12px;align-items:center}.camera-controls[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{padding:8px 12px;border:1px solid #ddd;border-radius:6px;background:#fff;font-size:14px;min-width:120px}.upload-controls[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.upload-buttons[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:8px}.upload-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{padding:8px 16px;border:none;border-radius:6px;font-size:14px;cursor:pointer;transition:all .3s ease}.radio-group[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:12px;align-items:center;padding:8px 0}.radio-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;cursor:pointer;font-size:14px}ion-modal[_ngcontent-%COMP%]{--background: rgba(0, 0, 0, .8)}ion-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:16px}ion-modal[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{max-width:95vw;max-height:85vh;border-radius:8px;box-shadow:0 0 20px #00000080;margin-bottom:16px;object-fit:contain}ion-modal[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{min-width:120px;border-radius:8px;font-weight:700;background-color:#007bff;color:#fff;--background: #007bff;--background-hover: #0056b3;--background-activated: #004085}@media (max-width: 480px){video[_ngcontent-%COMP%]{max-width:100%;height:200px}.camera-controls[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.camera-controls[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{width:100%}.upload-buttons[_ngcontent-%COMP%]{flex-direction:column}.upload-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;padding:12px}.radio-group[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:8px}.data-table[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]{min-width:600px}.data-table[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{padding:6px 4px;font-size:12px}.data-table[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:30px;height:30px}ion-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]{padding:8px}ion-modal[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{max-width:98vw;max-height:80vh}}@media (min-width: 481px) and (max-width: 768px){video[_ngcontent-%COMP%]{max-width:350px;height:250px}.camera-controls[_ngcontent-%COMP%], .upload-buttons[_ngcontent-%COMP%], .radio-group[_ngcontent-%COMP%]{justify-content:center}.data-table[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]{min-width:900px}}@media (min-width: 1025px){video[_ngcontent-%COMP%]{max-width:500px;height:350px}.camera-controls[_ngcontent-%COMP%], .upload-buttons[_ngcontent-%COMP%]{justify-content:flex-start}.data-table[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]{min-width:1200px}.data-table[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{padding:10px 8px}.data-table[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:50px;height:50px}}ion-button[_ngcontent-%COMP%]:focus, ion-input[_ngcontent-%COMP%]:focus, ion-select[_ngcontent-%COMP%]:focus, button[_ngcontent-%COMP%]:focus, select[_ngcontent-%COMP%]:focus, input[_ngcontent-%COMP%]:focus{outline:2px solid #007bff;outline-offset:2px}@media (prefers-reduced-motion: reduce){*[_ngcontent-%COMP%]{animation-duration:.01ms!important;transition-duration:.01ms!important}}"]});let e=o;return e})();export{ue as a,j3 as b,yr as c};
