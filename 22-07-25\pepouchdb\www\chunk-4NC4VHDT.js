import{a as R}from"./chunk-SCKONRGC.js";import{b as S}from"./chunk-BMJVVMK5.js";import{e as z}from"./chunk-XFXTD7QR.js";import{b as w,f as C,i as F,m as d,n as A,o as E,p as f,s as T}from"./chunk-EHNA26RN.js";import{g as I}from"./chunk-2R6CW7ES.js";var $=".sc-ion-input-otp-ios-h{--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--padding-top:16px;--padding-end:0;--padding-bottom:16px;--padding-start:0;--color:initial;--min-width:40px;--separator-width:8px;--separator-height:var(--separator-width);--separator-border-radius:999px;--separator-color:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-size:0.875rem}.input-otp-group.sc-ion-input-otp-ios{-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.native-wrapper.sc-ion-input-otp-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:var(--min-width)}.native-input.sc-ion-input-otp-ios{border-radius:var(--border-radius);width:var(--width);min-width:inherit;height:var(--height);border-width:var(--border-width);border-style:solid;border-color:var(--border-color);background:var(--background);color:var(--color);font-size:inherit;text-align:center;-webkit-appearance:none;-moz-appearance:none;appearance:none}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{caret-color:var(--highlight-color)}.input-otp-description.sc-ion-input-otp-ios{color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));font-size:0.75rem;line-height:1.25rem;text-align:center}.input-otp-description-hidden.sc-ion-input-otp-ios{display:none}.input-otp-separator.sc-ion-input-otp-ios{border-radius:var(--separator-border-radius);-ms-flex-negative:0;flex-shrink:0;width:var(--separator-width);height:var(--separator-height);background:var(--separator-color)}.input-otp-size-small.sc-ion-input-otp-ios-h{--width:40px;--height:40px}.input-otp-size-small.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios{gap:8px}.input-otp-size-medium.sc-ion-input-otp-ios-h{--width:48px;--height:48px}.input-otp-size-large.sc-ion-input-otp-ios-h{--width:56px;--height:56px}.input-otp-size-medium.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios,.input-otp-size-large.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios{gap:12px}.input-otp-shape-round.sc-ion-input-otp-ios-h{--border-radius:16px}.input-otp-shape-soft.sc-ion-input-otp-ios-h{--border-radius:8px}.input-otp-shape-rectangular.sc-ion-input-otp-ios-h{--border-radius:0}.input-otp-fill-outline.sc-ion-input-otp-ios-h{--background:none}.input-otp-fill-solid.sc-ion-input-otp-ios-h{--border-color:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-disabled.sc-ion-input-otp-ios-h{--color:var(--ion-color-step-350, var(--ion-text-color-step-650, #a6a6a6))}.input-otp-fill-outline.input-otp-disabled.sc-ion-input-otp-ios-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.input-otp-disabled.sc-ion-input-otp-ios-h,.input-otp-disabled.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:disabled{cursor:not-allowed}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{--border-color:var(--highlight-color);outline:none}.input-otp-fill-outline.input-otp-readonly.sc-ion-input-otp-ios-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-fill-solid.input-otp-disabled.sc-ion-input-otp-ios-h,.input-otp-fill-solid.input-otp-readonly.sc-ion-input-otp-ios-h{--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.ion-touched.ion-invalid.sc-ion-input-otp-ios-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-otp-ios-h{--highlight-color:var(--highlight-color-valid)}.has-focus.ion-valid.sc-ion-input-otp-ios-h,.ion-touched.ion-invalid.sc-ion-input-otp-ios-h{--border-color:var(--highlight-color)}.ion-color.sc-ion-input-otp-ios-h{--highlight-color-focused:var(--ion-color-base)}.input-otp-fill-outline.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{border-color:rgba(var(--ion-color-base-rgb), 0.6)}.input-otp-fill-outline.ion-color.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-outline.ion-color.has-focus.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.has-focus.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:var(--ion-color-danger, #c5000f)}.input-otp-fill-outline.ion-color.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-outline.ion-color.has-focus.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.has-focus.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:var(--ion-color-success, #2dd55b)}.input-otp-fill-outline.input-otp-disabled.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:rgba(var(--ion-color-base-rgb), 0.3)}.sc-ion-input-otp-ios-h{--border-width:0.55px}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{--border-width:1px}.input-otp-fill-outline.sc-ion-input-otp-ios-h{--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))))}",K=".sc-ion-input-otp-md-h{--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--padding-top:16px;--padding-end:0;--padding-bottom:16px;--padding-start:0;--color:initial;--min-width:40px;--separator-width:8px;--separator-height:var(--separator-width);--separator-border-radius:999px;--separator-color:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-size:0.875rem}.input-otp-group.sc-ion-input-otp-md{-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.native-wrapper.sc-ion-input-otp-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:var(--min-width)}.native-input.sc-ion-input-otp-md{border-radius:var(--border-radius);width:var(--width);min-width:inherit;height:var(--height);border-width:var(--border-width);border-style:solid;border-color:var(--border-color);background:var(--background);color:var(--color);font-size:inherit;text-align:center;-webkit-appearance:none;-moz-appearance:none;appearance:none}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{caret-color:var(--highlight-color)}.input-otp-description.sc-ion-input-otp-md{color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));font-size:0.75rem;line-height:1.25rem;text-align:center}.input-otp-description-hidden.sc-ion-input-otp-md{display:none}.input-otp-separator.sc-ion-input-otp-md{border-radius:var(--separator-border-radius);-ms-flex-negative:0;flex-shrink:0;width:var(--separator-width);height:var(--separator-height);background:var(--separator-color)}.input-otp-size-small.sc-ion-input-otp-md-h{--width:40px;--height:40px}.input-otp-size-small.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md{gap:8px}.input-otp-size-medium.sc-ion-input-otp-md-h{--width:48px;--height:48px}.input-otp-size-large.sc-ion-input-otp-md-h{--width:56px;--height:56px}.input-otp-size-medium.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md,.input-otp-size-large.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md{gap:12px}.input-otp-shape-round.sc-ion-input-otp-md-h{--border-radius:16px}.input-otp-shape-soft.sc-ion-input-otp-md-h{--border-radius:8px}.input-otp-shape-rectangular.sc-ion-input-otp-md-h{--border-radius:0}.input-otp-fill-outline.sc-ion-input-otp-md-h{--background:none}.input-otp-fill-solid.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-disabled.sc-ion-input-otp-md-h{--color:var(--ion-color-step-350, var(--ion-text-color-step-650, #a6a6a6))}.input-otp-fill-outline.input-otp-disabled.sc-ion-input-otp-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.input-otp-disabled.sc-ion-input-otp-md-h,.input-otp-disabled.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:disabled{cursor:not-allowed}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{--border-color:var(--highlight-color);outline:none}.input-otp-fill-outline.input-otp-readonly.sc-ion-input-otp-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-fill-solid.input-otp-disabled.sc-ion-input-otp-md-h,.input-otp-fill-solid.input-otp-readonly.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.ion-touched.ion-invalid.sc-ion-input-otp-md-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-otp-md-h{--highlight-color:var(--highlight-color-valid)}.has-focus.ion-valid.sc-ion-input-otp-md-h,.ion-touched.ion-invalid.sc-ion-input-otp-md-h{--border-color:var(--highlight-color)}.ion-color.sc-ion-input-otp-md-h{--highlight-color-focused:var(--ion-color-base)}.input-otp-fill-outline.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{border-color:rgba(var(--ion-color-base-rgb), 0.6)}.input-otp-fill-outline.ion-color.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-outline.ion-color.has-focus.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.has-focus.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:var(--ion-color-danger, #c5000f)}.input-otp-fill-outline.ion-color.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-outline.ion-color.has-focus.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.has-focus.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:var(--ion-color-success, #2dd55b)}.input-otp-fill-outline.input-otp-disabled.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:rgba(var(--ion-color-base-rgb), 0.3)}.sc-ion-input-otp-md-h{--border-width:1px}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{--border-width:2px}.input-otp-fill-outline.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3))}",N=class{constructor(o){F(this,o),this.ionInput=f(this,"ionInput",7),this.ionChange=f(this,"ionChange",7),this.ionComplete=f(this,"ionComplete",7),this.ionBlur=f(this,"ionBlur",7),this.ionFocus=f(this,"ionFocus",7),this.inheritedAttributes={},this.inputRefs=[],this.inputId=`ion-input-otp-${D++}`,this.parsedSeparators=[],this.isKeyboardNavigation=!1,this.inputValues=[],this.hasFocus=!1,this.previousInputValues=[],this.autocapitalize="off",this.disabled=!1,this.fill="outline",this.length=4,this.readonly=!1,this.shape="round",this.size="medium",this.type="number",this.value="",this.onFocus=i=>t=>{var n;let{inputRefs:r}=this;this.hasFocus||(this.ionFocus.emit(t),this.focusedValue=this.value),this.hasFocus=!0;let s=i;if(!this.isKeyboardNavigation){let p=this.inputValues[i]?i:this.getFirstEmptyIndex();s=p===-1?this.length-1:p,(n=this.inputRefs[s])===null||n===void 0||n.focus()}r.forEach((p,l)=>{p.tabIndex=l===s?0:-1}),this.isKeyboardNavigation=!1},this.onBlur=i=>{let{inputRefs:t}=this,n=i.relatedTarget;n!=null&&t.includes(n)||(this.hasFocus=!1,this.updateTabIndexes(),this.ionBlur.emit(i),this.focusedValue!==this.value&&this.emitIonChange(i))},this.onKeyDown=i=>t=>{let{length:n}=this,r=R(this.el),s=t.target,p=["a","c","v","x","r","z","y"];if(!(s.selectionStart!==s.selectionEnd||(t.metaKey||t.ctrlKey)&&p.includes(t.key.toLowerCase()))){if(t.key==="Backspace")if(this.inputValues[i]){for(let e=i;e<n-1;e++)this.inputValues[e]=this.inputValues[e+1];this.inputValues[n-1]="";for(let e=0;e<n;e++)this.inputRefs[e].value=this.inputValues[e]||"";this.updateValue(t),t.preventDefault()}else!this.inputValues[i]&&i>0&&this.focusPrevious(i);else if(t.key==="ArrowLeft"||t.key==="ArrowRight"){this.isKeyboardNavigation=!0,t.preventDefault();let e=t.key==="ArrowLeft";e&&r||!e&&!r?this.inputValues[i]&&i<n-1&&this.focusNext(i):this.focusPrevious(i)}else if(t.key==="Tab"){this.isKeyboardNavigation=!0;return}}},this.onInput=i=>t=>{var n,r;let{length:s,validKeyPattern:p}=this,l=t.target,e=l.value,h=this.previousInputValues[i]||"";if(e.length-h.length>1){let u=e.split("").filter(a=>p.test(a)).slice(0,s);u.length===0&&requestAnimationFrame(()=>{this.inputRefs.forEach(a=>{a.value=""})});for(let a=0;a<s;a++)this.inputValues[a]=u[a]||"",this.inputRefs[a].value=u[a]||"";this.updateValue(t),setTimeout(()=>{var a;let b=u.length<s?u.length:s-1;(a=this.inputRefs[b])===null||a===void 0||a.focus()},20),this.previousInputValues=[...this.inputValues];return}if(e.length>0&&!p.test(e[e.length-1])){l.value=this.inputValues[i]||"",this.previousInputValues=[...this.inputValues];return}let v=l.selectionStart===0&&l.selectionEnd===e.length,y=!this.inputValues[i];if(v||y){this.inputValues[i]=e,l.value=e,this.updateValue(t),this.focusNext(i),this.previousInputValues=[...this.inputValues];return}let x=this.inputValues[this.inputValues.length-1]==="";if(this.inputValues[i]&&x&&e.length===2){let u=t.data;if(u||(u=e.split("").find((a,b)=>a!==h[b])||e[e.length-1]),!p.test(u)){l.value=this.inputValues[i]||"",this.previousInputValues=[...this.inputValues];return}for(let a=this.inputValues.length-1;a>i;a--)this.inputValues[a]=this.inputValues[a-1],this.inputRefs[a].value=this.inputValues[a]||"";this.inputValues[i]=u,this.inputRefs[i].value=u,this.updateValue(t),this.previousInputValues=[...this.inputValues];return}let V=((n=l.selectionStart)!==null&&n!==void 0?n:e.length)-1,m=(r=e[V])!==null&&r!==void 0?r:e[0];if(!p.test(m)){l.value=this.inputValues[i]||"",this.previousInputValues=[...this.inputValues];return}this.inputValues[i]=m,l.value=m,this.updateValue(t),this.previousInputValues=[...this.inputValues]},this.onPaste=i=>{var t,n;let{inputRefs:r,length:s,validKeyPattern:p}=this;i.preventDefault();let l=(t=i.clipboardData)===null||t===void 0?void 0:t.getData("text");if(!l){this.emitIonInput(i);return}let e=l.split("").filter(g=>p.test(g)).slice(0,s);e.forEach((g,v)=>{v<s&&(this.inputRefs[v].value=g,this.inputValues[v]=g)}),this.value=e.join(""),this.updateValue(i);let h=e.length<s?e.length:s-1;(n=r[h])===null||n===void 0||n.focus()}}setFocus(o){return I(this,null,function*(){var i,t;if(typeof o=="number"){let n=Math.max(0,Math.min(o,this.length-1));(i=this.inputRefs[n])===null||i===void 0||i.focus()}else{let n=this.getTabbableIndex();(t=this.inputRefs[n])===null||t===void 0||t.focus()}})}valueChanged(){this.initializeValues(),this.updateTabIndexes()}processSeparators(){let{separators:o,length:i}=this;if(o===void 0){this.parsedSeparators=[];return}if(typeof o=="string"&&o!=="all"&&!/^(\d+)(,\d+)*$/.test(o)){w(`[ion-input-otp] - Invalid separators format. Expected a comma-separated list of numbers, an array of numbers, or "all". Received: ${o}`,this.el),this.parsedSeparators=[];return}let t;o==="all"?t=Array.from({length:i-1},(s,p)=>p+1):Array.isArray(o)?t=o:t=o.split(",").map(s=>parseInt(s,10)).filter(s=>!isNaN(s)),t.filter((s,p)=>t.indexOf(s)!==p).length>0&&w(`[ion-input-otp] - Duplicate separator positions are not allowed. Received: ${o}`,this.el);let r=t.filter(s=>s>i);r.length>0&&w(`[ion-input-otp] - The following separator positions are greater than the input length (${i}): ${r.join(", ")}. These separators will be ignored.`,this.el),this.parsedSeparators=t.filter(s=>s<=i)}componentWillLoad(){this.inheritedAttributes=z(this.el),this.processSeparators(),this.initializeValues()}componentDidLoad(){this.updateTabIndexes()}get validKeyPattern(){return new RegExp(`^${this.getPattern()}$`,"u")}getPattern(){let{pattern:o,type:i}=this;return o||(i==="number"?"[\\p{N}]":"[\\p{L}\\p{N}]")}getInputmode(){let{inputmode:o}=this;return o||(this.type=="number"?"numeric":"text")}initializeValues(){if(this.inputValues=Array(this.length).fill(""),this.value==null||String(this.value).length===0)return;String(this.value).split("").slice(0,this.length).forEach((i,t)=>{this.validKeyPattern.test(i)&&(this.inputValues[t]=i)}),this.value=this.inputValues.join(""),this.previousInputValues=[...this.inputValues]}updateValue(o){let{inputValues:i,length:t}=this,n=i.join("");this.value=n,this.emitIonInput(o),n.length===t&&this.ionComplete.emit({value:n})}emitIonChange(o){let{value:i}=this,t=i==null?i:i.toString();this.ionChange.emit({value:t,event:o})}emitIonInput(o){let{value:i}=this,t=i==null?i:i.toString();this.ionInput.emit({value:t,event:o})}focusNext(o){var i;let{inputRefs:t,length:n}=this;o<n-1&&((i=t[o+1])===null||i===void 0||i.focus())}focusPrevious(o){var i;let{inputRefs:t}=this;o>0&&((i=t[o-1])===null||i===void 0||i.focus())}getFirstEmptyIndex(){var o;let{inputValues:i,length:t}=this;return(o=Array.from({length:t},(r,s)=>i[s]||"").findIndex(r=>!r||r===""))!==null&&o!==void 0?o:-1}getTabbableIndex(){let{length:o}=this,i=this.getFirstEmptyIndex();return i===-1?o-1:i}updateTabIndexes(){let{inputRefs:o,inputValues:i,length:t}=this,n=-1;for(let r=0;r<t;r++)if(!i[r]||i[r]===""){n=r;break}o.forEach((r,s)=>{let p=n===-1?s===t-1:n===s;r.tabIndex=p?0:-1;let l=!i[s]||i[s]==="";r.setAttribute("aria-hidden",l&&!p?"true":"false")})}showSeparator(o){let{length:i}=this;return this.parsedSeparators.includes(o+1)&&o<i-1}render(){var o,i;let{autocapitalize:t,color:n,disabled:r,el:s,fill:p,hasFocus:l,inheritedAttributes:e,inputId:h,inputRefs:g,inputValues:v,length:y,readonly:x,shape:k,size:V}=this,m=C(this),u=this.getInputmode(),a=this.getTabbableIndex(),b=this.getPattern(),_=((i=(o=s.querySelector(".input-otp-description"))===null||o===void 0?void 0:o.textContent)===null||i===void 0?void 0:i.trim())!=="";return d(A,{key:"f15a29fb17b681ef55885ca36d3d5f899cbaca83",class:S(n,{[m]:!0,"has-focus":l,[`input-otp-size-${V}`]:!0,[`input-otp-shape-${k}`]:!0,[`input-otp-fill-${p}`]:!0,"input-otp-disabled":r,"input-otp-readonly":x})},d("div",Object.assign({key:"d7e1d4edd8aafcf2ed4313301287282e90fc7e82",role:"group","aria-label":"One-time password input",class:"input-otp-group"},e),Array.from({length:y}).map((j,c)=>d(T,null,d("div",{class:"native-wrapper"},d("input",{class:"native-input",id:`${h}-${c}`,"aria-label":`Input ${c+1} of ${y}`,type:"text",autoCapitalize:t,inputmode:u,pattern:b,disabled:r,readOnly:x,tabIndex:c===a?0:-1,value:v[c]||"",autocomplete:"one-time-code",ref:P=>g[c]=P,onInput:this.onInput(c),onBlur:this.onBlur,onFocus:this.onFocus(c),onKeyDown:this.onKeyDown(c),onPaste:this.onPaste})),this.showSeparator(c)&&d("div",{class:"input-otp-separator"})))),d("div",{key:"3724a3159d02860971879a906092f9965f5a7c47",class:{"input-otp-description":!0,"input-otp-description-hidden":!_}},d("slot",{key:"11baa2624926a08274508afe0833d9237a8dc35c"})))}get el(){return E(this)}static get watchers(){return{value:["valueChanged"],separators:["processSeparators"],length:["processSeparators"]}}},D=0;N.style={ios:$,md:K};export{N as ion_input_otp};
