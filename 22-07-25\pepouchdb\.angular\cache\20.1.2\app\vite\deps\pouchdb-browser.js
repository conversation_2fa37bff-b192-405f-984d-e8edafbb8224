import {
  __async,
  __commonJS,
  __toESM
} from "./chunk-UL2P3LPA.js";

// node_modules/spark-md5/spark-md5.js
var require_spark_md5 = __commonJS({
  "node_modules/spark-md5/spark-md5.js"(exports, module) {
    (function(factory) {
      if (typeof exports === "object") {
        module.exports = factory();
      } else if (typeof define === "function" && define.amd) {
        define(factory);
      } else {
        var glob;
        try {
          glob = window;
        } catch (e) {
          glob = self;
        }
        glob.SparkMD5 = factory();
      }
    })(function(undefined2) {
      "use strict";
      var add32 = function(a, b) {
        return a + b & 4294967295;
      }, hex_chr = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"];
      function cmn(q, a, b, x, s, t) {
        a = add32(add32(a, q), add32(x, t));
        return add32(a << s | a >>> 32 - s, b);
      }
      function md5cycle(x, k) {
        var a = x[0], b = x[1], c = x[2], d = x[3];
        a += (b & c | ~b & d) + k[0] - 680876936 | 0;
        a = (a << 7 | a >>> 25) + b | 0;
        d += (a & b | ~a & c) + k[1] - 389564586 | 0;
        d = (d << 12 | d >>> 20) + a | 0;
        c += (d & a | ~d & b) + k[2] + 606105819 | 0;
        c = (c << 17 | c >>> 15) + d | 0;
        b += (c & d | ~c & a) + k[3] - 1044525330 | 0;
        b = (b << 22 | b >>> 10) + c | 0;
        a += (b & c | ~b & d) + k[4] - 176418897 | 0;
        a = (a << 7 | a >>> 25) + b | 0;
        d += (a & b | ~a & c) + k[5] + 1200080426 | 0;
        d = (d << 12 | d >>> 20) + a | 0;
        c += (d & a | ~d & b) + k[6] - 1473231341 | 0;
        c = (c << 17 | c >>> 15) + d | 0;
        b += (c & d | ~c & a) + k[7] - 45705983 | 0;
        b = (b << 22 | b >>> 10) + c | 0;
        a += (b & c | ~b & d) + k[8] + 1770035416 | 0;
        a = (a << 7 | a >>> 25) + b | 0;
        d += (a & b | ~a & c) + k[9] - 1958414417 | 0;
        d = (d << 12 | d >>> 20) + a | 0;
        c += (d & a | ~d & b) + k[10] - 42063 | 0;
        c = (c << 17 | c >>> 15) + d | 0;
        b += (c & d | ~c & a) + k[11] - 1990404162 | 0;
        b = (b << 22 | b >>> 10) + c | 0;
        a += (b & c | ~b & d) + k[12] + 1804603682 | 0;
        a = (a << 7 | a >>> 25) + b | 0;
        d += (a & b | ~a & c) + k[13] - 40341101 | 0;
        d = (d << 12 | d >>> 20) + a | 0;
        c += (d & a | ~d & b) + k[14] - 1502002290 | 0;
        c = (c << 17 | c >>> 15) + d | 0;
        b += (c & d | ~c & a) + k[15] + 1236535329 | 0;
        b = (b << 22 | b >>> 10) + c | 0;
        a += (b & d | c & ~d) + k[1] - 165796510 | 0;
        a = (a << 5 | a >>> 27) + b | 0;
        d += (a & c | b & ~c) + k[6] - 1069501632 | 0;
        d = (d << 9 | d >>> 23) + a | 0;
        c += (d & b | a & ~b) + k[11] + 643717713 | 0;
        c = (c << 14 | c >>> 18) + d | 0;
        b += (c & a | d & ~a) + k[0] - 373897302 | 0;
        b = (b << 20 | b >>> 12) + c | 0;
        a += (b & d | c & ~d) + k[5] - 701558691 | 0;
        a = (a << 5 | a >>> 27) + b | 0;
        d += (a & c | b & ~c) + k[10] + 38016083 | 0;
        d = (d << 9 | d >>> 23) + a | 0;
        c += (d & b | a & ~b) + k[15] - 660478335 | 0;
        c = (c << 14 | c >>> 18) + d | 0;
        b += (c & a | d & ~a) + k[4] - 405537848 | 0;
        b = (b << 20 | b >>> 12) + c | 0;
        a += (b & d | c & ~d) + k[9] + 568446438 | 0;
        a = (a << 5 | a >>> 27) + b | 0;
        d += (a & c | b & ~c) + k[14] - 1019803690 | 0;
        d = (d << 9 | d >>> 23) + a | 0;
        c += (d & b | a & ~b) + k[3] - 187363961 | 0;
        c = (c << 14 | c >>> 18) + d | 0;
        b += (c & a | d & ~a) + k[8] + 1163531501 | 0;
        b = (b << 20 | b >>> 12) + c | 0;
        a += (b & d | c & ~d) + k[13] - 1444681467 | 0;
        a = (a << 5 | a >>> 27) + b | 0;
        d += (a & c | b & ~c) + k[2] - 51403784 | 0;
        d = (d << 9 | d >>> 23) + a | 0;
        c += (d & b | a & ~b) + k[7] + 1735328473 | 0;
        c = (c << 14 | c >>> 18) + d | 0;
        b += (c & a | d & ~a) + k[12] - 1926607734 | 0;
        b = (b << 20 | b >>> 12) + c | 0;
        a += (b ^ c ^ d) + k[5] - 378558 | 0;
        a = (a << 4 | a >>> 28) + b | 0;
        d += (a ^ b ^ c) + k[8] - 2022574463 | 0;
        d = (d << 11 | d >>> 21) + a | 0;
        c += (d ^ a ^ b) + k[11] + 1839030562 | 0;
        c = (c << 16 | c >>> 16) + d | 0;
        b += (c ^ d ^ a) + k[14] - 35309556 | 0;
        b = (b << 23 | b >>> 9) + c | 0;
        a += (b ^ c ^ d) + k[1] - 1530992060 | 0;
        a = (a << 4 | a >>> 28) + b | 0;
        d += (a ^ b ^ c) + k[4] + 1272893353 | 0;
        d = (d << 11 | d >>> 21) + a | 0;
        c += (d ^ a ^ b) + k[7] - 155497632 | 0;
        c = (c << 16 | c >>> 16) + d | 0;
        b += (c ^ d ^ a) + k[10] - 1094730640 | 0;
        b = (b << 23 | b >>> 9) + c | 0;
        a += (b ^ c ^ d) + k[13] + 681279174 | 0;
        a = (a << 4 | a >>> 28) + b | 0;
        d += (a ^ b ^ c) + k[0] - 358537222 | 0;
        d = (d << 11 | d >>> 21) + a | 0;
        c += (d ^ a ^ b) + k[3] - 722521979 | 0;
        c = (c << 16 | c >>> 16) + d | 0;
        b += (c ^ d ^ a) + k[6] + 76029189 | 0;
        b = (b << 23 | b >>> 9) + c | 0;
        a += (b ^ c ^ d) + k[9] - 640364487 | 0;
        a = (a << 4 | a >>> 28) + b | 0;
        d += (a ^ b ^ c) + k[12] - 421815835 | 0;
        d = (d << 11 | d >>> 21) + a | 0;
        c += (d ^ a ^ b) + k[15] + 530742520 | 0;
        c = (c << 16 | c >>> 16) + d | 0;
        b += (c ^ d ^ a) + k[2] - 995338651 | 0;
        b = (b << 23 | b >>> 9) + c | 0;
        a += (c ^ (b | ~d)) + k[0] - 198630844 | 0;
        a = (a << 6 | a >>> 26) + b | 0;
        d += (b ^ (a | ~c)) + k[7] + 1126891415 | 0;
        d = (d << 10 | d >>> 22) + a | 0;
        c += (a ^ (d | ~b)) + k[14] - 1416354905 | 0;
        c = (c << 15 | c >>> 17) + d | 0;
        b += (d ^ (c | ~a)) + k[5] - 57434055 | 0;
        b = (b << 21 | b >>> 11) + c | 0;
        a += (c ^ (b | ~d)) + k[12] + 1700485571 | 0;
        a = (a << 6 | a >>> 26) + b | 0;
        d += (b ^ (a | ~c)) + k[3] - 1894986606 | 0;
        d = (d << 10 | d >>> 22) + a | 0;
        c += (a ^ (d | ~b)) + k[10] - 1051523 | 0;
        c = (c << 15 | c >>> 17) + d | 0;
        b += (d ^ (c | ~a)) + k[1] - 2054922799 | 0;
        b = (b << 21 | b >>> 11) + c | 0;
        a += (c ^ (b | ~d)) + k[8] + 1873313359 | 0;
        a = (a << 6 | a >>> 26) + b | 0;
        d += (b ^ (a | ~c)) + k[15] - 30611744 | 0;
        d = (d << 10 | d >>> 22) + a | 0;
        c += (a ^ (d | ~b)) + k[6] - 1560198380 | 0;
        c = (c << 15 | c >>> 17) + d | 0;
        b += (d ^ (c | ~a)) + k[13] + 1309151649 | 0;
        b = (b << 21 | b >>> 11) + c | 0;
        a += (c ^ (b | ~d)) + k[4] - 145523070 | 0;
        a = (a << 6 | a >>> 26) + b | 0;
        d += (b ^ (a | ~c)) + k[11] - 1120210379 | 0;
        d = (d << 10 | d >>> 22) + a | 0;
        c += (a ^ (d | ~b)) + k[2] + 718787259 | 0;
        c = (c << 15 | c >>> 17) + d | 0;
        b += (d ^ (c | ~a)) + k[9] - 343485551 | 0;
        b = (b << 21 | b >>> 11) + c | 0;
        x[0] = a + x[0] | 0;
        x[1] = b + x[1] | 0;
        x[2] = c + x[2] | 0;
        x[3] = d + x[3] | 0;
      }
      function md5blk(s) {
        var md5blks = [], i;
        for (i = 0; i < 64; i += 4) {
          md5blks[i >> 2] = s.charCodeAt(i) + (s.charCodeAt(i + 1) << 8) + (s.charCodeAt(i + 2) << 16) + (s.charCodeAt(i + 3) << 24);
        }
        return md5blks;
      }
      function md5blk_array(a) {
        var md5blks = [], i;
        for (i = 0; i < 64; i += 4) {
          md5blks[i >> 2] = a[i] + (a[i + 1] << 8) + (a[i + 2] << 16) + (a[i + 3] << 24);
        }
        return md5blks;
      }
      function md51(s) {
        var n = s.length, state = [1732584193, -271733879, -1732584194, 271733878], i, length, tail, tmp, lo, hi;
        for (i = 64; i <= n; i += 64) {
          md5cycle(state, md5blk(s.substring(i - 64, i)));
        }
        s = s.substring(i - 64);
        length = s.length;
        tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        for (i = 0; i < length; i += 1) {
          tail[i >> 2] |= s.charCodeAt(i) << (i % 4 << 3);
        }
        tail[i >> 2] |= 128 << (i % 4 << 3);
        if (i > 55) {
          md5cycle(state, tail);
          for (i = 0; i < 16; i += 1) {
            tail[i] = 0;
          }
        }
        tmp = n * 8;
        tmp = tmp.toString(16).match(/(.*?)(.{0,8})$/);
        lo = parseInt(tmp[2], 16);
        hi = parseInt(tmp[1], 16) || 0;
        tail[14] = lo;
        tail[15] = hi;
        md5cycle(state, tail);
        return state;
      }
      function md51_array(a) {
        var n = a.length, state = [1732584193, -271733879, -1732584194, 271733878], i, length, tail, tmp, lo, hi;
        for (i = 64; i <= n; i += 64) {
          md5cycle(state, md5blk_array(a.subarray(i - 64, i)));
        }
        a = i - 64 < n ? a.subarray(i - 64) : new Uint8Array(0);
        length = a.length;
        tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        for (i = 0; i < length; i += 1) {
          tail[i >> 2] |= a[i] << (i % 4 << 3);
        }
        tail[i >> 2] |= 128 << (i % 4 << 3);
        if (i > 55) {
          md5cycle(state, tail);
          for (i = 0; i < 16; i += 1) {
            tail[i] = 0;
          }
        }
        tmp = n * 8;
        tmp = tmp.toString(16).match(/(.*?)(.{0,8})$/);
        lo = parseInt(tmp[2], 16);
        hi = parseInt(tmp[1], 16) || 0;
        tail[14] = lo;
        tail[15] = hi;
        md5cycle(state, tail);
        return state;
      }
      function rhex(n) {
        var s = "", j;
        for (j = 0; j < 4; j += 1) {
          s += hex_chr[n >> j * 8 + 4 & 15] + hex_chr[n >> j * 8 & 15];
        }
        return s;
      }
      function hex(x) {
        var i;
        for (i = 0; i < x.length; i += 1) {
          x[i] = rhex(x[i]);
        }
        return x.join("");
      }
      if (hex(md51("hello")) !== "5d41402abc4b2a76b9719d911017c592") {
        add32 = function(x, y) {
          var lsw = (x & 65535) + (y & 65535), msw = (x >> 16) + (y >> 16) + (lsw >> 16);
          return msw << 16 | lsw & 65535;
        };
      }
      if (typeof ArrayBuffer !== "undefined" && !ArrayBuffer.prototype.slice) {
        (function() {
          function clamp(val, length) {
            val = val | 0 || 0;
            if (val < 0) {
              return Math.max(val + length, 0);
            }
            return Math.min(val, length);
          }
          ArrayBuffer.prototype.slice = function(from, to) {
            var length = this.byteLength, begin = clamp(from, length), end = length, num, target, targetArray, sourceArray;
            if (to !== undefined2) {
              end = clamp(to, length);
            }
            if (begin > end) {
              return new ArrayBuffer(0);
            }
            num = end - begin;
            target = new ArrayBuffer(num);
            targetArray = new Uint8Array(target);
            sourceArray = new Uint8Array(this, begin, num);
            targetArray.set(sourceArray);
            return target;
          };
        })();
      }
      function toUtf8(str) {
        if (/[\u0080-\uFFFF]/.test(str)) {
          str = unescape(encodeURIComponent(str));
        }
        return str;
      }
      function utf8Str2ArrayBuffer(str, returnUInt8Array) {
        var length = str.length, buff = new ArrayBuffer(length), arr = new Uint8Array(buff), i;
        for (i = 0; i < length; i += 1) {
          arr[i] = str.charCodeAt(i);
        }
        return returnUInt8Array ? arr : buff;
      }
      function arrayBuffer2Utf8Str(buff) {
        return String.fromCharCode.apply(null, new Uint8Array(buff));
      }
      function concatenateArrayBuffers(first, second, returnUInt8Array) {
        var result = new Uint8Array(first.byteLength + second.byteLength);
        result.set(new Uint8Array(first));
        result.set(new Uint8Array(second), first.byteLength);
        return returnUInt8Array ? result : result.buffer;
      }
      function hexToBinaryString(hex2) {
        var bytes = [], length = hex2.length, x;
        for (x = 0; x < length - 1; x += 2) {
          bytes.push(parseInt(hex2.substr(x, 2), 16));
        }
        return String.fromCharCode.apply(String, bytes);
      }
      function SparkMD5() {
        this.reset();
      }
      SparkMD5.prototype.append = function(str) {
        this.appendBinary(toUtf8(str));
        return this;
      };
      SparkMD5.prototype.appendBinary = function(contents) {
        this._buff += contents;
        this._length += contents.length;
        var length = this._buff.length, i;
        for (i = 64; i <= length; i += 64) {
          md5cycle(this._hash, md5blk(this._buff.substring(i - 64, i)));
        }
        this._buff = this._buff.substring(i - 64);
        return this;
      };
      SparkMD5.prototype.end = function(raw) {
        var buff = this._buff, length = buff.length, i, tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], ret;
        for (i = 0; i < length; i += 1) {
          tail[i >> 2] |= buff.charCodeAt(i) << (i % 4 << 3);
        }
        this._finish(tail, length);
        ret = hex(this._hash);
        if (raw) {
          ret = hexToBinaryString(ret);
        }
        this.reset();
        return ret;
      };
      SparkMD5.prototype.reset = function() {
        this._buff = "";
        this._length = 0;
        this._hash = [1732584193, -271733879, -1732584194, 271733878];
        return this;
      };
      SparkMD5.prototype.getState = function() {
        return {
          buff: this._buff,
          length: this._length,
          hash: this._hash.slice()
        };
      };
      SparkMD5.prototype.setState = function(state) {
        this._buff = state.buff;
        this._length = state.length;
        this._hash = state.hash;
        return this;
      };
      SparkMD5.prototype.destroy = function() {
        delete this._hash;
        delete this._buff;
        delete this._length;
      };
      SparkMD5.prototype._finish = function(tail, length) {
        var i = length, tmp, lo, hi;
        tail[i >> 2] |= 128 << (i % 4 << 3);
        if (i > 55) {
          md5cycle(this._hash, tail);
          for (i = 0; i < 16; i += 1) {
            tail[i] = 0;
          }
        }
        tmp = this._length * 8;
        tmp = tmp.toString(16).match(/(.*?)(.{0,8})$/);
        lo = parseInt(tmp[2], 16);
        hi = parseInt(tmp[1], 16) || 0;
        tail[14] = lo;
        tail[15] = hi;
        md5cycle(this._hash, tail);
      };
      SparkMD5.hash = function(str, raw) {
        return SparkMD5.hashBinary(toUtf8(str), raw);
      };
      SparkMD5.hashBinary = function(content, raw) {
        var hash = md51(content), ret = hex(hash);
        return raw ? hexToBinaryString(ret) : ret;
      };
      SparkMD5.ArrayBuffer = function() {
        this.reset();
      };
      SparkMD5.ArrayBuffer.prototype.append = function(arr) {
        var buff = concatenateArrayBuffers(this._buff.buffer, arr, true), length = buff.length, i;
        this._length += arr.byteLength;
        for (i = 64; i <= length; i += 64) {
          md5cycle(this._hash, md5blk_array(buff.subarray(i - 64, i)));
        }
        this._buff = i - 64 < length ? new Uint8Array(buff.buffer.slice(i - 64)) : new Uint8Array(0);
        return this;
      };
      SparkMD5.ArrayBuffer.prototype.end = function(raw) {
        var buff = this._buff, length = buff.length, tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], i, ret;
        for (i = 0; i < length; i += 1) {
          tail[i >> 2] |= buff[i] << (i % 4 << 3);
        }
        this._finish(tail, length);
        ret = hex(this._hash);
        if (raw) {
          ret = hexToBinaryString(ret);
        }
        this.reset();
        return ret;
      };
      SparkMD5.ArrayBuffer.prototype.reset = function() {
        this._buff = new Uint8Array(0);
        this._length = 0;
        this._hash = [1732584193, -271733879, -1732584194, 271733878];
        return this;
      };
      SparkMD5.ArrayBuffer.prototype.getState = function() {
        var state = SparkMD5.prototype.getState.call(this);
        state.buff = arrayBuffer2Utf8Str(state.buff);
        return state;
      };
      SparkMD5.ArrayBuffer.prototype.setState = function(state) {
        state.buff = utf8Str2ArrayBuffer(state.buff, true);
        return SparkMD5.prototype.setState.call(this, state);
      };
      SparkMD5.ArrayBuffer.prototype.destroy = SparkMD5.prototype.destroy;
      SparkMD5.ArrayBuffer.prototype._finish = SparkMD5.prototype._finish;
      SparkMD5.ArrayBuffer.hash = function(arr, raw) {
        var hash = md51_array(new Uint8Array(arr)), ret = hex(hash);
        return raw ? hexToBinaryString(ret) : ret;
      };
      return SparkMD5;
    });
  }
});

// node_modules/vuvuzela/index.js
var require_vuvuzela = __commonJS({
  "node_modules/vuvuzela/index.js"(exports) {
    "use strict";
    exports.stringify = function stringify3(input) {
      var queue2 = [];
      queue2.push({ obj: input });
      var res = "";
      var next, obj, prefix, val, i, arrayPrefix, keys2, k, key, value, objPrefix;
      while (next = queue2.pop()) {
        obj = next.obj;
        prefix = next.prefix || "";
        val = next.val || "";
        res += prefix;
        if (val) {
          res += val;
        } else if (typeof obj !== "object") {
          res += typeof obj === "undefined" ? null : JSON.stringify(obj);
        } else if (obj === null) {
          res += "null";
        } else if (Array.isArray(obj)) {
          queue2.push({ val: "]" });
          for (i = obj.length - 1; i >= 0; i--) {
            arrayPrefix = i === 0 ? "" : ",";
            queue2.push({ obj: obj[i], prefix: arrayPrefix });
          }
          queue2.push({ val: "[" });
        } else {
          keys2 = [];
          for (k in obj) {
            if (obj.hasOwnProperty(k)) {
              keys2.push(k);
            }
          }
          queue2.push({ val: "}" });
          for (i = keys2.length - 1; i >= 0; i--) {
            key = keys2[i];
            value = obj[key];
            objPrefix = i > 0 ? "," : "";
            objPrefix += JSON.stringify(key) + ":";
            queue2.push({ obj: value, prefix: objPrefix });
          }
          queue2.push({ val: "{" });
        }
      }
      return res;
    };
    function pop2(obj, stack, metaStack) {
      var lastMetaElement = metaStack[metaStack.length - 1];
      if (obj === lastMetaElement.element) {
        metaStack.pop();
        lastMetaElement = metaStack[metaStack.length - 1];
      }
      var element = lastMetaElement.element;
      var lastElementIndex = lastMetaElement.index;
      if (Array.isArray(element)) {
        element.push(obj);
      } else if (lastElementIndex === stack.length - 2) {
        var key = stack.pop();
        element[key] = obj;
      } else {
        stack.push(obj);
      }
    }
    exports.parse = function(str) {
      var stack = [];
      var metaStack = [];
      var i = 0;
      var collationIndex2, parsedNum, numChar;
      var parsedString, lastCh, numConsecutiveSlashes, ch;
      var arrayElement, objElement;
      while (true) {
        collationIndex2 = str[i++];
        if (collationIndex2 === "}" || collationIndex2 === "]" || typeof collationIndex2 === "undefined") {
          if (stack.length === 1) {
            return stack.pop();
          } else {
            pop2(stack.pop(), stack, metaStack);
            continue;
          }
        }
        switch (collationIndex2) {
          case " ":
          case "	":
          case "\n":
          case ":":
          case ",":
            break;
          case "n":
            i += 3;
            pop2(null, stack, metaStack);
            break;
          case "t":
            i += 3;
            pop2(true, stack, metaStack);
            break;
          case "f":
            i += 4;
            pop2(false, stack, metaStack);
            break;
          case "0":
          case "1":
          case "2":
          case "3":
          case "4":
          case "5":
          case "6":
          case "7":
          case "8":
          case "9":
          case "-":
            parsedNum = "";
            i--;
            while (true) {
              numChar = str[i++];
              if (/[\d\.\-e\+]/.test(numChar)) {
                parsedNum += numChar;
              } else {
                i--;
                break;
              }
            }
            pop2(parseFloat(parsedNum), stack, metaStack);
            break;
          case '"':
            parsedString = "";
            lastCh = void 0;
            numConsecutiveSlashes = 0;
            while (true) {
              ch = str[i++];
              if (ch !== '"' || lastCh === "\\" && numConsecutiveSlashes % 2 === 1) {
                parsedString += ch;
                lastCh = ch;
                if (lastCh === "\\") {
                  numConsecutiveSlashes++;
                } else {
                  numConsecutiveSlashes = 0;
                }
              } else {
                break;
              }
            }
            pop2(JSON.parse('"' + parsedString + '"'), stack, metaStack);
            break;
          case "[":
            arrayElement = { element: [], index: stack.length };
            stack.push(arrayElement.element);
            metaStack.push(arrayElement);
            break;
          case "{":
            objElement = { element: {}, index: stack.length };
            stack.push(objElement.element);
            metaStack.push(objElement);
            break;
          default:
            throw new Error(
              "unexpectedly reached end of input: " + collationIndex2
            );
        }
      }
    };
  }
});

// node_modules/events/events.js
var require_events = __commonJS({
  "node_modules/events/events.js"(exports, module) {
    "use strict";
    var R = typeof Reflect === "object" ? Reflect : null;
    var ReflectApply = R && typeof R.apply === "function" ? R.apply : function ReflectApply2(target, receiver, args) {
      return Function.prototype.apply.call(target, receiver, args);
    };
    var ReflectOwnKeys;
    if (R && typeof R.ownKeys === "function") {
      ReflectOwnKeys = R.ownKeys;
    } else if (Object.getOwnPropertySymbols) {
      ReflectOwnKeys = function ReflectOwnKeys2(target) {
        return Object.getOwnPropertyNames(target).concat(Object.getOwnPropertySymbols(target));
      };
    } else {
      ReflectOwnKeys = function ReflectOwnKeys2(target) {
        return Object.getOwnPropertyNames(target);
      };
    }
    function ProcessEmitWarning(warning) {
      if (console && console.warn) console.warn(warning);
    }
    var NumberIsNaN = Number.isNaN || function NumberIsNaN2(value) {
      return value !== value;
    };
    function EventEmitter() {
      EventEmitter.init.call(this);
    }
    module.exports = EventEmitter;
    module.exports.once = once2;
    EventEmitter.EventEmitter = EventEmitter;
    EventEmitter.prototype._events = void 0;
    EventEmitter.prototype._eventsCount = 0;
    EventEmitter.prototype._maxListeners = void 0;
    var defaultMaxListeners = 10;
    function checkListener(listener) {
      if (typeof listener !== "function") {
        throw new TypeError('The "listener" argument must be of type Function. Received type ' + typeof listener);
      }
    }
    Object.defineProperty(EventEmitter, "defaultMaxListeners", {
      enumerable: true,
      get: function() {
        return defaultMaxListeners;
      },
      set: function(arg) {
        if (typeof arg !== "number" || arg < 0 || NumberIsNaN(arg)) {
          throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received ' + arg + ".");
        }
        defaultMaxListeners = arg;
      }
    });
    EventEmitter.init = function() {
      if (this._events === void 0 || this._events === Object.getPrototypeOf(this)._events) {
        this._events = /* @__PURE__ */ Object.create(null);
        this._eventsCount = 0;
      }
      this._maxListeners = this._maxListeners || void 0;
    };
    EventEmitter.prototype.setMaxListeners = function setMaxListeners(n) {
      if (typeof n !== "number" || n < 0 || NumberIsNaN(n)) {
        throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received ' + n + ".");
      }
      this._maxListeners = n;
      return this;
    };
    function _getMaxListeners(that) {
      if (that._maxListeners === void 0)
        return EventEmitter.defaultMaxListeners;
      return that._maxListeners;
    }
    EventEmitter.prototype.getMaxListeners = function getMaxListeners() {
      return _getMaxListeners(this);
    };
    EventEmitter.prototype.emit = function emit(type) {
      var args = [];
      for (var i = 1; i < arguments.length; i++) args.push(arguments[i]);
      var doError = type === "error";
      var events = this._events;
      if (events !== void 0)
        doError = doError && events.error === void 0;
      else if (!doError)
        return false;
      if (doError) {
        var er;
        if (args.length > 0)
          er = args[0];
        if (er instanceof Error) {
          throw er;
        }
        var err = new Error("Unhandled error." + (er ? " (" + er.message + ")" : ""));
        err.context = er;
        throw err;
      }
      var handler = events[type];
      if (handler === void 0)
        return false;
      if (typeof handler === "function") {
        ReflectApply(handler, this, args);
      } else {
        var len = handler.length;
        var listeners = arrayClone(handler, len);
        for (var i = 0; i < len; ++i)
          ReflectApply(listeners[i], this, args);
      }
      return true;
    };
    function _addListener(target, type, listener, prepend) {
      var m;
      var events;
      var existing;
      checkListener(listener);
      events = target._events;
      if (events === void 0) {
        events = target._events = /* @__PURE__ */ Object.create(null);
        target._eventsCount = 0;
      } else {
        if (events.newListener !== void 0) {
          target.emit(
            "newListener",
            type,
            listener.listener ? listener.listener : listener
          );
          events = target._events;
        }
        existing = events[type];
      }
      if (existing === void 0) {
        existing = events[type] = listener;
        ++target._eventsCount;
      } else {
        if (typeof existing === "function") {
          existing = events[type] = prepend ? [listener, existing] : [existing, listener];
        } else if (prepend) {
          existing.unshift(listener);
        } else {
          existing.push(listener);
        }
        m = _getMaxListeners(target);
        if (m > 0 && existing.length > m && !existing.warned) {
          existing.warned = true;
          var w = new Error("Possible EventEmitter memory leak detected. " + existing.length + " " + String(type) + " listeners added. Use emitter.setMaxListeners() to increase limit");
          w.name = "MaxListenersExceededWarning";
          w.emitter = target;
          w.type = type;
          w.count = existing.length;
          ProcessEmitWarning(w);
        }
      }
      return target;
    }
    EventEmitter.prototype.addListener = function addListener(type, listener) {
      return _addListener(this, type, listener, false);
    };
    EventEmitter.prototype.on = EventEmitter.prototype.addListener;
    EventEmitter.prototype.prependListener = function prependListener(type, listener) {
      return _addListener(this, type, listener, true);
    };
    function onceWrapper() {
      if (!this.fired) {
        this.target.removeListener(this.type, this.wrapFn);
        this.fired = true;
        if (arguments.length === 0)
          return this.listener.call(this.target);
        return this.listener.apply(this.target, arguments);
      }
    }
    function _onceWrap(target, type, listener) {
      var state = { fired: false, wrapFn: void 0, target, type, listener };
      var wrapped = onceWrapper.bind(state);
      wrapped.listener = listener;
      state.wrapFn = wrapped;
      return wrapped;
    }
    EventEmitter.prototype.once = function once3(type, listener) {
      checkListener(listener);
      this.on(type, _onceWrap(this, type, listener));
      return this;
    };
    EventEmitter.prototype.prependOnceListener = function prependOnceListener(type, listener) {
      checkListener(listener);
      this.prependListener(type, _onceWrap(this, type, listener));
      return this;
    };
    EventEmitter.prototype.removeListener = function removeListener(type, listener) {
      var list, events, position, i, originalListener;
      checkListener(listener);
      events = this._events;
      if (events === void 0)
        return this;
      list = events[type];
      if (list === void 0)
        return this;
      if (list === listener || list.listener === listener) {
        if (--this._eventsCount === 0)
          this._events = /* @__PURE__ */ Object.create(null);
        else {
          delete events[type];
          if (events.removeListener)
            this.emit("removeListener", type, list.listener || listener);
        }
      } else if (typeof list !== "function") {
        position = -1;
        for (i = list.length - 1; i >= 0; i--) {
          if (list[i] === listener || list[i].listener === listener) {
            originalListener = list[i].listener;
            position = i;
            break;
          }
        }
        if (position < 0)
          return this;
        if (position === 0)
          list.shift();
        else {
          spliceOne(list, position);
        }
        if (list.length === 1)
          events[type] = list[0];
        if (events.removeListener !== void 0)
          this.emit("removeListener", type, originalListener || listener);
      }
      return this;
    };
    EventEmitter.prototype.off = EventEmitter.prototype.removeListener;
    EventEmitter.prototype.removeAllListeners = function removeAllListeners(type) {
      var listeners, events, i;
      events = this._events;
      if (events === void 0)
        return this;
      if (events.removeListener === void 0) {
        if (arguments.length === 0) {
          this._events = /* @__PURE__ */ Object.create(null);
          this._eventsCount = 0;
        } else if (events[type] !== void 0) {
          if (--this._eventsCount === 0)
            this._events = /* @__PURE__ */ Object.create(null);
          else
            delete events[type];
        }
        return this;
      }
      if (arguments.length === 0) {
        var keys2 = Object.keys(events);
        var key;
        for (i = 0; i < keys2.length; ++i) {
          key = keys2[i];
          if (key === "removeListener") continue;
          this.removeAllListeners(key);
        }
        this.removeAllListeners("removeListener");
        this._events = /* @__PURE__ */ Object.create(null);
        this._eventsCount = 0;
        return this;
      }
      listeners = events[type];
      if (typeof listeners === "function") {
        this.removeListener(type, listeners);
      } else if (listeners !== void 0) {
        for (i = listeners.length - 1; i >= 0; i--) {
          this.removeListener(type, listeners[i]);
        }
      }
      return this;
    };
    function _listeners(target, type, unwrap) {
      var events = target._events;
      if (events === void 0)
        return [];
      var evlistener = events[type];
      if (evlistener === void 0)
        return [];
      if (typeof evlistener === "function")
        return unwrap ? [evlistener.listener || evlistener] : [evlistener];
      return unwrap ? unwrapListeners(evlistener) : arrayClone(evlistener, evlistener.length);
    }
    EventEmitter.prototype.listeners = function listeners(type) {
      return _listeners(this, type, true);
    };
    EventEmitter.prototype.rawListeners = function rawListeners(type) {
      return _listeners(this, type, false);
    };
    EventEmitter.listenerCount = function(emitter, type) {
      if (typeof emitter.listenerCount === "function") {
        return emitter.listenerCount(type);
      } else {
        return listenerCount2.call(emitter, type);
      }
    };
    EventEmitter.prototype.listenerCount = listenerCount2;
    function listenerCount2(type) {
      var events = this._events;
      if (events !== void 0) {
        var evlistener = events[type];
        if (typeof evlistener === "function") {
          return 1;
        } else if (evlistener !== void 0) {
          return evlistener.length;
        }
      }
      return 0;
    }
    EventEmitter.prototype.eventNames = function eventNames() {
      return this._eventsCount > 0 ? ReflectOwnKeys(this._events) : [];
    };
    function arrayClone(arr, n) {
      var copy = new Array(n);
      for (var i = 0; i < n; ++i)
        copy[i] = arr[i];
      return copy;
    }
    function spliceOne(list, index) {
      for (; index + 1 < list.length; index++)
        list[index] = list[index + 1];
      list.pop();
    }
    function unwrapListeners(arr) {
      var ret = new Array(arr.length);
      for (var i = 0; i < ret.length; ++i) {
        ret[i] = arr[i].listener || arr[i];
      }
      return ret;
    }
    function once2(emitter, name) {
      return new Promise(function(resolve, reject) {
        function errorListener(err) {
          emitter.removeListener(name, resolver);
          reject(err);
        }
        function resolver() {
          if (typeof emitter.removeListener === "function") {
            emitter.removeListener("error", errorListener);
          }
          resolve([].slice.call(arguments));
        }
        ;
        eventTargetAgnosticAddListener(emitter, name, resolver, { once: true });
        if (name !== "error") {
          addErrorHandlerIfEventEmitter(emitter, errorListener, { once: true });
        }
      });
    }
    function addErrorHandlerIfEventEmitter(emitter, handler, flags) {
      if (typeof emitter.on === "function") {
        eventTargetAgnosticAddListener(emitter, "error", handler, flags);
      }
    }
    function eventTargetAgnosticAddListener(emitter, name, listener, flags) {
      if (typeof emitter.on === "function") {
        if (flags.once) {
          emitter.once(name, listener);
        } else {
          emitter.on(name, listener);
        }
      } else if (typeof emitter.addEventListener === "function") {
        emitter.addEventListener(name, function wrapListener(arg) {
          if (flags.once) {
            emitter.removeEventListener(name, wrapListener);
          }
          listener(arg);
        });
      } else {
        throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type ' + typeof emitter);
      }
    }
  }
});

// node_modules/pouchdb-browser/lib/index.es.js
var import_spark_md5 = __toESM(require_spark_md5());

// node_modules/uuid/dist/esm-browser/rng.js
var getRandomValues;
var rnds8 = new Uint8Array(16);
function rng() {
  if (!getRandomValues) {
    getRandomValues = typeof crypto !== "undefined" && crypto.getRandomValues && crypto.getRandomValues.bind(crypto) || typeof msCrypto !== "undefined" && typeof msCrypto.getRandomValues === "function" && msCrypto.getRandomValues.bind(msCrypto);
    if (!getRandomValues) {
      throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");
    }
  }
  return getRandomValues(rnds8);
}

// node_modules/uuid/dist/esm-browser/regex.js
var regex_default = /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;

// node_modules/uuid/dist/esm-browser/validate.js
function validate(uuid2) {
  return typeof uuid2 === "string" && regex_default.test(uuid2);
}
var validate_default = validate;

// node_modules/uuid/dist/esm-browser/stringify.js
var byteToHex = [];
for (i = 0; i < 256; ++i) {
  byteToHex.push((i + 256).toString(16).substr(1));
}
var i;
function stringify(arr) {
  var offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;
  var uuid2 = (byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + "-" + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + "-" + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + "-" + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + "-" + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]]).toLowerCase();
  if (!validate_default(uuid2)) {
    throw TypeError("Stringified UUID is invalid");
  }
  return uuid2;
}
var stringify_default = stringify;

// node_modules/uuid/dist/esm-browser/parse.js
function parse(uuid2) {
  if (!validate_default(uuid2)) {
    throw TypeError("Invalid UUID");
  }
  var v;
  var arr = new Uint8Array(16);
  arr[0] = (v = parseInt(uuid2.slice(0, 8), 16)) >>> 24;
  arr[1] = v >>> 16 & 255;
  arr[2] = v >>> 8 & 255;
  arr[3] = v & 255;
  arr[4] = (v = parseInt(uuid2.slice(9, 13), 16)) >>> 8;
  arr[5] = v & 255;
  arr[6] = (v = parseInt(uuid2.slice(14, 18), 16)) >>> 8;
  arr[7] = v & 255;
  arr[8] = (v = parseInt(uuid2.slice(19, 23), 16)) >>> 8;
  arr[9] = v & 255;
  arr[10] = (v = parseInt(uuid2.slice(24, 36), 16)) / 1099511627776 & 255;
  arr[11] = v / 4294967296 & 255;
  arr[12] = v >>> 24 & 255;
  arr[13] = v >>> 16 & 255;
  arr[14] = v >>> 8 & 255;
  arr[15] = v & 255;
  return arr;
}
var parse_default = parse;

// node_modules/uuid/dist/esm-browser/v35.js
function stringToBytes(str) {
  str = unescape(encodeURIComponent(str));
  var bytes = [];
  for (var i = 0; i < str.length; ++i) {
    bytes.push(str.charCodeAt(i));
  }
  return bytes;
}
var DNS = "6ba7b810-9dad-11d1-80b4-00c04fd430c8";
var URL = "6ba7b811-9dad-11d1-80b4-00c04fd430c8";
function v35_default(name, version2, hashfunc) {
  function generateUUID(value, namespace, buf, offset) {
    if (typeof value === "string") {
      value = stringToBytes(value);
    }
    if (typeof namespace === "string") {
      namespace = parse_default(namespace);
    }
    if (namespace.length !== 16) {
      throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");
    }
    var bytes = new Uint8Array(16 + value.length);
    bytes.set(namespace);
    bytes.set(value, namespace.length);
    bytes = hashfunc(bytes);
    bytes[6] = bytes[6] & 15 | version2;
    bytes[8] = bytes[8] & 63 | 128;
    if (buf) {
      offset = offset || 0;
      for (var i = 0; i < 16; ++i) {
        buf[offset + i] = bytes[i];
      }
      return buf;
    }
    return stringify_default(bytes);
  }
  try {
    generateUUID.name = name;
  } catch (err) {
  }
  generateUUID.DNS = DNS;
  generateUUID.URL = URL;
  return generateUUID;
}

// node_modules/uuid/dist/esm-browser/md5.js
function md5(bytes) {
  if (typeof bytes === "string") {
    var msg = unescape(encodeURIComponent(bytes));
    bytes = new Uint8Array(msg.length);
    for (var i = 0; i < msg.length; ++i) {
      bytes[i] = msg.charCodeAt(i);
    }
  }
  return md5ToHexEncodedArray(wordsToMd5(bytesToWords(bytes), bytes.length * 8));
}
function md5ToHexEncodedArray(input) {
  var output = [];
  var length32 = input.length * 32;
  var hexTab = "0123456789abcdef";
  for (var i = 0; i < length32; i += 8) {
    var x = input[i >> 5] >>> i % 32 & 255;
    var hex = parseInt(hexTab.charAt(x >>> 4 & 15) + hexTab.charAt(x & 15), 16);
    output.push(hex);
  }
  return output;
}
function getOutputLength(inputLength8) {
  return (inputLength8 + 64 >>> 9 << 4) + 14 + 1;
}
function wordsToMd5(x, len) {
  x[len >> 5] |= 128 << len % 32;
  x[getOutputLength(len) - 1] = len;
  var a = 1732584193;
  var b = -271733879;
  var c = -1732584194;
  var d = 271733878;
  for (var i = 0; i < x.length; i += 16) {
    var olda = a;
    var oldb = b;
    var oldc = c;
    var oldd = d;
    a = md5ff(a, b, c, d, x[i], 7, -680876936);
    d = md5ff(d, a, b, c, x[i + 1], 12, -389564586);
    c = md5ff(c, d, a, b, x[i + 2], 17, 606105819);
    b = md5ff(b, c, d, a, x[i + 3], 22, -1044525330);
    a = md5ff(a, b, c, d, x[i + 4], 7, -176418897);
    d = md5ff(d, a, b, c, x[i + 5], 12, 1200080426);
    c = md5ff(c, d, a, b, x[i + 6], 17, -1473231341);
    b = md5ff(b, c, d, a, x[i + 7], 22, -45705983);
    a = md5ff(a, b, c, d, x[i + 8], 7, 1770035416);
    d = md5ff(d, a, b, c, x[i + 9], 12, -1958414417);
    c = md5ff(c, d, a, b, x[i + 10], 17, -42063);
    b = md5ff(b, c, d, a, x[i + 11], 22, -1990404162);
    a = md5ff(a, b, c, d, x[i + 12], 7, 1804603682);
    d = md5ff(d, a, b, c, x[i + 13], 12, -40341101);
    c = md5ff(c, d, a, b, x[i + 14], 17, -1502002290);
    b = md5ff(b, c, d, a, x[i + 15], 22, 1236535329);
    a = md5gg(a, b, c, d, x[i + 1], 5, -165796510);
    d = md5gg(d, a, b, c, x[i + 6], 9, -1069501632);
    c = md5gg(c, d, a, b, x[i + 11], 14, 643717713);
    b = md5gg(b, c, d, a, x[i], 20, -373897302);
    a = md5gg(a, b, c, d, x[i + 5], 5, -701558691);
    d = md5gg(d, a, b, c, x[i + 10], 9, 38016083);
    c = md5gg(c, d, a, b, x[i + 15], 14, -660478335);
    b = md5gg(b, c, d, a, x[i + 4], 20, -405537848);
    a = md5gg(a, b, c, d, x[i + 9], 5, 568446438);
    d = md5gg(d, a, b, c, x[i + 14], 9, -1019803690);
    c = md5gg(c, d, a, b, x[i + 3], 14, -187363961);
    b = md5gg(b, c, d, a, x[i + 8], 20, 1163531501);
    a = md5gg(a, b, c, d, x[i + 13], 5, -1444681467);
    d = md5gg(d, a, b, c, x[i + 2], 9, -51403784);
    c = md5gg(c, d, a, b, x[i + 7], 14, 1735328473);
    b = md5gg(b, c, d, a, x[i + 12], 20, -1926607734);
    a = md5hh(a, b, c, d, x[i + 5], 4, -378558);
    d = md5hh(d, a, b, c, x[i + 8], 11, -2022574463);
    c = md5hh(c, d, a, b, x[i + 11], 16, 1839030562);
    b = md5hh(b, c, d, a, x[i + 14], 23, -35309556);
    a = md5hh(a, b, c, d, x[i + 1], 4, -1530992060);
    d = md5hh(d, a, b, c, x[i + 4], 11, 1272893353);
    c = md5hh(c, d, a, b, x[i + 7], 16, -155497632);
    b = md5hh(b, c, d, a, x[i + 10], 23, -1094730640);
    a = md5hh(a, b, c, d, x[i + 13], 4, 681279174);
    d = md5hh(d, a, b, c, x[i], 11, -358537222);
    c = md5hh(c, d, a, b, x[i + 3], 16, -722521979);
    b = md5hh(b, c, d, a, x[i + 6], 23, 76029189);
    a = md5hh(a, b, c, d, x[i + 9], 4, -640364487);
    d = md5hh(d, a, b, c, x[i + 12], 11, -421815835);
    c = md5hh(c, d, a, b, x[i + 15], 16, 530742520);
    b = md5hh(b, c, d, a, x[i + 2], 23, -995338651);
    a = md5ii(a, b, c, d, x[i], 6, -198630844);
    d = md5ii(d, a, b, c, x[i + 7], 10, 1126891415);
    c = md5ii(c, d, a, b, x[i + 14], 15, -1416354905);
    b = md5ii(b, c, d, a, x[i + 5], 21, -57434055);
    a = md5ii(a, b, c, d, x[i + 12], 6, 1700485571);
    d = md5ii(d, a, b, c, x[i + 3], 10, -1894986606);
    c = md5ii(c, d, a, b, x[i + 10], 15, -1051523);
    b = md5ii(b, c, d, a, x[i + 1], 21, -2054922799);
    a = md5ii(a, b, c, d, x[i + 8], 6, 1873313359);
    d = md5ii(d, a, b, c, x[i + 15], 10, -30611744);
    c = md5ii(c, d, a, b, x[i + 6], 15, -1560198380);
    b = md5ii(b, c, d, a, x[i + 13], 21, 1309151649);
    a = md5ii(a, b, c, d, x[i + 4], 6, -145523070);
    d = md5ii(d, a, b, c, x[i + 11], 10, -1120210379);
    c = md5ii(c, d, a, b, x[i + 2], 15, 718787259);
    b = md5ii(b, c, d, a, x[i + 9], 21, -343485551);
    a = safeAdd(a, olda);
    b = safeAdd(b, oldb);
    c = safeAdd(c, oldc);
    d = safeAdd(d, oldd);
  }
  return [a, b, c, d];
}
function bytesToWords(input) {
  if (input.length === 0) {
    return [];
  }
  var length8 = input.length * 8;
  var output = new Uint32Array(getOutputLength(length8));
  for (var i = 0; i < length8; i += 8) {
    output[i >> 5] |= (input[i / 8] & 255) << i % 32;
  }
  return output;
}
function safeAdd(x, y) {
  var lsw = (x & 65535) + (y & 65535);
  var msw = (x >> 16) + (y >> 16) + (lsw >> 16);
  return msw << 16 | lsw & 65535;
}
function bitRotateLeft(num, cnt) {
  return num << cnt | num >>> 32 - cnt;
}
function md5cmn(q, a, b, x, s, t) {
  return safeAdd(bitRotateLeft(safeAdd(safeAdd(a, q), safeAdd(x, t)), s), b);
}
function md5ff(a, b, c, d, x, s, t) {
  return md5cmn(b & c | ~b & d, a, b, x, s, t);
}
function md5gg(a, b, c, d, x, s, t) {
  return md5cmn(b & d | c & ~d, a, b, x, s, t);
}
function md5hh(a, b, c, d, x, s, t) {
  return md5cmn(b ^ c ^ d, a, b, x, s, t);
}
function md5ii(a, b, c, d, x, s, t) {
  return md5cmn(c ^ (b | ~d), a, b, x, s, t);
}
var md5_default = md5;

// node_modules/uuid/dist/esm-browser/v3.js
var v3 = v35_default("v3", 48, md5_default);

// node_modules/uuid/dist/esm-browser/v4.js
function v4(options, buf, offset) {
  options = options || {};
  var rnds = options.random || (options.rng || rng)();
  rnds[6] = rnds[6] & 15 | 64;
  rnds[8] = rnds[8] & 63 | 128;
  if (buf) {
    offset = offset || 0;
    for (var i = 0; i < 16; ++i) {
      buf[offset + i] = rnds[i];
    }
    return buf;
  }
  return stringify_default(rnds);
}
var v4_default = v4;

// node_modules/uuid/dist/esm-browser/sha1.js
function f(s, x, y, z) {
  switch (s) {
    case 0:
      return x & y ^ ~x & z;
    case 1:
      return x ^ y ^ z;
    case 2:
      return x & y ^ x & z ^ y & z;
    case 3:
      return x ^ y ^ z;
  }
}
function ROTL(x, n) {
  return x << n | x >>> 32 - n;
}
function sha1(bytes) {
  var K = [1518500249, 1859775393, 2400959708, 3395469782];
  var H = [1732584193, 4023233417, 2562383102, 271733878, 3285377520];
  if (typeof bytes === "string") {
    var msg = unescape(encodeURIComponent(bytes));
    bytes = [];
    for (var i = 0; i < msg.length; ++i) {
      bytes.push(msg.charCodeAt(i));
    }
  } else if (!Array.isArray(bytes)) {
    bytes = Array.prototype.slice.call(bytes);
  }
  bytes.push(128);
  var l = bytes.length / 4 + 2;
  var N = Math.ceil(l / 16);
  var M = new Array(N);
  for (var _i = 0; _i < N; ++_i) {
    var arr = new Uint32Array(16);
    for (var j = 0; j < 16; ++j) {
      arr[j] = bytes[_i * 64 + j * 4] << 24 | bytes[_i * 64 + j * 4 + 1] << 16 | bytes[_i * 64 + j * 4 + 2] << 8 | bytes[_i * 64 + j * 4 + 3];
    }
    M[_i] = arr;
  }
  M[N - 1][14] = (bytes.length - 1) * 8 / Math.pow(2, 32);
  M[N - 1][14] = Math.floor(M[N - 1][14]);
  M[N - 1][15] = (bytes.length - 1) * 8 & 4294967295;
  for (var _i2 = 0; _i2 < N; ++_i2) {
    var W = new Uint32Array(80);
    for (var t = 0; t < 16; ++t) {
      W[t] = M[_i2][t];
    }
    for (var _t = 16; _t < 80; ++_t) {
      W[_t] = ROTL(W[_t - 3] ^ W[_t - 8] ^ W[_t - 14] ^ W[_t - 16], 1);
    }
    var a = H[0];
    var b = H[1];
    var c = H[2];
    var d = H[3];
    var e = H[4];
    for (var _t2 = 0; _t2 < 80; ++_t2) {
      var s = Math.floor(_t2 / 20);
      var T = ROTL(a, 5) + f(s, b, c, d) + e + K[s] + W[_t2] >>> 0;
      e = d;
      d = c;
      c = ROTL(b, 30) >>> 0;
      b = a;
      a = T;
    }
    H[0] = H[0] + a >>> 0;
    H[1] = H[1] + b >>> 0;
    H[2] = H[2] + c >>> 0;
    H[3] = H[3] + d >>> 0;
    H[4] = H[4] + e >>> 0;
  }
  return [H[0] >> 24 & 255, H[0] >> 16 & 255, H[0] >> 8 & 255, H[0] & 255, H[1] >> 24 & 255, H[1] >> 16 & 255, H[1] >> 8 & 255, H[1] & 255, H[2] >> 24 & 255, H[2] >> 16 & 255, H[2] >> 8 & 255, H[2] & 255, H[3] >> 24 & 255, H[3] >> 16 & 255, H[3] >> 8 & 255, H[3] & 255, H[4] >> 24 & 255, H[4] >> 16 & 255, H[4] >> 8 & 255, H[4] & 255];
}
var sha1_default = sha1;

// node_modules/uuid/dist/esm-browser/v5.js
var v5 = v35_default("v5", 80, sha1_default);

// node_modules/pouchdb-browser/lib/index.es.js
var import_vuvuzela = __toESM(require_vuvuzela());
var import_events = __toESM(require_events());
function isBinaryObject(object) {
  return typeof ArrayBuffer !== "undefined" && object instanceof ArrayBuffer || typeof Blob !== "undefined" && object instanceof Blob;
}
function cloneBinaryObject(object) {
  return object instanceof ArrayBuffer ? object.slice(0) : object.slice(0, object.size, object.type);
}
var funcToString = Function.prototype.toString;
var objectCtorString = funcToString.call(Object);
function isPlainObject(value) {
  var proto = Object.getPrototypeOf(value);
  if (proto === null) {
    return true;
  }
  var Ctor = proto.constructor;
  return typeof Ctor == "function" && Ctor instanceof Ctor && funcToString.call(Ctor) == objectCtorString;
}
function clone(object) {
  var newObject;
  var i;
  var len;
  if (!object || typeof object !== "object") {
    return object;
  }
  if (Array.isArray(object)) {
    newObject = [];
    for (i = 0, len = object.length; i < len; i++) {
      newObject[i] = clone(object[i]);
    }
    return newObject;
  }
  if (object instanceof Date && isFinite(object)) {
    return object.toISOString();
  }
  if (isBinaryObject(object)) {
    return cloneBinaryObject(object);
  }
  if (!isPlainObject(object)) {
    return object;
  }
  newObject = {};
  for (i in object) {
    if (Object.prototype.hasOwnProperty.call(object, i)) {
      var value = clone(object[i]);
      if (typeof value !== "undefined") {
        newObject[i] = value;
      }
    }
  }
  return newObject;
}
function once(fun) {
  var called = false;
  return function(...args) {
    if (called) {
      throw new Error("once called more than once");
    } else {
      called = true;
      fun.apply(this, args);
    }
  };
}
function toPromise(func) {
  return function(...args) {
    args = clone(args);
    var self2 = this;
    var usedCB = typeof args[args.length - 1] === "function" ? args.pop() : false;
    var promise = new Promise(function(fulfill, reject) {
      var resp;
      try {
        var callback = once(function(err, mesg) {
          if (err) {
            reject(err);
          } else {
            fulfill(mesg);
          }
        });
        args.push(callback);
        resp = func.apply(self2, args);
        if (resp && typeof resp.then === "function") {
          fulfill(resp);
        }
      } catch (e) {
        reject(e);
      }
    });
    if (usedCB) {
      promise.then(function(result) {
        usedCB(null, result);
      }, usedCB);
    }
    return promise;
  };
}
function logApiCall(self2, name, args) {
  if (self2.constructor.listeners("debug").length) {
    var logArgs = ["api", self2.name, name];
    for (var i = 0; i < args.length - 1; i++) {
      logArgs.push(args[i]);
    }
    self2.constructor.emit("debug", logArgs);
    var origCallback = args[args.length - 1];
    args[args.length - 1] = function(err, res) {
      var responseArgs = ["api", self2.name, name];
      responseArgs = responseArgs.concat(
        err ? ["error", err] : ["success", res]
      );
      self2.constructor.emit("debug", responseArgs);
      origCallback(err, res);
    };
  }
}
function adapterFun(name, callback) {
  return toPromise(function(...args) {
    if (this._closed) {
      return Promise.reject(new Error("database is closed"));
    }
    if (this._destroyed) {
      return Promise.reject(new Error("database is destroyed"));
    }
    var self2 = this;
    logApiCall(self2, name, args);
    if (!this.taskqueue.isReady) {
      return new Promise(function(fulfill, reject) {
        self2.taskqueue.addTask(function(failed) {
          if (failed) {
            reject(failed);
          } else {
            fulfill(self2[name].apply(self2, args));
          }
        });
      });
    }
    return callback.apply(this, args);
  });
}
function pick(obj, arr) {
  var res = {};
  for (var i = 0, len = arr.length; i < len; i++) {
    var prop = arr[i];
    if (prop in obj) {
      res[prop] = obj[prop];
    }
  }
  return res;
}
var MAX_NUM_CONCURRENT_REQUESTS = 6;
function identityFunction(x) {
  return x;
}
function formatResultForOpenRevsGet(result) {
  return [{
    ok: result
  }];
}
function bulkGet(db, opts, callback) {
  var requests = opts.docs;
  var requestsById = /* @__PURE__ */ new Map();
  requests.forEach(function(request) {
    if (requestsById.has(request.id)) {
      requestsById.get(request.id).push(request);
    } else {
      requestsById.set(request.id, [request]);
    }
  });
  var numDocs = requestsById.size;
  var numDone = 0;
  var perDocResults = new Array(numDocs);
  function collapseResultsAndFinish() {
    var results = [];
    perDocResults.forEach(function(res) {
      res.docs.forEach(function(info) {
        results.push({
          id: res.id,
          docs: [info]
        });
      });
    });
    callback(null, { results });
  }
  function checkDone() {
    if (++numDone === numDocs) {
      collapseResultsAndFinish();
    }
  }
  function gotResult(docIndex, id, docs) {
    perDocResults[docIndex] = { id, docs };
    checkDone();
  }
  var allRequests = [];
  requestsById.forEach(function(value, key) {
    allRequests.push(key);
  });
  var i = 0;
  function nextBatch() {
    if (i >= allRequests.length) {
      return;
    }
    var upTo = Math.min(i + MAX_NUM_CONCURRENT_REQUESTS, allRequests.length);
    var batch = allRequests.slice(i, upTo);
    processBatch(batch, i);
    i += batch.length;
  }
  function processBatch(batch, offset) {
    batch.forEach(function(docId, j) {
      var docIdx = offset + j;
      var docRequests = requestsById.get(docId);
      var docOpts = pick(docRequests[0], ["atts_since", "attachments"]);
      docOpts.open_revs = docRequests.map(function(request) {
        return request.rev;
      });
      docOpts.open_revs = docOpts.open_revs.filter(identityFunction);
      var formatResult = identityFunction;
      if (docOpts.open_revs.length === 0) {
        delete docOpts.open_revs;
        formatResult = formatResultForOpenRevsGet;
      }
      ["revs", "attachments", "binary", "ajax", "latest"].forEach(function(param) {
        if (param in opts) {
          docOpts[param] = opts[param];
        }
      });
      db.get(docId, docOpts, function(err, res) {
        var result;
        if (err) {
          result = [{ error: err }];
        } else {
          result = formatResult(res);
        }
        gotResult(docIdx, docId, result);
        nextBatch();
      });
    });
  }
  nextBatch();
}
var hasLocal;
try {
  localStorage.setItem("_pouch_check_localstorage", 1);
  hasLocal = !!localStorage.getItem("_pouch_check_localstorage");
} catch (e) {
  hasLocal = false;
}
function hasLocalStorage() {
  return hasLocal;
}
var nextTick = typeof queueMicrotask === "function" ? queueMicrotask : function nextTick2(fn) {
  Promise.resolve().then(fn);
};
var Changes = class extends import_events.default {
  constructor() {
    super();
    this._listeners = {};
    if (hasLocalStorage()) {
      addEventListener("storage", (e) => {
        this.emit(e.key);
      });
    }
  }
  addListener(dbName, id, db, opts) {
    if (this._listeners[id]) {
      return;
    }
    var inprogress = false;
    var self2 = this;
    function eventFunction() {
      if (!self2._listeners[id]) {
        return;
      }
      if (inprogress) {
        inprogress = "waiting";
        return;
      }
      inprogress = true;
      var changesOpts = pick(opts, [
        "style",
        "include_docs",
        "attachments",
        "conflicts",
        "filter",
        "doc_ids",
        "view",
        "since",
        "query_params",
        "binary",
        "return_docs"
      ]);
      function onError() {
        inprogress = false;
      }
      db.changes(changesOpts).on("change", function(c) {
        if (c.seq > opts.since && !opts.cancelled) {
          opts.since = c.seq;
          opts.onChange(c);
        }
      }).on("complete", function() {
        if (inprogress === "waiting") {
          nextTick(eventFunction);
        }
        inprogress = false;
      }).on("error", onError);
    }
    this._listeners[id] = eventFunction;
    this.on(dbName, eventFunction);
  }
  removeListener(dbName, id) {
    if (!(id in this._listeners)) {
      return;
    }
    super.removeListener(dbName, this._listeners[id]);
    delete this._listeners[id];
  }
  notifyLocalWindows(dbName) {
    if (hasLocalStorage()) {
      localStorage[dbName] = localStorage[dbName] === "a" ? "b" : "a";
    }
  }
  notify(dbName) {
    this.emit(dbName);
    this.notifyLocalWindows(dbName);
  }
};
function guardedConsole(method) {
  if (typeof console !== "undefined" && typeof console[method] === "function") {
    var args = Array.prototype.slice.call(arguments, 1);
    console[method].apply(console, args);
  }
}
function randomNumber(min, max) {
  var maxTimeout = 6e5;
  min = parseInt(min, 10) || 0;
  max = parseInt(max, 10);
  if (max !== max || max <= min) {
    max = (min || 1) << 1;
  } else {
    max = max + 1;
  }
  if (max > maxTimeout) {
    min = maxTimeout >> 1;
    max = maxTimeout;
  }
  var ratio = Math.random();
  var range = max - min;
  return ~~(range * ratio + min);
}
function defaultBackOff(min) {
  var max = 0;
  if (!min) {
    max = 2e3;
  }
  return randomNumber(min, max);
}
function explainError(status, str) {
  guardedConsole("info", "The above " + status + " is totally normal. " + str);
}
var PouchError = class extends Error {
  constructor(status, error, reason) {
    super();
    this.status = status;
    this.name = error;
    this.message = reason;
    this.error = true;
  }
  toString() {
    return JSON.stringify({
      status: this.status,
      name: this.name,
      message: this.message,
      reason: this.reason
    });
  }
};
var UNAUTHORIZED = new PouchError(401, "unauthorized", "Name or password is incorrect.");
var MISSING_BULK_DOCS = new PouchError(400, "bad_request", "Missing JSON list of 'docs'");
var MISSING_DOC = new PouchError(404, "not_found", "missing");
var REV_CONFLICT = new PouchError(409, "conflict", "Document update conflict");
var INVALID_ID = new PouchError(400, "bad_request", "_id field must contain a string");
var MISSING_ID = new PouchError(412, "missing_id", "_id is required for puts");
var RESERVED_ID = new PouchError(400, "bad_request", "Only reserved document ids may start with underscore.");
var NOT_OPEN = new PouchError(412, "precondition_failed", "Database not open");
var UNKNOWN_ERROR = new PouchError(500, "unknown_error", "Database encountered an unknown error");
var BAD_ARG = new PouchError(500, "badarg", "Some query argument is invalid");
var INVALID_REQUEST = new PouchError(400, "invalid_request", "Request was invalid");
var QUERY_PARSE_ERROR = new PouchError(400, "query_parse_error", "Some query parameter is invalid");
var DOC_VALIDATION = new PouchError(500, "doc_validation", "Bad special document member");
var BAD_REQUEST = new PouchError(400, "bad_request", "Something wrong with the request");
var NOT_AN_OBJECT = new PouchError(400, "bad_request", "Document must be a JSON object");
var DB_MISSING = new PouchError(404, "not_found", "Database not found");
var IDB_ERROR = new PouchError(500, "indexed_db_went_bad", "unknown");
var WSQ_ERROR = new PouchError(500, "web_sql_went_bad", "unknown");
var LDB_ERROR = new PouchError(500, "levelDB_went_went_bad", "unknown");
var FORBIDDEN = new PouchError(403, "forbidden", "Forbidden by design doc validate_doc_update function");
var INVALID_REV = new PouchError(400, "bad_request", "Invalid rev format");
var FILE_EXISTS = new PouchError(412, "file_exists", "The database could not be created, the file already exists.");
var MISSING_STUB = new PouchError(412, "missing_stub", "A pre-existing attachment stub wasn't found");
var INVALID_URL = new PouchError(413, "invalid_url", "Provided URL is invalid");
function createError(error, reason) {
  function CustomPouchError(reason2) {
    var names = Object.getOwnPropertyNames(error);
    for (var i = 0, len = names.length; i < len; i++) {
      if (typeof error[names[i]] !== "function") {
        this[names[i]] = error[names[i]];
      }
    }
    if (this.stack === void 0) {
      this.stack = new Error().stack;
    }
    if (reason2 !== void 0) {
      this.reason = reason2;
    }
  }
  CustomPouchError.prototype = PouchError.prototype;
  return new CustomPouchError(reason);
}
function generateErrorFromResponse(err) {
  if (typeof err !== "object") {
    var data = err;
    err = UNKNOWN_ERROR;
    err.data = data;
  }
  if ("error" in err && err.error === "conflict") {
    err.name = "conflict";
    err.status = 409;
  }
  if (!("name" in err)) {
    err.name = err.error || "unknown";
  }
  if (!("status" in err)) {
    err.status = 500;
  }
  if (!("message" in err)) {
    err.message = err.message || err.reason;
  }
  if (!("stack" in err)) {
    err.stack = new Error().stack;
  }
  return err;
}
function tryFilter(filter2, doc, req) {
  try {
    return !filter2(doc, req);
  } catch (err) {
    var msg = "Filter function threw: " + err.toString();
    return createError(BAD_REQUEST, msg);
  }
}
function filterChange(opts) {
  var req = {};
  var hasFilter = opts.filter && typeof opts.filter === "function";
  req.query = opts.query_params;
  return function filter2(change) {
    if (!change.doc) {
      change.doc = {};
    }
    var filterReturn = hasFilter && tryFilter(opts.filter, change.doc, req);
    if (typeof filterReturn === "object") {
      return filterReturn;
    }
    if (filterReturn) {
      return false;
    }
    if (!opts.include_docs) {
      delete change.doc;
    } else if (!opts.attachments) {
      for (var att in change.doc._attachments) {
        if (Object.prototype.hasOwnProperty.call(change.doc._attachments, att)) {
          change.doc._attachments[att].stub = true;
        }
      }
    }
    return true;
  };
}
function invalidIdError(id) {
  var err;
  if (!id) {
    err = createError(MISSING_ID);
  } else if (typeof id !== "string") {
    err = createError(INVALID_ID);
  } else if (/^_/.test(id) && !/^_(design|local)/.test(id)) {
    err = createError(RESERVED_ID);
  }
  if (err) {
    throw err;
  }
}
function isRemote(db) {
  if (typeof db._remote === "boolean") {
    return db._remote;
  }
  if (typeof db.type === "function") {
    guardedConsole(
      "warn",
      "db.type() is deprecated and will be removed in a future version of PouchDB"
    );
    return db.type() === "http";
  }
  return false;
}
function listenerCount(ee, type) {
  return "listenerCount" in ee ? ee.listenerCount(type) : import_events.default.listenerCount(ee, type);
}
function parseDesignDocFunctionName(s) {
  if (!s) {
    return null;
  }
  var parts = s.split("/");
  if (parts.length === 2) {
    return parts;
  }
  if (parts.length === 1) {
    return [s, s];
  }
  return null;
}
function normalizeDesignDocFunctionName(s) {
  var normalized = parseDesignDocFunctionName(s);
  return normalized ? normalized.join("/") : null;
}
var keys = [
  "source",
  "protocol",
  "authority",
  "userInfo",
  "user",
  "password",
  "host",
  "port",
  "relative",
  "path",
  "directory",
  "file",
  "query",
  "anchor"
];
var qName = "queryKey";
var qParser = /(?:^|&)([^&=]*)=?([^&]*)/g;
var parser = /^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/;
function parseUri(str) {
  var m = parser.exec(str);
  var uri = {};
  var i = 14;
  while (i--) {
    var key = keys[i];
    var value = m[i] || "";
    var encoded = ["user", "password"].indexOf(key) !== -1;
    uri[key] = encoded ? decodeURIComponent(value) : value;
  }
  uri[qName] = {};
  uri[keys[12]].replace(qParser, function($0, $1, $2) {
    if ($1) {
      uri[qName][$1] = $2;
    }
  });
  return uri;
}
function scopeEval(source, scope) {
  var keys2 = [];
  var values = [];
  for (var key in scope) {
    if (Object.prototype.hasOwnProperty.call(scope, key)) {
      keys2.push(key);
      values.push(scope[key]);
    }
  }
  keys2.push(source);
  return Function.apply(null, keys2).apply(null, values);
}
function upsert(db, docId, diffFun) {
  return db.get(docId).catch(function(err) {
    if (err.status !== 404) {
      throw err;
    }
    return {};
  }).then(function(doc) {
    var docRev = doc._rev;
    var newDoc = diffFun(doc);
    if (!newDoc) {
      return { updated: false, rev: docRev };
    }
    newDoc._id = docId;
    newDoc._rev = docRev;
    return tryAndPut(db, newDoc, diffFun);
  });
}
function tryAndPut(db, doc, diffFun) {
  return db.put(doc).then(function(res) {
    return {
      updated: true,
      rev: res.rev
    };
  }, function(err) {
    if (err.status !== 409) {
      throw err;
    }
    return upsert(db, doc._id, diffFun);
  });
}
var thisAtob = function(str) {
  return atob(str);
};
var thisBtoa = function(str) {
  return btoa(str);
};
function createBlob(parts, properties) {
  parts = parts || [];
  properties = properties || {};
  try {
    return new Blob(parts, properties);
  } catch (e) {
    if (e.name !== "TypeError") {
      throw e;
    }
    var Builder = typeof BlobBuilder !== "undefined" ? BlobBuilder : typeof MSBlobBuilder !== "undefined" ? MSBlobBuilder : typeof MozBlobBuilder !== "undefined" ? MozBlobBuilder : WebKitBlobBuilder;
    var builder = new Builder();
    for (var i = 0; i < parts.length; i += 1) {
      builder.append(parts[i]);
    }
    return builder.getBlob(properties.type);
  }
}
function binaryStringToArrayBuffer(bin) {
  var length = bin.length;
  var buf = new ArrayBuffer(length);
  var arr = new Uint8Array(buf);
  for (var i = 0; i < length; i++) {
    arr[i] = bin.charCodeAt(i);
  }
  return buf;
}
function binStringToBluffer(binString, type) {
  return createBlob([binaryStringToArrayBuffer(binString)], { type });
}
function b64ToBluffer(b64, type) {
  return binStringToBluffer(thisAtob(b64), type);
}
function arrayBufferToBinaryString(buffer) {
  var binary = "";
  var bytes = new Uint8Array(buffer);
  var length = bytes.byteLength;
  for (var i = 0; i < length; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return binary;
}
function readAsBinaryString(blob, callback) {
  var reader = new FileReader();
  var hasBinaryString = typeof reader.readAsBinaryString === "function";
  reader.onloadend = function(e) {
    var result = e.target.result || "";
    if (hasBinaryString) {
      return callback(result);
    }
    callback(arrayBufferToBinaryString(result));
  };
  if (hasBinaryString) {
    reader.readAsBinaryString(blob);
  } else {
    reader.readAsArrayBuffer(blob);
  }
}
function blobToBinaryString(blobOrBuffer, callback) {
  readAsBinaryString(blobOrBuffer, function(bin) {
    callback(bin);
  });
}
function blobToBase64(blobOrBuffer, callback) {
  blobToBinaryString(blobOrBuffer, function(base64) {
    callback(thisBtoa(base64));
  });
}
function readAsArrayBuffer(blob, callback) {
  var reader = new FileReader();
  reader.onloadend = function(e) {
    var result = e.target.result || new ArrayBuffer(0);
    callback(result);
  };
  reader.readAsArrayBuffer(blob);
}
var setImmediateShim = self.setImmediate || self.setTimeout;
var MD5_CHUNK_SIZE = 32768;
function rawToBase64(raw) {
  return thisBtoa(raw);
}
function appendBlob(buffer, blob, start, end, callback) {
  if (start > 0 || end < blob.size) {
    blob = blob.slice(start, end);
  }
  readAsArrayBuffer(blob, function(arrayBuffer) {
    buffer.append(arrayBuffer);
    callback();
  });
}
function appendString(buffer, string, start, end, callback) {
  if (start > 0 || end < string.length) {
    string = string.substring(start, end);
  }
  buffer.appendBinary(string);
  callback();
}
function binaryMd5(data, callback) {
  var inputIsString = typeof data === "string";
  var len = inputIsString ? data.length : data.size;
  var chunkSize = Math.min(MD5_CHUNK_SIZE, len);
  var chunks = Math.ceil(len / chunkSize);
  var currentChunk = 0;
  var buffer = inputIsString ? new import_spark_md5.default() : new import_spark_md5.default.ArrayBuffer();
  var append = inputIsString ? appendString : appendBlob;
  function next() {
    setImmediateShim(loadNextChunk);
  }
  function done() {
    var raw = buffer.end(true);
    var base64 = rawToBase64(raw);
    callback(base64);
    buffer.destroy();
  }
  function loadNextChunk() {
    var start = currentChunk * chunkSize;
    var end = start + chunkSize;
    currentChunk++;
    if (currentChunk < chunks) {
      append(buffer, data, start, end, next);
    } else {
      append(buffer, data, start, end, done);
    }
  }
  loadNextChunk();
}
function stringMd5(string) {
  return import_spark_md5.default.hash(string);
}
function rev(doc, deterministic_revs) {
  if (!deterministic_revs) {
    return v4_default().replace(/-/g, "").toLowerCase();
  }
  var mutateableDoc = Object.assign({}, doc);
  delete mutateableDoc._rev_tree;
  return stringMd5(JSON.stringify(mutateableDoc));
}
var uuid = v4_default;
function winningRev(metadata) {
  var winningId;
  var winningPos;
  var winningDeleted;
  var toVisit = metadata.rev_tree.slice();
  var node;
  while (node = toVisit.pop()) {
    var tree = node.ids;
    var branches = tree[2];
    var pos = node.pos;
    if (branches.length) {
      for (var i = 0, len = branches.length; i < len; i++) {
        toVisit.push({ pos: pos + 1, ids: branches[i] });
      }
      continue;
    }
    var deleted = !!tree[1].deleted;
    var id = tree[0];
    if (!winningId || (winningDeleted !== deleted ? winningDeleted : winningPos !== pos ? winningPos < pos : winningId < id)) {
      winningId = id;
      winningPos = pos;
      winningDeleted = deleted;
    }
  }
  return winningPos + "-" + winningId;
}
function traverseRevTree(revs, callback) {
  var toVisit = revs.slice();
  var node;
  while (node = toVisit.pop()) {
    var pos = node.pos;
    var tree = node.ids;
    var branches = tree[2];
    var newCtx = callback(branches.length === 0, pos, tree[0], node.ctx, tree[1]);
    for (var i = 0, len = branches.length; i < len; i++) {
      toVisit.push({ pos: pos + 1, ids: branches[i], ctx: newCtx });
    }
  }
}
function sortByPos(a, b) {
  return a.pos - b.pos;
}
function collectLeaves(revs) {
  var leaves = [];
  traverseRevTree(revs, function(isLeaf, pos, id, acc, opts) {
    if (isLeaf) {
      leaves.push({ rev: pos + "-" + id, pos, opts });
    }
  });
  leaves.sort(sortByPos).reverse();
  for (var i = 0, len = leaves.length; i < len; i++) {
    delete leaves[i].pos;
  }
  return leaves;
}
function collectConflicts(metadata) {
  var win = winningRev(metadata);
  var leaves = collectLeaves(metadata.rev_tree);
  var conflicts = [];
  for (var i = 0, len = leaves.length; i < len; i++) {
    var leaf = leaves[i];
    if (leaf.rev !== win && !leaf.opts.deleted) {
      conflicts.push(leaf.rev);
    }
  }
  return conflicts;
}
function compactTree(metadata) {
  var revs = [];
  traverseRevTree(metadata.rev_tree, function(isLeaf, pos, revHash, ctx, opts) {
    if (opts.status === "available" && !isLeaf) {
      revs.push(pos + "-" + revHash);
      opts.status = "missing";
    }
  });
  return revs;
}
function findPathToLeaf(revs, targetRev) {
  let path = [];
  const toVisit = revs.slice();
  let node;
  while (node = toVisit.pop()) {
    const { pos, ids: tree } = node;
    const rev2 = `${pos}-${tree[0]}`;
    const branches = tree[2];
    path.push(rev2);
    if (rev2 === targetRev) {
      if (branches.length !== 0) {
        throw new Error("The requested revision is not a leaf");
      }
      return path.reverse();
    }
    if (branches.length === 0 || branches.length > 1) {
      path = [];
    }
    for (let i = 0, len = branches.length; i < len; i++) {
      toVisit.push({ pos: pos + 1, ids: branches[i] });
    }
  }
  if (path.length === 0) {
    throw new Error("The requested revision does not exist");
  }
  return path.reverse();
}
function rootToLeaf(revs) {
  var paths = [];
  var toVisit = revs.slice();
  var node;
  while (node = toVisit.pop()) {
    var pos = node.pos;
    var tree = node.ids;
    var id = tree[0];
    var opts = tree[1];
    var branches = tree[2];
    var isLeaf = branches.length === 0;
    var history = node.history ? node.history.slice() : [];
    history.push({ id, opts });
    if (isLeaf) {
      paths.push({ pos: pos + 1 - history.length, ids: history });
    }
    for (var i = 0, len = branches.length; i < len; i++) {
      toVisit.push({ pos: pos + 1, ids: branches[i], history });
    }
  }
  return paths.reverse();
}
function sortByPos$1(a, b) {
  return a.pos - b.pos;
}
function binarySearch(arr, item, comparator) {
  var low = 0;
  var high = arr.length;
  var mid;
  while (low < high) {
    mid = low + high >>> 1;
    if (comparator(arr[mid], item) < 0) {
      low = mid + 1;
    } else {
      high = mid;
    }
  }
  return low;
}
function insertSorted(arr, item, comparator) {
  var idx = binarySearch(arr, item, comparator);
  arr.splice(idx, 0, item);
}
function pathToTree(path, numStemmed) {
  var root;
  var leaf;
  for (var i = numStemmed, len = path.length; i < len; i++) {
    var node = path[i];
    var currentLeaf = [node.id, node.opts, []];
    if (leaf) {
      leaf[2].push(currentLeaf);
      leaf = currentLeaf;
    } else {
      root = leaf = currentLeaf;
    }
  }
  return root;
}
function compareTree(a, b) {
  return a[0] < b[0] ? -1 : 1;
}
function mergeTree(in_tree1, in_tree2) {
  var queue2 = [{ tree1: in_tree1, tree2: in_tree2 }];
  var conflicts = false;
  while (queue2.length > 0) {
    var item = queue2.pop();
    var tree1 = item.tree1;
    var tree2 = item.tree2;
    if (tree1[1].status || tree2[1].status) {
      tree1[1].status = tree1[1].status === "available" || tree2[1].status === "available" ? "available" : "missing";
    }
    for (var i = 0; i < tree2[2].length; i++) {
      if (!tree1[2][0]) {
        conflicts = "new_leaf";
        tree1[2][0] = tree2[2][i];
        continue;
      }
      var merged = false;
      for (var j = 0; j < tree1[2].length; j++) {
        if (tree1[2][j][0] === tree2[2][i][0]) {
          queue2.push({ tree1: tree1[2][j], tree2: tree2[2][i] });
          merged = true;
        }
      }
      if (!merged) {
        conflicts = "new_branch";
        insertSorted(tree1[2], tree2[2][i], compareTree);
      }
    }
  }
  return { conflicts, tree: in_tree1 };
}
function doMerge(tree, path, dontExpand) {
  var restree = [];
  var conflicts = false;
  var merged = false;
  var res;
  if (!tree.length) {
    return { tree: [path], conflicts: "new_leaf" };
  }
  for (var i = 0, len = tree.length; i < len; i++) {
    var branch = tree[i];
    if (branch.pos === path.pos && branch.ids[0] === path.ids[0]) {
      res = mergeTree(branch.ids, path.ids);
      restree.push({ pos: branch.pos, ids: res.tree });
      conflicts = conflicts || res.conflicts;
      merged = true;
    } else if (dontExpand !== true) {
      var t1 = branch.pos < path.pos ? branch : path;
      var t2 = branch.pos < path.pos ? path : branch;
      var diff = t2.pos - t1.pos;
      var candidateParents = [];
      var trees = [];
      trees.push({ ids: t1.ids, diff, parent: null, parentIdx: null });
      while (trees.length > 0) {
        var item = trees.pop();
        if (item.diff === 0) {
          if (item.ids[0] === t2.ids[0]) {
            candidateParents.push(item);
          }
          continue;
        }
        var elements = item.ids[2];
        for (var j = 0, elementsLen = elements.length; j < elementsLen; j++) {
          trees.push({
            ids: elements[j],
            diff: item.diff - 1,
            parent: item.ids,
            parentIdx: j
          });
        }
      }
      var el = candidateParents[0];
      if (!el) {
        restree.push(branch);
      } else {
        res = mergeTree(el.ids, t2.ids);
        el.parent[2][el.parentIdx] = res.tree;
        restree.push({ pos: t1.pos, ids: t1.ids });
        conflicts = conflicts || res.conflicts;
        merged = true;
      }
    } else {
      restree.push(branch);
    }
  }
  if (!merged) {
    restree.push(path);
  }
  restree.sort(sortByPos$1);
  return {
    tree: restree,
    conflicts: conflicts || "internal_node"
  };
}
function stem(tree, depth) {
  var paths = rootToLeaf(tree);
  var stemmedRevs;
  var result;
  for (var i = 0, len = paths.length; i < len; i++) {
    var path = paths[i];
    var stemmed = path.ids;
    var node;
    if (stemmed.length > depth) {
      if (!stemmedRevs) {
        stemmedRevs = {};
      }
      var numStemmed = stemmed.length - depth;
      node = {
        pos: path.pos + numStemmed,
        ids: pathToTree(stemmed, numStemmed)
      };
      for (var s = 0; s < numStemmed; s++) {
        var rev2 = path.pos + s + "-" + stemmed[s].id;
        stemmedRevs[rev2] = true;
      }
    } else {
      node = {
        pos: path.pos,
        ids: pathToTree(stemmed, 0)
      };
    }
    if (result) {
      result = doMerge(result, node, true).tree;
    } else {
      result = [node];
    }
  }
  if (stemmedRevs) {
    traverseRevTree(result, function(isLeaf, pos, revHash) {
      delete stemmedRevs[pos + "-" + revHash];
    });
  }
  return {
    tree: result,
    revs: stemmedRevs ? Object.keys(stemmedRevs) : []
  };
}
function merge(tree, path, depth) {
  var newTree = doMerge(tree, path);
  var stemmed = stem(newTree.tree, depth);
  return {
    tree: stemmed.tree,
    stemmedRevs: stemmed.revs,
    conflicts: newTree.conflicts
  };
}
function revExists(revs, rev2) {
  var toVisit = revs.slice();
  var splitRev = rev2.split("-");
  var targetPos = parseInt(splitRev[0], 10);
  var targetId = splitRev[1];
  var node;
  while (node = toVisit.pop()) {
    if (node.pos === targetPos && node.ids[0] === targetId) {
      return true;
    }
    var branches = node.ids[2];
    for (var i = 0, len = branches.length; i < len; i++) {
      toVisit.push({ pos: node.pos + 1, ids: branches[i] });
    }
  }
  return false;
}
function getTrees(node) {
  return node.ids;
}
function isDeleted(metadata, rev2) {
  if (!rev2) {
    rev2 = winningRev(metadata);
  }
  var id = rev2.substring(rev2.indexOf("-") + 1);
  var toVisit = metadata.rev_tree.map(getTrees);
  var tree;
  while (tree = toVisit.pop()) {
    if (tree[0] === id) {
      return !!tree[1].deleted;
    }
    toVisit = toVisit.concat(tree[2]);
  }
}
function isLocalId(id) {
  return typeof id === "string" && id.startsWith("_local/");
}
function latest(rev2, metadata) {
  var toVisit = metadata.rev_tree.slice();
  var node;
  while (node = toVisit.pop()) {
    var pos = node.pos;
    var tree = node.ids;
    var id = tree[0];
    var opts = tree[1];
    var branches = tree[2];
    var isLeaf = branches.length === 0;
    var history = node.history ? node.history.slice() : [];
    history.push({ id, pos, opts });
    if (isLeaf) {
      for (var i = 0, len = history.length; i < len; i++) {
        var historyNode = history[i];
        var historyRev = historyNode.pos + "-" + historyNode.id;
        if (historyRev === rev2) {
          return pos + "-" + id;
        }
      }
    }
    for (var j = 0, l = branches.length; j < l; j++) {
      toVisit.push({ pos: pos + 1, ids: branches[j], history });
    }
  }
  throw new Error("Unable to resolve latest revision for id " + metadata.id + ", rev " + rev2);
}
function tryCatchInChangeListener(self2, change, pending, lastSeq) {
  try {
    self2.emit("change", change, pending, lastSeq);
  } catch (e) {
    guardedConsole("error", 'Error in .on("change", function):', e);
  }
}
function processChange(doc, metadata, opts) {
  var changeList = [{ rev: doc._rev }];
  if (opts.style === "all_docs") {
    changeList = collectLeaves(metadata.rev_tree).map(function(x) {
      return { rev: x.rev };
    });
  }
  var change = {
    id: metadata.id,
    changes: changeList,
    doc
  };
  if (isDeleted(metadata, doc._rev)) {
    change.deleted = true;
  }
  if (opts.conflicts) {
    change.doc._conflicts = collectConflicts(metadata);
    if (!change.doc._conflicts.length) {
      delete change.doc._conflicts;
    }
  }
  return change;
}
var Changes$1 = class extends import_events.default {
  constructor(db, opts, callback) {
    super();
    this.db = db;
    opts = opts ? clone(opts) : {};
    var complete = opts.complete = once((err, resp) => {
      if (err) {
        if (listenerCount(this, "error") > 0) {
          this.emit("error", err);
        }
      } else {
        this.emit("complete", resp);
      }
      this.removeAllListeners();
      db.removeListener("destroyed", onDestroy);
    });
    if (callback) {
      this.on("complete", function(resp) {
        callback(null, resp);
      });
      this.on("error", callback);
    }
    const onDestroy = () => {
      this.cancel();
    };
    db.once("destroyed", onDestroy);
    opts.onChange = (change, pending, lastSeq) => {
      if (this.isCancelled) {
        return;
      }
      tryCatchInChangeListener(this, change, pending, lastSeq);
    };
    var promise = new Promise(function(fulfill, reject) {
      opts.complete = function(err, res) {
        if (err) {
          reject(err);
        } else {
          fulfill(res);
        }
      };
    });
    this.once("cancel", function() {
      db.removeListener("destroyed", onDestroy);
      opts.complete(null, { status: "cancelled" });
    });
    this.then = promise.then.bind(promise);
    this["catch"] = promise["catch"].bind(promise);
    this.then(function(result) {
      complete(null, result);
    }, complete);
    if (!db.taskqueue.isReady) {
      db.taskqueue.addTask((failed) => {
        if (failed) {
          opts.complete(failed);
        } else if (this.isCancelled) {
          this.emit("cancel");
        } else {
          this.validateChanges(opts);
        }
      });
    } else {
      this.validateChanges(opts);
    }
  }
  cancel() {
    this.isCancelled = true;
    if (this.db.taskqueue.isReady) {
      this.emit("cancel");
    }
  }
  validateChanges(opts) {
    var callback = opts.complete;
    if (PouchDB._changesFilterPlugin) {
      PouchDB._changesFilterPlugin.validate(opts, (err) => {
        if (err) {
          return callback(err);
        }
        this.doChanges(opts);
      });
    } else {
      this.doChanges(opts);
    }
  }
  doChanges(opts) {
    var callback = opts.complete;
    opts = clone(opts);
    if ("live" in opts && !("continuous" in opts)) {
      opts.continuous = opts.live;
    }
    opts.processChange = processChange;
    if (opts.since === "latest") {
      opts.since = "now";
    }
    if (!opts.since) {
      opts.since = 0;
    }
    if (opts.since === "now") {
      this.db.info().then((info) => {
        if (this.isCancelled) {
          callback(null, { status: "cancelled" });
          return;
        }
        opts.since = info.update_seq;
        this.doChanges(opts);
      }, callback);
      return;
    }
    if (PouchDB._changesFilterPlugin) {
      PouchDB._changesFilterPlugin.normalize(opts);
      if (PouchDB._changesFilterPlugin.shouldFilter(this, opts)) {
        return PouchDB._changesFilterPlugin.filter(this, opts);
      }
    } else {
      ["doc_ids", "filter", "selector", "view"].forEach(function(key) {
        if (key in opts) {
          guardedConsole(
            "warn",
            'The "' + key + '" option was passed in to changes/replicate, but pouchdb-changes-filter plugin is not installed, so it was ignored. Please install the plugin to enable filtering.'
          );
        }
      });
    }
    if (!("descending" in opts)) {
      opts.descending = false;
    }
    opts.limit = opts.limit === 0 ? 1 : opts.limit;
    opts.complete = callback;
    var newPromise = this.db._changes(opts);
    if (newPromise && typeof newPromise.cancel === "function") {
      const cancel = this.cancel;
      this.cancel = (...args) => {
        newPromise.cancel();
        cancel.apply(this, args);
      };
    }
  }
};
function yankError(callback, docId) {
  return function(err, results) {
    if (err || results[0] && results[0].error) {
      err = err || results[0];
      err.docId = docId;
      callback(err);
    } else {
      callback(null, results.length ? results[0] : results);
    }
  };
}
function cleanDocs(docs) {
  for (var i = 0; i < docs.length; i++) {
    var doc = docs[i];
    if (doc._deleted) {
      delete doc._attachments;
    } else if (doc._attachments) {
      var atts = Object.keys(doc._attachments);
      for (var j = 0; j < atts.length; j++) {
        var att = atts[j];
        doc._attachments[att] = pick(
          doc._attachments[att],
          ["data", "digest", "content_type", "length", "revpos", "stub"]
        );
      }
    }
  }
}
function compareByIdThenRev(a, b) {
  if (a._id === b._id) {
    const aStart = a._revisions ? a._revisions.start : 0;
    const bStart = b._revisions ? b._revisions.start : 0;
    return aStart - bStart;
  }
  return a._id < b._id ? -1 : 1;
}
function computeHeight(revs) {
  var height = {};
  var edges = [];
  traverseRevTree(revs, function(isLeaf, pos, id, prnt) {
    var rev$$1 = pos + "-" + id;
    if (isLeaf) {
      height[rev$$1] = 0;
    }
    if (prnt !== void 0) {
      edges.push({ from: prnt, to: rev$$1 });
    }
    return rev$$1;
  });
  edges.reverse();
  edges.forEach(function(edge) {
    if (height[edge.from] === void 0) {
      height[edge.from] = 1 + height[edge.to];
    } else {
      height[edge.from] = Math.min(height[edge.from], 1 + height[edge.to]);
    }
  });
  return height;
}
function allDocsKeysParse(opts) {
  var keys2 = "limit" in opts ? opts.keys.slice(opts.skip, opts.limit + opts.skip) : opts.skip > 0 ? opts.keys.slice(opts.skip) : opts.keys;
  opts.keys = keys2;
  opts.skip = 0;
  delete opts.limit;
  if (opts.descending) {
    keys2.reverse();
    opts.descending = false;
  }
}
function doNextCompaction(self2) {
  var task = self2._compactionQueue[0];
  var opts = task.opts;
  var callback = task.callback;
  self2.get("_local/compaction").catch(function() {
    return false;
  }).then(function(doc) {
    if (doc && doc.last_seq) {
      opts.last_seq = doc.last_seq;
    }
    self2._compact(opts, function(err, res) {
      if (err) {
        callback(err);
      } else {
        callback(null, res);
      }
      nextTick(function() {
        self2._compactionQueue.shift();
        if (self2._compactionQueue.length) {
          doNextCompaction(self2);
        }
      });
    });
  });
}
function appendPurgeSeq(db, docId, rev$$1) {
  return db.get("_local/purges").then(function(doc) {
    const purgeSeq = doc.purgeSeq + 1;
    doc.purges.push({
      docId,
      rev: rev$$1,
      purgeSeq
    });
    if (doc.purges.length > self.purged_infos_limit) {
      doc.purges.splice(0, doc.purges.length - self.purged_infos_limit);
    }
    doc.purgeSeq = purgeSeq;
    return doc;
  }).catch(function(err) {
    if (err.status !== 404) {
      throw err;
    }
    return {
      _id: "_local/purges",
      purges: [{
        docId,
        rev: rev$$1,
        purgeSeq: 0
      }],
      purgeSeq: 0
    };
  }).then(function(doc) {
    return db.put(doc);
  });
}
function attachmentNameError(name) {
  if (name.charAt(0) === "_") {
    return name + " is not a valid attachment name, attachment names cannot start with '_'";
  }
  return false;
}
function isNotSingleDoc(doc) {
  return doc === null || typeof doc !== "object" || Array.isArray(doc);
}
var validRevRegex = /^\d+-[^-]*$/;
function isValidRev(rev$$1) {
  return typeof rev$$1 === "string" && validRevRegex.test(rev$$1);
}
var AbstractPouchDB = class extends import_events.default {
  _setup() {
    this.post = adapterFun("post", function(doc, opts, callback) {
      if (typeof opts === "function") {
        callback = opts;
        opts = {};
      }
      if (isNotSingleDoc(doc)) {
        return callback(createError(NOT_AN_OBJECT));
      }
      this.bulkDocs({ docs: [doc] }, opts, yankError(callback, doc._id));
    }).bind(this);
    this.put = adapterFun("put", function(doc, opts, cb) {
      if (typeof opts === "function") {
        cb = opts;
        opts = {};
      }
      if (isNotSingleDoc(doc)) {
        return cb(createError(NOT_AN_OBJECT));
      }
      invalidIdError(doc._id);
      if ("_rev" in doc && !isValidRev(doc._rev)) {
        return cb(createError(INVALID_REV));
      }
      if (isLocalId(doc._id) && typeof this._putLocal === "function") {
        if (doc._deleted) {
          return this._removeLocal(doc, cb);
        } else {
          return this._putLocal(doc, cb);
        }
      }
      const putDoc = (next) => {
        if (typeof this._put === "function" && opts.new_edits !== false) {
          this._put(doc, opts, next);
        } else {
          this.bulkDocs({ docs: [doc] }, opts, yankError(next, doc._id));
        }
      };
      if (opts.force && doc._rev) {
        transformForceOptionToNewEditsOption();
        putDoc(function(err) {
          var result = err ? null : { ok: true, id: doc._id, rev: doc._rev };
          cb(err, result);
        });
      } else {
        putDoc(cb);
      }
      function transformForceOptionToNewEditsOption() {
        var parts = doc._rev.split("-");
        var oldRevId = parts[1];
        var oldRevNum = parseInt(parts[0], 10);
        var newRevNum = oldRevNum + 1;
        var newRevId = rev();
        doc._revisions = {
          start: newRevNum,
          ids: [newRevId, oldRevId]
        };
        doc._rev = newRevNum + "-" + newRevId;
        opts.new_edits = false;
      }
    }).bind(this);
    this.putAttachment = adapterFun("putAttachment", function(docId, attachmentId, rev$$1, blob, type) {
      var api = this;
      if (typeof type === "function") {
        type = blob;
        blob = rev$$1;
        rev$$1 = null;
      }
      if (typeof type === "undefined") {
        type = blob;
        blob = rev$$1;
        rev$$1 = null;
      }
      if (!type) {
        guardedConsole("warn", "Attachment", attachmentId, "on document", docId, "is missing content_type");
      }
      function createAttachment(doc) {
        var prevrevpos = "_rev" in doc ? parseInt(doc._rev, 10) : 0;
        doc._attachments = doc._attachments || {};
        doc._attachments[attachmentId] = {
          content_type: type,
          data: blob,
          revpos: ++prevrevpos
        };
        return api.put(doc);
      }
      return api.get(docId).then(function(doc) {
        if (doc._rev !== rev$$1) {
          throw createError(REV_CONFLICT);
        }
        return createAttachment(doc);
      }, function(err) {
        if (err.reason === MISSING_DOC.message) {
          return createAttachment({ _id: docId });
        } else {
          throw err;
        }
      });
    }).bind(this);
    this.removeAttachment = adapterFun("removeAttachment", function(docId, attachmentId, rev$$1, callback) {
      this.get(docId, (err, obj) => {
        if (err) {
          callback(err);
          return;
        }
        if (obj._rev !== rev$$1) {
          callback(createError(REV_CONFLICT));
          return;
        }
        if (!obj._attachments) {
          return callback();
        }
        delete obj._attachments[attachmentId];
        if (Object.keys(obj._attachments).length === 0) {
          delete obj._attachments;
        }
        this.put(obj, callback);
      });
    }).bind(this);
    this.remove = adapterFun("remove", function(docOrId, optsOrRev, opts, callback) {
      var doc;
      if (typeof optsOrRev === "string") {
        doc = {
          _id: docOrId,
          _rev: optsOrRev
        };
        if (typeof opts === "function") {
          callback = opts;
          opts = {};
        }
      } else {
        doc = docOrId;
        if (typeof optsOrRev === "function") {
          callback = optsOrRev;
          opts = {};
        } else {
          callback = opts;
          opts = optsOrRev;
        }
      }
      opts = opts || {};
      opts.was_delete = true;
      var newDoc = { _id: doc._id, _rev: doc._rev || opts.rev };
      newDoc._deleted = true;
      if (isLocalId(newDoc._id) && typeof this._removeLocal === "function") {
        return this._removeLocal(doc, callback);
      }
      this.bulkDocs({ docs: [newDoc] }, opts, yankError(callback, newDoc._id));
    }).bind(this);
    this.revsDiff = adapterFun("revsDiff", function(req, opts, callback) {
      if (typeof opts === "function") {
        callback = opts;
        opts = {};
      }
      var ids = Object.keys(req);
      if (!ids.length) {
        return callback(null, {});
      }
      var count = 0;
      var missing = /* @__PURE__ */ new Map();
      function addToMissing(id, revId) {
        if (!missing.has(id)) {
          missing.set(id, { missing: [] });
        }
        missing.get(id).missing.push(revId);
      }
      function processDoc(id, rev_tree) {
        var missingForId = req[id].slice(0);
        traverseRevTree(rev_tree, function(isLeaf, pos, revHash, ctx, opts2) {
          var rev$$1 = pos + "-" + revHash;
          var idx = missingForId.indexOf(rev$$1);
          if (idx === -1) {
            return;
          }
          missingForId.splice(idx, 1);
          if (opts2.status !== "available") {
            addToMissing(id, rev$$1);
          }
        });
        missingForId.forEach(function(rev$$1) {
          addToMissing(id, rev$$1);
        });
      }
      ids.forEach(function(id) {
        this._getRevisionTree(id, function(err, rev_tree) {
          if (err && err.status === 404 && err.message === "missing") {
            missing.set(id, { missing: req[id] });
          } else if (err) {
            return callback(err);
          } else {
            processDoc(id, rev_tree);
          }
          if (++count === ids.length) {
            var missingObj = {};
            missing.forEach(function(value, key) {
              missingObj[key] = value;
            });
            return callback(null, missingObj);
          }
        });
      }, this);
    }).bind(this);
    this.bulkGet = adapterFun("bulkGet", function(opts, callback) {
      bulkGet(this, opts, callback);
    }).bind(this);
    this.compactDocument = adapterFun("compactDocument", function(docId, maxHeight, callback) {
      this._getRevisionTree(docId, (err, revTree) => {
        if (err) {
          return callback(err);
        }
        var height = computeHeight(revTree);
        var candidates = [];
        var revs = [];
        Object.keys(height).forEach(function(rev$$1) {
          if (height[rev$$1] > maxHeight) {
            candidates.push(rev$$1);
          }
        });
        traverseRevTree(revTree, function(isLeaf, pos, revHash, ctx, opts) {
          var rev$$1 = pos + "-" + revHash;
          if (opts.status === "available" && candidates.indexOf(rev$$1) !== -1) {
            revs.push(rev$$1);
          }
        });
        this._doCompaction(docId, revs, callback);
      });
    }).bind(this);
    this.compact = adapterFun("compact", function(opts, callback) {
      if (typeof opts === "function") {
        callback = opts;
        opts = {};
      }
      opts = opts || {};
      this._compactionQueue = this._compactionQueue || [];
      this._compactionQueue.push({ opts, callback });
      if (this._compactionQueue.length === 1) {
        doNextCompaction(this);
      }
    }).bind(this);
    this.get = adapterFun("get", function(id, opts, cb) {
      if (typeof opts === "function") {
        cb = opts;
        opts = {};
      }
      opts = opts || {};
      if (typeof id !== "string") {
        return cb(createError(INVALID_ID));
      }
      if (isLocalId(id) && typeof this._getLocal === "function") {
        return this._getLocal(id, cb);
      }
      var leaves = [];
      const finishOpenRevs = () => {
        var result = [];
        var count = leaves.length;
        if (!count) {
          return cb(null, result);
        }
        leaves.forEach((leaf) => {
          this.get(id, {
            rev: leaf,
            revs: opts.revs,
            latest: opts.latest,
            attachments: opts.attachments,
            binary: opts.binary
          }, function(err, doc) {
            if (!err) {
              var existing;
              for (var i2 = 0, l2 = result.length; i2 < l2; i2++) {
                if (result[i2].ok && result[i2].ok._rev === doc._rev) {
                  existing = true;
                  break;
                }
              }
              if (!existing) {
                result.push({ ok: doc });
              }
            } else {
              result.push({ missing: leaf });
            }
            count--;
            if (!count) {
              cb(null, result);
            }
          });
        });
      };
      if (opts.open_revs) {
        if (opts.open_revs === "all") {
          this._getRevisionTree(id, function(err, rev_tree) {
            if (err) {
              return cb(err);
            }
            leaves = collectLeaves(rev_tree).map(function(leaf) {
              return leaf.rev;
            });
            finishOpenRevs();
          });
        } else {
          if (Array.isArray(opts.open_revs)) {
            leaves = opts.open_revs;
            for (var i = 0; i < leaves.length; i++) {
              var l = leaves[i];
              if (!isValidRev(l)) {
                return cb(createError(INVALID_REV));
              }
            }
            finishOpenRevs();
          } else {
            return cb(createError(UNKNOWN_ERROR, "function_clause"));
          }
        }
        return;
      }
      return this._get(id, opts, (err, result) => {
        if (err) {
          err.docId = id;
          return cb(err);
        }
        var doc = result.doc;
        var metadata = result.metadata;
        var ctx = result.ctx;
        if (opts.conflicts) {
          var conflicts = collectConflicts(metadata);
          if (conflicts.length) {
            doc._conflicts = conflicts;
          }
        }
        if (isDeleted(metadata, doc._rev)) {
          doc._deleted = true;
        }
        if (opts.revs || opts.revs_info) {
          var splittedRev = doc._rev.split("-");
          var revNo = parseInt(splittedRev[0], 10);
          var revHash = splittedRev[1];
          var paths = rootToLeaf(metadata.rev_tree);
          var path = null;
          for (var i2 = 0; i2 < paths.length; i2++) {
            var currentPath = paths[i2];
            const hashIndex = currentPath.ids.findIndex((x) => x.id === revHash);
            var hashFoundAtRevPos = hashIndex === revNo - 1;
            if (hashFoundAtRevPos || !path && hashIndex !== -1) {
              path = currentPath;
            }
          }
          if (!path) {
            err = new Error("invalid rev tree");
            err.docId = id;
            return cb(err);
          }
          const pathId = doc._rev.split("-")[1];
          const indexOfRev = path.ids.findIndex((x) => x.id === pathId) + 1;
          var howMany = path.ids.length - indexOfRev;
          path.ids.splice(indexOfRev, howMany);
          path.ids.reverse();
          if (opts.revs) {
            doc._revisions = {
              start: path.pos + path.ids.length - 1,
              ids: path.ids.map(function(rev$$1) {
                return rev$$1.id;
              })
            };
          }
          if (opts.revs_info) {
            var pos = path.pos + path.ids.length;
            doc._revs_info = path.ids.map(function(rev$$1) {
              pos--;
              return {
                rev: pos + "-" + rev$$1.id,
                status: rev$$1.opts.status
              };
            });
          }
        }
        if (opts.attachments && doc._attachments) {
          var attachments = doc._attachments;
          var count = Object.keys(attachments).length;
          if (count === 0) {
            return cb(null, doc);
          }
          Object.keys(attachments).forEach((key2) => {
            this._getAttachment(doc._id, key2, attachments[key2], {
              binary: opts.binary,
              metadata,
              ctx
            }, function(err2, data) {
              var att = doc._attachments[key2];
              att.data = data;
              delete att.stub;
              delete att.length;
              if (!--count) {
                cb(null, doc);
              }
            });
          });
        } else {
          if (doc._attachments) {
            for (var key in doc._attachments) {
              if (Object.prototype.hasOwnProperty.call(doc._attachments, key)) {
                doc._attachments[key].stub = true;
              }
            }
          }
          cb(null, doc);
        }
      });
    }).bind(this);
    this.getAttachment = adapterFun("getAttachment", function(docId, attachmentId, opts, callback) {
      if (opts instanceof Function) {
        callback = opts;
        opts = {};
      }
      this._get(docId, opts, (err, res) => {
        if (err) {
          return callback(err);
        }
        if (res.doc._attachments && res.doc._attachments[attachmentId]) {
          opts.ctx = res.ctx;
          opts.binary = true;
          opts.metadata = res.metadata;
          this._getAttachment(
            docId,
            attachmentId,
            res.doc._attachments[attachmentId],
            opts,
            callback
          );
        } else {
          return callback(createError(MISSING_DOC));
        }
      });
    }).bind(this);
    this.allDocs = adapterFun("allDocs", function(opts, callback) {
      if (typeof opts === "function") {
        callback = opts;
        opts = {};
      }
      opts.skip = typeof opts.skip !== "undefined" ? opts.skip : 0;
      if (opts.start_key) {
        opts.startkey = opts.start_key;
      }
      if (opts.end_key) {
        opts.endkey = opts.end_key;
      }
      if ("keys" in opts) {
        if (!Array.isArray(opts.keys)) {
          return callback(new TypeError("options.keys must be an array"));
        }
        var incompatibleOpt = ["startkey", "endkey", "key"].filter(function(incompatibleOpt2) {
          return incompatibleOpt2 in opts;
        })[0];
        if (incompatibleOpt) {
          callback(createError(
            QUERY_PARSE_ERROR,
            "Query parameter `" + incompatibleOpt + "` is not compatible with multi-get"
          ));
          return;
        }
        if (!isRemote(this)) {
          allDocsKeysParse(opts);
          if (opts.keys.length === 0) {
            return this._allDocs({ limit: 0 }, callback);
          }
        }
      }
      return this._allDocs(opts, callback);
    }).bind(this);
    this.close = adapterFun("close", function(callback) {
      this._closed = true;
      this.emit("closed");
      return this._close(callback);
    }).bind(this);
    this.info = adapterFun("info", function(callback) {
      this._info((err, info) => {
        if (err) {
          return callback(err);
        }
        info.db_name = info.db_name || this.name;
        info.auto_compaction = !!(this.auto_compaction && !isRemote(this));
        info.adapter = this.adapter;
        callback(null, info);
      });
    }).bind(this);
    this.id = adapterFun("id", function(callback) {
      return this._id(callback);
    }).bind(this);
    this.bulkDocs = adapterFun("bulkDocs", function(req, opts, callback) {
      if (typeof opts === "function") {
        callback = opts;
        opts = {};
      }
      opts = opts || {};
      if (Array.isArray(req)) {
        req = {
          docs: req
        };
      }
      if (!req || !req.docs || !Array.isArray(req.docs)) {
        return callback(createError(MISSING_BULK_DOCS));
      }
      for (var i = 0; i < req.docs.length; ++i) {
        const doc = req.docs[i];
        if (isNotSingleDoc(doc)) {
          return callback(createError(NOT_AN_OBJECT));
        }
        if ("_rev" in doc && !isValidRev(doc._rev)) {
          return callback(createError(INVALID_REV));
        }
      }
      var attachmentError;
      req.docs.forEach(function(doc) {
        if (doc._attachments) {
          Object.keys(doc._attachments).forEach(function(name) {
            attachmentError = attachmentError || attachmentNameError(name);
            if (!doc._attachments[name].content_type) {
              guardedConsole("warn", "Attachment", name, "on document", doc._id, "is missing content_type");
            }
          });
        }
      });
      if (attachmentError) {
        return callback(createError(BAD_REQUEST, attachmentError));
      }
      if (!("new_edits" in opts)) {
        if ("new_edits" in req) {
          opts.new_edits = req.new_edits;
        } else {
          opts.new_edits = true;
        }
      }
      var adapter = this;
      if (!opts.new_edits && !isRemote(adapter)) {
        req.docs.sort(compareByIdThenRev);
      }
      cleanDocs(req.docs);
      var ids = req.docs.map(function(doc) {
        return doc._id;
      });
      this._bulkDocs(req, opts, function(err, res) {
        if (err) {
          return callback(err);
        }
        if (!opts.new_edits) {
          res = res.filter(function(x) {
            return x.error;
          });
        }
        if (!isRemote(adapter)) {
          for (var i2 = 0, l = res.length; i2 < l; i2++) {
            res[i2].id = res[i2].id || ids[i2];
          }
        }
        callback(null, res);
      });
    }).bind(this);
    this.registerDependentDatabase = adapterFun("registerDependentDatabase", function(dependentDb, callback) {
      var dbOptions = clone(this.__opts);
      if (this.__opts.view_adapter) {
        dbOptions.adapter = this.__opts.view_adapter;
      }
      var depDB = new this.constructor(dependentDb, dbOptions);
      function diffFun(doc) {
        doc.dependentDbs = doc.dependentDbs || {};
        if (doc.dependentDbs[dependentDb]) {
          return false;
        }
        doc.dependentDbs[dependentDb] = true;
        return doc;
      }
      upsert(this, "_local/_pouch_dependentDbs", diffFun).then(function() {
        callback(null, { db: depDB });
      }).catch(callback);
    }).bind(this);
    this.destroy = adapterFun("destroy", function(opts, callback) {
      if (typeof opts === "function") {
        callback = opts;
        opts = {};
      }
      var usePrefix = "use_prefix" in this ? this.use_prefix : true;
      const destroyDb = () => {
        this._destroy(opts, (err, resp) => {
          if (err) {
            return callback(err);
          }
          this._destroyed = true;
          this.emit("destroyed");
          callback(null, resp || { "ok": true });
        });
      };
      if (isRemote(this)) {
        return destroyDb();
      }
      this.get("_local/_pouch_dependentDbs", (err, localDoc) => {
        if (err) {
          if (err.status !== 404) {
            return callback(err);
          } else {
            return destroyDb();
          }
        }
        var dependentDbs = localDoc.dependentDbs;
        var PouchDB2 = this.constructor;
        var deletedMap = Object.keys(dependentDbs).map((name) => {
          var trueName = usePrefix ? name.replace(new RegExp("^" + PouchDB2.prefix), "") : name;
          return new PouchDB2(trueName, this.__opts).destroy();
        });
        Promise.all(deletedMap).then(destroyDb, callback);
      });
    }).bind(this);
  }
  _compact(opts, callback) {
    var changesOpts = {
      return_docs: false,
      last_seq: opts.last_seq || 0,
      since: opts.last_seq || 0
    };
    var promises = [];
    var taskId;
    var compactedDocs = 0;
    const onChange = (row) => {
      this.activeTasks.update(taskId, {
        completed_items: ++compactedDocs
      });
      promises.push(this.compactDocument(row.id, 0));
    };
    const onError = (err) => {
      this.activeTasks.remove(taskId, err);
      callback(err);
    };
    const onComplete = (resp) => {
      var lastSeq = resp.last_seq;
      Promise.all(promises).then(() => {
        return upsert(this, "_local/compaction", (doc) => {
          if (!doc.last_seq || doc.last_seq < lastSeq) {
            doc.last_seq = lastSeq;
            return doc;
          }
          return false;
        });
      }).then(() => {
        this.activeTasks.remove(taskId);
        callback(null, { ok: true });
      }).catch(onError);
    };
    this.info().then((info) => {
      taskId = this.activeTasks.add({
        name: "database_compaction",
        total_items: info.update_seq - changesOpts.last_seq
      });
      this.changes(changesOpts).on("change", onChange).on("complete", onComplete).on("error", onError);
    });
  }
  changes(opts, callback) {
    if (typeof opts === "function") {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    opts.return_docs = "return_docs" in opts ? opts.return_docs : !opts.live;
    return new Changes$1(this, opts, callback);
  }
  type() {
    return typeof this._type === "function" ? this._type() : this.adapter;
  }
};
AbstractPouchDB.prototype.purge = adapterFun("_purge", function(docId, rev$$1, callback) {
  if (typeof this._purge === "undefined") {
    return callback(createError(UNKNOWN_ERROR, "Purge is not implemented in the " + this.adapter + " adapter."));
  }
  var self2 = this;
  self2._getRevisionTree(docId, (error, revs) => {
    if (error) {
      return callback(error);
    }
    if (!revs) {
      return callback(createError(MISSING_DOC));
    }
    let path;
    try {
      path = findPathToLeaf(revs, rev$$1);
    } catch (error2) {
      return callback(error2.message || error2);
    }
    self2._purge(docId, path, (error2, result) => {
      if (error2) {
        return callback(error2);
      } else {
        appendPurgeSeq(self2, docId, rev$$1).then(function() {
          return callback(null, result);
        });
      }
    });
  });
});
var TaskQueue = class {
  constructor() {
    this.isReady = false;
    this.failed = false;
    this.queue = [];
  }
  execute() {
    var fun;
    if (this.failed) {
      while (fun = this.queue.shift()) {
        fun(this.failed);
      }
    } else {
      while (fun = this.queue.shift()) {
        fun();
      }
    }
  }
  fail(err) {
    this.failed = err;
    this.execute();
  }
  ready(db) {
    this.isReady = true;
    this.db = db;
    this.execute();
  }
  addTask(fun) {
    this.queue.push(fun);
    if (this.failed) {
      this.execute();
    }
  }
};
function parseAdapter(name, opts) {
  var match2 = name.match(/([a-z-]*):\/\/(.*)/);
  if (match2) {
    return {
      name: /https?/.test(match2[1]) ? match2[1] + "://" + match2[2] : match2[2],
      adapter: match2[1]
    };
  }
  var adapters = PouchDB.adapters;
  var preferredAdapters = PouchDB.preferredAdapters;
  var prefix = PouchDB.prefix;
  var adapterName = opts.adapter;
  if (!adapterName) {
    for (var i = 0; i < preferredAdapters.length; ++i) {
      adapterName = preferredAdapters[i];
      if (adapterName === "idb" && "websql" in adapters && hasLocalStorage() && localStorage["_pouch__websqldb_" + prefix + name]) {
        guardedConsole("log", 'PouchDB is downgrading "' + name + '" to WebSQL to avoid data loss, because it was already opened with WebSQL.');
        continue;
      }
      break;
    }
  }
  var adapter = adapters[adapterName];
  var usePrefix = adapter && "use_prefix" in adapter ? adapter.use_prefix : true;
  return {
    name: usePrefix ? prefix + name : name,
    adapter: adapterName
  };
}
function inherits(A, B) {
  A.prototype = Object.create(B.prototype, {
    constructor: { value: A }
  });
}
function createClass(parent, init2) {
  let klass = function(...args) {
    if (!(this instanceof klass)) {
      return new klass(...args);
    }
    init2.apply(this, args);
  };
  inherits(klass, parent);
  return klass;
}
function prepareForDestruction(self2) {
  function onDestroyed(from_constructor) {
    self2.removeListener("closed", onClosed);
    if (!from_constructor) {
      self2.constructor.emit("destroyed", self2.name);
    }
  }
  function onClosed() {
    self2.removeListener("destroyed", onDestroyed);
    self2.constructor.emit("unref", self2);
  }
  self2.once("destroyed", onDestroyed);
  self2.once("closed", onClosed);
  self2.constructor.emit("ref", self2);
}
var PouchInternal = class extends AbstractPouchDB {
  constructor(name, opts) {
    super();
    this._setup(name, opts);
  }
  _setup(name, opts) {
    super._setup();
    opts = opts || {};
    if (name && typeof name === "object") {
      opts = name;
      name = opts.name;
      delete opts.name;
    }
    if (opts.deterministic_revs === void 0) {
      opts.deterministic_revs = true;
    }
    this.__opts = opts = clone(opts);
    this.auto_compaction = opts.auto_compaction;
    this.purged_infos_limit = opts.purged_infos_limit || 1e3;
    this.prefix = PouchDB.prefix;
    if (typeof name !== "string") {
      throw new Error("Missing/invalid DB name");
    }
    var prefixedName = (opts.prefix || "") + name;
    var backend = parseAdapter(prefixedName, opts);
    opts.name = backend.name;
    opts.adapter = opts.adapter || backend.adapter;
    this.name = name;
    this._adapter = opts.adapter;
    PouchDB.emit("debug", ["adapter", "Picked adapter: ", opts.adapter]);
    if (!PouchDB.adapters[opts.adapter] || !PouchDB.adapters[opts.adapter].valid()) {
      throw new Error("Invalid Adapter: " + opts.adapter);
    }
    if (opts.view_adapter) {
      if (!PouchDB.adapters[opts.view_adapter] || !PouchDB.adapters[opts.view_adapter].valid()) {
        throw new Error("Invalid View Adapter: " + opts.view_adapter);
      }
    }
    this.taskqueue = new TaskQueue();
    this.adapter = opts.adapter;
    PouchDB.adapters[opts.adapter].call(this, opts, (err) => {
      if (err) {
        return this.taskqueue.fail(err);
      }
      prepareForDestruction(this);
      this.emit("created", this);
      PouchDB.emit("created", this.name);
      this.taskqueue.ready(this);
    });
  }
};
var PouchDB = createClass(PouchInternal, function(name, opts) {
  PouchInternal.prototype._setup.call(this, name, opts);
});
var f$1 = fetch;
var h = Headers;
var ActiveTasks = class {
  constructor() {
    this.tasks = {};
  }
  list() {
    return Object.values(this.tasks);
  }
  add(task) {
    const id = v4_default();
    this.tasks[id] = {
      id,
      name: task.name,
      total_items: task.total_items,
      created_at: (/* @__PURE__ */ new Date()).toJSON()
    };
    return id;
  }
  get(id) {
    return this.tasks[id];
  }
  /* eslint-disable no-unused-vars */
  remove(id, reason) {
    delete this.tasks[id];
    return this.tasks;
  }
  update(id, updatedTask) {
    const task = this.tasks[id];
    if (typeof task !== "undefined") {
      const mergedTask = {
        id: task.id,
        name: task.name,
        created_at: task.created_at,
        total_items: updatedTask.total_items || task.total_items,
        completed_items: updatedTask.completed_items || task.completed_items,
        updated_at: (/* @__PURE__ */ new Date()).toJSON()
      };
      this.tasks[id] = mergedTask;
    }
    return this.tasks;
  }
};
PouchDB.adapters = {};
PouchDB.preferredAdapters = [];
PouchDB.prefix = "_pouch_";
var eventEmitter = new import_events.default();
function setUpEventEmitter(Pouch) {
  Object.keys(import_events.default.prototype).forEach(function(key) {
    if (typeof import_events.default.prototype[key] === "function") {
      Pouch[key] = eventEmitter[key].bind(eventEmitter);
    }
  });
  var destructListeners = Pouch._destructionListeners = /* @__PURE__ */ new Map();
  Pouch.on("ref", function onConstructorRef(db) {
    if (!destructListeners.has(db.name)) {
      destructListeners.set(db.name, []);
    }
    destructListeners.get(db.name).push(db);
  });
  Pouch.on("unref", function onConstructorUnref(db) {
    if (!destructListeners.has(db.name)) {
      return;
    }
    var dbList = destructListeners.get(db.name);
    var pos = dbList.indexOf(db);
    if (pos < 0) {
      return;
    }
    dbList.splice(pos, 1);
    if (dbList.length > 1) {
      destructListeners.set(db.name, dbList);
    } else {
      destructListeners.delete(db.name);
    }
  });
  Pouch.on("destroyed", function onConstructorDestroyed(name) {
    if (!destructListeners.has(name)) {
      return;
    }
    var dbList = destructListeners.get(name);
    destructListeners.delete(name);
    dbList.forEach(function(db) {
      db.emit("destroyed", true);
    });
  });
}
setUpEventEmitter(PouchDB);
PouchDB.adapter = function(id, obj, addToPreferredAdapters) {
  if (obj.valid()) {
    PouchDB.adapters[id] = obj;
    if (addToPreferredAdapters) {
      PouchDB.preferredAdapters.push(id);
    }
  }
};
PouchDB.plugin = function(obj) {
  if (typeof obj === "function") {
    obj(PouchDB);
  } else if (typeof obj !== "object" || Object.keys(obj).length === 0) {
    throw new Error('Invalid plugin: got "' + obj + '", expected an object or a function');
  } else {
    Object.keys(obj).forEach(function(id) {
      PouchDB.prototype[id] = obj[id];
    });
  }
  if (this.__defaults) {
    PouchDB.__defaults = Object.assign({}, this.__defaults);
  }
  return PouchDB;
};
PouchDB.defaults = function(defaultOpts) {
  let PouchWithDefaults = createClass(PouchDB, function(name, opts) {
    opts = opts || {};
    if (name && typeof name === "object") {
      opts = name;
      name = opts.name;
      delete opts.name;
    }
    opts = Object.assign({}, PouchWithDefaults.__defaults, opts);
    PouchDB.call(this, name, opts);
  });
  PouchWithDefaults.preferredAdapters = PouchDB.preferredAdapters.slice();
  Object.keys(PouchDB).forEach(function(key) {
    if (!(key in PouchWithDefaults)) {
      PouchWithDefaults[key] = PouchDB[key];
    }
  });
  PouchWithDefaults.__defaults = Object.assign({}, this.__defaults, defaultOpts);
  return PouchWithDefaults;
};
PouchDB.fetch = function(url, opts) {
  return f$1(url, opts);
};
PouchDB.prototype.activeTasks = PouchDB.activeTasks = new ActiveTasks();
var version = "9.0.0";
function getFieldFromDoc(doc, parsedField) {
  var value = doc;
  for (var i = 0, len = parsedField.length; i < len; i++) {
    var key = parsedField[i];
    value = value[key];
    if (!value) {
      break;
    }
  }
  return value;
}
function compare(left, right) {
  return left < right ? -1 : left > right ? 1 : 0;
}
function parseField(fieldName) {
  var fields = [];
  var current = "";
  for (var i = 0, len = fieldName.length; i < len; i++) {
    var ch = fieldName[i];
    if (i > 0 && fieldName[i - 1] === "\\" && (ch === "$" || ch === ".")) {
      current = current.substring(0, current.length - 1) + ch;
    } else if (ch === ".") {
      fields.push(current);
      current = "";
    } else {
      current += ch;
    }
  }
  fields.push(current);
  return fields;
}
var combinationFields = ["$or", "$nor", "$not"];
function isCombinationalField(field) {
  return combinationFields.indexOf(field) > -1;
}
function getKey(obj) {
  return Object.keys(obj)[0];
}
function getValue(obj) {
  return obj[getKey(obj)];
}
function mergeAndedSelectors(selectors) {
  var res = {};
  var first = { $or: true, $nor: true };
  selectors.forEach(function(selector) {
    Object.keys(selector).forEach(function(field) {
      var matcher = selector[field];
      if (typeof matcher !== "object") {
        matcher = { $eq: matcher };
      }
      if (isCombinationalField(field)) {
        if (matcher instanceof Array) {
          if (first[field]) {
            first[field] = false;
            res[field] = matcher;
            return;
          }
          var entries = [];
          res[field].forEach(function(existing) {
            Object.keys(matcher).forEach(function(key) {
              var m = matcher[key];
              var longest = Math.max(Object.keys(existing).length, Object.keys(m).length);
              var merged = mergeAndedSelectors([existing, m]);
              if (Object.keys(merged).length <= longest) {
                return;
              }
              entries.push(merged);
            });
          });
          res[field] = entries;
        } else {
          res[field] = mergeAndedSelectors([matcher]);
        }
      } else {
        var fieldMatchers = res[field] = res[field] || {};
        Object.keys(matcher).forEach(function(operator) {
          var value = matcher[operator];
          if (operator === "$gt" || operator === "$gte") {
            return mergeGtGte(operator, value, fieldMatchers);
          } else if (operator === "$lt" || operator === "$lte") {
            return mergeLtLte(operator, value, fieldMatchers);
          } else if (operator === "$ne") {
            return mergeNe(value, fieldMatchers);
          } else if (operator === "$eq") {
            return mergeEq(value, fieldMatchers);
          } else if (operator === "$regex") {
            return mergeRegex(value, fieldMatchers);
          }
          fieldMatchers[operator] = value;
        });
      }
    });
  });
  return res;
}
function mergeGtGte(operator, value, fieldMatchers) {
  if (typeof fieldMatchers.$eq !== "undefined") {
    return;
  }
  if (typeof fieldMatchers.$gte !== "undefined") {
    if (operator === "$gte") {
      if (value > fieldMatchers.$gte) {
        fieldMatchers.$gte = value;
      }
    } else {
      if (value >= fieldMatchers.$gte) {
        delete fieldMatchers.$gte;
        fieldMatchers.$gt = value;
      }
    }
  } else if (typeof fieldMatchers.$gt !== "undefined") {
    if (operator === "$gte") {
      if (value > fieldMatchers.$gt) {
        delete fieldMatchers.$gt;
        fieldMatchers.$gte = value;
      }
    } else {
      if (value > fieldMatchers.$gt) {
        fieldMatchers.$gt = value;
      }
    }
  } else {
    fieldMatchers[operator] = value;
  }
}
function mergeLtLte(operator, value, fieldMatchers) {
  if (typeof fieldMatchers.$eq !== "undefined") {
    return;
  }
  if (typeof fieldMatchers.$lte !== "undefined") {
    if (operator === "$lte") {
      if (value < fieldMatchers.$lte) {
        fieldMatchers.$lte = value;
      }
    } else {
      if (value <= fieldMatchers.$lte) {
        delete fieldMatchers.$lte;
        fieldMatchers.$lt = value;
      }
    }
  } else if (typeof fieldMatchers.$lt !== "undefined") {
    if (operator === "$lte") {
      if (value < fieldMatchers.$lt) {
        delete fieldMatchers.$lt;
        fieldMatchers.$lte = value;
      }
    } else {
      if (value < fieldMatchers.$lt) {
        fieldMatchers.$lt = value;
      }
    }
  } else {
    fieldMatchers[operator] = value;
  }
}
function mergeNe(value, fieldMatchers) {
  if ("$ne" in fieldMatchers) {
    fieldMatchers.$ne.push(value);
  } else {
    fieldMatchers.$ne = [value];
  }
}
function mergeEq(value, fieldMatchers) {
  delete fieldMatchers.$gt;
  delete fieldMatchers.$gte;
  delete fieldMatchers.$lt;
  delete fieldMatchers.$lte;
  delete fieldMatchers.$ne;
  fieldMatchers.$eq = value;
}
function mergeRegex(value, fieldMatchers) {
  if ("$regex" in fieldMatchers) {
    fieldMatchers.$regex.push(value);
  } else {
    fieldMatchers.$regex = [value];
  }
}
function mergeAndedSelectorsNested(obj) {
  for (var prop in obj) {
    if (Array.isArray(obj)) {
      for (var i in obj) {
        if (obj[i]["$and"]) {
          obj[i] = mergeAndedSelectors(obj[i]["$and"]);
        }
      }
    }
    var value = obj[prop];
    if (typeof value === "object") {
      mergeAndedSelectorsNested(value);
    }
  }
  return obj;
}
function isAndInSelector(obj, isAnd) {
  for (var prop in obj) {
    if (prop === "$and") {
      isAnd = true;
    }
    var value = obj[prop];
    if (typeof value === "object") {
      isAnd = isAndInSelector(value, isAnd);
    }
  }
  return isAnd;
}
function massageSelector(input) {
  var result = clone(input);
  if (isAndInSelector(result, false)) {
    result = mergeAndedSelectorsNested(result);
    if ("$and" in result) {
      result = mergeAndedSelectors(result["$and"]);
    }
  }
  ["$or", "$nor"].forEach(function(orOrNor) {
    if (orOrNor in result) {
      result[orOrNor].forEach(function(subSelector) {
        var fields2 = Object.keys(subSelector);
        for (var i2 = 0; i2 < fields2.length; i2++) {
          var field2 = fields2[i2];
          var matcher2 = subSelector[field2];
          if (typeof matcher2 !== "object" || matcher2 === null) {
            subSelector[field2] = { $eq: matcher2 };
          }
        }
      });
    }
  });
  if ("$not" in result) {
    result["$not"] = mergeAndedSelectors([result["$not"]]);
  }
  var fields = Object.keys(result);
  for (var i = 0; i < fields.length; i++) {
    var field = fields[i];
    var matcher = result[field];
    if (typeof matcher !== "object" || matcher === null) {
      matcher = { $eq: matcher };
    }
    result[field] = matcher;
  }
  normalizeArrayOperators(result);
  return result;
}
function normalizeArrayOperators(selector) {
  Object.keys(selector).forEach(function(field) {
    var matcher = selector[field];
    if (Array.isArray(matcher)) {
      matcher.forEach(function(matcherItem) {
        if (matcherItem && typeof matcherItem === "object") {
          normalizeArrayOperators(matcherItem);
        }
      });
    } else if (field === "$ne") {
      selector.$ne = [matcher];
    } else if (field === "$regex") {
      selector.$regex = [matcher];
    } else if (matcher && typeof matcher === "object") {
      normalizeArrayOperators(matcher);
    }
  });
}
function pad(str, padWith, upToLength) {
  var padding = "";
  var targetLength = upToLength - str.length;
  while (padding.length < targetLength) {
    padding += padWith;
  }
  return padding;
}
function padLeft(str, padWith, upToLength) {
  var padding = pad(str, padWith, upToLength);
  return padding + str;
}
var MIN_MAGNITUDE = -324;
var MAGNITUDE_DIGITS = 3;
var SEP = "";
function collate(a, b) {
  if (a === b) {
    return 0;
  }
  a = normalizeKey(a);
  b = normalizeKey(b);
  var ai = collationIndex(a);
  var bi = collationIndex(b);
  if (ai - bi !== 0) {
    return ai - bi;
  }
  switch (typeof a) {
    case "number":
      return a - b;
    case "boolean":
      return a < b ? -1 : 1;
    case "string":
      return stringCollate(a, b);
  }
  return Array.isArray(a) ? arrayCollate(a, b) : objectCollate(a, b);
}
function normalizeKey(key) {
  switch (typeof key) {
    case "undefined":
      return null;
    case "number":
      if (key === Infinity || key === -Infinity || isNaN(key)) {
        return null;
      }
      return key;
    case "object":
      var origKey = key;
      if (Array.isArray(key)) {
        var len = key.length;
        key = new Array(len);
        for (var i = 0; i < len; i++) {
          key[i] = normalizeKey(origKey[i]);
        }
      } else if (key instanceof Date) {
        return key.toJSON();
      } else if (key !== null) {
        key = {};
        for (var k in origKey) {
          if (Object.prototype.hasOwnProperty.call(origKey, k)) {
            var val = origKey[k];
            if (typeof val !== "undefined") {
              key[k] = normalizeKey(val);
            }
          }
        }
      }
  }
  return key;
}
function indexify(key) {
  if (key !== null) {
    switch (typeof key) {
      case "boolean":
        return key ? 1 : 0;
      case "number":
        return numToIndexableString(key);
      case "string":
        return key.replace(/\u0002/g, "").replace(/\u0001/g, "").replace(/\u0000/g, "");
      /* eslint-enable no-control-regex */
      case "object":
        var isArray2 = Array.isArray(key);
        var arr = isArray2 ? key : Object.keys(key);
        var i = -1;
        var len = arr.length;
        var result = "";
        if (isArray2) {
          while (++i < len) {
            result += toIndexableString(arr[i]);
          }
        } else {
          while (++i < len) {
            var objKey = arr[i];
            result += toIndexableString(objKey) + toIndexableString(key[objKey]);
          }
        }
        return result;
    }
  }
  return "";
}
function toIndexableString(key) {
  var zero = "\0";
  key = normalizeKey(key);
  return collationIndex(key) + SEP + indexify(key) + zero;
}
function parseNumber(str, i) {
  var originalIdx = i;
  var num;
  var zero = str[i] === "1";
  if (zero) {
    num = 0;
    i++;
  } else {
    var neg = str[i] === "0";
    i++;
    var numAsString = "";
    var magAsString = str.substring(i, i + MAGNITUDE_DIGITS);
    var magnitude = parseInt(magAsString, 10) + MIN_MAGNITUDE;
    if (neg) {
      magnitude = -magnitude;
    }
    i += MAGNITUDE_DIGITS;
    while (true) {
      var ch = str[i];
      if (ch === "\0") {
        break;
      } else {
        numAsString += ch;
      }
      i++;
    }
    numAsString = numAsString.split(".");
    if (numAsString.length === 1) {
      num = parseInt(numAsString, 10);
    } else {
      num = parseFloat(numAsString[0] + "." + numAsString[1]);
    }
    if (neg) {
      num = num - 10;
    }
    if (magnitude !== 0) {
      num = parseFloat(num + "e" + magnitude);
    }
  }
  return { num, length: i - originalIdx };
}
function pop(stack, metaStack) {
  var obj = stack.pop();
  if (metaStack.length) {
    var lastMetaElement = metaStack[metaStack.length - 1];
    if (obj === lastMetaElement.element) {
      metaStack.pop();
      lastMetaElement = metaStack[metaStack.length - 1];
    }
    var element = lastMetaElement.element;
    var lastElementIndex = lastMetaElement.index;
    if (Array.isArray(element)) {
      element.push(obj);
    } else if (lastElementIndex === stack.length - 2) {
      var key = stack.pop();
      element[key] = obj;
    } else {
      stack.push(obj);
    }
  }
}
function parseIndexableString(str) {
  var stack = [];
  var metaStack = [];
  var i = 0;
  while (true) {
    var collationIndex2 = str[i++];
    if (collationIndex2 === "\0") {
      if (stack.length === 1) {
        return stack.pop();
      } else {
        pop(stack, metaStack);
        continue;
      }
    }
    switch (collationIndex2) {
      case "1":
        stack.push(null);
        break;
      case "2":
        stack.push(str[i] === "1");
        i++;
        break;
      case "3":
        var parsedNum = parseNumber(str, i);
        stack.push(parsedNum.num);
        i += parsedNum.length;
        break;
      case "4":
        var parsedStr = "";
        while (true) {
          var ch = str[i];
          if (ch === "\0") {
            break;
          }
          parsedStr += ch;
          i++;
        }
        parsedStr = parsedStr.replace(/\u0001\u0001/g, "\0").replace(/\u0001\u0002/g, "").replace(/\u0002\u0002/g, "");
        stack.push(parsedStr);
        break;
      case "5":
        var arrayElement = { element: [], index: stack.length };
        stack.push(arrayElement.element);
        metaStack.push(arrayElement);
        break;
      case "6":
        var objElement = { element: {}, index: stack.length };
        stack.push(objElement.element);
        metaStack.push(objElement);
        break;
      /* istanbul ignore next */
      default:
        throw new Error(
          "bad collationIndex or unexpectedly reached end of input: " + collationIndex2
        );
    }
  }
}
function arrayCollate(a, b) {
  var len = Math.min(a.length, b.length);
  for (var i = 0; i < len; i++) {
    var sort = collate(a[i], b[i]);
    if (sort !== 0) {
      return sort;
    }
  }
  return a.length === b.length ? 0 : a.length > b.length ? 1 : -1;
}
function stringCollate(a, b) {
  return a === b ? 0 : a > b ? 1 : -1;
}
function objectCollate(a, b) {
  var ak = Object.keys(a), bk = Object.keys(b);
  var len = Math.min(ak.length, bk.length);
  for (var i = 0; i < len; i++) {
    var sort = collate(ak[i], bk[i]);
    if (sort !== 0) {
      return sort;
    }
    sort = collate(a[ak[i]], b[bk[i]]);
    if (sort !== 0) {
      return sort;
    }
  }
  return ak.length === bk.length ? 0 : ak.length > bk.length ? 1 : -1;
}
function collationIndex(x) {
  var id = ["boolean", "number", "string", "object"];
  var idx = id.indexOf(typeof x);
  if (~idx) {
    if (x === null) {
      return 1;
    }
    if (Array.isArray(x)) {
      return 5;
    }
    return idx < 3 ? idx + 2 : idx + 3;
  }
  if (Array.isArray(x)) {
    return 5;
  }
}
function numToIndexableString(num) {
  if (num === 0) {
    return "1";
  }
  var expFormat = num.toExponential().split(/e\+?/);
  var magnitude = parseInt(expFormat[1], 10);
  var neg = num < 0;
  var result = neg ? "0" : "2";
  var magForComparison = (neg ? -magnitude : magnitude) - MIN_MAGNITUDE;
  var magString = padLeft(magForComparison.toString(), "0", MAGNITUDE_DIGITS);
  result += SEP + magString;
  var factor = Math.abs(parseFloat(expFormat[0]));
  if (neg) {
    factor = 10 - factor;
  }
  var factorStr = factor.toFixed(20);
  factorStr = factorStr.replace(/\.?0+$/, "");
  result += SEP + factorStr;
  return result;
}
function createFieldSorter(sort) {
  function getFieldValuesAsArray(doc) {
    return sort.map(function(sorting) {
      var fieldName = getKey(sorting);
      var parsedField = parseField(fieldName);
      var docFieldValue = getFieldFromDoc(doc, parsedField);
      return docFieldValue;
    });
  }
  return function(aRow, bRow) {
    var aFieldValues = getFieldValuesAsArray(aRow.doc);
    var bFieldValues = getFieldValuesAsArray(bRow.doc);
    var collation = collate(aFieldValues, bFieldValues);
    if (collation !== 0) {
      return collation;
    }
    return compare(aRow.doc._id, bRow.doc._id);
  };
}
function filterInMemoryFields(rows, requestDef, inMemoryFields) {
  rows = rows.filter(function(row) {
    return rowFilter(row.doc, requestDef.selector, inMemoryFields);
  });
  if (requestDef.sort) {
    var fieldSorter = createFieldSorter(requestDef.sort);
    rows = rows.sort(fieldSorter);
    if (typeof requestDef.sort[0] !== "string" && getValue(requestDef.sort[0]) === "desc") {
      rows = rows.reverse();
    }
  }
  if ("limit" in requestDef || "skip" in requestDef) {
    var skip = requestDef.skip || 0;
    var limit = ("limit" in requestDef ? requestDef.limit : rows.length) + skip;
    rows = rows.slice(skip, limit);
  }
  return rows;
}
function rowFilter(doc, selector, inMemoryFields) {
  return inMemoryFields.every(function(field) {
    var matcher = selector[field];
    var parsedField = parseField(field);
    var docFieldValue = getFieldFromDoc(doc, parsedField);
    if (isCombinationalField(field)) {
      return matchCominationalSelector(field, matcher, doc);
    }
    return matchSelector(matcher, doc, parsedField, docFieldValue);
  });
}
function matchSelector(matcher, doc, parsedField, docFieldValue) {
  if (!matcher) {
    return true;
  }
  if (typeof matcher === "object") {
    return Object.keys(matcher).every(function(maybeUserOperator) {
      var userValue = matcher[maybeUserOperator];
      if (maybeUserOperator.indexOf("$") === 0) {
        return match(maybeUserOperator, doc, userValue, parsedField, docFieldValue);
      } else {
        var subParsedField = parseField(maybeUserOperator);
        if (docFieldValue === void 0 && typeof userValue !== "object" && subParsedField.length > 0) {
          return false;
        }
        var subDocFieldValue = getFieldFromDoc(docFieldValue, subParsedField);
        if (typeof userValue === "object") {
          return matchSelector(userValue, doc, parsedField, subDocFieldValue);
        }
        return match("$eq", doc, userValue, subParsedField, subDocFieldValue);
      }
    });
  }
  return matcher === docFieldValue;
}
function matchCominationalSelector(field, matcher, doc) {
  if (field === "$or") {
    return matcher.some(function(orMatchers) {
      return rowFilter(doc, orMatchers, Object.keys(orMatchers));
    });
  }
  if (field === "$not") {
    return !rowFilter(doc, matcher, Object.keys(matcher));
  }
  return !matcher.find(function(orMatchers) {
    return rowFilter(doc, orMatchers, Object.keys(orMatchers));
  });
}
function match(userOperator, doc, userValue, parsedField, docFieldValue) {
  if (!matchers[userOperator]) {
    throw new Error('unknown operator "' + userOperator + '" - should be one of $eq, $lte, $lt, $gt, $gte, $exists, $ne, $in, $nin, $size, $mod, $regex, $elemMatch, $type, $allMatch or $all');
  }
  return matchers[userOperator](doc, userValue, parsedField, docFieldValue);
}
function fieldExists(docFieldValue) {
  return typeof docFieldValue !== "undefined" && docFieldValue !== null;
}
function fieldIsNotUndefined(docFieldValue) {
  return typeof docFieldValue !== "undefined";
}
function modField(docFieldValue, userValue) {
  if (typeof docFieldValue !== "number" || parseInt(docFieldValue, 10) !== docFieldValue) {
    return false;
  }
  var divisor = userValue[0];
  var mod = userValue[1];
  return docFieldValue % divisor === mod;
}
function arrayContainsValue(docFieldValue, userValue) {
  return userValue.some(function(val) {
    if (docFieldValue instanceof Array) {
      return docFieldValue.some(function(docFieldValueItem) {
        return collate(val, docFieldValueItem) === 0;
      });
    }
    return collate(val, docFieldValue) === 0;
  });
}
function arrayContainsAllValues(docFieldValue, userValue) {
  return userValue.every(function(val) {
    return docFieldValue.some(function(docFieldValueItem) {
      return collate(val, docFieldValueItem) === 0;
    });
  });
}
function arraySize(docFieldValue, userValue) {
  return docFieldValue.length === userValue;
}
function regexMatch(docFieldValue, userValue) {
  var re = new RegExp(userValue);
  return re.test(docFieldValue);
}
function typeMatch(docFieldValue, userValue) {
  switch (userValue) {
    case "null":
      return docFieldValue === null;
    case "boolean":
      return typeof docFieldValue === "boolean";
    case "number":
      return typeof docFieldValue === "number";
    case "string":
      return typeof docFieldValue === "string";
    case "array":
      return docFieldValue instanceof Array;
    case "object":
      return {}.toString.call(docFieldValue) === "[object Object]";
  }
}
var matchers = {
  "$elemMatch": function(doc, userValue, parsedField, docFieldValue) {
    if (!Array.isArray(docFieldValue)) {
      return false;
    }
    if (docFieldValue.length === 0) {
      return false;
    }
    if (typeof docFieldValue[0] === "object" && docFieldValue[0] !== null) {
      return docFieldValue.some(function(val) {
        return rowFilter(val, userValue, Object.keys(userValue));
      });
    }
    return docFieldValue.some(function(val) {
      return matchSelector(userValue, doc, parsedField, val);
    });
  },
  "$allMatch": function(doc, userValue, parsedField, docFieldValue) {
    if (!Array.isArray(docFieldValue)) {
      return false;
    }
    if (docFieldValue.length === 0) {
      return false;
    }
    if (typeof docFieldValue[0] === "object" && docFieldValue[0] !== null) {
      return docFieldValue.every(function(val) {
        return rowFilter(val, userValue, Object.keys(userValue));
      });
    }
    return docFieldValue.every(function(val) {
      return matchSelector(userValue, doc, parsedField, val);
    });
  },
  "$eq": function(doc, userValue, parsedField, docFieldValue) {
    return fieldIsNotUndefined(docFieldValue) && collate(docFieldValue, userValue) === 0;
  },
  "$gte": function(doc, userValue, parsedField, docFieldValue) {
    return fieldIsNotUndefined(docFieldValue) && collate(docFieldValue, userValue) >= 0;
  },
  "$gt": function(doc, userValue, parsedField, docFieldValue) {
    return fieldIsNotUndefined(docFieldValue) && collate(docFieldValue, userValue) > 0;
  },
  "$lte": function(doc, userValue, parsedField, docFieldValue) {
    return fieldIsNotUndefined(docFieldValue) && collate(docFieldValue, userValue) <= 0;
  },
  "$lt": function(doc, userValue, parsedField, docFieldValue) {
    return fieldIsNotUndefined(docFieldValue) && collate(docFieldValue, userValue) < 0;
  },
  "$exists": function(doc, userValue, parsedField, docFieldValue) {
    if (userValue) {
      return fieldIsNotUndefined(docFieldValue);
    }
    return !fieldIsNotUndefined(docFieldValue);
  },
  "$mod": function(doc, userValue, parsedField, docFieldValue) {
    return fieldExists(docFieldValue) && modField(docFieldValue, userValue);
  },
  "$ne": function(doc, userValue, parsedField, docFieldValue) {
    return userValue.every(function(neValue) {
      return collate(docFieldValue, neValue) !== 0;
    });
  },
  "$in": function(doc, userValue, parsedField, docFieldValue) {
    return fieldExists(docFieldValue) && arrayContainsValue(docFieldValue, userValue);
  },
  "$nin": function(doc, userValue, parsedField, docFieldValue) {
    return fieldExists(docFieldValue) && !arrayContainsValue(docFieldValue, userValue);
  },
  "$size": function(doc, userValue, parsedField, docFieldValue) {
    return fieldExists(docFieldValue) && Array.isArray(docFieldValue) && arraySize(docFieldValue, userValue);
  },
  "$all": function(doc, userValue, parsedField, docFieldValue) {
    return Array.isArray(docFieldValue) && arrayContainsAllValues(docFieldValue, userValue);
  },
  "$regex": function(doc, userValue, parsedField, docFieldValue) {
    return fieldExists(docFieldValue) && typeof docFieldValue == "string" && userValue.every(function(regexValue) {
      return regexMatch(docFieldValue, regexValue);
    });
  },
  "$type": function(doc, userValue, parsedField, docFieldValue) {
    return typeMatch(docFieldValue, userValue);
  }
};
function matchesSelector(doc, selector) {
  if (typeof selector !== "object") {
    throw new Error("Selector error: expected a JSON object");
  }
  selector = massageSelector(selector);
  var row = {
    doc
  };
  var rowsMatched = filterInMemoryFields([row], { selector }, Object.keys(selector));
  return rowsMatched && rowsMatched.length === 1;
}
function evalFilter(input) {
  return scopeEval('"use strict";\nreturn ' + input + ";", {});
}
function evalView(input) {
  var code = [
    "return function(doc) {",
    '  "use strict";',
    "  var emitted = false;",
    "  var emit = function (a, b) {",
    "    emitted = true;",
    "  };",
    "  var view = " + input + ";",
    "  view(doc);",
    "  if (emitted) {",
    "    return true;",
    "  }",
    "};"
  ].join("\n");
  return scopeEval(code, {});
}
function validate2(opts, callback) {
  if (opts.selector) {
    if (opts.filter && opts.filter !== "_selector") {
      var filterName = typeof opts.filter === "string" ? opts.filter : "function";
      return callback(new Error('selector invalid for filter "' + filterName + '"'));
    }
  }
  callback();
}
function normalize(opts) {
  if (opts.view && !opts.filter) {
    opts.filter = "_view";
  }
  if (opts.selector && !opts.filter) {
    opts.filter = "_selector";
  }
  if (opts.filter && typeof opts.filter === "string") {
    if (opts.filter === "_view") {
      opts.view = normalizeDesignDocFunctionName(opts.view);
    } else {
      opts.filter = normalizeDesignDocFunctionName(opts.filter);
    }
  }
}
function shouldFilter(changesHandler2, opts) {
  return opts.filter && typeof opts.filter === "string" && !opts.doc_ids && !isRemote(changesHandler2.db);
}
function filter(changesHandler2, opts) {
  var callback = opts.complete;
  if (opts.filter === "_view") {
    if (!opts.view || typeof opts.view !== "string") {
      var err = createError(
        BAD_REQUEST,
        "`view` filter parameter not found or invalid."
      );
      return callback(err);
    }
    var viewName = parseDesignDocFunctionName(opts.view);
    changesHandler2.db.get("_design/" + viewName[0], function(err2, ddoc) {
      if (changesHandler2.isCancelled) {
        return callback(null, { status: "cancelled" });
      }
      if (err2) {
        return callback(generateErrorFromResponse(err2));
      }
      var mapFun = ddoc && ddoc.views && ddoc.views[viewName[1]] && ddoc.views[viewName[1]].map;
      if (!mapFun) {
        return callback(createError(
          MISSING_DOC,
          ddoc.views ? "missing json key: " + viewName[1] : "missing json key: views"
        ));
      }
      opts.filter = evalView(mapFun);
      changesHandler2.doChanges(opts);
    });
  } else if (opts.selector) {
    opts.filter = function(doc) {
      return matchesSelector(doc, opts.selector);
    };
    changesHandler2.doChanges(opts);
  } else {
    var filterName = parseDesignDocFunctionName(opts.filter);
    changesHandler2.db.get("_design/" + filterName[0], function(err2, ddoc) {
      if (changesHandler2.isCancelled) {
        return callback(null, { status: "cancelled" });
      }
      if (err2) {
        return callback(generateErrorFromResponse(err2));
      }
      var filterFun = ddoc && ddoc.filters && ddoc.filters[filterName[1]];
      if (!filterFun) {
        return callback(createError(
          MISSING_DOC,
          ddoc && ddoc.filters ? "missing json key: " + filterName[1] : "missing json key: filters"
        ));
      }
      opts.filter = evalFilter(filterFun);
      changesHandler2.doChanges(opts);
    });
  }
}
function applyChangesFilterPlugin(PouchDB2) {
  PouchDB2._changesFilterPlugin = {
    validate: validate2,
    normalize,
    shouldFilter,
    filter
  };
}
PouchDB.plugin(applyChangesFilterPlugin);
PouchDB.version = version;
function checkBlobSupport(txn, store, docIdOrCreateDoc) {
  return new Promise(function(resolve) {
    var blob$$1 = createBlob([""]);
    let req;
    if (typeof docIdOrCreateDoc === "function") {
      const createDoc = docIdOrCreateDoc;
      const doc = createDoc(blob$$1);
      req = txn.objectStore(store).put(doc);
    } else {
      const docId = docIdOrCreateDoc;
      req = txn.objectStore(store).put(blob$$1, docId);
    }
    req.onsuccess = function() {
      var matchedChrome = navigator.userAgent.match(/Chrome\/(\d+)/);
      var matchedEdge = navigator.userAgent.match(/Edge\//);
      resolve(matchedEdge || !matchedChrome || parseInt(matchedChrome[1], 10) >= 43);
    };
    req.onerror = txn.onabort = function(e) {
      e.preventDefault();
      e.stopPropagation();
      resolve(false);
    };
  }).catch(function() {
    return false;
  });
}
function toObject(array) {
  return array.reduce(function(obj, item) {
    obj[item] = true;
    return obj;
  }, {});
}
var reservedWords = toObject([
  "_id",
  "_rev",
  "_access",
  "_attachments",
  "_deleted",
  "_revisions",
  "_revs_info",
  "_conflicts",
  "_deleted_conflicts",
  "_local_seq",
  "_rev_tree",
  // replication documents
  "_replication_id",
  "_replication_state",
  "_replication_state_time",
  "_replication_state_reason",
  "_replication_stats",
  // Specific to Couchbase Sync Gateway
  "_removed"
]);
var dataWords = toObject([
  "_access",
  "_attachments",
  // replication documents
  "_replication_id",
  "_replication_state",
  "_replication_state_time",
  "_replication_state_reason",
  "_replication_stats"
]);
function parseRevisionInfo(rev$$1) {
  if (!/^\d+-/.test(rev$$1)) {
    return createError(INVALID_REV);
  }
  var idx = rev$$1.indexOf("-");
  var left = rev$$1.substring(0, idx);
  var right = rev$$1.substring(idx + 1);
  return {
    prefix: parseInt(left, 10),
    id: right
  };
}
function makeRevTreeFromRevisions(revisions, opts) {
  var pos = revisions.start - revisions.ids.length + 1;
  var revisionIds = revisions.ids;
  var ids = [revisionIds[0], opts, []];
  for (var i = 1, len = revisionIds.length; i < len; i++) {
    ids = [revisionIds[i], { status: "missing" }, [ids]];
  }
  return [{
    pos,
    ids
  }];
}
function parseDoc(doc, newEdits, dbOpts) {
  if (!dbOpts) {
    dbOpts = {
      deterministic_revs: true
    };
  }
  var nRevNum;
  var newRevId;
  var revInfo;
  var opts = { status: "available" };
  if (doc._deleted) {
    opts.deleted = true;
  }
  if (newEdits) {
    if (!doc._id) {
      doc._id = uuid();
    }
    newRevId = rev(doc, dbOpts.deterministic_revs);
    if (doc._rev) {
      revInfo = parseRevisionInfo(doc._rev);
      if (revInfo.error) {
        return revInfo;
      }
      doc._rev_tree = [{
        pos: revInfo.prefix,
        ids: [revInfo.id, { status: "missing" }, [[newRevId, opts, []]]]
      }];
      nRevNum = revInfo.prefix + 1;
    } else {
      doc._rev_tree = [{
        pos: 1,
        ids: [newRevId, opts, []]
      }];
      nRevNum = 1;
    }
  } else {
    if (doc._revisions) {
      doc._rev_tree = makeRevTreeFromRevisions(doc._revisions, opts);
      nRevNum = doc._revisions.start;
      newRevId = doc._revisions.ids[0];
    }
    if (!doc._rev_tree) {
      revInfo = parseRevisionInfo(doc._rev);
      if (revInfo.error) {
        return revInfo;
      }
      nRevNum = revInfo.prefix;
      newRevId = revInfo.id;
      doc._rev_tree = [{
        pos: nRevNum,
        ids: [newRevId, opts, []]
      }];
    }
  }
  invalidIdError(doc._id);
  doc._rev = nRevNum + "-" + newRevId;
  var result = { metadata: {}, data: {} };
  for (var key in doc) {
    if (Object.prototype.hasOwnProperty.call(doc, key)) {
      var specialKey = key[0] === "_";
      if (specialKey && !reservedWords[key]) {
        var error = createError(DOC_VALIDATION, key);
        error.message = DOC_VALIDATION.message + ": " + key;
        throw error;
      } else if (specialKey && !dataWords[key]) {
        result.metadata[key.slice(1)] = doc[key];
      } else {
        result.data[key] = doc[key];
      }
    }
  }
  return result;
}
function parseBase64(data) {
  try {
    return thisAtob(data);
  } catch (e) {
    var err = createError(
      BAD_ARG,
      "Attachment is not a valid base64 string"
    );
    return { error: err };
  }
}
function preprocessString(att, blobType, callback) {
  var asBinary = parseBase64(att.data);
  if (asBinary.error) {
    return callback(asBinary.error);
  }
  att.length = asBinary.length;
  if (blobType === "blob") {
    att.data = binStringToBluffer(asBinary, att.content_type);
  } else if (blobType === "base64") {
    att.data = thisBtoa(asBinary);
  } else {
    att.data = asBinary;
  }
  binaryMd5(asBinary, function(result) {
    att.digest = "md5-" + result;
    callback();
  });
}
function preprocessBlob(att, blobType, callback) {
  binaryMd5(att.data, function(md52) {
    att.digest = "md5-" + md52;
    att.length = att.data.size || att.data.length || 0;
    if (blobType === "binary") {
      blobToBinaryString(att.data, function(binString) {
        att.data = binString;
        callback();
      });
    } else if (blobType === "base64") {
      blobToBase64(att.data, function(b64) {
        att.data = b64;
        callback();
      });
    } else {
      callback();
    }
  });
}
function preprocessAttachment(att, blobType, callback) {
  if (att.stub) {
    return callback();
  }
  if (typeof att.data === "string") {
    preprocessString(att, blobType, callback);
  } else {
    preprocessBlob(att, blobType, callback);
  }
}
function preprocessAttachments(docInfos, blobType, callback) {
  if (!docInfos.length) {
    return callback();
  }
  var docv = 0;
  var overallErr;
  docInfos.forEach(function(docInfo) {
    var attachments = docInfo.data && docInfo.data._attachments ? Object.keys(docInfo.data._attachments) : [];
    var recv = 0;
    if (!attachments.length) {
      return done();
    }
    function processedAttachment(err) {
      overallErr = err;
      recv++;
      if (recv === attachments.length) {
        done();
      }
    }
    for (var key in docInfo.data._attachments) {
      if (Object.prototype.hasOwnProperty.call(docInfo.data._attachments, key)) {
        preprocessAttachment(
          docInfo.data._attachments[key],
          blobType,
          processedAttachment
        );
      }
    }
  });
  function done() {
    docv++;
    if (docInfos.length === docv) {
      if (overallErr) {
        callback(overallErr);
      } else {
        callback();
      }
    }
  }
}
function updateDoc(revLimit, prev, docInfo, results, i, cb, writeDoc, newEdits) {
  if (revExists(prev.rev_tree, docInfo.metadata.rev) && !newEdits) {
    results[i] = docInfo;
    return cb();
  }
  var previousWinningRev = prev.winningRev || winningRev(prev);
  var previouslyDeleted = "deleted" in prev ? prev.deleted : isDeleted(prev, previousWinningRev);
  var deleted = "deleted" in docInfo.metadata ? docInfo.metadata.deleted : isDeleted(docInfo.metadata);
  var isRoot = /^1-/.test(docInfo.metadata.rev);
  if (previouslyDeleted && !deleted && newEdits && isRoot) {
    var newDoc = docInfo.data;
    newDoc._rev = previousWinningRev;
    newDoc._id = docInfo.metadata.id;
    docInfo = parseDoc(newDoc, newEdits);
  }
  var merged = merge(prev.rev_tree, docInfo.metadata.rev_tree[0], revLimit);
  var inConflict = newEdits && (previouslyDeleted && deleted && merged.conflicts !== "new_leaf" || !previouslyDeleted && merged.conflicts !== "new_leaf" || previouslyDeleted && !deleted && merged.conflicts === "new_branch");
  if (inConflict) {
    var err = createError(REV_CONFLICT);
    results[i] = err;
    return cb();
  }
  var newRev = docInfo.metadata.rev;
  docInfo.metadata.rev_tree = merged.tree;
  docInfo.stemmedRevs = merged.stemmedRevs || [];
  if (prev.rev_map) {
    docInfo.metadata.rev_map = prev.rev_map;
  }
  var winningRev$$1 = winningRev(docInfo.metadata);
  var winningRevIsDeleted = isDeleted(docInfo.metadata, winningRev$$1);
  var delta = previouslyDeleted === winningRevIsDeleted ? 0 : previouslyDeleted < winningRevIsDeleted ? -1 : 1;
  var newRevIsDeleted;
  if (newRev === winningRev$$1) {
    newRevIsDeleted = winningRevIsDeleted;
  } else {
    newRevIsDeleted = isDeleted(docInfo.metadata, newRev);
  }
  writeDoc(
    docInfo,
    winningRev$$1,
    winningRevIsDeleted,
    newRevIsDeleted,
    true,
    delta,
    i,
    cb
  );
}
function rootIsMissing(docInfo) {
  return docInfo.metadata.rev_tree[0].ids[1].status === "missing";
}
function processDocs(revLimit, docInfos, api, fetchedDocs, tx, results, writeDoc, opts, overallCallback) {
  revLimit = revLimit || 1e3;
  function insertDoc(docInfo, resultsIdx, callback) {
    var winningRev$$1 = winningRev(docInfo.metadata);
    var deleted = isDeleted(docInfo.metadata, winningRev$$1);
    if ("was_delete" in opts && deleted) {
      results[resultsIdx] = createError(MISSING_DOC, "deleted");
      return callback();
    }
    var inConflict = newEdits && rootIsMissing(docInfo);
    if (inConflict) {
      var err = createError(REV_CONFLICT);
      results[resultsIdx] = err;
      return callback();
    }
    var delta = deleted ? 0 : 1;
    writeDoc(
      docInfo,
      winningRev$$1,
      deleted,
      deleted,
      false,
      delta,
      resultsIdx,
      callback
    );
  }
  var newEdits = opts.new_edits;
  var idsToDocs = /* @__PURE__ */ new Map();
  var docsDone = 0;
  var docsToDo = docInfos.length;
  function checkAllDocsDone() {
    if (++docsDone === docsToDo && overallCallback) {
      overallCallback();
    }
  }
  docInfos.forEach(function(currentDoc, resultsIdx) {
    if (currentDoc._id && isLocalId(currentDoc._id)) {
      var fun = currentDoc._deleted ? "_removeLocal" : "_putLocal";
      api[fun](currentDoc, { ctx: tx }, function(err, res) {
        results[resultsIdx] = err || res;
        checkAllDocsDone();
      });
      return;
    }
    var id = currentDoc.metadata.id;
    if (idsToDocs.has(id)) {
      docsToDo--;
      idsToDocs.get(id).push([currentDoc, resultsIdx]);
    } else {
      idsToDocs.set(id, [[currentDoc, resultsIdx]]);
    }
  });
  idsToDocs.forEach(function(docs, id) {
    var numDone = 0;
    function docWritten() {
      if (++numDone < docs.length) {
        nextDoc();
      } else {
        checkAllDocsDone();
      }
    }
    function nextDoc() {
      var value = docs[numDone];
      var currentDoc = value[0];
      var resultsIdx = value[1];
      if (fetchedDocs.has(id)) {
        updateDoc(
          revLimit,
          fetchedDocs.get(id),
          currentDoc,
          results,
          resultsIdx,
          docWritten,
          writeDoc,
          newEdits
        );
      } else {
        var merged = merge([], currentDoc.metadata.rev_tree[0], revLimit);
        currentDoc.metadata.rev_tree = merged.tree;
        currentDoc.stemmedRevs = merged.stemmedRevs || [];
        insertDoc(currentDoc, resultsIdx, docWritten);
      }
    }
    nextDoc();
  });
}
var ADAPTER_VERSION = 5;
var DOC_STORE = "document-store";
var BY_SEQ_STORE = "by-sequence";
var ATTACH_STORE = "attach-store";
var ATTACH_AND_SEQ_STORE = "attach-seq-store";
var META_STORE = "meta-store";
var LOCAL_STORE = "local-store";
var DETECT_BLOB_SUPPORT_STORE = "detect-blob-support";
function safeJsonParse(str) {
  try {
    return JSON.parse(str);
  } catch (e) {
    return import_vuvuzela.default.parse(str);
  }
}
function safeJsonStringify(json) {
  try {
    return JSON.stringify(json);
  } catch (e) {
    return import_vuvuzela.default.stringify(json);
  }
}
function idbError(callback) {
  return function(evt) {
    var message = "unknown_error";
    if (evt.target && evt.target.error) {
      message = evt.target.error.name || evt.target.error.message;
    }
    callback(createError(IDB_ERROR, message, evt.type));
  };
}
function encodeMetadata(metadata, winningRev2, deleted) {
  return {
    data: safeJsonStringify(metadata),
    winningRev: winningRev2,
    deletedOrLocal: deleted ? "1" : "0",
    seq: metadata.seq,
    // highest seq for this doc
    id: metadata.id
  };
}
function decodeMetadata(storedObject) {
  if (!storedObject) {
    return null;
  }
  var metadata = safeJsonParse(storedObject.data);
  metadata.winningRev = storedObject.winningRev;
  metadata.deleted = storedObject.deletedOrLocal === "1";
  metadata.seq = storedObject.seq;
  return metadata;
}
function decodeDoc(doc) {
  if (!doc) {
    return doc;
  }
  var idx = doc._doc_id_rev.lastIndexOf(":");
  doc._id = doc._doc_id_rev.substring(0, idx - 1);
  doc._rev = doc._doc_id_rev.substring(idx + 1);
  delete doc._doc_id_rev;
  return doc;
}
function readBlobData(body, type, asBlob, callback) {
  if (asBlob) {
    if (!body) {
      callback(createBlob([""], { type }));
    } else if (typeof body !== "string") {
      callback(body);
    } else {
      callback(b64ToBluffer(body, type));
    }
  } else {
    if (!body) {
      callback("");
    } else if (typeof body !== "string") {
      readAsBinaryString(body, function(binary) {
        callback(thisBtoa(binary));
      });
    } else {
      callback(body);
    }
  }
}
function fetchAttachmentsIfNecessary(doc, opts, txn, cb) {
  var attachments = Object.keys(doc._attachments || {});
  if (!attachments.length) {
    return cb && cb();
  }
  var numDone = 0;
  function checkDone() {
    if (++numDone === attachments.length && cb) {
      cb();
    }
  }
  function fetchAttachment(doc2, att) {
    var attObj = doc2._attachments[att];
    var digest = attObj.digest;
    var req = txn.objectStore(ATTACH_STORE).get(digest);
    req.onsuccess = function(e) {
      attObj.body = e.target.result.body;
      checkDone();
    };
  }
  attachments.forEach(function(att) {
    if (opts.attachments && opts.include_docs) {
      fetchAttachment(doc, att);
    } else {
      doc._attachments[att].stub = true;
      checkDone();
    }
  });
}
function postProcessAttachments(results, asBlob) {
  return Promise.all(results.map(function(row) {
    if (row.doc && row.doc._attachments) {
      var attNames = Object.keys(row.doc._attachments);
      return Promise.all(attNames.map(function(att) {
        var attObj = row.doc._attachments[att];
        if (!("body" in attObj)) {
          return;
        }
        var body = attObj.body;
        var type = attObj.content_type;
        return new Promise(function(resolve) {
          readBlobData(body, type, asBlob, function(data) {
            row.doc._attachments[att] = Object.assign(
              pick(attObj, ["digest", "content_type"]),
              { data }
            );
            resolve();
          });
        });
      }));
    }
  }));
}
function compactRevs(revs, docId, txn) {
  var possiblyOrphanedDigests = [];
  var seqStore = txn.objectStore(BY_SEQ_STORE);
  var attStore = txn.objectStore(ATTACH_STORE);
  var attAndSeqStore = txn.objectStore(ATTACH_AND_SEQ_STORE);
  var count = revs.length;
  function checkDone() {
    count--;
    if (!count) {
      deleteOrphanedAttachments();
    }
  }
  function deleteOrphanedAttachments() {
    if (!possiblyOrphanedDigests.length) {
      return;
    }
    possiblyOrphanedDigests.forEach(function(digest) {
      var countReq = attAndSeqStore.index("digestSeq").count(
        IDBKeyRange.bound(
          digest + "::",
          digest + "::￿",
          false,
          false
        )
      );
      countReq.onsuccess = function(e) {
        var count2 = e.target.result;
        if (!count2) {
          attStore.delete(digest);
        }
      };
    });
  }
  revs.forEach(function(rev$$1) {
    var index = seqStore.index("_doc_id_rev");
    var key = docId + "::" + rev$$1;
    index.getKey(key).onsuccess = function(e) {
      var seq = e.target.result;
      if (typeof seq !== "number") {
        return checkDone();
      }
      seqStore.delete(seq);
      var cursor = attAndSeqStore.index("seq").openCursor(IDBKeyRange.only(seq));
      cursor.onsuccess = function(event) {
        var cursor2 = event.target.result;
        if (cursor2) {
          var digest = cursor2.value.digestSeq.split("::")[0];
          possiblyOrphanedDigests.push(digest);
          attAndSeqStore.delete(cursor2.primaryKey);
          cursor2.continue();
        } else {
          checkDone();
        }
      };
    };
  });
}
function openTransactionSafely(idb, stores, mode) {
  try {
    return {
      txn: idb.transaction(stores, mode)
    };
  } catch (err) {
    return {
      error: err
    };
  }
}
var changesHandler = new Changes();
function idbBulkDocs(dbOpts, req, opts, api, idb, callback) {
  var docInfos = req.docs;
  var txn;
  var docStore;
  var bySeqStore;
  var attachStore;
  var attachAndSeqStore;
  var metaStore;
  var docInfoError;
  var metaDoc;
  for (var i = 0, len = docInfos.length; i < len; i++) {
    var doc = docInfos[i];
    if (doc._id && isLocalId(doc._id)) {
      continue;
    }
    doc = docInfos[i] = parseDoc(doc, opts.new_edits, dbOpts);
    if (doc.error && !docInfoError) {
      docInfoError = doc;
    }
  }
  if (docInfoError) {
    return callback(docInfoError);
  }
  var allDocsProcessed = false;
  var docCountDelta = 0;
  var results = new Array(docInfos.length);
  var fetchedDocs = /* @__PURE__ */ new Map();
  var preconditionErrored = false;
  var blobType = api._meta.blobSupport ? "blob" : "base64";
  preprocessAttachments(docInfos, blobType, function(err) {
    if (err) {
      return callback(err);
    }
    startTransaction();
  });
  function startTransaction() {
    var stores = [
      DOC_STORE,
      BY_SEQ_STORE,
      ATTACH_STORE,
      LOCAL_STORE,
      ATTACH_AND_SEQ_STORE,
      META_STORE
    ];
    var txnResult = openTransactionSafely(idb, stores, "readwrite");
    if (txnResult.error) {
      return callback(txnResult.error);
    }
    txn = txnResult.txn;
    txn.onabort = idbError(callback);
    txn.ontimeout = idbError(callback);
    txn.oncomplete = complete;
    docStore = txn.objectStore(DOC_STORE);
    bySeqStore = txn.objectStore(BY_SEQ_STORE);
    attachStore = txn.objectStore(ATTACH_STORE);
    attachAndSeqStore = txn.objectStore(ATTACH_AND_SEQ_STORE);
    metaStore = txn.objectStore(META_STORE);
    metaStore.get(META_STORE).onsuccess = function(e) {
      metaDoc = e.target.result;
      updateDocCountIfReady();
    };
    verifyAttachments(function(err) {
      if (err) {
        preconditionErrored = true;
        return callback(err);
      }
      fetchExistingDocs();
    });
  }
  function onAllDocsProcessed() {
    allDocsProcessed = true;
    updateDocCountIfReady();
  }
  function idbProcessDocs() {
    processDocs(
      dbOpts.revs_limit,
      docInfos,
      api,
      fetchedDocs,
      txn,
      results,
      writeDoc,
      opts,
      onAllDocsProcessed
    );
  }
  function updateDocCountIfReady() {
    if (!metaDoc || !allDocsProcessed) {
      return;
    }
    metaDoc.docCount += docCountDelta;
    metaStore.put(metaDoc);
  }
  function fetchExistingDocs() {
    if (!docInfos.length) {
      return;
    }
    var numFetched = 0;
    function checkDone() {
      if (++numFetched === docInfos.length) {
        idbProcessDocs();
      }
    }
    function readMetadata(event) {
      var metadata = decodeMetadata(event.target.result);
      if (metadata) {
        fetchedDocs.set(metadata.id, metadata);
      }
      checkDone();
    }
    for (var i2 = 0, len2 = docInfos.length; i2 < len2; i2++) {
      var docInfo = docInfos[i2];
      if (docInfo._id && isLocalId(docInfo._id)) {
        checkDone();
        continue;
      }
      var req2 = docStore.get(docInfo.metadata.id);
      req2.onsuccess = readMetadata;
    }
  }
  function complete() {
    if (preconditionErrored) {
      return;
    }
    changesHandler.notify(api._meta.name);
    callback(null, results);
  }
  function verifyAttachment(digest, callback2) {
    var req2 = attachStore.get(digest);
    req2.onsuccess = function(e) {
      if (!e.target.result) {
        var err = createError(
          MISSING_STUB,
          "unknown stub attachment with digest " + digest
        );
        err.status = 412;
        callback2(err);
      } else {
        callback2();
      }
    };
  }
  function verifyAttachments(finish) {
    var digests = [];
    docInfos.forEach(function(docInfo) {
      if (docInfo.data && docInfo.data._attachments) {
        Object.keys(docInfo.data._attachments).forEach(function(filename) {
          var att = docInfo.data._attachments[filename];
          if (att.stub) {
            digests.push(att.digest);
          }
        });
      }
    });
    if (!digests.length) {
      return finish();
    }
    var numDone = 0;
    var err;
    function checkDone() {
      if (++numDone === digests.length) {
        finish(err);
      }
    }
    digests.forEach(function(digest) {
      verifyAttachment(digest, function(attErr) {
        if (attErr && !err) {
          err = attErr;
        }
        checkDone();
      });
    });
  }
  function writeDoc(docInfo, winningRev$$1, winningRevIsDeleted, newRevIsDeleted, isUpdate, delta, resultsIdx, callback2) {
    docInfo.metadata.winningRev = winningRev$$1;
    docInfo.metadata.deleted = winningRevIsDeleted;
    var doc2 = docInfo.data;
    doc2._id = docInfo.metadata.id;
    doc2._rev = docInfo.metadata.rev;
    if (newRevIsDeleted) {
      doc2._deleted = true;
    }
    var hasAttachments = doc2._attachments && Object.keys(doc2._attachments).length;
    if (hasAttachments) {
      return writeAttachments(
        docInfo,
        winningRev$$1,
        winningRevIsDeleted,
        isUpdate,
        resultsIdx,
        callback2
      );
    }
    docCountDelta += delta;
    updateDocCountIfReady();
    finishDoc(
      docInfo,
      winningRev$$1,
      winningRevIsDeleted,
      isUpdate,
      resultsIdx,
      callback2
    );
  }
  function finishDoc(docInfo, winningRev$$1, winningRevIsDeleted, isUpdate, resultsIdx, callback2) {
    var doc2 = docInfo.data;
    var metadata = docInfo.metadata;
    doc2._doc_id_rev = metadata.id + "::" + metadata.rev;
    delete doc2._id;
    delete doc2._rev;
    function afterPutDoc(e) {
      var revsToDelete = docInfo.stemmedRevs || [];
      if (isUpdate && api.auto_compaction) {
        revsToDelete = revsToDelete.concat(compactTree(docInfo.metadata));
      }
      if (revsToDelete && revsToDelete.length) {
        compactRevs(revsToDelete, docInfo.metadata.id, txn);
      }
      metadata.seq = e.target.result;
      var metadataToStore = encodeMetadata(
        metadata,
        winningRev$$1,
        winningRevIsDeleted
      );
      var metaDataReq = docStore.put(metadataToStore);
      metaDataReq.onsuccess = afterPutMetadata;
    }
    function afterPutDocError(e) {
      e.preventDefault();
      e.stopPropagation();
      var index = bySeqStore.index("_doc_id_rev");
      var getKeyReq = index.getKey(doc2._doc_id_rev);
      getKeyReq.onsuccess = function(e2) {
        var putReq2 = bySeqStore.put(doc2, e2.target.result);
        putReq2.onsuccess = afterPutDoc;
      };
    }
    function afterPutMetadata() {
      results[resultsIdx] = {
        ok: true,
        id: metadata.id,
        rev: metadata.rev
      };
      fetchedDocs.set(docInfo.metadata.id, docInfo.metadata);
      insertAttachmentMappings(docInfo, metadata.seq, callback2);
    }
    var putReq = bySeqStore.put(doc2);
    putReq.onsuccess = afterPutDoc;
    putReq.onerror = afterPutDocError;
  }
  function writeAttachments(docInfo, winningRev$$1, winningRevIsDeleted, isUpdate, resultsIdx, callback2) {
    var doc2 = docInfo.data;
    var numDone = 0;
    var attachments = Object.keys(doc2._attachments);
    function collectResults() {
      if (numDone === attachments.length) {
        finishDoc(
          docInfo,
          winningRev$$1,
          winningRevIsDeleted,
          isUpdate,
          resultsIdx,
          callback2
        );
      }
    }
    function attachmentSaved() {
      numDone++;
      collectResults();
    }
    attachments.forEach(function(key) {
      var att = docInfo.data._attachments[key];
      if (!att.stub) {
        var data = att.data;
        delete att.data;
        att.revpos = parseInt(winningRev$$1, 10);
        var digest = att.digest;
        saveAttachment(digest, data, attachmentSaved);
      } else {
        numDone++;
        collectResults();
      }
    });
  }
  function insertAttachmentMappings(docInfo, seq, callback2) {
    var attsAdded = 0;
    var attsToAdd = Object.keys(docInfo.data._attachments || {});
    if (!attsToAdd.length) {
      return callback2();
    }
    function checkDone() {
      if (++attsAdded === attsToAdd.length) {
        callback2();
      }
    }
    function add(att) {
      var digest = docInfo.data._attachments[att].digest;
      var req2 = attachAndSeqStore.put({
        seq,
        digestSeq: digest + "::" + seq
      });
      req2.onsuccess = checkDone;
      req2.onerror = function(e) {
        e.preventDefault();
        e.stopPropagation();
        checkDone();
      };
    }
    for (var i2 = 0; i2 < attsToAdd.length; i2++) {
      add(attsToAdd[i2]);
    }
  }
  function saveAttachment(digest, data, callback2) {
    var getKeyReq = attachStore.count(digest);
    getKeyReq.onsuccess = function(e) {
      var count = e.target.result;
      if (count) {
        return callback2();
      }
      var newAtt = {
        digest,
        body: data
      };
      var putReq = attachStore.put(newAtt);
      putReq.onsuccess = callback2;
    };
  }
}
function runBatchedCursor(objectStore, keyRange, descending, batchSize, onBatch) {
  if (batchSize === -1) {
    batchSize = 1e3;
  }
  var useGetAll = typeof objectStore.getAll === "function" && typeof objectStore.getAllKeys === "function" && batchSize > 1 && !descending;
  var keysBatch;
  var valuesBatch;
  var pseudoCursor;
  function onGetAll(e) {
    valuesBatch = e.target.result;
    if (keysBatch) {
      onBatch(keysBatch, valuesBatch, pseudoCursor);
    }
  }
  function onGetAllKeys(e) {
    keysBatch = e.target.result;
    if (valuesBatch) {
      onBatch(keysBatch, valuesBatch, pseudoCursor);
    }
  }
  function continuePseudoCursor() {
    if (!keysBatch.length) {
      return onBatch();
    }
    var lastKey = keysBatch[keysBatch.length - 1];
    var newKeyRange;
    if (keyRange && keyRange.upper) {
      try {
        newKeyRange = IDBKeyRange.bound(
          lastKey,
          keyRange.upper,
          true,
          keyRange.upperOpen
        );
      } catch (e) {
        if (e.name === "DataError" && e.code === 0) {
          return onBatch();
        }
      }
    } else {
      newKeyRange = IDBKeyRange.lowerBound(lastKey, true);
    }
    keyRange = newKeyRange;
    keysBatch = null;
    valuesBatch = null;
    objectStore.getAll(keyRange, batchSize).onsuccess = onGetAll;
    objectStore.getAllKeys(keyRange, batchSize).onsuccess = onGetAllKeys;
  }
  function onCursor(e) {
    var cursor = e.target.result;
    if (!cursor) {
      return onBatch();
    }
    onBatch([cursor.key], [cursor.value], cursor);
  }
  if (useGetAll) {
    pseudoCursor = { "continue": continuePseudoCursor };
    objectStore.getAll(keyRange, batchSize).onsuccess = onGetAll;
    objectStore.getAllKeys(keyRange, batchSize).onsuccess = onGetAllKeys;
  } else if (descending) {
    objectStore.openCursor(keyRange, "prev").onsuccess = onCursor;
  } else {
    objectStore.openCursor(keyRange).onsuccess = onCursor;
  }
}
function getAll(objectStore, keyRange, onSuccess) {
  if (typeof objectStore.getAll === "function") {
    objectStore.getAll(keyRange).onsuccess = onSuccess;
    return;
  }
  var values = [];
  function onCursor(e) {
    var cursor = e.target.result;
    if (cursor) {
      values.push(cursor.value);
      cursor.continue();
    } else {
      onSuccess({
        target: {
          result: values
        }
      });
    }
  }
  objectStore.openCursor(keyRange).onsuccess = onCursor;
}
function allDocsKeys(keys2, docStore, onBatch) {
  var valuesBatch = new Array(keys2.length);
  var count = 0;
  keys2.forEach(function(key, index) {
    docStore.get(key).onsuccess = function(event) {
      if (event.target.result) {
        valuesBatch[index] = event.target.result;
      } else {
        valuesBatch[index] = { key, error: "not_found" };
      }
      count++;
      if (count === keys2.length) {
        onBatch(keys2, valuesBatch, {});
      }
    };
  });
}
function createKeyRange(start, end, inclusiveEnd, key, descending) {
  try {
    if (start && end) {
      if (descending) {
        return IDBKeyRange.bound(end, start, !inclusiveEnd, false);
      } else {
        return IDBKeyRange.bound(start, end, false, !inclusiveEnd);
      }
    } else if (start) {
      if (descending) {
        return IDBKeyRange.upperBound(start);
      } else {
        return IDBKeyRange.lowerBound(start);
      }
    } else if (end) {
      if (descending) {
        return IDBKeyRange.lowerBound(end, !inclusiveEnd);
      } else {
        return IDBKeyRange.upperBound(end, !inclusiveEnd);
      }
    } else if (key) {
      return IDBKeyRange.only(key);
    }
  } catch (e) {
    return { error: e };
  }
  return null;
}
function idbAllDocs(opts, idb, callback) {
  var start = "startkey" in opts ? opts.startkey : false;
  var end = "endkey" in opts ? opts.endkey : false;
  var key = "key" in opts ? opts.key : false;
  var keys2 = "keys" in opts ? opts.keys : false;
  var skip = opts.skip || 0;
  var limit = typeof opts.limit === "number" ? opts.limit : -1;
  var inclusiveEnd = opts.inclusive_end !== false;
  var keyRange;
  var keyRangeError;
  if (!keys2) {
    keyRange = createKeyRange(start, end, inclusiveEnd, key, opts.descending);
    keyRangeError = keyRange && keyRange.error;
    if (keyRangeError && !(keyRangeError.name === "DataError" && keyRangeError.code === 0)) {
      return callback(createError(
        IDB_ERROR,
        keyRangeError.name,
        keyRangeError.message
      ));
    }
  }
  var stores = [DOC_STORE, BY_SEQ_STORE, META_STORE];
  if (opts.attachments) {
    stores.push(ATTACH_STORE);
  }
  var txnResult = openTransactionSafely(idb, stores, "readonly");
  if (txnResult.error) {
    return callback(txnResult.error);
  }
  var txn = txnResult.txn;
  txn.oncomplete = onTxnComplete;
  txn.onabort = idbError(callback);
  var docStore = txn.objectStore(DOC_STORE);
  var seqStore = txn.objectStore(BY_SEQ_STORE);
  var metaStore = txn.objectStore(META_STORE);
  var docIdRevIndex = seqStore.index("_doc_id_rev");
  var results = [];
  var docCount;
  var updateSeq;
  metaStore.get(META_STORE).onsuccess = function(e) {
    docCount = e.target.result.docCount;
  };
  if (opts.update_seq) {
    seqStore.openKeyCursor(null, "prev").onsuccess = (e) => {
      var cursor = e.target.result;
      if (cursor && cursor.key) {
        updateSeq = cursor.key;
      }
    };
  }
  function fetchDocAsynchronously(metadata, row, winningRev$$1) {
    var key2 = metadata.id + "::" + winningRev$$1;
    docIdRevIndex.get(key2).onsuccess = function onGetDoc(e) {
      row.doc = decodeDoc(e.target.result) || {};
      if (opts.conflicts) {
        var conflicts = collectConflicts(metadata);
        if (conflicts.length) {
          row.doc._conflicts = conflicts;
        }
      }
      fetchAttachmentsIfNecessary(row.doc, opts, txn);
    };
  }
  function allDocsInner(winningRev$$1, metadata) {
    var row = {
      id: metadata.id,
      key: metadata.id,
      value: {
        rev: winningRev$$1
      }
    };
    var deleted = metadata.deleted;
    if (deleted) {
      if (keys2) {
        results.push(row);
        row.value.deleted = true;
        row.doc = null;
      }
    } else if (skip-- <= 0) {
      results.push(row);
      if (opts.include_docs) {
        fetchDocAsynchronously(metadata, row, winningRev$$1);
      }
    }
  }
  function processBatch(batchValues) {
    for (var i = 0, len = batchValues.length; i < len; i++) {
      if (results.length === limit) {
        break;
      }
      var batchValue = batchValues[i];
      if (batchValue.error && keys2) {
        results.push(batchValue);
        continue;
      }
      var metadata = decodeMetadata(batchValue);
      var winningRev$$1 = metadata.winningRev;
      allDocsInner(winningRev$$1, metadata);
    }
  }
  function onBatch(batchKeys, batchValues, cursor) {
    if (!cursor) {
      return;
    }
    processBatch(batchValues);
    if (results.length < limit) {
      cursor.continue();
    }
  }
  function onGetAll(e) {
    var values = e.target.result;
    if (opts.descending) {
      values = values.reverse();
    }
    processBatch(values);
  }
  function onResultsReady() {
    var returnVal = {
      total_rows: docCount,
      offset: opts.skip,
      rows: results
    };
    if (opts.update_seq && updateSeq !== void 0) {
      returnVal.update_seq = updateSeq;
    }
    callback(null, returnVal);
  }
  function onTxnComplete() {
    if (opts.attachments) {
      postProcessAttachments(results, opts.binary).then(onResultsReady);
    } else {
      onResultsReady();
    }
  }
  if (keyRangeError || limit === 0) {
    return;
  }
  if (keys2) {
    return allDocsKeys(keys2, docStore, onBatch);
  }
  if (limit === -1) {
    return getAll(docStore, keyRange, onGetAll);
  }
  runBatchedCursor(docStore, keyRange, opts.descending, limit + skip, onBatch);
}
function countDocs(txn, cb) {
  var index = txn.objectStore(DOC_STORE).index("deletedOrLocal");
  index.count(IDBKeyRange.only("0")).onsuccess = function(e) {
    cb(e.target.result);
  };
}
var running = false;
var queue = [];
function tryCode(fun, err, res, PouchDB2) {
  try {
    fun(err, res);
  } catch (err2) {
    PouchDB2.emit("error", err2);
  }
}
function applyNext() {
  if (running || !queue.length) {
    return;
  }
  running = true;
  queue.shift()();
}
function enqueueTask(action, callback, PouchDB2) {
  queue.push(function runAction() {
    action(function runCallback(err, res) {
      tryCode(callback, err, res, PouchDB2);
      running = false;
      nextTick(function runNext() {
        applyNext(PouchDB2);
      });
    });
  });
  applyNext();
}
function changes(opts, api, dbName, idb) {
  opts = clone(opts);
  if (opts.continuous) {
    var id = dbName + ":" + uuid();
    changesHandler.addListener(dbName, id, api, opts);
    changesHandler.notify(dbName);
    return {
      cancel: function() {
        changesHandler.removeListener(dbName, id);
      }
    };
  }
  var docIds = opts.doc_ids && new Set(opts.doc_ids);
  opts.since = opts.since || 0;
  var lastSeq = opts.since;
  var limit = "limit" in opts ? opts.limit : -1;
  if (limit === 0) {
    limit = 1;
  }
  var results = [];
  var numResults = 0;
  var filter2 = filterChange(opts);
  var docIdsToMetadata = /* @__PURE__ */ new Map();
  var txn;
  var bySeqStore;
  var docStore;
  var docIdRevIndex;
  function onBatch(batchKeys, batchValues, cursor) {
    if (!cursor || !batchKeys.length) {
      return;
    }
    var winningDocs = new Array(batchKeys.length);
    var metadatas = new Array(batchKeys.length);
    function processMetadataAndWinningDoc(metadata, winningDoc) {
      var change = opts.processChange(winningDoc, metadata, opts);
      lastSeq = change.seq = metadata.seq;
      var filtered = filter2(change);
      if (typeof filtered === "object") {
        return Promise.reject(filtered);
      }
      if (!filtered) {
        return Promise.resolve();
      }
      numResults++;
      if (opts.return_docs) {
        results.push(change);
      }
      if (opts.attachments && opts.include_docs) {
        return new Promise(function(resolve) {
          fetchAttachmentsIfNecessary(winningDoc, opts, txn, function() {
            postProcessAttachments([change], opts.binary).then(function() {
              resolve(change);
            });
          });
        });
      } else {
        return Promise.resolve(change);
      }
    }
    function onBatchDone() {
      var promises = [];
      for (var i = 0, len = winningDocs.length; i < len; i++) {
        if (numResults === limit) {
          break;
        }
        var winningDoc = winningDocs[i];
        if (!winningDoc) {
          continue;
        }
        var metadata = metadatas[i];
        promises.push(processMetadataAndWinningDoc(metadata, winningDoc));
      }
      Promise.all(promises).then(function(changes2) {
        for (var i2 = 0, len2 = changes2.length; i2 < len2; i2++) {
          if (changes2[i2]) {
            opts.onChange(changes2[i2]);
          }
        }
      }).catch(opts.complete);
      if (numResults !== limit) {
        cursor.continue();
      }
    }
    var numDone = 0;
    batchValues.forEach(function(value, i) {
      var doc = decodeDoc(value);
      var seq = batchKeys[i];
      fetchWinningDocAndMetadata(doc, seq, function(metadata, winningDoc) {
        metadatas[i] = metadata;
        winningDocs[i] = winningDoc;
        if (++numDone === batchKeys.length) {
          onBatchDone();
        }
      });
    });
  }
  function onGetMetadata(doc, seq, metadata, cb) {
    if (metadata.seq !== seq) {
      return cb();
    }
    if (metadata.winningRev === doc._rev) {
      return cb(metadata, doc);
    }
    var docIdRev = doc._id + "::" + metadata.winningRev;
    var req = docIdRevIndex.get(docIdRev);
    req.onsuccess = function(e) {
      cb(metadata, decodeDoc(e.target.result));
    };
  }
  function fetchWinningDocAndMetadata(doc, seq, cb) {
    if (docIds && !docIds.has(doc._id)) {
      return cb();
    }
    var metadata = docIdsToMetadata.get(doc._id);
    if (metadata) {
      return onGetMetadata(doc, seq, metadata, cb);
    }
    docStore.get(doc._id).onsuccess = function(e) {
      metadata = decodeMetadata(e.target.result);
      docIdsToMetadata.set(doc._id, metadata);
      onGetMetadata(doc, seq, metadata, cb);
    };
  }
  function finish() {
    opts.complete(null, {
      results,
      last_seq: lastSeq
    });
  }
  function onTxnComplete() {
    if (!opts.continuous && opts.attachments) {
      postProcessAttachments(results).then(finish);
    } else {
      finish();
    }
  }
  var objectStores = [DOC_STORE, BY_SEQ_STORE];
  if (opts.attachments) {
    objectStores.push(ATTACH_STORE);
  }
  var txnResult = openTransactionSafely(idb, objectStores, "readonly");
  if (txnResult.error) {
    return opts.complete(txnResult.error);
  }
  txn = txnResult.txn;
  txn.onabort = idbError(opts.complete);
  txn.oncomplete = onTxnComplete;
  bySeqStore = txn.objectStore(BY_SEQ_STORE);
  docStore = txn.objectStore(DOC_STORE);
  docIdRevIndex = bySeqStore.index("_doc_id_rev");
  var keyRange = opts.since && !opts.descending ? IDBKeyRange.lowerBound(opts.since, true) : null;
  runBatchedCursor(bySeqStore, keyRange, opts.descending, limit, onBatch);
}
var cachedDBs = /* @__PURE__ */ new Map();
var blobSupportPromise;
var openReqList = /* @__PURE__ */ new Map();
function IdbPouch(opts, callback) {
  var api = this;
  enqueueTask(function(thisCallback) {
    init(api, opts, thisCallback);
  }, callback, api.constructor);
}
function init(api, opts, callback) {
  var dbName = opts.name;
  var idb = null;
  var idbGlobalFailureError = null;
  api._meta = null;
  function enrichCallbackError(callback2) {
    return function(error, result) {
      if (error && error instanceof Error && !error.reason) {
        if (idbGlobalFailureError) {
          error.reason = idbGlobalFailureError;
        }
      }
      callback2(error, result);
    };
  }
  function createSchema(db) {
    var docStore = db.createObjectStore(DOC_STORE, { keyPath: "id" });
    db.createObjectStore(BY_SEQ_STORE, { autoIncrement: true }).createIndex("_doc_id_rev", "_doc_id_rev", { unique: true });
    db.createObjectStore(ATTACH_STORE, { keyPath: "digest" });
    db.createObjectStore(META_STORE, { keyPath: "id", autoIncrement: false });
    db.createObjectStore(DETECT_BLOB_SUPPORT_STORE);
    docStore.createIndex("deletedOrLocal", "deletedOrLocal", { unique: false });
    db.createObjectStore(LOCAL_STORE, { keyPath: "_id" });
    var attAndSeqStore = db.createObjectStore(
      ATTACH_AND_SEQ_STORE,
      { autoIncrement: true }
    );
    attAndSeqStore.createIndex("seq", "seq");
    attAndSeqStore.createIndex("digestSeq", "digestSeq", { unique: true });
  }
  function addDeletedOrLocalIndex(txn, callback2) {
    var docStore = txn.objectStore(DOC_STORE);
    docStore.createIndex("deletedOrLocal", "deletedOrLocal", { unique: false });
    docStore.openCursor().onsuccess = function(event) {
      var cursor = event.target.result;
      if (cursor) {
        var metadata = cursor.value;
        var deleted = isDeleted(metadata);
        metadata.deletedOrLocal = deleted ? "1" : "0";
        docStore.put(metadata);
        cursor.continue();
      } else {
        callback2();
      }
    };
  }
  function createLocalStoreSchema(db) {
    db.createObjectStore(LOCAL_STORE, { keyPath: "_id" }).createIndex("_doc_id_rev", "_doc_id_rev", { unique: true });
  }
  function migrateLocalStore(txn, cb) {
    var localStore = txn.objectStore(LOCAL_STORE);
    var docStore = txn.objectStore(DOC_STORE);
    var seqStore = txn.objectStore(BY_SEQ_STORE);
    var cursor = docStore.openCursor();
    cursor.onsuccess = function(event) {
      var cursor2 = event.target.result;
      if (cursor2) {
        var metadata = cursor2.value;
        var docId = metadata.id;
        var local = isLocalId(docId);
        var rev$$1 = winningRev(metadata);
        if (local) {
          var docIdRev = docId + "::" + rev$$1;
          var start = docId + "::";
          var end = docId + "::~";
          var index = seqStore.index("_doc_id_rev");
          var range = IDBKeyRange.bound(start, end, false, false);
          var seqCursor = index.openCursor(range);
          seqCursor.onsuccess = function(e) {
            seqCursor = e.target.result;
            if (!seqCursor) {
              docStore.delete(cursor2.primaryKey);
              cursor2.continue();
            } else {
              var data = seqCursor.value;
              if (data._doc_id_rev === docIdRev) {
                localStore.put(data);
              }
              seqStore.delete(seqCursor.primaryKey);
              seqCursor.continue();
            }
          };
        } else {
          cursor2.continue();
        }
      } else if (cb) {
        cb();
      }
    };
  }
  function addAttachAndSeqStore(db) {
    var attAndSeqStore = db.createObjectStore(
      ATTACH_AND_SEQ_STORE,
      { autoIncrement: true }
    );
    attAndSeqStore.createIndex("seq", "seq");
    attAndSeqStore.createIndex("digestSeq", "digestSeq", { unique: true });
  }
  function migrateAttsAndSeqs(txn, callback2) {
    var seqStore = txn.objectStore(BY_SEQ_STORE);
    var attStore = txn.objectStore(ATTACH_STORE);
    var attAndSeqStore = txn.objectStore(ATTACH_AND_SEQ_STORE);
    var req2 = attStore.count();
    req2.onsuccess = function(e) {
      var count = e.target.result;
      if (!count) {
        return callback2();
      }
      seqStore.openCursor().onsuccess = function(e2) {
        var cursor = e2.target.result;
        if (!cursor) {
          return callback2();
        }
        var doc = cursor.value;
        var seq = cursor.primaryKey;
        var atts = Object.keys(doc._attachments || {});
        var digestMap = {};
        for (var j = 0; j < atts.length; j++) {
          var att = doc._attachments[atts[j]];
          digestMap[att.digest] = true;
        }
        var digests = Object.keys(digestMap);
        for (j = 0; j < digests.length; j++) {
          var digest = digests[j];
          attAndSeqStore.put({
            seq,
            digestSeq: digest + "::" + seq
          });
        }
        cursor.continue();
      };
    };
  }
  function migrateMetadata(txn) {
    function decodeMetadataCompat(storedObject) {
      if (!storedObject.data) {
        storedObject.deleted = storedObject.deletedOrLocal === "1";
        return storedObject;
      }
      return decodeMetadata(storedObject);
    }
    var bySeqStore = txn.objectStore(BY_SEQ_STORE);
    var docStore = txn.objectStore(DOC_STORE);
    var cursor = docStore.openCursor();
    cursor.onsuccess = function(e) {
      var cursor2 = e.target.result;
      if (!cursor2) {
        return;
      }
      var metadata = decodeMetadataCompat(cursor2.value);
      metadata.winningRev = metadata.winningRev || winningRev(metadata);
      function fetchMetadataSeq() {
        var start = metadata.id + "::";
        var end = metadata.id + "::￿";
        var req2 = bySeqStore.index("_doc_id_rev").openCursor(
          IDBKeyRange.bound(start, end)
        );
        var metadataSeq = 0;
        req2.onsuccess = function(e2) {
          var cursor3 = e2.target.result;
          if (!cursor3) {
            metadata.seq = metadataSeq;
            return onGetMetadataSeq();
          }
          var seq = cursor3.primaryKey;
          if (seq > metadataSeq) {
            metadataSeq = seq;
          }
          cursor3.continue();
        };
      }
      function onGetMetadataSeq() {
        var metadataToStore = encodeMetadata(
          metadata,
          metadata.winningRev,
          metadata.deleted
        );
        var req2 = docStore.put(metadataToStore);
        req2.onsuccess = function() {
          cursor2.continue();
        };
      }
      if (metadata.seq) {
        return onGetMetadataSeq();
      }
      fetchMetadataSeq();
    };
  }
  api._remote = false;
  api.type = function() {
    return "idb";
  };
  api._id = toPromise(function(callback2) {
    callback2(null, api._meta.instanceId);
  });
  api._bulkDocs = function idb_bulkDocs(req2, reqOpts, callback2) {
    idbBulkDocs(opts, req2, reqOpts, api, idb, enrichCallbackError(callback2));
  };
  api._get = function idb_get(id, opts2, callback2) {
    var doc;
    var metadata;
    var err;
    var txn = opts2.ctx;
    if (!txn) {
      var txnResult = openTransactionSafely(
        idb,
        [DOC_STORE, BY_SEQ_STORE, ATTACH_STORE],
        "readonly"
      );
      if (txnResult.error) {
        return callback2(txnResult.error);
      }
      txn = txnResult.txn;
    }
    function finish() {
      callback2(err, { doc, metadata, ctx: txn });
    }
    txn.objectStore(DOC_STORE).get(id).onsuccess = function(e) {
      metadata = decodeMetadata(e.target.result);
      if (!metadata) {
        err = createError(MISSING_DOC, "missing");
        return finish();
      }
      var rev$$1;
      if (!opts2.rev) {
        rev$$1 = metadata.winningRev;
        var deleted = isDeleted(metadata);
        if (deleted) {
          err = createError(MISSING_DOC, "deleted");
          return finish();
        }
      } else {
        rev$$1 = opts2.latest ? latest(opts2.rev, metadata) : opts2.rev;
      }
      var objectStore = txn.objectStore(BY_SEQ_STORE);
      var key = metadata.id + "::" + rev$$1;
      objectStore.index("_doc_id_rev").get(key).onsuccess = function(e2) {
        doc = e2.target.result;
        if (doc) {
          doc = decodeDoc(doc);
        }
        if (!doc) {
          err = createError(MISSING_DOC, "missing");
          return finish();
        }
        finish();
      };
    };
  };
  api._getAttachment = function(docId, attachId, attachment, opts2, callback2) {
    var txn;
    if (opts2.ctx) {
      txn = opts2.ctx;
    } else {
      var txnResult = openTransactionSafely(
        idb,
        [DOC_STORE, BY_SEQ_STORE, ATTACH_STORE],
        "readonly"
      );
      if (txnResult.error) {
        return callback2(txnResult.error);
      }
      txn = txnResult.txn;
    }
    var digest = attachment.digest;
    var type = attachment.content_type;
    txn.objectStore(ATTACH_STORE).get(digest).onsuccess = function(e) {
      var body = e.target.result.body;
      readBlobData(body, type, opts2.binary, function(blobData) {
        callback2(null, blobData);
      });
    };
  };
  api._info = function idb_info(callback2) {
    var updateSeq;
    var docCount;
    var txnResult = openTransactionSafely(idb, [META_STORE, BY_SEQ_STORE], "readonly");
    if (txnResult.error) {
      return callback2(txnResult.error);
    }
    var txn = txnResult.txn;
    txn.objectStore(META_STORE).get(META_STORE).onsuccess = function(e) {
      docCount = e.target.result.docCount;
    };
    txn.objectStore(BY_SEQ_STORE).openKeyCursor(null, "prev").onsuccess = function(e) {
      var cursor = e.target.result;
      updateSeq = cursor ? cursor.key : 0;
    };
    txn.oncomplete = function() {
      callback2(null, {
        doc_count: docCount,
        update_seq: updateSeq,
        // for debugging
        idb_attachment_format: api._meta.blobSupport ? "binary" : "base64"
      });
    };
  };
  api._allDocs = function idb_allDocs(opts2, callback2) {
    idbAllDocs(opts2, idb, enrichCallbackError(callback2));
  };
  api._changes = function idbChanges(opts2) {
    return changes(opts2, api, dbName, idb);
  };
  api._close = function(callback2) {
    idb.close();
    cachedDBs.delete(dbName);
    callback2();
  };
  api._getRevisionTree = function(docId, callback2) {
    var txnResult = openTransactionSafely(idb, [DOC_STORE], "readonly");
    if (txnResult.error) {
      return callback2(txnResult.error);
    }
    var txn = txnResult.txn;
    var req2 = txn.objectStore(DOC_STORE).get(docId);
    req2.onsuccess = function(event) {
      var doc = decodeMetadata(event.target.result);
      if (!doc) {
        callback2(createError(MISSING_DOC));
      } else {
        callback2(null, doc.rev_tree);
      }
    };
  };
  api._doCompaction = function(docId, revs, callback2) {
    var stores = [
      DOC_STORE,
      BY_SEQ_STORE,
      ATTACH_STORE,
      ATTACH_AND_SEQ_STORE
    ];
    var txnResult = openTransactionSafely(idb, stores, "readwrite");
    if (txnResult.error) {
      return callback2(txnResult.error);
    }
    var txn = txnResult.txn;
    var docStore = txn.objectStore(DOC_STORE);
    docStore.get(docId).onsuccess = function(event) {
      var metadata = decodeMetadata(event.target.result);
      traverseRevTree(metadata.rev_tree, function(isLeaf, pos, revHash, ctx, opts2) {
        var rev$$1 = pos + "-" + revHash;
        if (revs.indexOf(rev$$1) !== -1) {
          opts2.status = "missing";
        }
      });
      compactRevs(revs, docId, txn);
      var winningRev$$1 = metadata.winningRev;
      var deleted = metadata.deleted;
      txn.objectStore(DOC_STORE).put(
        encodeMetadata(metadata, winningRev$$1, deleted)
      );
    };
    txn.onabort = idbError(callback2);
    txn.oncomplete = function() {
      callback2();
    };
  };
  api._getLocal = function(id, callback2) {
    var txnResult = openTransactionSafely(idb, [LOCAL_STORE], "readonly");
    if (txnResult.error) {
      return callback2(txnResult.error);
    }
    var tx = txnResult.txn;
    var req2 = tx.objectStore(LOCAL_STORE).get(id);
    req2.onerror = idbError(callback2);
    req2.onsuccess = function(e) {
      var doc = e.target.result;
      if (!doc) {
        callback2(createError(MISSING_DOC));
      } else {
        delete doc["_doc_id_rev"];
        callback2(null, doc);
      }
    };
  };
  api._putLocal = function(doc, opts2, callback2) {
    if (typeof opts2 === "function") {
      callback2 = opts2;
      opts2 = {};
    }
    delete doc._revisions;
    var oldRev = doc._rev;
    var id = doc._id;
    if (!oldRev) {
      doc._rev = "0-1";
    } else {
      doc._rev = "0-" + (parseInt(oldRev.split("-")[1], 10) + 1);
    }
    var tx = opts2.ctx;
    var ret;
    if (!tx) {
      var txnResult = openTransactionSafely(idb, [LOCAL_STORE], "readwrite");
      if (txnResult.error) {
        return callback2(txnResult.error);
      }
      tx = txnResult.txn;
      tx.onerror = idbError(callback2);
      tx.oncomplete = function() {
        if (ret) {
          callback2(null, ret);
        }
      };
    }
    var oStore = tx.objectStore(LOCAL_STORE);
    var req2;
    if (oldRev) {
      req2 = oStore.get(id);
      req2.onsuccess = function(e) {
        var oldDoc = e.target.result;
        if (!oldDoc || oldDoc._rev !== oldRev) {
          callback2(createError(REV_CONFLICT));
        } else {
          var req3 = oStore.put(doc);
          req3.onsuccess = function() {
            ret = { ok: true, id: doc._id, rev: doc._rev };
            if (opts2.ctx) {
              callback2(null, ret);
            }
          };
        }
      };
    } else {
      req2 = oStore.add(doc);
      req2.onerror = function(e) {
        callback2(createError(REV_CONFLICT));
        e.preventDefault();
        e.stopPropagation();
      };
      req2.onsuccess = function() {
        ret = { ok: true, id: doc._id, rev: doc._rev };
        if (opts2.ctx) {
          callback2(null, ret);
        }
      };
    }
  };
  api._removeLocal = function(doc, opts2, callback2) {
    if (typeof opts2 === "function") {
      callback2 = opts2;
      opts2 = {};
    }
    var tx = opts2.ctx;
    if (!tx) {
      var txnResult = openTransactionSafely(idb, [LOCAL_STORE], "readwrite");
      if (txnResult.error) {
        return callback2(txnResult.error);
      }
      tx = txnResult.txn;
      tx.oncomplete = function() {
        if (ret) {
          callback2(null, ret);
        }
      };
    }
    var ret;
    var id = doc._id;
    var oStore = tx.objectStore(LOCAL_STORE);
    var req2 = oStore.get(id);
    req2.onerror = idbError(callback2);
    req2.onsuccess = function(e) {
      var oldDoc = e.target.result;
      if (!oldDoc || oldDoc._rev !== doc._rev) {
        callback2(createError(MISSING_DOC));
      } else {
        oStore.delete(id);
        ret = { ok: true, id, rev: "0-0" };
        if (opts2.ctx) {
          callback2(null, ret);
        }
      }
    };
  };
  api._destroy = function(opts2, callback2) {
    changesHandler.removeAllListeners(dbName);
    var openReq = openReqList.get(dbName);
    if (openReq && openReq.result) {
      openReq.result.close();
      cachedDBs.delete(dbName);
    }
    var req2 = indexedDB.deleteDatabase(dbName);
    req2.onsuccess = function() {
      openReqList.delete(dbName);
      if (hasLocalStorage() && dbName in localStorage) {
        delete localStorage[dbName];
      }
      callback2(null, { "ok": true });
    };
    req2.onerror = idbError(callback2);
  };
  var cached = cachedDBs.get(dbName);
  if (cached) {
    idb = cached.idb;
    api._meta = cached.global;
    return nextTick(function() {
      callback(null, api);
    });
  }
  var req = indexedDB.open(dbName, ADAPTER_VERSION);
  openReqList.set(dbName, req);
  req.onupgradeneeded = function(e) {
    var db = e.target.result;
    if (e.oldVersion < 1) {
      return createSchema(db);
    }
    var txn = e.currentTarget.transaction;
    if (e.oldVersion < 3) {
      createLocalStoreSchema(db);
    }
    if (e.oldVersion < 4) {
      addAttachAndSeqStore(db);
    }
    var migrations = [
      addDeletedOrLocalIndex,
      // v1 -> v2
      migrateLocalStore,
      // v2 -> v3
      migrateAttsAndSeqs,
      // v3 -> v4
      migrateMetadata
      // v4 -> v5
    ];
    var i = e.oldVersion;
    function next() {
      var migration = migrations[i - 1];
      i++;
      if (migration) {
        migration(txn, next);
      }
    }
    next();
  };
  req.onsuccess = function(e) {
    idb = e.target.result;
    idb.onversionchange = function() {
      idb.close();
      cachedDBs.delete(dbName);
    };
    idb.onabort = function(e2) {
      guardedConsole("error", "Database has a global failure", e2.target.error);
      idbGlobalFailureError = e2.target.error;
      idb.close();
      cachedDBs.delete(dbName);
    };
    var txn = idb.transaction([
      META_STORE,
      DETECT_BLOB_SUPPORT_STORE,
      DOC_STORE
    ], "readwrite");
    var storedMetaDoc = false;
    var metaDoc;
    var docCount;
    var blobSupport;
    var instanceId;
    function completeSetup() {
      if (typeof blobSupport === "undefined" || !storedMetaDoc) {
        return;
      }
      api._meta = {
        name: dbName,
        instanceId,
        blobSupport
      };
      cachedDBs.set(dbName, {
        idb,
        global: api._meta
      });
      callback(null, api);
    }
    function storeMetaDocIfReady() {
      if (typeof docCount === "undefined" || typeof metaDoc === "undefined") {
        return;
      }
      var instanceKey = dbName + "_id";
      if (instanceKey in metaDoc) {
        instanceId = metaDoc[instanceKey];
      } else {
        metaDoc[instanceKey] = instanceId = uuid();
      }
      metaDoc.docCount = docCount;
      txn.objectStore(META_STORE).put(metaDoc);
    }
    txn.objectStore(META_STORE).get(META_STORE).onsuccess = function(e2) {
      metaDoc = e2.target.result || { id: META_STORE };
      storeMetaDocIfReady();
    };
    countDocs(txn, function(count) {
      docCount = count;
      storeMetaDocIfReady();
    });
    if (!blobSupportPromise) {
      blobSupportPromise = checkBlobSupport(txn, DETECT_BLOB_SUPPORT_STORE, "key");
    }
    blobSupportPromise.then(function(val) {
      blobSupport = val;
      completeSetup();
    });
    txn.oncomplete = function() {
      storedMetaDoc = true;
      completeSetup();
    };
    txn.onabort = idbError(callback);
  };
  req.onerror = function(e) {
    var msg = e.target.error && e.target.error.message;
    if (!msg) {
      msg = "Failed to open indexedDB, are you in private browsing mode?";
    } else if (msg.indexOf("stored database is a higher version") !== -1) {
      msg = new Error('This DB was created with the newer "indexeddb" adapter, but you are trying to open it with the older "idb" adapter');
    }
    guardedConsole("error", msg);
    callback(createError(IDB_ERROR, msg));
  };
}
IdbPouch.valid = function() {
  try {
    return typeof indexedDB !== "undefined" && typeof IDBKeyRange !== "undefined";
  } catch (e) {
    return false;
  }
};
function IDBPouch(PouchDB2) {
  PouchDB2.adapter("idb", IdbPouch, true);
}
function pool(promiseFactories, limit) {
  return new Promise(function(resolve, reject) {
    var running2 = 0;
    var current = 0;
    var done = 0;
    var len = promiseFactories.length;
    var err;
    function runNext() {
      running2++;
      promiseFactories[current++]().then(onSuccess, onError);
    }
    function doNext() {
      if (++done === len) {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      } else {
        runNextBatch();
      }
    }
    function onSuccess() {
      running2--;
      doNext();
    }
    function onError(thisErr) {
      running2--;
      err = err || thisErr;
      doNext();
    }
    function runNextBatch() {
      while (running2 < limit && current < len) {
        runNext();
      }
    }
    runNextBatch();
  });
}
var CHANGES_BATCH_SIZE = 25;
var MAX_SIMULTANEOUS_REVS = 50;
var CHANGES_TIMEOUT_BUFFER = 5e3;
var DEFAULT_HEARTBEAT = 1e4;
var supportsBulkGetMap = {};
function readAttachmentsAsBlobOrBuffer(row) {
  const doc = row.doc || row.ok;
  const atts = doc && doc._attachments;
  if (!atts) {
    return;
  }
  Object.keys(atts).forEach(function(filename) {
    const att = atts[filename];
    att.data = b64ToBluffer(att.data, att.content_type);
  });
}
function encodeDocId(id) {
  if (/^_design/.test(id)) {
    return "_design/" + encodeURIComponent(id.slice(8));
  }
  if (id.startsWith("_local/")) {
    return "_local/" + encodeURIComponent(id.slice(7));
  }
  return encodeURIComponent(id);
}
function preprocessAttachments$1(doc) {
  if (!doc._attachments || !Object.keys(doc._attachments)) {
    return Promise.resolve();
  }
  return Promise.all(Object.keys(doc._attachments).map(function(key) {
    const attachment = doc._attachments[key];
    if (attachment.data && typeof attachment.data !== "string") {
      return new Promise(function(resolve) {
        blobToBase64(attachment.data, resolve);
      }).then(function(b64) {
        attachment.data = b64;
      });
    }
  }));
}
function hasUrlPrefix(opts) {
  if (!opts.prefix) {
    return false;
  }
  const protocol = parseUri(opts.prefix).protocol;
  return protocol === "http" || protocol === "https";
}
function getHost(name, opts) {
  if (hasUrlPrefix(opts)) {
    const dbName = opts.name.substr(opts.prefix.length);
    const prefix = opts.prefix.replace(/\/?$/, "/");
    name = prefix + encodeURIComponent(dbName);
  }
  const uri = parseUri(name);
  if (uri.user || uri.password) {
    uri.auth = { username: uri.user, password: uri.password };
  }
  const parts = uri.path.replace(/(^\/|\/$)/g, "").split("/");
  uri.db = parts.pop();
  if (uri.db.indexOf("%") === -1) {
    uri.db = encodeURIComponent(uri.db);
  }
  uri.path = parts.join("/");
  return uri;
}
function genDBUrl(opts, path) {
  return genUrl(opts, opts.db + "/" + path);
}
function genUrl(opts, path) {
  const pathDel = !opts.path ? "" : "/";
  return opts.protocol + "://" + opts.host + (opts.port ? ":" + opts.port : "") + "/" + opts.path + pathDel + path;
}
function paramsToStr(params) {
  const paramKeys = Object.keys(params);
  if (paramKeys.length === 0) {
    return "";
  }
  return "?" + paramKeys.map((key) => key + "=" + encodeURIComponent(params[key])).join("&");
}
function shouldCacheBust(opts) {
  const ua = typeof navigator !== "undefined" && navigator.userAgent ? navigator.userAgent.toLowerCase() : "";
  const isIE = ua.indexOf("msie") !== -1;
  const isTrident = ua.indexOf("trident") !== -1;
  const isEdge = ua.indexOf("edge") !== -1;
  const isGET = !("method" in opts) || opts.method === "GET";
  return (isIE || isTrident || isEdge) && isGET;
}
function HttpPouch(opts, callback) {
  const api = this;
  const host = getHost(opts.name, opts);
  const dbUrl = genDBUrl(host, "");
  opts = clone(opts);
  const ourFetch = function(url, options) {
    return __async(this, null, function* () {
      options = options || {};
      options.headers = options.headers || new h();
      options.credentials = "include";
      if (opts.auth || host.auth) {
        const nAuth = opts.auth || host.auth;
        const str = nAuth.username + ":" + nAuth.password;
        const token = thisBtoa(unescape(encodeURIComponent(str)));
        options.headers.set("Authorization", "Basic " + token);
      }
      const headers = opts.headers || {};
      Object.keys(headers).forEach(function(key) {
        options.headers.append(key, headers[key]);
      });
      if (shouldCacheBust(options)) {
        url += (url.indexOf("?") === -1 ? "?" : "&") + "_nonce=" + Date.now();
      }
      const fetchFun = opts.fetch || f$1;
      return yield fetchFun(url, options);
    });
  };
  function adapterFun$$1(name, fun) {
    return adapterFun(name, function(...args) {
      setup().then(function() {
        return fun.apply(this, args);
      }).catch(function(e) {
        const callback2 = args.pop();
        callback2(e);
      });
    }).bind(api);
  }
  function fetchJSON(url, options) {
    return __async(this, null, function* () {
      const result = {};
      options = options || {};
      options.headers = options.headers || new h();
      if (!options.headers.get("Content-Type")) {
        options.headers.set("Content-Type", "application/json");
      }
      if (!options.headers.get("Accept")) {
        options.headers.set("Accept", "application/json");
      }
      const response = yield ourFetch(url, options);
      result.ok = response.ok;
      result.status = response.status;
      const json = yield response.json();
      result.data = json;
      if (!result.ok) {
        result.data.status = result.status;
        const err = generateErrorFromResponse(result.data);
        throw err;
      }
      if (Array.isArray(result.data)) {
        result.data = result.data.map(function(v) {
          if (v.error || v.missing) {
            return generateErrorFromResponse(v);
          } else {
            return v;
          }
        });
      }
      return result;
    });
  }
  let setupPromise;
  function setup() {
    return __async(this, null, function* () {
      if (opts.skip_setup) {
        return Promise.resolve();
      }
      if (setupPromise) {
        return setupPromise;
      }
      setupPromise = fetchJSON(dbUrl).catch(function(err) {
        if (err && err.status && err.status === 404) {
          explainError(404, "PouchDB is just detecting if the remote exists.");
          return fetchJSON(dbUrl, { method: "PUT" });
        } else {
          return Promise.reject(err);
        }
      }).catch(function(err) {
        if (err && err.status && err.status === 412) {
          return true;
        }
        return Promise.reject(err);
      });
      setupPromise.catch(function() {
        setupPromise = null;
      });
      return setupPromise;
    });
  }
  nextTick(function() {
    callback(null, api);
  });
  api._remote = true;
  api.type = function() {
    return "http";
  };
  api.id = adapterFun$$1("id", function(callback2) {
    return __async(this, null, function* () {
      let result;
      try {
        const response = yield ourFetch(genUrl(host, ""));
        result = yield response.json();
      } catch (err) {
        result = {};
      }
      const uuid$$1 = result && result.uuid ? result.uuid + host.db : genDBUrl(host, "");
      callback2(null, uuid$$1);
    });
  });
  api.compact = adapterFun$$1("compact", function(opts2, callback2) {
    return __async(this, null, function* () {
      if (typeof opts2 === "function") {
        callback2 = opts2;
        opts2 = {};
      }
      opts2 = clone(opts2);
      yield fetchJSON(genDBUrl(host, "_compact"), { method: "POST" });
      function ping() {
        api.info(function(err, res) {
          if (res && !res.compact_running) {
            callback2(null, { ok: true });
          } else {
            setTimeout(ping, opts2.interval || 200);
          }
        });
      }
      ping();
    });
  });
  api.bulkGet = adapterFun("bulkGet", function(opts2, callback2) {
    const self2 = this;
    function doBulkGet(cb) {
      return __async(this, null, function* () {
        const params = {};
        if (opts2.revs) {
          params.revs = true;
        }
        if (opts2.attachments) {
          params.attachments = true;
        }
        if (opts2.latest) {
          params.latest = true;
        }
        try {
          const result = yield fetchJSON(genDBUrl(host, "_bulk_get" + paramsToStr(params)), {
            method: "POST",
            body: JSON.stringify({ docs: opts2.docs })
          });
          if (opts2.attachments && opts2.binary) {
            result.data.results.forEach(function(res) {
              res.docs.forEach(readAttachmentsAsBlobOrBuffer);
            });
          }
          cb(null, result.data);
        } catch (error) {
          cb(error);
        }
      });
    }
    function doBulkGetShim() {
      const batchSize = MAX_SIMULTANEOUS_REVS;
      const numBatches = Math.ceil(opts2.docs.length / batchSize);
      let numDone = 0;
      const results = new Array(numBatches);
      function onResult(batchNum) {
        return function(err, res) {
          results[batchNum] = res.results;
          if (++numDone === numBatches) {
            callback2(null, { results: results.flat() });
          }
        };
      }
      for (let i = 0; i < numBatches; i++) {
        const subOpts = pick(opts2, ["revs", "attachments", "binary", "latest"]);
        subOpts.docs = opts2.docs.slice(
          i * batchSize,
          Math.min(opts2.docs.length, (i + 1) * batchSize)
        );
        bulkGet(self2, subOpts, onResult(i));
      }
    }
    const dbUrl2 = genUrl(host, "");
    const supportsBulkGet = supportsBulkGetMap[dbUrl2];
    if (typeof supportsBulkGet !== "boolean") {
      doBulkGet(function(err, res) {
        if (err) {
          supportsBulkGetMap[dbUrl2] = false;
          explainError(
            err.status,
            "PouchDB is just detecting if the remote supports the _bulk_get API."
          );
          doBulkGetShim();
        } else {
          supportsBulkGetMap[dbUrl2] = true;
          callback2(null, res);
        }
      });
    } else if (supportsBulkGet) {
      doBulkGet(callback2);
    } else {
      doBulkGetShim();
    }
  });
  api._info = function(callback2) {
    return __async(this, null, function* () {
      try {
        yield setup();
        const response = yield ourFetch(genDBUrl(host, ""));
        const info = yield response.json();
        info.host = genDBUrl(host, "");
        callback2(null, info);
      } catch (err) {
        callback2(err);
      }
    });
  };
  api.fetch = function(path, options) {
    return __async(this, null, function* () {
      yield setup();
      const url = path.substring(0, 1) === "/" ? genUrl(host, path.substring(1)) : genDBUrl(host, path);
      return ourFetch(url, options);
    });
  };
  api.get = adapterFun$$1("get", function(id, opts2, callback2) {
    return __async(this, null, function* () {
      if (typeof opts2 === "function") {
        callback2 = opts2;
        opts2 = {};
      }
      opts2 = clone(opts2);
      const params = {};
      if (opts2.revs) {
        params.revs = true;
      }
      if (opts2.revs_info) {
        params.revs_info = true;
      }
      if (opts2.latest) {
        params.latest = true;
      }
      if (opts2.open_revs) {
        if (opts2.open_revs !== "all") {
          opts2.open_revs = JSON.stringify(opts2.open_revs);
        }
        params.open_revs = opts2.open_revs;
      }
      if (opts2.rev) {
        params.rev = opts2.rev;
      }
      if (opts2.conflicts) {
        params.conflicts = opts2.conflicts;
      }
      if (opts2.update_seq) {
        params.update_seq = opts2.update_seq;
      }
      id = encodeDocId(id);
      function fetchAttachments(doc) {
        const atts = doc._attachments;
        const filenames = atts && Object.keys(atts);
        if (!atts || !filenames.length) {
          return;
        }
        function fetchData(filename) {
          return __async(this, null, function* () {
            const att = atts[filename];
            const path = encodeDocId(doc._id) + "/" + encodeAttachmentId(filename) + "?rev=" + doc._rev;
            const response = yield ourFetch(genDBUrl(host, path));
            let blob;
            if ("buffer" in response) {
              blob = yield response.buffer();
            } else {
              blob = yield response.blob();
            }
            let data;
            if (opts2.binary) {
              const typeFieldDescriptor = Object.getOwnPropertyDescriptor(blob.__proto__, "type");
              if (!typeFieldDescriptor || typeFieldDescriptor.set) {
                blob.type = att.content_type;
              }
              data = blob;
            } else {
              data = yield new Promise(function(resolve) {
                blobToBase64(blob, resolve);
              });
            }
            delete att.stub;
            delete att.length;
            att.data = data;
          });
        }
        const promiseFactories = filenames.map(function(filename) {
          return function() {
            return fetchData(filename);
          };
        });
        return pool(promiseFactories, 5);
      }
      function fetchAllAttachments(docOrDocs) {
        if (Array.isArray(docOrDocs)) {
          return Promise.all(docOrDocs.map(function(doc) {
            if (doc.ok) {
              return fetchAttachments(doc.ok);
            }
          }));
        }
        return fetchAttachments(docOrDocs);
      }
      const url = genDBUrl(host, id + paramsToStr(params));
      try {
        const res = yield fetchJSON(url);
        if (opts2.attachments) {
          yield fetchAllAttachments(res.data);
        }
        callback2(null, res.data);
      } catch (error) {
        error.docId = id;
        callback2(error);
      }
    });
  });
  api.remove = adapterFun$$1("remove", function(docOrId, optsOrRev, opts2, cb) {
    return __async(this, null, function* () {
      let doc;
      if (typeof optsOrRev === "string") {
        doc = {
          _id: docOrId,
          _rev: optsOrRev
        };
        if (typeof opts2 === "function") {
          cb = opts2;
          opts2 = {};
        }
      } else {
        doc = docOrId;
        if (typeof optsOrRev === "function") {
          cb = optsOrRev;
          opts2 = {};
        } else {
          cb = opts2;
          opts2 = optsOrRev;
        }
      }
      const rev$$1 = doc._rev || opts2.rev;
      const url = genDBUrl(host, encodeDocId(doc._id)) + "?rev=" + rev$$1;
      try {
        const result = yield fetchJSON(url, { method: "DELETE" });
        cb(null, result.data);
      } catch (error) {
        cb(error);
      }
    });
  });
  function encodeAttachmentId(attachmentId) {
    return attachmentId.split("/").map(encodeURIComponent).join("/");
  }
  api.getAttachment = adapterFun$$1("getAttachment", function(docId, attachmentId, opts2, callback2) {
    return __async(this, null, function* () {
      if (typeof opts2 === "function") {
        callback2 = opts2;
        opts2 = {};
      }
      const params = opts2.rev ? "?rev=" + opts2.rev : "";
      const url = genDBUrl(host, encodeDocId(docId)) + "/" + encodeAttachmentId(attachmentId) + params;
      let contentType;
      try {
        const response = yield ourFetch(url, { method: "GET" });
        if (!response.ok) {
          throw response;
        }
        contentType = response.headers.get("content-type");
        let blob;
        if (typeof process !== "undefined" && !process.browser && typeof response.buffer === "function") {
          blob = yield response.buffer();
        } else {
          blob = yield response.blob();
        }
        if (typeof process !== "undefined" && !process.browser) {
          const typeFieldDescriptor = Object.getOwnPropertyDescriptor(blob.__proto__, "type");
          if (!typeFieldDescriptor || typeFieldDescriptor.set) {
            blob.type = contentType;
          }
        }
        callback2(null, blob);
      } catch (err) {
        callback2(err);
      }
    });
  });
  api.removeAttachment = adapterFun$$1("removeAttachment", function(docId, attachmentId, rev$$1, callback2) {
    return __async(this, null, function* () {
      const url = genDBUrl(host, encodeDocId(docId) + "/" + encodeAttachmentId(attachmentId)) + "?rev=" + rev$$1;
      try {
        const result = yield fetchJSON(url, { method: "DELETE" });
        callback2(null, result.data);
      } catch (error) {
        callback2(error);
      }
    });
  });
  api.putAttachment = adapterFun$$1("putAttachment", function(docId, attachmentId, rev$$1, blob, type, callback2) {
    return __async(this, null, function* () {
      if (typeof type === "function") {
        callback2 = type;
        type = blob;
        blob = rev$$1;
        rev$$1 = null;
      }
      const id = encodeDocId(docId) + "/" + encodeAttachmentId(attachmentId);
      let url = genDBUrl(host, id);
      if (rev$$1) {
        url += "?rev=" + rev$$1;
      }
      if (typeof blob === "string") {
        let binary;
        try {
          binary = thisAtob(blob);
        } catch (err) {
          return callback2(createError(
            BAD_ARG,
            "Attachment is not a valid base64 string"
          ));
        }
        blob = binary ? binStringToBluffer(binary, type) : "";
      }
      try {
        const result = yield fetchJSON(url, {
          headers: new h({ "Content-Type": type }),
          method: "PUT",
          body: blob
        });
        callback2(null, result.data);
      } catch (error) {
        callback2(error);
      }
    });
  });
  api._bulkDocs = function(req, opts2, callback2) {
    return __async(this, null, function* () {
      req.new_edits = opts2.new_edits;
      try {
        yield setup();
        yield Promise.all(req.docs.map(preprocessAttachments$1));
        const result = yield fetchJSON(genDBUrl(host, "_bulk_docs"), {
          method: "POST",
          body: JSON.stringify(req)
        });
        callback2(null, result.data);
      } catch (error) {
        callback2(error);
      }
    });
  };
  api._put = function(doc, opts2, callback2) {
    return __async(this, null, function* () {
      try {
        yield setup();
        yield preprocessAttachments$1(doc);
        const result = yield fetchJSON(genDBUrl(host, encodeDocId(doc._id)), {
          method: "PUT",
          body: JSON.stringify(doc)
        });
        callback2(null, result.data);
      } catch (error) {
        error.docId = doc && doc._id;
        callback2(error);
      }
    });
  };
  api.allDocs = adapterFun$$1("allDocs", function(opts2, callback2) {
    return __async(this, null, function* () {
      if (typeof opts2 === "function") {
        callback2 = opts2;
        opts2 = {};
      }
      opts2 = clone(opts2);
      const params = {};
      let body;
      let method = "GET";
      if (opts2.conflicts) {
        params.conflicts = true;
      }
      if (opts2.update_seq) {
        params.update_seq = true;
      }
      if (opts2.descending) {
        params.descending = true;
      }
      if (opts2.include_docs) {
        params.include_docs = true;
      }
      if (opts2.attachments) {
        params.attachments = true;
      }
      if (opts2.key) {
        params.key = JSON.stringify(opts2.key);
      }
      if (opts2.start_key) {
        opts2.startkey = opts2.start_key;
      }
      if (opts2.startkey) {
        params.startkey = JSON.stringify(opts2.startkey);
      }
      if (opts2.end_key) {
        opts2.endkey = opts2.end_key;
      }
      if (opts2.endkey) {
        params.endkey = JSON.stringify(opts2.endkey);
      }
      if (typeof opts2.inclusive_end !== "undefined") {
        params.inclusive_end = !!opts2.inclusive_end;
      }
      if (typeof opts2.limit !== "undefined") {
        params.limit = opts2.limit;
      }
      if (typeof opts2.skip !== "undefined") {
        params.skip = opts2.skip;
      }
      const paramStr = paramsToStr(params);
      if (typeof opts2.keys !== "undefined") {
        method = "POST";
        body = { keys: opts2.keys };
      }
      try {
        const result = yield fetchJSON(genDBUrl(host, "_all_docs" + paramStr), {
          method,
          body: JSON.stringify(body)
        });
        if (opts2.include_docs && opts2.attachments && opts2.binary) {
          result.data.rows.forEach(readAttachmentsAsBlobOrBuffer);
        }
        callback2(null, result.data);
      } catch (error) {
        callback2(error);
      }
    });
  });
  api._changes = function(opts2) {
    const batchSize = "batch_size" in opts2 ? opts2.batch_size : CHANGES_BATCH_SIZE;
    opts2 = clone(opts2);
    if (opts2.continuous && !("heartbeat" in opts2)) {
      opts2.heartbeat = DEFAULT_HEARTBEAT;
    }
    let requestTimeout = "timeout" in opts2 ? opts2.timeout : 30 * 1e3;
    if ("timeout" in opts2 && opts2.timeout && requestTimeout - opts2.timeout < CHANGES_TIMEOUT_BUFFER) {
      requestTimeout = opts2.timeout + CHANGES_TIMEOUT_BUFFER;
    }
    if ("heartbeat" in opts2 && opts2.heartbeat && requestTimeout - opts2.heartbeat < CHANGES_TIMEOUT_BUFFER) {
      requestTimeout = opts2.heartbeat + CHANGES_TIMEOUT_BUFFER;
    }
    const params = {};
    if ("timeout" in opts2 && opts2.timeout) {
      params.timeout = opts2.timeout;
    }
    const limit = typeof opts2.limit !== "undefined" ? opts2.limit : false;
    let leftToFetch = limit;
    if (opts2.style) {
      params.style = opts2.style;
    }
    if (opts2.include_docs || opts2.filter && typeof opts2.filter === "function") {
      params.include_docs = true;
    }
    if (opts2.attachments) {
      params.attachments = true;
    }
    if (opts2.continuous) {
      params.feed = "longpoll";
    }
    if (opts2.seq_interval) {
      params.seq_interval = opts2.seq_interval;
    }
    if (opts2.conflicts) {
      params.conflicts = true;
    }
    if (opts2.descending) {
      params.descending = true;
    }
    if (opts2.update_seq) {
      params.update_seq = true;
    }
    if ("heartbeat" in opts2) {
      if (opts2.heartbeat) {
        params.heartbeat = opts2.heartbeat;
      }
    }
    if (opts2.filter && typeof opts2.filter === "string") {
      params.filter = opts2.filter;
    }
    if (opts2.view && typeof opts2.view === "string") {
      params.filter = "_view";
      params.view = opts2.view;
    }
    if (opts2.query_params && typeof opts2.query_params === "object") {
      for (const param_name in opts2.query_params) {
        if (Object.prototype.hasOwnProperty.call(opts2.query_params, param_name)) {
          params[param_name] = opts2.query_params[param_name];
        }
      }
    }
    let method = "GET";
    let body;
    if (opts2.doc_ids) {
      params.filter = "_doc_ids";
      method = "POST";
      body = { doc_ids: opts2.doc_ids };
    } else if (opts2.selector) {
      params.filter = "_selector";
      method = "POST";
      body = { selector: opts2.selector };
    }
    const controller = new AbortController();
    let lastFetchedSeq;
    const fetchData = function(since, callback2) {
      return __async(this, null, function* () {
        if (opts2.aborted) {
          return;
        }
        params.since = since;
        if (typeof params.since === "object") {
          params.since = JSON.stringify(params.since);
        }
        if (opts2.descending) {
          if (limit) {
            params.limit = leftToFetch;
          }
        } else {
          params.limit = !limit || leftToFetch > batchSize ? batchSize : leftToFetch;
        }
        const url = genDBUrl(host, "_changes" + paramsToStr(params));
        const fetchOpts = {
          signal: controller.signal,
          method,
          body: JSON.stringify(body)
        };
        lastFetchedSeq = since;
        if (opts2.aborted) {
          return;
        }
        try {
          yield setup();
          const result = yield fetchJSON(url, fetchOpts);
          callback2(null, result.data);
        } catch (error) {
          callback2(error);
        }
      });
    };
    const results = { results: [] };
    const fetched = function(err, res) {
      if (opts2.aborted) {
        return;
      }
      let raw_results_length = 0;
      if (res && res.results) {
        raw_results_length = res.results.length;
        results.last_seq = res.last_seq;
        let pending = null;
        let lastSeq = null;
        if (typeof res.pending === "number") {
          pending = res.pending;
        }
        if (typeof results.last_seq === "string" || typeof results.last_seq === "number") {
          lastSeq = results.last_seq;
        }
        const req = {};
        req.query = opts2.query_params;
        res.results = res.results.filter(function(c) {
          leftToFetch--;
          const ret = filterChange(opts2)(c);
          if (ret) {
            if (opts2.include_docs && opts2.attachments && opts2.binary) {
              readAttachmentsAsBlobOrBuffer(c);
            }
            if (opts2.return_docs) {
              results.results.push(c);
            }
            opts2.onChange(c, pending, lastSeq);
          }
          return ret;
        });
      } else if (err) {
        opts2.aborted = true;
        opts2.complete(err);
        return;
      }
      if (res && res.last_seq) {
        lastFetchedSeq = res.last_seq;
      }
      const finished = limit && leftToFetch <= 0 || res && raw_results_length < batchSize || opts2.descending;
      if (opts2.continuous && !(limit && leftToFetch <= 0) || !finished) {
        nextTick(function() {
          fetchData(lastFetchedSeq, fetched);
        });
      } else {
        opts2.complete(null, results);
      }
    };
    fetchData(opts2.since || 0, fetched);
    return {
      cancel: function() {
        opts2.aborted = true;
        controller.abort();
      }
    };
  };
  api.revsDiff = adapterFun$$1("revsDiff", function(req, opts2, callback2) {
    return __async(this, null, function* () {
      if (typeof opts2 === "function") {
        callback2 = opts2;
        opts2 = {};
      }
      try {
        const result = yield fetchJSON(genDBUrl(host, "_revs_diff"), {
          method: "POST",
          body: JSON.stringify(req)
        });
        callback2(null, result.data);
      } catch (error) {
        callback2(error);
      }
    });
  });
  api._close = function(callback2) {
    callback2();
  };
  api._destroy = function(options, callback2) {
    return __async(this, null, function* () {
      try {
        const json = yield fetchJSON(genDBUrl(host, ""), { method: "DELETE" });
        callback2(null, json);
      } catch (error) {
        if (error.status === 404) {
          callback2(null, { ok: true });
        } else {
          callback2(error);
        }
      }
    });
  };
}
HttpPouch.valid = function() {
  return true;
};
function HttpPouch$1(PouchDB2) {
  PouchDB2.adapter("http", HttpPouch, false);
  PouchDB2.adapter("https", HttpPouch, false);
}
var QueryParseError = class _QueryParseError extends Error {
  constructor(message) {
    super();
    this.status = 400;
    this.name = "query_parse_error";
    this.message = message;
    this.error = true;
    try {
      Error.captureStackTrace(this, _QueryParseError);
    } catch (e) {
    }
  }
};
var NotFoundError = class _NotFoundError extends Error {
  constructor(message) {
    super();
    this.status = 404;
    this.name = "not_found";
    this.message = message;
    this.error = true;
    try {
      Error.captureStackTrace(this, _NotFoundError);
    } catch (e) {
    }
  }
};
var BuiltInError = class _BuiltInError extends Error {
  constructor(message) {
    super();
    this.status = 500;
    this.name = "invalid_value";
    this.message = message;
    this.error = true;
    try {
      Error.captureStackTrace(this, _BuiltInError);
    } catch (e) {
    }
  }
};
function promisedCallback(promise, callback) {
  if (callback) {
    promise.then(function(res) {
      nextTick(function() {
        callback(null, res);
      });
    }, function(reason) {
      nextTick(function() {
        callback(reason);
      });
    });
  }
  return promise;
}
function callbackify(fun) {
  return function(...args) {
    var cb = args.pop();
    var promise = fun.apply(this, args);
    if (typeof cb === "function") {
      promisedCallback(promise, cb);
    }
    return promise;
  };
}
function fin(promise, finalPromiseFactory) {
  return promise.then(function(res) {
    return finalPromiseFactory().then(function() {
      return res;
    });
  }, function(reason) {
    return finalPromiseFactory().then(function() {
      throw reason;
    });
  });
}
function sequentialize(queue2, promiseFactory) {
  return function() {
    var args = arguments;
    var that = this;
    return queue2.add(function() {
      return promiseFactory.apply(that, args);
    });
  };
}
function uniq(arr) {
  var theSet = new Set(arr);
  var result = new Array(theSet.size);
  var index = -1;
  theSet.forEach(function(value) {
    result[++index] = value;
  });
  return result;
}
function mapToKeysArray(map) {
  var result = new Array(map.size);
  var index = -1;
  map.forEach(function(value, key) {
    result[++index] = key;
  });
  return result;
}
function createBuiltInError(name) {
  var message = "builtin " + name + " function requires map values to be numbers or number arrays";
  return new BuiltInError(message);
}
function sum(values) {
  var result = 0;
  for (var i = 0, len = values.length; i < len; i++) {
    var num = values[i];
    if (typeof num !== "number") {
      if (Array.isArray(num)) {
        result = typeof result === "number" ? [result] : result;
        for (var j = 0, jLen = num.length; j < jLen; j++) {
          var jNum = num[j];
          if (typeof jNum !== "number") {
            throw createBuiltInError("_sum");
          } else if (typeof result[j] === "undefined") {
            result.push(jNum);
          } else {
            result[j] += jNum;
          }
        }
      } else {
        throw createBuiltInError("_sum");
      }
    } else if (typeof result === "number") {
      result += num;
    } else {
      result[0] += num;
    }
  }
  return result;
}
var log = guardedConsole.bind(null, "log");
var isArray = Array.isArray;
var toJSON = JSON.parse;
function evalFunctionWithEval(func, emit) {
  return scopeEval(
    "return (" + func.replace(/;\s*$/, "") + ");",
    {
      emit,
      sum,
      log,
      isArray,
      toJSON
    }
  );
}
var TaskQueue$1 = class {
  constructor() {
    this.promise = Promise.resolve();
  }
  add(promiseFactory) {
    this.promise = this.promise.catch(() => {
    }).then(() => promiseFactory());
    return this.promise;
  }
  finish() {
    return this.promise;
  }
};
function stringify2(input) {
  if (!input) {
    return "undefined";
  }
  switch (typeof input) {
    case "function":
      return input.toString();
    case "string":
      return input.toString();
    default:
      return JSON.stringify(input);
  }
}
function createViewSignature(mapFun, reduceFun) {
  return stringify2(mapFun) + stringify2(reduceFun) + "undefined";
}
function createView(sourceDB, viewName, mapFun, reduceFun, temporary, localDocName2) {
  return __async(this, null, function* () {
    const viewSignature = createViewSignature(mapFun, reduceFun);
    let cachedViews;
    if (!temporary) {
      cachedViews = sourceDB._cachedViews = sourceDB._cachedViews || {};
      if (cachedViews[viewSignature]) {
        return cachedViews[viewSignature];
      }
    }
    const promiseForView = sourceDB.info().then(function(info) {
      return __async(this, null, function* () {
        const depDbName = info.db_name + "-mrview-" + (temporary ? "temp" : stringMd5(viewSignature));
        function diffFunction(doc) {
          doc.views = doc.views || {};
          let fullViewName = viewName;
          if (fullViewName.indexOf("/") === -1) {
            fullViewName = viewName + "/" + viewName;
          }
          const depDbs = doc.views[fullViewName] = doc.views[fullViewName] || {};
          if (depDbs[depDbName]) {
            return;
          }
          depDbs[depDbName] = true;
          return doc;
        }
        yield upsert(sourceDB, "_local/" + localDocName2, diffFunction);
        const res = yield sourceDB.registerDependentDatabase(depDbName);
        const db = res.db;
        db.auto_compaction = true;
        const view = {
          name: depDbName,
          db,
          sourceDB,
          adapter: sourceDB.adapter,
          mapFun,
          reduceFun
        };
        let lastSeqDoc;
        try {
          lastSeqDoc = yield view.db.get("_local/lastSeq");
        } catch (err) {
          if (err.status !== 404) {
            throw err;
          }
        }
        view.seq = lastSeqDoc ? lastSeqDoc.seq : 0;
        if (cachedViews) {
          view.db.once("destroyed", function() {
            delete cachedViews[viewSignature];
          });
        }
        return view;
      });
    });
    if (cachedViews) {
      cachedViews[viewSignature] = promiseForView;
    }
    return promiseForView;
  });
}
var persistentQueues = {};
var tempViewQueue = new TaskQueue$1();
var CHANGES_BATCH_SIZE$1 = 50;
function parseViewName(name) {
  return name.indexOf("/") === -1 ? [name, name] : name.split("/");
}
function isGenOne(changes2) {
  return changes2.length === 1 && /^1-/.test(changes2[0].rev);
}
function emitError(db, e, data) {
  try {
    db.emit("error", e);
  } catch (err) {
    guardedConsole(
      "error",
      "The user's map/reduce function threw an uncaught error.\nYou can debug this error by doing:\nmyDatabase.on('error', function (err) { debugger; });\nPlease double-check your map/reduce function."
    );
    guardedConsole("error", e, data);
  }
}
function createAbstractMapReduce(localDocName2, mapper2, reducer2, ddocValidator2) {
  function tryMap(db, fun, doc) {
    try {
      fun(doc);
    } catch (e) {
      emitError(db, e, { fun, doc });
    }
  }
  function tryReduce(db, fun, keys2, values, rereduce) {
    try {
      return { output: fun(keys2, values, rereduce) };
    } catch (e) {
      emitError(db, e, { fun, keys: keys2, values, rereduce });
      return { error: e };
    }
  }
  function sortByKeyThenValue(x, y) {
    const keyCompare = collate(x.key, y.key);
    return keyCompare !== 0 ? keyCompare : collate(x.value, y.value);
  }
  function sliceResults(results, limit, skip) {
    skip = skip || 0;
    if (typeof limit === "number") {
      return results.slice(skip, limit + skip);
    } else if (skip > 0) {
      return results.slice(skip);
    }
    return results;
  }
  function rowToDocId(row) {
    const val = row.value;
    const docId = val && typeof val === "object" && val._id || row.id;
    return docId;
  }
  function readAttachmentsAsBlobOrBuffer2(res) {
    for (const row of res.rows) {
      const atts = row.doc && row.doc._attachments;
      if (!atts) {
        continue;
      }
      for (const filename of Object.keys(atts)) {
        const att = atts[filename];
        atts[filename].data = b64ToBluffer(att.data, att.content_type);
      }
    }
  }
  function postprocessAttachments(opts) {
    return function(res) {
      if (opts.include_docs && opts.attachments && opts.binary) {
        readAttachmentsAsBlobOrBuffer2(res);
      }
      return res;
    };
  }
  function addHttpParam(paramName, opts, params, asJson) {
    let val = opts[paramName];
    if (typeof val !== "undefined") {
      if (asJson) {
        val = encodeURIComponent(JSON.stringify(val));
      }
      params.push(paramName + "=" + val);
    }
  }
  function coerceInteger(integerCandidate) {
    if (typeof integerCandidate !== "undefined") {
      const asNumber = Number(integerCandidate);
      if (!isNaN(asNumber) && asNumber === parseInt(integerCandidate, 10)) {
        return asNumber;
      } else {
        return integerCandidate;
      }
    }
  }
  function coerceOptions(opts) {
    opts.group_level = coerceInteger(opts.group_level);
    opts.limit = coerceInteger(opts.limit);
    opts.skip = coerceInteger(opts.skip);
    return opts;
  }
  function checkPositiveInteger(number) {
    if (number) {
      if (typeof number !== "number") {
        return new QueryParseError(`Invalid value for integer: "${number}"`);
      }
      if (number < 0) {
        return new QueryParseError(`Invalid value for positive integer: "${number}"`);
      }
    }
  }
  function checkQueryParseError(options, fun) {
    const startkeyName = options.descending ? "endkey" : "startkey";
    const endkeyName = options.descending ? "startkey" : "endkey";
    if (typeof options[startkeyName] !== "undefined" && typeof options[endkeyName] !== "undefined" && collate(options[startkeyName], options[endkeyName]) > 0) {
      throw new QueryParseError("No rows can match your key range, reverse your start_key and end_key or set {descending : true}");
    } else if (fun.reduce && options.reduce !== false) {
      if (options.include_docs) {
        throw new QueryParseError("{include_docs:true} is invalid for reduce");
      } else if (options.keys && options.keys.length > 1 && !options.group && !options.group_level) {
        throw new QueryParseError("Multi-key fetches for reduce views must use {group: true}");
      }
    }
    for (const optionName of ["group_level", "limit", "skip"]) {
      const error = checkPositiveInteger(options[optionName]);
      if (error) {
        throw error;
      }
    }
  }
  function httpQuery(db, fun, opts) {
    return __async(this, null, function* () {
      let params = [];
      let body;
      let method = "GET";
      let ok;
      addHttpParam("reduce", opts, params);
      addHttpParam("include_docs", opts, params);
      addHttpParam("attachments", opts, params);
      addHttpParam("limit", opts, params);
      addHttpParam("descending", opts, params);
      addHttpParam("group", opts, params);
      addHttpParam("group_level", opts, params);
      addHttpParam("skip", opts, params);
      addHttpParam("stale", opts, params);
      addHttpParam("conflicts", opts, params);
      addHttpParam("startkey", opts, params, true);
      addHttpParam("start_key", opts, params, true);
      addHttpParam("endkey", opts, params, true);
      addHttpParam("end_key", opts, params, true);
      addHttpParam("inclusive_end", opts, params);
      addHttpParam("key", opts, params, true);
      addHttpParam("update_seq", opts, params);
      params = params.join("&");
      params = params === "" ? "" : "?" + params;
      if (typeof opts.keys !== "undefined") {
        const MAX_URL_LENGTH = 2e3;
        const keysAsString = `keys=${encodeURIComponent(JSON.stringify(opts.keys))}`;
        if (keysAsString.length + params.length + 1 <= MAX_URL_LENGTH) {
          params += (params[0] === "?" ? "&" : "?") + keysAsString;
        } else {
          method = "POST";
          if (typeof fun === "string") {
            body = { keys: opts.keys };
          } else {
            fun.keys = opts.keys;
          }
        }
      }
      if (typeof fun === "string") {
        const parts = parseViewName(fun);
        const response2 = yield db.fetch("_design/" + parts[0] + "/_view/" + parts[1] + params, {
          headers: new h({ "Content-Type": "application/json" }),
          method,
          body: JSON.stringify(body)
        });
        ok = response2.ok;
        const result2 = yield response2.json();
        if (!ok) {
          result2.status = response2.status;
          throw generateErrorFromResponse(result2);
        }
        for (const row of result2.rows) {
          if (row.value && row.value.error && row.value.error === "builtin_reduce_error") {
            throw new Error(row.reason);
          }
        }
        return new Promise(function(resolve) {
          resolve(result2);
        }).then(postprocessAttachments(opts));
      }
      body = body || {};
      for (const key of Object.keys(fun)) {
        if (Array.isArray(fun[key])) {
          body[key] = fun[key];
        } else {
          body[key] = fun[key].toString();
        }
      }
      const response = yield db.fetch("_temp_view" + params, {
        headers: new h({ "Content-Type": "application/json" }),
        method: "POST",
        body: JSON.stringify(body)
      });
      ok = response.ok;
      const result = yield response.json();
      if (!ok) {
        result.status = response.status;
        throw generateErrorFromResponse(result);
      }
      return new Promise(function(resolve) {
        resolve(result);
      }).then(postprocessAttachments(opts));
    });
  }
  function customQuery(db, fun, opts) {
    return new Promise(function(resolve, reject) {
      db._query(fun, opts, function(err, res) {
        if (err) {
          return reject(err);
        }
        resolve(res);
      });
    });
  }
  function customViewCleanup(db) {
    return new Promise(function(resolve, reject) {
      db._viewCleanup(function(err, res) {
        if (err) {
          return reject(err);
        }
        resolve(res);
      });
    });
  }
  function defaultsTo(value) {
    return function(reason) {
      if (reason.status === 404) {
        return value;
      } else {
        throw reason;
      }
    };
  }
  function getDocsToPersist(docId, view, docIdsToChangesAndEmits) {
    return __async(this, null, function* () {
      const metaDocId = "_local/doc_" + docId;
      const defaultMetaDoc = { _id: metaDocId, keys: [] };
      const docData = docIdsToChangesAndEmits.get(docId);
      const indexableKeysToKeyValues = docData[0];
      const changes2 = docData[1];
      function getMetaDoc() {
        if (isGenOne(changes2)) {
          return Promise.resolve(defaultMetaDoc);
        }
        return view.db.get(metaDocId).catch(defaultsTo(defaultMetaDoc));
      }
      function getKeyValueDocs(metaDoc2) {
        if (!metaDoc2.keys.length) {
          return Promise.resolve({ rows: [] });
        }
        return view.db.allDocs({
          keys: metaDoc2.keys,
          include_docs: true
        });
      }
      function processKeyValueDocs(metaDoc2, kvDocsRes) {
        const kvDocs = [];
        const oldKeys = /* @__PURE__ */ new Set();
        for (const row of kvDocsRes.rows) {
          const doc = row.doc;
          if (!doc) {
            continue;
          }
          kvDocs.push(doc);
          oldKeys.add(doc._id);
          doc._deleted = !indexableKeysToKeyValues.has(doc._id);
          if (!doc._deleted) {
            const keyValue = indexableKeysToKeyValues.get(doc._id);
            if ("value" in keyValue) {
              doc.value = keyValue.value;
            }
          }
        }
        const newKeys = mapToKeysArray(indexableKeysToKeyValues);
        for (const key of newKeys) {
          if (!oldKeys.has(key)) {
            const kvDoc = {
              _id: key
            };
            const keyValue = indexableKeysToKeyValues.get(key);
            if ("value" in keyValue) {
              kvDoc.value = keyValue.value;
            }
            kvDocs.push(kvDoc);
          }
        }
        metaDoc2.keys = uniq(newKeys.concat(metaDoc2.keys));
        kvDocs.push(metaDoc2);
        return kvDocs;
      }
      const metaDoc = yield getMetaDoc();
      const keyValueDocs = yield getKeyValueDocs(metaDoc);
      return processKeyValueDocs(metaDoc, keyValueDocs);
    });
  }
  function updatePurgeSeq(view) {
    return view.sourceDB.get("_local/purges").then(function(res) {
      const purgeSeq = res.purgeSeq;
      return view.db.get("_local/purgeSeq").then(function(res2) {
        return res2._rev;
      }).catch(defaultsTo(void 0)).then(function(rev$$1) {
        return view.db.put({
          _id: "_local/purgeSeq",
          _rev: rev$$1,
          purgeSeq
        });
      });
    }).catch(function(err) {
      if (err.status !== 404) {
        throw err;
      }
    });
  }
  function saveKeyValues(view, docIdsToChangesAndEmits, seq) {
    var seqDocId = "_local/lastSeq";
    return view.db.get(seqDocId).catch(defaultsTo({ _id: seqDocId, seq: 0 })).then(function(lastSeqDoc) {
      var docIds = mapToKeysArray(docIdsToChangesAndEmits);
      return Promise.all(docIds.map(function(docId) {
        return getDocsToPersist(docId, view, docIdsToChangesAndEmits);
      })).then(function(listOfDocsToPersist) {
        var docsToPersist = listOfDocsToPersist.flat();
        lastSeqDoc.seq = seq;
        docsToPersist.push(lastSeqDoc);
        return view.db.bulkDocs({ docs: docsToPersist });
      }).then(() => updatePurgeSeq(view));
    });
  }
  function getQueue(view) {
    const viewName = typeof view === "string" ? view : view.name;
    let queue2 = persistentQueues[viewName];
    if (!queue2) {
      queue2 = persistentQueues[viewName] = new TaskQueue$1();
    }
    return queue2;
  }
  function updateView(view, opts) {
    return __async(this, null, function* () {
      return sequentialize(getQueue(view), function() {
        return updateViewInQueue(view, opts);
      })();
    });
  }
  function updateViewInQueue(view, opts) {
    return __async(this, null, function* () {
      let mapResults;
      let doc;
      let taskId;
      function emit(key, value) {
        const output = { id: doc._id, key: normalizeKey(key) };
        if (typeof value !== "undefined" && value !== null) {
          output.value = normalizeKey(value);
        }
        mapResults.push(output);
      }
      const mapFun = mapper2(view.mapFun, emit);
      let currentSeq = view.seq || 0;
      function createTask() {
        return view.sourceDB.info().then(function(info) {
          taskId = view.sourceDB.activeTasks.add({
            name: "view_indexing",
            total_items: info.update_seq - currentSeq
          });
        });
      }
      function processChange2(docIdsToChangesAndEmits, seq) {
        return function() {
          return saveKeyValues(view, docIdsToChangesAndEmits, seq);
        };
      }
      let indexed_docs = 0;
      const progress = {
        view: view.name,
        indexed_docs
      };
      view.sourceDB.emit("indexing", progress);
      const queue2 = new TaskQueue$1();
      function processNextBatch() {
        return __async(this, null, function* () {
          const response = yield view.sourceDB.changes({
            return_docs: true,
            conflicts: true,
            include_docs: true,
            style: "all_docs",
            since: currentSeq,
            limit: opts.changes_batch_size
          });
          const purges = yield getRecentPurges();
          return processBatch(response, purges);
        });
      }
      function getRecentPurges() {
        return view.db.get("_local/purgeSeq").then(function(res) {
          return res.purgeSeq;
        }).catch(defaultsTo(-1)).then(function(purgeSeq) {
          return view.sourceDB.get("_local/purges").then(function(res) {
            const recentPurges = res.purges.filter(function(purge, index) {
              return index > purgeSeq;
            }).map((purge) => purge.docId);
            const uniquePurges = recentPurges.filter(function(docId, index) {
              return recentPurges.indexOf(docId) === index;
            });
            return Promise.all(uniquePurges.map(function(docId) {
              return view.sourceDB.get(docId).then(function(doc2) {
                return { docId, doc: doc2 };
              }).catch(defaultsTo({ docId }));
            }));
          }).catch(defaultsTo([]));
        });
      }
      function processBatch(response, purges) {
        const results = response.results;
        if (!results.length && !purges.length) {
          return;
        }
        for (const purge of purges) {
          const index = results.findIndex(function(change) {
            return change.id === purge.docId;
          });
          if (index < 0) {
            const entry = {
              _id: purge.docId,
              doc: {
                _id: purge.docId,
                _deleted: 1
              },
              changes: []
            };
            if (purge.doc) {
              entry.doc = purge.doc;
              entry.changes.push({ rev: purge.doc._rev });
            }
            results.push(entry);
          }
        }
        const docIdsToChangesAndEmits = createDocIdsToChangesAndEmits(results);
        queue2.add(processChange2(docIdsToChangesAndEmits, currentSeq));
        indexed_docs = indexed_docs + results.length;
        const progress2 = {
          view: view.name,
          last_seq: response.last_seq,
          results_count: results.length,
          indexed_docs
        };
        view.sourceDB.emit("indexing", progress2);
        view.sourceDB.activeTasks.update(taskId, { completed_items: indexed_docs });
        if (results.length < opts.changes_batch_size) {
          return;
        }
        return processNextBatch();
      }
      function createDocIdsToChangesAndEmits(results) {
        const docIdsToChangesAndEmits = /* @__PURE__ */ new Map();
        for (const change of results) {
          if (change.doc._id[0] !== "_") {
            mapResults = [];
            doc = change.doc;
            if (!doc._deleted) {
              tryMap(view.sourceDB, mapFun, doc);
            }
            mapResults.sort(sortByKeyThenValue);
            const indexableKeysToKeyValues = createIndexableKeysToKeyValues(mapResults);
            docIdsToChangesAndEmits.set(change.doc._id, [
              indexableKeysToKeyValues,
              change.changes
            ]);
          }
          currentSeq = change.seq;
        }
        return docIdsToChangesAndEmits;
      }
      function createIndexableKeysToKeyValues(mapResults2) {
        const indexableKeysToKeyValues = /* @__PURE__ */ new Map();
        let lastKey;
        for (let i = 0, len = mapResults2.length; i < len; i++) {
          const emittedKeyValue = mapResults2[i];
          const complexKey = [emittedKeyValue.key, emittedKeyValue.id];
          if (i > 0 && collate(emittedKeyValue.key, lastKey) === 0) {
            complexKey.push(i);
          }
          indexableKeysToKeyValues.set(toIndexableString(complexKey), emittedKeyValue);
          lastKey = emittedKeyValue.key;
        }
        return indexableKeysToKeyValues;
      }
      try {
        yield createTask();
        yield processNextBatch();
        yield queue2.finish();
        view.seq = currentSeq;
        view.sourceDB.activeTasks.remove(taskId);
      } catch (error) {
        view.sourceDB.activeTasks.remove(taskId, error);
      }
    });
  }
  function reduceView(view, results, options) {
    if (options.group_level === 0) {
      delete options.group_level;
    }
    const shouldGroup = options.group || options.group_level;
    const reduceFun = reducer2(view.reduceFun);
    const groups = [];
    const lvl = isNaN(options.group_level) ? Number.POSITIVE_INFINITY : options.group_level;
    for (const result of results) {
      const last = groups[groups.length - 1];
      let groupKey = shouldGroup ? result.key : null;
      if (shouldGroup && Array.isArray(groupKey)) {
        groupKey = groupKey.slice(0, lvl);
      }
      if (last && collate(last.groupKey, groupKey) === 0) {
        last.keys.push([result.key, result.id]);
        last.values.push(result.value);
        continue;
      }
      groups.push({
        keys: [[result.key, result.id]],
        values: [result.value],
        groupKey
      });
    }
    results = [];
    for (const group of groups) {
      const reduceTry = tryReduce(view.sourceDB, reduceFun, group.keys, group.values, false);
      if (reduceTry.error && reduceTry.error instanceof BuiltInError) {
        throw reduceTry.error;
      }
      results.push({
        // CouchDB just sets the value to null if a non-built-in errors out
        value: reduceTry.error ? null : reduceTry.output,
        key: group.groupKey
      });
    }
    return { rows: sliceResults(results, options.limit, options.skip) };
  }
  function queryView(view, opts) {
    return sequentialize(getQueue(view), function() {
      return queryViewInQueue(view, opts);
    })();
  }
  function queryViewInQueue(view, opts) {
    return __async(this, null, function* () {
      let totalRows;
      const shouldReduce = view.reduceFun && opts.reduce !== false;
      const skip = opts.skip || 0;
      if (typeof opts.keys !== "undefined" && !opts.keys.length) {
        opts.limit = 0;
        delete opts.keys;
      }
      function fetchFromView(viewOpts) {
        return __async(this, null, function* () {
          viewOpts.include_docs = true;
          const res = yield view.db.allDocs(viewOpts);
          totalRows = res.total_rows;
          return res.rows.map(function(result) {
            if ("value" in result.doc && typeof result.doc.value === "object" && result.doc.value !== null) {
              const keys2 = Object.keys(result.doc.value).sort();
              const expectedKeys = ["id", "key", "value"];
              if (!(keys2 < expectedKeys || keys2 > expectedKeys)) {
                return result.doc.value;
              }
            }
            const parsedKeyAndDocId = parseIndexableString(result.doc._id);
            return {
              key: parsedKeyAndDocId[0],
              id: parsedKeyAndDocId[1],
              value: "value" in result.doc ? result.doc.value : null
            };
          });
        });
      }
      function onMapResultsReady(rows) {
        return __async(this, null, function* () {
          let finalResults;
          if (shouldReduce) {
            finalResults = reduceView(view, rows, opts);
          } else if (typeof opts.keys === "undefined") {
            finalResults = {
              total_rows: totalRows,
              offset: skip,
              rows
            };
          } else {
            finalResults = {
              total_rows: totalRows,
              offset: skip,
              rows: sliceResults(rows, opts.limit, opts.skip)
            };
          }
          if (opts.update_seq) {
            finalResults.update_seq = view.seq;
          }
          if (opts.include_docs) {
            const docIds = uniq(rows.map(rowToDocId));
            const allDocsRes = yield view.sourceDB.allDocs({
              keys: docIds,
              include_docs: true,
              conflicts: opts.conflicts,
              attachments: opts.attachments,
              binary: opts.binary
            });
            const docIdsToDocs = /* @__PURE__ */ new Map();
            for (const row of allDocsRes.rows) {
              docIdsToDocs.set(row.id, row.doc);
            }
            for (const row of rows) {
              const docId = rowToDocId(row);
              const doc = docIdsToDocs.get(docId);
              if (doc) {
                row.doc = doc;
              }
            }
          }
          return finalResults;
        });
      }
      if (typeof opts.keys !== "undefined") {
        const keys2 = opts.keys;
        const fetchPromises = keys2.map(function(key) {
          const viewOpts = {
            startkey: toIndexableString([key]),
            endkey: toIndexableString([key, {}])
          };
          if (opts.update_seq) {
            viewOpts.update_seq = true;
          }
          return fetchFromView(viewOpts);
        });
        const result = yield Promise.all(fetchPromises);
        const flattenedResult = result.flat();
        return onMapResultsReady(flattenedResult);
      } else {
        const viewOpts = {
          descending: opts.descending
        };
        if (opts.update_seq) {
          viewOpts.update_seq = true;
        }
        let startkey;
        let endkey;
        if ("start_key" in opts) {
          startkey = opts.start_key;
        }
        if ("startkey" in opts) {
          startkey = opts.startkey;
        }
        if ("end_key" in opts) {
          endkey = opts.end_key;
        }
        if ("endkey" in opts) {
          endkey = opts.endkey;
        }
        if (typeof startkey !== "undefined") {
          viewOpts.startkey = opts.descending ? toIndexableString([startkey, {}]) : toIndexableString([startkey]);
        }
        if (typeof endkey !== "undefined") {
          let inclusiveEnd = opts.inclusive_end !== false;
          if (opts.descending) {
            inclusiveEnd = !inclusiveEnd;
          }
          viewOpts.endkey = toIndexableString(
            inclusiveEnd ? [endkey, {}] : [endkey]
          );
        }
        if (typeof opts.key !== "undefined") {
          const keyStart = toIndexableString([opts.key]);
          const keyEnd = toIndexableString([opts.key, {}]);
          if (viewOpts.descending) {
            viewOpts.endkey = keyStart;
            viewOpts.startkey = keyEnd;
          } else {
            viewOpts.startkey = keyStart;
            viewOpts.endkey = keyEnd;
          }
        }
        if (!shouldReduce) {
          if (typeof opts.limit === "number") {
            viewOpts.limit = opts.limit;
          }
          viewOpts.skip = skip;
        }
        const result = yield fetchFromView(viewOpts);
        return onMapResultsReady(result);
      }
    });
  }
  function httpViewCleanup(db) {
    return __async(this, null, function* () {
      const response = yield db.fetch("_view_cleanup", {
        headers: new h({ "Content-Type": "application/json" }),
        method: "POST"
      });
      return response.json();
    });
  }
  function localViewCleanup(db) {
    return __async(this, null, function* () {
      try {
        const metaDoc = yield db.get("_local/" + localDocName2);
        const docsToViews = /* @__PURE__ */ new Map();
        for (const fullViewName of Object.keys(metaDoc.views)) {
          const parts = parseViewName(fullViewName);
          const designDocName = "_design/" + parts[0];
          const viewName = parts[1];
          let views = docsToViews.get(designDocName);
          if (!views) {
            views = /* @__PURE__ */ new Set();
            docsToViews.set(designDocName, views);
          }
          views.add(viewName);
        }
        const opts = {
          keys: mapToKeysArray(docsToViews),
          include_docs: true
        };
        const res = yield db.allDocs(opts);
        const viewsToStatus = {};
        for (const row of res.rows) {
          const ddocName = row.key.substring(8);
          for (const viewName of docsToViews.get(row.key)) {
            let fullViewName = ddocName + "/" + viewName;
            if (!metaDoc.views[fullViewName]) {
              fullViewName = viewName;
            }
            const viewDBNames = Object.keys(metaDoc.views[fullViewName]);
            const statusIsGood = row.doc && row.doc.views && row.doc.views[viewName];
            for (const viewDBName of viewDBNames) {
              viewsToStatus[viewDBName] = viewsToStatus[viewDBName] || statusIsGood;
            }
          }
        }
        const dbsToDelete = Object.keys(viewsToStatus).filter(function(viewDBName) {
          return !viewsToStatus[viewDBName];
        });
        const destroyPromises = dbsToDelete.map(function(viewDBName) {
          return sequentialize(getQueue(viewDBName), function() {
            return new db.constructor(viewDBName, db.__opts).destroy();
          })();
        });
        return Promise.all(destroyPromises).then(function() {
          return { ok: true };
        });
      } catch (err) {
        if (err.status === 404) {
          return { ok: true };
        } else {
          throw err;
        }
      }
    });
  }
  function queryPromised(db, fun, opts) {
    return __async(this, null, function* () {
      if (typeof db._query === "function") {
        return customQuery(db, fun, opts);
      }
      if (isRemote(db)) {
        return httpQuery(db, fun, opts);
      }
      const updateViewOpts = {
        changes_batch_size: db.__opts.view_update_changes_batch_size || CHANGES_BATCH_SIZE$1
      };
      if (typeof fun !== "string") {
        checkQueryParseError(opts, fun);
        tempViewQueue.add(function() {
          return __async(this, null, function* () {
            const view = yield createView(
              /* sourceDB */
              db,
              /* viewName */
              "temp_view/temp_view",
              /* mapFun */
              fun.map,
              /* reduceFun */
              fun.reduce,
              /* temporary */
              true,
              /* localDocName */
              localDocName2
            );
            return fin(
              updateView(view, updateViewOpts).then(
                function() {
                  return queryView(view, opts);
                }
              ),
              function() {
                return view.db.destroy();
              }
            );
          });
        });
        return tempViewQueue.finish();
      } else {
        const fullViewName = fun;
        const parts = parseViewName(fullViewName);
        const designDocName = parts[0];
        const viewName = parts[1];
        const doc = yield db.get("_design/" + designDocName);
        fun = doc.views && doc.views[viewName];
        if (!fun) {
          throw new NotFoundError(`ddoc ${doc._id} has no view named ${viewName}`);
        }
        ddocValidator2(doc, viewName);
        checkQueryParseError(opts, fun);
        const view = yield createView(
          /* sourceDB */
          db,
          /* viewName */
          fullViewName,
          /* mapFun */
          fun.map,
          /* reduceFun */
          fun.reduce,
          /* temporary */
          false,
          /* localDocName */
          localDocName2
        );
        if (opts.stale === "ok" || opts.stale === "update_after") {
          if (opts.stale === "update_after") {
            nextTick(function() {
              updateView(view, updateViewOpts);
            });
          }
          return queryView(view, opts);
        } else {
          yield updateView(view, updateViewOpts);
          return queryView(view, opts);
        }
      }
    });
  }
  function abstractQuery(fun, opts, callback) {
    const db = this;
    if (typeof opts === "function") {
      callback = opts;
      opts = {};
    }
    opts = opts ? coerceOptions(opts) : {};
    if (typeof fun === "function") {
      fun = { map: fun };
    }
    const promise = Promise.resolve().then(function() {
      return queryPromised(db, fun, opts);
    });
    promisedCallback(promise, callback);
    return promise;
  }
  const abstractViewCleanup = callbackify(function() {
    const db = this;
    if (typeof db._viewCleanup === "function") {
      return customViewCleanup(db);
    }
    if (isRemote(db)) {
      return httpViewCleanup(db);
    }
    return localViewCleanup(db);
  });
  return {
    query: abstractQuery,
    viewCleanup: abstractViewCleanup
  };
}
var builtInReduce = {
  _sum: function(keys2, values) {
    return sum(values);
  },
  _count: function(keys2, values) {
    return values.length;
  },
  _stats: function(keys2, values) {
    function sumsqr(values2) {
      var _sumsqr = 0;
      for (var i = 0, len = values2.length; i < len; i++) {
        var num = values2[i];
        _sumsqr += num * num;
      }
      return _sumsqr;
    }
    return {
      sum: sum(values),
      min: Math.min.apply(null, values),
      max: Math.max.apply(null, values),
      count: values.length,
      sumsqr: sumsqr(values)
    };
  }
};
function getBuiltIn(reduceFunString) {
  if (/^_sum/.test(reduceFunString)) {
    return builtInReduce._sum;
  } else if (/^_count/.test(reduceFunString)) {
    return builtInReduce._count;
  } else if (/^_stats/.test(reduceFunString)) {
    return builtInReduce._stats;
  } else if (/^_/.test(reduceFunString)) {
    throw new Error(reduceFunString + " is not a supported reduce function.");
  }
}
function mapper(mapFun, emit) {
  if (typeof mapFun === "function" && mapFun.length === 2) {
    var origMap = mapFun;
    return function(doc) {
      return origMap(doc, emit);
    };
  } else {
    return evalFunctionWithEval(mapFun.toString(), emit);
  }
}
function reducer(reduceFun) {
  var reduceFunString = reduceFun.toString();
  var builtIn = getBuiltIn(reduceFunString);
  if (builtIn) {
    return builtIn;
  } else {
    return evalFunctionWithEval(reduceFunString);
  }
}
function ddocValidator(ddoc, viewName) {
  var fun = ddoc.views && ddoc.views[viewName];
  if (typeof fun.map !== "string") {
    throw new NotFoundError("ddoc " + ddoc._id + " has no string view named " + viewName + ", instead found object of type: " + typeof fun.map);
  }
}
var localDocName = "mrviews";
var abstract = createAbstractMapReduce(localDocName, mapper, reducer, ddocValidator);
function query(fun, opts, callback) {
  return abstract.query.call(this, fun, opts, callback);
}
function viewCleanup(callback) {
  return abstract.viewCleanup.call(this, callback);
}
var mapreduce = {
  query,
  viewCleanup
};
function fileHasChanged(localDoc, remoteDoc, filename) {
  return !localDoc._attachments || !localDoc._attachments[filename] || localDoc._attachments[filename].digest !== remoteDoc._attachments[filename].digest;
}
function getDocAttachments(db, doc) {
  var filenames = Object.keys(doc._attachments);
  return Promise.all(filenames.map(function(filename) {
    return db.getAttachment(doc._id, filename, { rev: doc._rev });
  }));
}
function getDocAttachmentsFromTargetOrSource(target, src, doc) {
  var doCheckForLocalAttachments = isRemote(src) && !isRemote(target);
  var filenames = Object.keys(doc._attachments);
  if (!doCheckForLocalAttachments) {
    return getDocAttachments(src, doc);
  }
  return target.get(doc._id).then(function(localDoc) {
    return Promise.all(filenames.map(function(filename) {
      if (fileHasChanged(localDoc, doc, filename)) {
        return src.getAttachment(doc._id, filename);
      }
      return target.getAttachment(localDoc._id, filename);
    }));
  }).catch(function(error) {
    if (error.status !== 404) {
      throw error;
    }
    return getDocAttachments(src, doc);
  });
}
function createBulkGetOpts(diffs) {
  var requests = [];
  Object.keys(diffs).forEach(function(id) {
    var missingRevs = diffs[id].missing;
    missingRevs.forEach(function(missingRev) {
      requests.push({
        id,
        rev: missingRev
      });
    });
  });
  return {
    docs: requests,
    revs: true,
    latest: true
  };
}
function getDocs(src, target, diffs, state) {
  diffs = clone(diffs);
  var resultDocs = [], ok = true;
  function getAllDocs() {
    var bulkGetOpts = createBulkGetOpts(diffs);
    if (!bulkGetOpts.docs.length) {
      return;
    }
    return src.bulkGet(bulkGetOpts).then(function(bulkGetResponse) {
      if (state.cancelled) {
        throw new Error("cancelled");
      }
      return Promise.all(bulkGetResponse.results.map(function(bulkGetInfo) {
        return Promise.all(bulkGetInfo.docs.map(function(doc) {
          var remoteDoc = doc.ok;
          if (doc.error) {
            ok = false;
          }
          if (!remoteDoc || !remoteDoc._attachments) {
            return remoteDoc;
          }
          return getDocAttachmentsFromTargetOrSource(target, src, remoteDoc).then((attachments) => {
            var filenames = Object.keys(remoteDoc._attachments);
            attachments.forEach(function(attachment, i) {
              var att = remoteDoc._attachments[filenames[i]];
              delete att.stub;
              delete att.length;
              att.data = attachment;
            });
            return remoteDoc;
          });
        }));
      })).then(function(results) {
        resultDocs = resultDocs.concat(results.flat().filter(Boolean));
      });
    });
  }
  function returnResult() {
    return { ok, docs: resultDocs };
  }
  return Promise.resolve().then(getAllDocs).then(returnResult);
}
var CHECKPOINT_VERSION = 1;
var REPLICATOR = "pouchdb";
var CHECKPOINT_HISTORY_SIZE = 5;
var LOWEST_SEQ = 0;
function updateCheckpoint(db, id, checkpoint, session, returnValue) {
  return db.get(id).catch(function(err) {
    if (err.status === 404) {
      if (db.adapter === "http" || db.adapter === "https") {
        explainError(
          404,
          "PouchDB is just checking if a remote checkpoint exists."
        );
      }
      return {
        session_id: session,
        _id: id,
        history: [],
        replicator: REPLICATOR,
        version: CHECKPOINT_VERSION
      };
    }
    throw err;
  }).then(function(doc) {
    if (returnValue.cancelled) {
      return;
    }
    if (doc.last_seq === checkpoint) {
      return;
    }
    doc.history = (doc.history || []).filter(function(item) {
      return item.session_id !== session;
    });
    doc.history.unshift({
      last_seq: checkpoint,
      session_id: session
    });
    doc.history = doc.history.slice(0, CHECKPOINT_HISTORY_SIZE);
    doc.version = CHECKPOINT_VERSION;
    doc.replicator = REPLICATOR;
    doc.session_id = session;
    doc.last_seq = checkpoint;
    return db.put(doc).catch(function(err) {
      if (err.status === 409) {
        return updateCheckpoint(db, id, checkpoint, session, returnValue);
      }
      throw err;
    });
  });
}
var CheckpointerInternal = class {
  constructor(src, target, id, returnValue, opts = {
    writeSourceCheckpoint: true,
    writeTargetCheckpoint: true
  }) {
    this.src = src;
    this.target = target;
    this.id = id;
    this.returnValue = returnValue;
    this.opts = opts;
    if (typeof opts.writeSourceCheckpoint === "undefined") {
      opts.writeSourceCheckpoint = true;
    }
    if (typeof opts.writeTargetCheckpoint === "undefined") {
      opts.writeTargetCheckpoint = true;
    }
  }
  writeCheckpoint(checkpoint, session) {
    var self2 = this;
    return this.updateTarget(checkpoint, session).then(function() {
      return self2.updateSource(checkpoint, session);
    });
  }
  updateTarget(checkpoint, session) {
    if (this.opts.writeTargetCheckpoint) {
      return updateCheckpoint(
        this.target,
        this.id,
        checkpoint,
        session,
        this.returnValue
      );
    } else {
      return Promise.resolve(true);
    }
  }
  updateSource(checkpoint, session) {
    if (this.opts.writeSourceCheckpoint) {
      var self2 = this;
      return updateCheckpoint(
        this.src,
        this.id,
        checkpoint,
        session,
        this.returnValue
      ).catch(function(err) {
        if (isForbiddenError(err)) {
          self2.opts.writeSourceCheckpoint = false;
          return true;
        }
        throw err;
      });
    } else {
      return Promise.resolve(true);
    }
  }
  getCheckpoint() {
    var self2 = this;
    if (!self2.opts.writeSourceCheckpoint && !self2.opts.writeTargetCheckpoint) {
      return Promise.resolve(LOWEST_SEQ);
    }
    if (self2.opts && self2.opts.writeSourceCheckpoint && !self2.opts.writeTargetCheckpoint) {
      return self2.src.get(self2.id).then(function(sourceDoc) {
        return sourceDoc.last_seq || LOWEST_SEQ;
      }).catch(function(err) {
        if (err.status !== 404) {
          throw err;
        }
        return LOWEST_SEQ;
      });
    }
    return self2.target.get(self2.id).then(function(targetDoc) {
      if (self2.opts && self2.opts.writeTargetCheckpoint && !self2.opts.writeSourceCheckpoint) {
        return targetDoc.last_seq || LOWEST_SEQ;
      }
      return self2.src.get(self2.id).then(function(sourceDoc) {
        if (targetDoc.version !== sourceDoc.version) {
          return LOWEST_SEQ;
        }
        var version2;
        if (targetDoc.version) {
          version2 = targetDoc.version.toString();
        } else {
          version2 = "undefined";
        }
        if (version2 in comparisons) {
          return comparisons[version2](targetDoc, sourceDoc);
        }
        return LOWEST_SEQ;
      }, function(err) {
        if (err.status === 404 && targetDoc.last_seq) {
          return self2.src.put({
            _id: self2.id,
            last_seq: LOWEST_SEQ
          }).then(function() {
            return LOWEST_SEQ;
          }, function(err2) {
            if (isForbiddenError(err2)) {
              self2.opts.writeSourceCheckpoint = false;
              return targetDoc.last_seq;
            }
            return LOWEST_SEQ;
          });
        }
        throw err;
      });
    }).catch(function(err) {
      if (err.status !== 404) {
        throw err;
      }
      return LOWEST_SEQ;
    });
  }
};
var comparisons = {
  "undefined": function(targetDoc, sourceDoc) {
    if (collate(targetDoc.last_seq, sourceDoc.last_seq) === 0) {
      return sourceDoc.last_seq;
    }
    return 0;
  },
  "1": function(targetDoc, sourceDoc) {
    return compareReplicationLogs(sourceDoc, targetDoc).last_seq;
  }
};
function compareReplicationLogs(srcDoc, tgtDoc) {
  if (srcDoc.session_id === tgtDoc.session_id) {
    return {
      last_seq: srcDoc.last_seq,
      history: srcDoc.history
    };
  }
  return compareReplicationHistory(srcDoc.history, tgtDoc.history);
}
function compareReplicationHistory(sourceHistory, targetHistory) {
  var S = sourceHistory[0];
  var sourceRest = sourceHistory.slice(1);
  var T = targetHistory[0];
  var targetRest = targetHistory.slice(1);
  if (!S || targetHistory.length === 0) {
    return {
      last_seq: LOWEST_SEQ,
      history: []
    };
  }
  var sourceId = S.session_id;
  if (hasSessionId(sourceId, targetHistory)) {
    return {
      last_seq: S.last_seq,
      history: sourceHistory
    };
  }
  var targetId = T.session_id;
  if (hasSessionId(targetId, sourceRest)) {
    return {
      last_seq: T.last_seq,
      history: targetRest
    };
  }
  return compareReplicationHistory(sourceRest, targetRest);
}
function hasSessionId(sessionId, history) {
  var props = history[0];
  var rest = history.slice(1);
  if (!sessionId || history.length === 0) {
    return false;
  }
  if (sessionId === props.session_id) {
    return true;
  }
  return hasSessionId(sessionId, rest);
}
function isForbiddenError(err) {
  return typeof err.status === "number" && Math.floor(err.status / 100) === 4;
}
function Checkpointer(src, target, id, returnValue, opts) {
  if (!(this instanceof CheckpointerInternal)) {
    return new CheckpointerInternal(src, target, id, returnValue, opts);
  }
  return Checkpointer;
}
var STARTING_BACK_OFF = 0;
function backOff(opts, returnValue, error, callback) {
  if (opts.retry === false) {
    returnValue.emit("error", error);
    returnValue.removeAllListeners();
    return;
  }
  if (typeof opts.back_off_function !== "function") {
    opts.back_off_function = defaultBackOff;
  }
  returnValue.emit("requestError", error);
  if (returnValue.state === "active" || returnValue.state === "pending") {
    returnValue.emit("paused", error);
    returnValue.state = "stopped";
    var backOffSet = function backoffTimeSet() {
      opts.current_back_off = STARTING_BACK_OFF;
    };
    var removeBackOffSetter = function removeBackOffTimeSet() {
      returnValue.removeListener("active", backOffSet);
    };
    returnValue.once("paused", removeBackOffSetter);
    returnValue.once("active", backOffSet);
  }
  opts.current_back_off = opts.current_back_off || STARTING_BACK_OFF;
  opts.current_back_off = opts.back_off_function(opts.current_back_off);
  setTimeout(callback, opts.current_back_off);
}
function sortObjectPropertiesByKey(queryParams) {
  return Object.keys(queryParams).sort(collate).reduce(function(result, key) {
    result[key] = queryParams[key];
    return result;
  }, {});
}
function generateReplicationId(src, target, opts) {
  var docIds = opts.doc_ids ? opts.doc_ids.sort(collate) : "";
  var filterFun = opts.filter ? opts.filter.toString() : "";
  var queryParams = "";
  var filterViewName = "";
  var selector = "";
  if (opts.selector) {
    selector = JSON.stringify(opts.selector);
  }
  if (opts.filter && opts.query_params) {
    queryParams = JSON.stringify(sortObjectPropertiesByKey(opts.query_params));
  }
  if (opts.filter && opts.filter === "_view") {
    filterViewName = opts.view.toString();
  }
  return Promise.all([src.id(), target.id()]).then(function(res) {
    var queryData = res[0] + res[1] + filterFun + filterViewName + queryParams + docIds + selector;
    return new Promise(function(resolve) {
      binaryMd5(queryData, resolve);
    });
  }).then(function(md5sum) {
    md5sum = md5sum.replace(/\//g, ".").replace(/\+/g, "_");
    return "_local/" + md5sum;
  });
}
function replicate(src, target, opts, returnValue, result) {
  var batches = [];
  var currentBatch;
  var pendingBatch = {
    seq: 0,
    changes: [],
    docs: []
  };
  var writingCheckpoint = false;
  var changesCompleted = false;
  var replicationCompleted = false;
  var initial_last_seq = 0;
  var last_seq = 0;
  var continuous = opts.continuous || opts.live || false;
  var batch_size = opts.batch_size || 100;
  var batches_limit = opts.batches_limit || 10;
  var style = opts.style || "all_docs";
  var changesPending = false;
  var doc_ids = opts.doc_ids;
  var selector = opts.selector;
  var repId;
  var checkpointer;
  var changedDocs = [];
  var session = uuid();
  var taskId;
  result = result || {
    ok: true,
    start_time: (/* @__PURE__ */ new Date()).toISOString(),
    docs_read: 0,
    docs_written: 0,
    doc_write_failures: 0,
    errors: []
  };
  var changesOpts = {};
  returnValue.ready(src, target);
  function initCheckpointer() {
    if (checkpointer) {
      return Promise.resolve();
    }
    return generateReplicationId(src, target, opts).then(function(res) {
      repId = res;
      var checkpointOpts = {};
      if (opts.checkpoint === false) {
        checkpointOpts = { writeSourceCheckpoint: false, writeTargetCheckpoint: false };
      } else if (opts.checkpoint === "source") {
        checkpointOpts = { writeSourceCheckpoint: true, writeTargetCheckpoint: false };
      } else if (opts.checkpoint === "target") {
        checkpointOpts = { writeSourceCheckpoint: false, writeTargetCheckpoint: true };
      } else {
        checkpointOpts = { writeSourceCheckpoint: true, writeTargetCheckpoint: true };
      }
      checkpointer = new Checkpointer(src, target, repId, returnValue, checkpointOpts);
    });
  }
  function writeDocs() {
    changedDocs = [];
    if (currentBatch.docs.length === 0) {
      return;
    }
    var docs = currentBatch.docs;
    var bulkOpts = { timeout: opts.timeout };
    return target.bulkDocs({ docs, new_edits: false }, bulkOpts).then(function(res) {
      if (returnValue.cancelled) {
        completeReplication();
        throw new Error("cancelled");
      }
      var errorsById = /* @__PURE__ */ Object.create(null);
      res.forEach(function(res2) {
        if (res2.error) {
          errorsById[res2.id] = res2;
        }
      });
      var errorsNo = Object.keys(errorsById).length;
      result.doc_write_failures += errorsNo;
      result.docs_written += docs.length - errorsNo;
      docs.forEach(function(doc) {
        var error = errorsById[doc._id];
        if (error) {
          result.errors.push(error);
          var errorName = (error.name || "").toLowerCase();
          if (errorName === "unauthorized" || errorName === "forbidden") {
            returnValue.emit("denied", clone(error));
          } else {
            throw error;
          }
        } else {
          changedDocs.push(doc);
        }
      });
    }, function(err) {
      result.doc_write_failures += docs.length;
      throw err;
    });
  }
  function finishBatch() {
    if (currentBatch.error) {
      throw new Error("There was a problem getting docs.");
    }
    result.last_seq = last_seq = currentBatch.seq;
    var outResult = clone(result);
    if (changedDocs.length) {
      outResult.docs = changedDocs;
      if (typeof currentBatch.pending === "number") {
        outResult.pending = currentBatch.pending;
        delete currentBatch.pending;
      }
      returnValue.emit("change", outResult);
    }
    writingCheckpoint = true;
    src.info().then(function(info) {
      var task = src.activeTasks.get(taskId);
      if (!currentBatch || !task) {
        return;
      }
      var completed = task.completed_items || 0;
      var total_items = parseInt(info.update_seq, 10) - parseInt(initial_last_seq, 10);
      src.activeTasks.update(taskId, {
        completed_items: completed + currentBatch.changes.length,
        total_items
      });
    });
    return checkpointer.writeCheckpoint(
      currentBatch.seq,
      session
    ).then(function() {
      returnValue.emit("checkpoint", { "checkpoint": currentBatch.seq });
      writingCheckpoint = false;
      if (returnValue.cancelled) {
        completeReplication();
        throw new Error("cancelled");
      }
      currentBatch = void 0;
      getChanges();
    }).catch(function(err) {
      onCheckpointError(err);
      throw err;
    });
  }
  function getDiffs() {
    var diff = {};
    currentBatch.changes.forEach(function(change) {
      returnValue.emit("checkpoint", { "revs_diff": change });
      if (change.id === "_user/") {
        return;
      }
      diff[change.id] = change.changes.map(function(x) {
        return x.rev;
      });
    });
    return target.revsDiff(diff).then(function(diffs) {
      if (returnValue.cancelled) {
        completeReplication();
        throw new Error("cancelled");
      }
      currentBatch.diffs = diffs;
    });
  }
  function getBatchDocs() {
    return getDocs(src, target, currentBatch.diffs, returnValue).then(function(got) {
      currentBatch.error = !got.ok;
      got.docs.forEach(function(doc) {
        delete currentBatch.diffs[doc._id];
        result.docs_read++;
        currentBatch.docs.push(doc);
      });
    });
  }
  function startNextBatch() {
    if (returnValue.cancelled || currentBatch) {
      return;
    }
    if (batches.length === 0) {
      processPendingBatch(true);
      return;
    }
    currentBatch = batches.shift();
    returnValue.emit("checkpoint", { "start_next_batch": currentBatch.seq });
    getDiffs().then(getBatchDocs).then(writeDocs).then(finishBatch).then(startNextBatch).catch(function(err) {
      abortReplication("batch processing terminated with error", err);
    });
  }
  function processPendingBatch(immediate) {
    if (pendingBatch.changes.length === 0) {
      if (batches.length === 0 && !currentBatch) {
        if (continuous && changesOpts.live || changesCompleted) {
          returnValue.state = "pending";
          returnValue.emit("paused");
        }
        if (changesCompleted) {
          completeReplication();
        }
      }
      return;
    }
    if (immediate || changesCompleted || pendingBatch.changes.length >= batch_size) {
      batches.push(pendingBatch);
      pendingBatch = {
        seq: 0,
        changes: [],
        docs: []
      };
      if (returnValue.state === "pending" || returnValue.state === "stopped") {
        returnValue.state = "active";
        returnValue.emit("active");
      }
      startNextBatch();
    }
  }
  function abortReplication(reason, err) {
    if (replicationCompleted) {
      return;
    }
    if (!err.message) {
      err.message = reason;
    }
    result.ok = false;
    result.status = "aborting";
    batches = [];
    pendingBatch = {
      seq: 0,
      changes: [],
      docs: []
    };
    completeReplication(err);
  }
  function completeReplication(fatalError) {
    if (replicationCompleted) {
      return;
    }
    if (returnValue.cancelled) {
      result.status = "cancelled";
      if (writingCheckpoint) {
        return;
      }
    }
    result.status = result.status || "complete";
    result.end_time = (/* @__PURE__ */ new Date()).toISOString();
    result.last_seq = last_seq;
    replicationCompleted = true;
    src.activeTasks.remove(taskId, fatalError);
    if (fatalError) {
      fatalError = createError(fatalError);
      fatalError.result = result;
      var errorName = (fatalError.name || "").toLowerCase();
      if (errorName === "unauthorized" || errorName === "forbidden") {
        returnValue.emit("error", fatalError);
        returnValue.removeAllListeners();
      } else {
        backOff(opts, returnValue, fatalError, function() {
          replicate(src, target, opts, returnValue);
        });
      }
    } else {
      returnValue.emit("complete", result);
      returnValue.removeAllListeners();
    }
  }
  function onChange(change, pending, lastSeq) {
    if (returnValue.cancelled) {
      return completeReplication();
    }
    if (typeof pending === "number") {
      pendingBatch.pending = pending;
    }
    var filter2 = filterChange(opts)(change);
    if (!filter2) {
      var task = src.activeTasks.get(taskId);
      if (task) {
        var completed = task.completed_items || 0;
        src.activeTasks.update(taskId, { completed_items: ++completed });
      }
      return;
    }
    pendingBatch.seq = change.seq || lastSeq;
    pendingBatch.changes.push(change);
    returnValue.emit("checkpoint", { "pending_batch": pendingBatch.seq });
    nextTick(function() {
      processPendingBatch(batches.length === 0 && changesOpts.live);
    });
  }
  function onChangesComplete(changes2) {
    changesPending = false;
    if (returnValue.cancelled) {
      return completeReplication();
    }
    if (changes2.results.length > 0) {
      changesOpts.since = changes2.results[changes2.results.length - 1].seq;
      getChanges();
      processPendingBatch(true);
    } else {
      var complete = function() {
        if (continuous) {
          changesOpts.live = true;
          getChanges();
        } else {
          changesCompleted = true;
        }
        processPendingBatch(true);
      };
      if (!currentBatch && changes2.results.length === 0) {
        writingCheckpoint = true;
        checkpointer.writeCheckpoint(
          changes2.last_seq,
          session
        ).then(function() {
          writingCheckpoint = false;
          result.last_seq = last_seq = changes2.last_seq;
          if (returnValue.cancelled) {
            completeReplication();
            throw new Error("cancelled");
          } else {
            complete();
          }
        }).catch(onCheckpointError);
      } else {
        complete();
      }
    }
  }
  function onChangesError(err) {
    changesPending = false;
    if (returnValue.cancelled) {
      return completeReplication();
    }
    abortReplication("changes rejected", err);
  }
  function getChanges() {
    if (!(!changesPending && !changesCompleted && batches.length < batches_limit)) {
      return;
    }
    changesPending = true;
    function abortChanges() {
      changes2.cancel();
    }
    function removeListener() {
      returnValue.removeListener("cancel", abortChanges);
    }
    if (returnValue._changes) {
      returnValue.removeListener("cancel", returnValue._abortChanges);
      returnValue._changes.cancel();
    }
    returnValue.once("cancel", abortChanges);
    var changes2 = src.changes(changesOpts).on("change", onChange);
    changes2.then(removeListener, removeListener);
    changes2.then(onChangesComplete).catch(onChangesError);
    if (opts.retry) {
      returnValue._changes = changes2;
      returnValue._abortChanges = abortChanges;
    }
  }
  function createTask(checkpoint) {
    return src.info().then(function(info) {
      var total_items = typeof opts.since === "undefined" ? parseInt(info.update_seq, 10) - parseInt(checkpoint, 10) : parseInt(info.update_seq, 10);
      taskId = src.activeTasks.add({
        name: `${continuous ? "continuous " : ""}replication from ${info.db_name}`,
        total_items
      });
      return checkpoint;
    });
  }
  function startChanges() {
    initCheckpointer().then(function() {
      if (returnValue.cancelled) {
        completeReplication();
        return;
      }
      return checkpointer.getCheckpoint().then(createTask).then(function(checkpoint) {
        last_seq = checkpoint;
        initial_last_seq = checkpoint;
        changesOpts = {
          since: last_seq,
          limit: batch_size,
          batch_size,
          style,
          doc_ids,
          selector,
          return_docs: true
          // required so we know when we're done
        };
        if (opts.filter) {
          if (typeof opts.filter !== "string") {
            changesOpts.include_docs = true;
          } else {
            changesOpts.filter = opts.filter;
          }
        }
        if ("heartbeat" in opts) {
          changesOpts.heartbeat = opts.heartbeat;
        }
        if ("timeout" in opts) {
          changesOpts.timeout = opts.timeout;
        }
        if (opts.query_params) {
          changesOpts.query_params = opts.query_params;
        }
        if (opts.view) {
          changesOpts.view = opts.view;
        }
        getChanges();
      });
    }).catch(function(err) {
      abortReplication("getCheckpoint rejected with ", err);
    });
  }
  function onCheckpointError(err) {
    writingCheckpoint = false;
    abortReplication("writeCheckpoint completed with error", err);
  }
  if (returnValue.cancelled) {
    completeReplication();
    return;
  }
  if (!returnValue._addedListeners) {
    returnValue.once("cancel", completeReplication);
    if (typeof opts.complete === "function") {
      returnValue.once("error", opts.complete);
      returnValue.once("complete", function(result2) {
        opts.complete(null, result2);
      });
    }
    returnValue._addedListeners = true;
  }
  if (typeof opts.since === "undefined") {
    startChanges();
  } else {
    initCheckpointer().then(function() {
      writingCheckpoint = true;
      return checkpointer.writeCheckpoint(opts.since, session);
    }).then(function() {
      writingCheckpoint = false;
      if (returnValue.cancelled) {
        completeReplication();
        return;
      }
      last_seq = opts.since;
      startChanges();
    }).catch(onCheckpointError);
  }
}
var Replication = class extends import_events.default {
  constructor() {
    super();
    this.cancelled = false;
    this.state = "pending";
    const promise = new Promise((fulfill, reject) => {
      this.once("complete", fulfill);
      this.once("error", reject);
    });
    this.then = function(resolve, reject) {
      return promise.then(resolve, reject);
    };
    this.catch = function(reject) {
      return promise.catch(reject);
    };
    this.catch(function() {
    });
  }
  cancel() {
    this.cancelled = true;
    this.state = "cancelled";
    this.emit("cancel");
  }
  ready(src, target) {
    if (this._readyCalled) {
      return;
    }
    this._readyCalled = true;
    const onDestroy = () => {
      this.cancel();
    };
    src.once("destroyed", onDestroy);
    target.once("destroyed", onDestroy);
    function cleanup() {
      src.removeListener("destroyed", onDestroy);
      target.removeListener("destroyed", onDestroy);
    }
    this.once("complete", cleanup);
    this.once("error", cleanup);
  }
};
function toPouch(db, opts) {
  var PouchConstructor = opts.PouchConstructor;
  if (typeof db === "string") {
    return new PouchConstructor(db, opts);
  } else {
    return db;
  }
}
function replicateWrapper(src, target, opts, callback) {
  if (typeof opts === "function") {
    callback = opts;
    opts = {};
  }
  if (typeof opts === "undefined") {
    opts = {};
  }
  if (opts.doc_ids && !Array.isArray(opts.doc_ids)) {
    throw createError(
      BAD_REQUEST,
      "`doc_ids` filter parameter is not a list."
    );
  }
  opts.complete = callback;
  opts = clone(opts);
  opts.continuous = opts.continuous || opts.live;
  opts.retry = "retry" in opts ? opts.retry : false;
  opts.PouchConstructor = opts.PouchConstructor || this;
  var replicateRet = new Replication(opts);
  var srcPouch = toPouch(src, opts);
  var targetPouch = toPouch(target, opts);
  replicate(srcPouch, targetPouch, opts, replicateRet);
  return replicateRet;
}
function sync(src, target, opts, callback) {
  if (typeof opts === "function") {
    callback = opts;
    opts = {};
  }
  if (typeof opts === "undefined") {
    opts = {};
  }
  opts = clone(opts);
  opts.PouchConstructor = opts.PouchConstructor || this;
  src = toPouch(src, opts);
  target = toPouch(target, opts);
  return new Sync(src, target, opts, callback);
}
var Sync = class extends import_events.default {
  constructor(src, target, opts, callback) {
    super();
    this.canceled = false;
    const optsPush = opts.push ? Object.assign({}, opts, opts.push) : opts;
    const optsPull = opts.pull ? Object.assign({}, opts, opts.pull) : opts;
    this.push = replicateWrapper(src, target, optsPush);
    this.pull = replicateWrapper(target, src, optsPull);
    this.pushPaused = true;
    this.pullPaused = true;
    const pullChange = (change) => {
      this.emit("change", {
        direction: "pull",
        change
      });
    };
    const pushChange = (change) => {
      this.emit("change", {
        direction: "push",
        change
      });
    };
    const pushDenied = (doc) => {
      this.emit("denied", {
        direction: "push",
        doc
      });
    };
    const pullDenied = (doc) => {
      this.emit("denied", {
        direction: "pull",
        doc
      });
    };
    const pushPaused = () => {
      this.pushPaused = true;
      if (this.pullPaused) {
        this.emit("paused");
      }
    };
    const pullPaused = () => {
      this.pullPaused = true;
      if (this.pushPaused) {
        this.emit("paused");
      }
    };
    const pushActive = () => {
      this.pushPaused = false;
      if (this.pullPaused) {
        this.emit("active", {
          direction: "push"
        });
      }
    };
    const pullActive = () => {
      this.pullPaused = false;
      if (this.pushPaused) {
        this.emit("active", {
          direction: "pull"
        });
      }
    };
    let removed = {};
    const removeAll = (type) => {
      return (event, func) => {
        const isChange = event === "change" && (func === pullChange || func === pushChange);
        const isDenied = event === "denied" && (func === pullDenied || func === pushDenied);
        const isPaused = event === "paused" && (func === pullPaused || func === pushPaused);
        const isActive = event === "active" && (func === pullActive || func === pushActive);
        if (isChange || isDenied || isPaused || isActive) {
          if (!(event in removed)) {
            removed[event] = {};
          }
          removed[event][type] = true;
          if (Object.keys(removed[event]).length === 2) {
            this.removeAllListeners(event);
          }
        }
      };
    };
    if (opts.live) {
      this.push.on("complete", this.pull.cancel.bind(this.pull));
      this.pull.on("complete", this.push.cancel.bind(this.push));
    }
    function addOneListener(ee, event, listener) {
      if (ee.listeners(event).indexOf(listener) == -1) {
        ee.on(event, listener);
      }
    }
    this.on("newListener", function(event) {
      if (event === "change") {
        addOneListener(this.pull, "change", pullChange);
        addOneListener(this.push, "change", pushChange);
      } else if (event === "denied") {
        addOneListener(this.pull, "denied", pullDenied);
        addOneListener(this.push, "denied", pushDenied);
      } else if (event === "active") {
        addOneListener(this.pull, "active", pullActive);
        addOneListener(this.push, "active", pushActive);
      } else if (event === "paused") {
        addOneListener(this.pull, "paused", pullPaused);
        addOneListener(this.push, "paused", pushPaused);
      }
    });
    this.on("removeListener", function(event) {
      if (event === "change") {
        this.pull.removeListener("change", pullChange);
        this.push.removeListener("change", pushChange);
      } else if (event === "denied") {
        this.pull.removeListener("denied", pullDenied);
        this.push.removeListener("denied", pushDenied);
      } else if (event === "active") {
        this.pull.removeListener("active", pullActive);
        this.push.removeListener("active", pushActive);
      } else if (event === "paused") {
        this.pull.removeListener("paused", pullPaused);
        this.push.removeListener("paused", pushPaused);
      }
    });
    this.pull.on("removeListener", removeAll("pull"));
    this.push.on("removeListener", removeAll("push"));
    const promise = Promise.all([
      this.push,
      this.pull
    ]).then((resp) => {
      const out = {
        push: resp[0],
        pull: resp[1]
      };
      this.emit("complete", out);
      if (callback) {
        callback(null, out);
      }
      this.removeAllListeners();
      return out;
    }, (err) => {
      this.cancel();
      if (callback) {
        callback(err);
      } else {
        this.emit("error", err);
      }
      this.removeAllListeners();
      if (callback) {
        throw err;
      }
    });
    this.then = function(success, err) {
      return promise.then(success, err);
    };
    this.catch = function(err) {
      return promise.catch(err);
    };
  }
  cancel() {
    if (!this.canceled) {
      this.canceled = true;
      this.push.cancel();
      this.pull.cancel();
    }
  }
};
function replication(PouchDB2) {
  PouchDB2.replicate = replicateWrapper;
  PouchDB2.sync = sync;
  Object.defineProperty(PouchDB2.prototype, "replicate", {
    get: function() {
      var self2 = this;
      if (typeof this.replicateMethods === "undefined") {
        this.replicateMethods = {
          from: function(other, opts, callback) {
            return self2.constructor.replicate(other, self2, opts, callback);
          },
          to: function(other, opts, callback) {
            return self2.constructor.replicate(self2, other, opts, callback);
          }
        };
      }
      return this.replicateMethods;
    }
  });
  PouchDB2.prototype.sync = function(dbName, opts, callback) {
    return this.constructor.sync(this, dbName, opts, callback);
  };
}
PouchDB.plugin(IDBPouch).plugin(HttpPouch$1).plugin(mapreduce).plugin(replication);
var index_es_default = PouchDB;
export {
  index_es_default as default
};
//# sourceMappingURL=pouchdb-browser.js.map
