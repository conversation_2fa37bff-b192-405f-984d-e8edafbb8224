<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>Register Patient</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ion-padding">
  <form [formGroup]="patientForm" (ngSubmit)="savePatients()">
    <!-- Section 0: Basic Info -->
    <ion-card>
      <ion-card-header (click)="toggleSection('section0')" style="cursor: pointer;">
        <ion-card-title>
          Create ABHA Number
          <ion-icon
            [name]="activeSection === 'section0' ? 'chevron-up-outline' : 'chevron-down-outline'"
            slot="end"
          >
          </ion-icon>
        </ion-card-title>
      </ion-card-header>

      <ion-card-content *ngIf="activeSection === 'section0'">
        <input type="hidden" formControlName="_id" />
         <input type="hidden" formControlName="_rev" />
        <div class="gridp">
          <ion-item>
            <ion-label position="floating">First Name</ion-label>
            <ion-input formControlName="first_name"></ion-input>
          </ion-item>

          <ion-item>
            <ion-label position="floating">Last Name</ion-label>
            <ion-input formControlName="last_name"></ion-input>
          </ion-item>

          <ion-item>
            <ion-label position="floating">Age</ion-label>
            <ion-input type="number" formControlName="age"></ion-input>
          </ion-item>

          <ion-item>
            <ion-label position="floating">Age (Years)</ion-label>
            <ion-input type="text" formControlName="ageYears"></ion-input>
          </ion-item>

          <ion-item class="full-width">
            <ion-label position="stacked">Gender</ion-label>
            <ion-radio-group formControlName="gender">
              <div class="radio-group">
                <label>
                  <ion-radio value="Male"></ion-radio>
                  <span>Male</span>
                </label>
                <label>
                  <ion-radio value="Female"></ion-radio>
                  <span>Female</span>
                </label>
                <label>
                  <ion-radio value="Other"></ion-radio>
                  <span>Other</span>
                </label>
              </div>
            </ion-radio-group>
          </ion-item>

          <!-- <ion-item>
          <ion-label position="floating">Mobile</ion-label>
          <ion-input formControlName="mobile" type="tel"></ion-input>
        </ion-item> -->
        </div>
        <ion-label position="stacked">Address:</ion-label>
        <ion-item>
          <ion-input formControlName="address"></ion-input>
        </ion-item>
      </ion-card-content>
    </ion-card>

    <!-- Section 1: Additional Info -->
    <ion-card>
      <ion-card-header (click)="toggleSection('section1')">
        <ion-card-title>
          Additional Information
          <ion-icon
            [name]="activeSection === 'section1' ? 'chevron-up-outline' : 'chevron-down-outline'"
            slot="end"
          ></ion-icon>
        </ion-card-title>
      </ion-card-header>

      <ion-card-content *ngIf="activeSection === 'section1'">
        <div class="gridp">
          <ion-item>
            <ion-label position="floating">Date of Birth</ion-label>
            <ion-input type="date" formControlName="date_of_birth"></ion-input>
          </ion-item>

          <ion-item class="full-width">
            <ion-label position="stacked">Marital Status</ion-label>
            <ion-radio-group formControlName="maritalstatus">
              <div class="radio-group">
                <label>
                  <ion-radio value="Single"></ion-radio>
                  <span>Single</span>
                </label>
                <label>
                  <ion-radio value="Married"></ion-radio>
                  <span>Married</span>
                </label>
                <label>
                  <ion-radio value="Separated"></ion-radio>
                  <span>Separated</span>
                </label>
                <label>
                  <ion-radio value="Widow"></ion-radio>
                  <span>Widow</span>
                </label>
              </div>
            </ion-radio-group>
          </ion-item>






          <!-- <div style="display: flex; align-items: center; gap: 16px;">
            <ion-radio-group formControlName="maritalstatus" style="display: flex; flex-direction: row; align-items: center; gap: 8px;">
              <ion-item lines="none" style="min-width: auto; padding: 0;">
                <ion-label>Single</ion-label>
                <ion-radio slot="start" value="Single"></ion-radio>
              </ion-item>
              <ion-item lines="none" style="min-width: auto; padding: 0;">
                <ion-label>Married</ion-label>
                <ion-radio slot="start" value="Married"></ion-radio>
              </ion-item>
              <ion-item lines="none" style="min-width: auto; padding: 0;">
                <ion-label>Separated</ion-label>
                <ion-radio slot="start" value="Separated"></ion-radio>
              </ion-item>
              <ion-item lines="none" style="min-width: auto; padding: 0;">
                <ion-label>Widow</ion-label>
                <ion-radio slot="start" value="Widow"></ion-radio>
              </ion-item>
            </ion-radio-group>
          </div> -->

          <ion-item>
            <ion-label position="floating">Weight (kg)</ion-label>
            <ion-input type="number" formControlName="weight"></ion-input>
          </ion-item>

          <ion-item>
            <ion-label position="floating">Height (cm)</ion-label>
            <ion-input type="number" formControlName="height"></ion-input>
          </ion-item>

          <!-- Country -->
          <ion-item>
            <ion-label>Country</ion-label>
            <ion-select
              formControlName="country"
              placeholder="Select Country"
              (ionChange)="onCountryChange($event)"
            >
              <ion-select-option
                *ngFor="let country of countryList"
                [value]="country.name"
                >{{ country.name }}</ion-select-option
              >
            </ion-select>
          </ion-item>

          <!-- State -->
          <ion-item *ngIf="stateList.length">
            <ion-label>State</ion-label>
            <ion-select
              formControlName="state"
              placeholder="Select State"
              (ionChange)="onStateChange($event)"
            >
              <ion-select-option
                *ngFor="let state of stateList"
                [value]="state.name"
                >{{ state.name }}</ion-select-option
              >
            </ion-select>
          </ion-item>

          <!-- District -->
          <ion-item *ngIf="districtList.length">
            <ion-label>District</ion-label>
            <ion-select
              formControlName="district"
              placeholder="Select District"
            >
              <ion-select-option
                *ngFor="let district of districtList"
                [value]="district"
                >{{ district }}</ion-select-option
              >
            </ion-select>
          </ion-item>

          <ion-item>
            <ion-label position="floating">Block</ion-label>
            <ion-input formControlName="block"></ion-input>
          </ion-item>

          <ion-item>
            <ion-label position="floating">Village</ion-label>
            <ion-input formControlName="village"></ion-input>
          </ion-item>

          <ion-item>
            <ion-label position="floating">head of household First Name</ion-label>
            <ion-input formControlName="head_of_household_fname"></ion-input>
          </ion-item>

          <ion-item>
            <ion-label position="floating">head of household Last Name</ion-label>
            <ion-input formControlName="head_of_household_lname"></ion-input>
          </ion-item>

          <ion-item>
            <ion-label position="floating">Mobile</ion-label>
            <ion-input type="tel" formControlName="mobile"></ion-input>
          </ion-item>

          <ion-item>
            <ion-label position="floating">Email</ion-label>
            <ion-input type="email" formControlName="email"></ion-input>
          </ion-item>

          <ion-item>
            <ion-label position="floating">UID</ion-label>
            <ion-input formControlName="uid"></ion-input>
          </ion-item>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Section 2: Capture Image -->
    <ion-card>
      <ion-card-header (click)="toggleSection('captureImage')">
        <ion-card-title>
          Capture Patient Image
          <ion-icon
            [name]="activeSection === 'captureImage' ? 'chevron-up-outline' : 'chevron-down-outline'"
            slot="end"
          ></ion-icon>
        </ion-card-title>
      </ion-card-header>
      

      <ion-card-content *ngIf="activeSection === 'captureImage'">
        <div class="camera-controls">
          <label for="cameraSelect"><strong>Camera:</strong></label>
          <select id="cameraSelect" (change)="switchCamera($event)">
            <option *ngFor="let device of videoDevices; let i = index" [value]="device.deviceId">
              {{ device.label || 'Camera ' + (i + 1) }}
            </option>
          </select>
        </div>

        <div class="upload-buttons">
          <ion-button size="small" fill="outline" (click)="startCamera()">
            <ion-icon name="videocam-outline" slot="start"></ion-icon>
            Start Camera
          </ion-button>
          <ion-button expand="block" color="primary" (click)="captureImage()">
            <ion-icon name="camera-outline" slot="start"></ion-icon>
            Take Snapshot
          </ion-button>
        </div>

        <div style="display: flex; justify-content: center; margin: 16px 0;">
          <video #video autoplay muted playsinline></video>
        </div>

        <canvas #canvas hidden></canvas>

        <div *ngIf="photoPreviewUrl" style="display: flex; justify-content: center; margin-top: 16px;">
          <ion-img [src]="photoPreviewUrl" style="max-width: 300px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"></ion-img>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Section 3: Upload Document -->
    <ion-card>
      <ion-card-header (click)="toggleSection('uploadDocument')">
        <ion-card-title>
          Upload Documents
          <ion-icon
            [name]="activeSection === 'uploadDocument' ? 'chevron-up-outline' : 'chevron-down-outline'"
            slot="end"
          ></ion-icon>
        </ion-card-title>
      </ion-card-header>
<ion-card-content *ngIf="activeSection === 'uploadDocument'">
  <div class="upload-controls">
    <ion-label color="success" style="font-weight: bold; margin-bottom: 12px; display: block;">
      <ion-icon name="cloud-upload-outline"></ion-icon>
      Upload Document/Image
    </ion-label>

    <div class="upload-buttons">
      <ion-button fill="outline" color="primary" (click)="onBrowseClick()">
        <ion-icon name="folder-open-outline" slot="start"></ion-icon>
        Browse Files
      </ion-button>

      <ion-button fill="outline" color="secondary" (click)="onCaptureClick()">
        <ion-icon name="camera-outline" slot="start"></ion-icon>
        Capture Image
      </ion-button>
    </div>

    <!-- Image Type Dropdown -->
    <div *ngIf="showUploadControls" style="margin-bottom: 16px;">
      <ion-item>
        <ion-label>Document Type</ion-label>
        <ion-select formControlName="imageType" placeholder="Select Type" interface="popover">
          <ion-select-option value="73">X-ray</ion-select-option>
          <ion-select-option value="74">CT Scan</ion-select-option>
          <ion-select-option value="97">MRI Scan</ion-select-option>
          <ion-select-option value="117">Dermatoscope</ion-select-option>
          <ion-select-option value="118">ECG</ion-select-option>
          <ion-select-option value="136">CAT Scan</ion-select-option>
          <ion-select-option value="140">Breast Cancer Screening</ion-select-option>
          <ion-select-option value="148">General Image</ion-select-option>
          <ion-select-option value="156">Otoscope</ion-select-option>
          <ion-select-option value="163">Chest Image</ion-select-option>
          <ion-select-option value="168">General Examination</ion-select-option>
        </ion-select>
      </ion-item>
    </div>

    <!-- Camera Select Dropdown -->
    <div *ngIf="showUploadControls && isCaptureMode" class="camera-controls">
      <label for="uploadCameraSelect"><strong>Camera:</strong></label>
      <select id="uploadCameraSelect" (change)="switchUploadCamera($event)">
        <option *ngFor="let device of videoDevices; let i = index" [value]="device.deviceId">
          {{ device.label || 'Camera ' + (i + 1) }}
        </option>
      </select>
    </div>

    <!-- Video Camera Preview -->
    <div *ngIf="showUploadControls && isCaptureMode" style="display: flex; justify-content: center; margin: 16px 0;">
      <video #uploadVideo autoplay muted playsinline style="border-radius: 8px;"></video>
    </div>
    <canvas #canvas hidden></canvas>

    <!-- Take Snapshot Button -->
    <div *ngIf="showUploadControls && isCaptureMode" style="margin-bottom: 16px;">
      <ion-button expand="block" color="primary" (click)="captureUploadImage()">
        <ion-icon name="camera-outline" slot="start"></ion-icon>
        Take Snapshot
      </ion-button>
    </div>

    <!-- File Upload -->
    <div *ngIf="showUploadControls && !isCaptureMode" style="margin-bottom: 16px;">
      <ion-item>
        <ion-label position="stacked">Choose File</ion-label>
        <input type="file" (change)="onFileSelected($event)" accept="image/*,application/pdf" style="width: 100%; padding: 8px;" />
      </ion-item>
    </div>
  </div>

  <!-- Document List -->
  <ion-list style="margin-top: 15px;">
    <ion-item *ngFor="let doc of patientForm.get('documents')?.value; let i = index">
      <ion-label>
        <p>{{ doc.fileName }}</p>
        <small>Type: {{ doc.type }}</small>
      </ion-label>
      <ion-button fill="clear" color="danger" (click)="removeDocument(i)">
        <ion-icon name="trash-outline"></ion-icon>
      </ion-button>
    </ion-item>
  </ion-list>
</ion-card-content>

</ion-card>
    <!-- Submit -->
    <ion-button expand="block" type="submit" [disabled]="patientForm.invalid">
      {{ editIndex === null ? 'Save Patient' : 'Update Patient' }}
    </ion-button>
  </form>

  <!-- Submitted Records -->
  <ion-card
  *ngIf="submittedPatients.length > 0"
  style="margin-top: 20px; overflow-x: auto; display: block; max-width: 100%; box-shadow: 0 4px 10px rgba(0,0,0,0.1); border-radius: 12px;"
>
  <ion-card-header
    style="background: #0077b6; color: white; padding: 12px; border-top-left-radius: 12px; border-top-right-radius: 12px;"
  >
    <ion-card-title style="font-size: 18px; font-weight: 600;">
      📋 Submitted Patient Records
    </ion-card-title>
    <div style="font-size: 14px; font-weight: 500; margin-top: 4px;">
      Total Entries: {{ submittedPatients.length }}
    </div>
  </ion-card-header>

  <ion-card-content style="padding: 0; overflow-x: auto;">
    <ion-grid
      style="min-width: 1200px; width: 100%; border-collapse: collapse; font-size: 14px;"
    >
      <!-- Header Row -->
      <ion-row
        style="background: #f4f4f4; font-weight: 600; border-bottom: 2px solid #ccc; text-align: center;"
      >
        <ion-col style="min-width: 120px; padding: 10px;">Name</ion-col>
        <ion-col style="min-width: 90px; padding: 10px;">DOB</ion-col>
        <ion-col style="min-width: 70px; padding: 10px;">Age</ion-col>
        <ion-col style="min-width: 90px; padding: 10px;">Gender</ion-col>
        <ion-col style="min-width: 100px; padding: 10px;">Country</ion-col>
        <ion-col style="min-width: 90px; padding: 10px;">Height</ion-col>
        <ion-col style="min-width: 90px; padding: 10px;">Weight</ion-col>
        <ion-col style="min-width: 120px; padding: 10px;">Contact</ion-col>
        <ion-col style="min-width: 150px; padding: 10px;">Email</ion-col>
        <ion-col style="min-width: 90px; padding: 10px;">Photo</ion-col>
        <ion-col style="min-width: 120px; padding: 10px;">Docs</ion-col>
        <ion-col style="min-width: 160px; padding: 10px;">Actions</ion-col>
      </ion-row>

      <!-- Data Rows -->
      <ion-row
        *ngFor="let patient of submittedPatients; let i = index"
        style="border-bottom: 1px solid #ddd; text-align: center; transition: background 0.3s;"
        (mouseenter)="hoverIndex = i"
        (mouseleave)="hoverIndex = null"
        [ngStyle]="{'background': hoverIndex === i ? '#f9f9f9' : 'white'}"
      >
        <ion-col style="min-width: 120px; padding: 10px;">{{ patient.first_name }} {{ patient.last_name }}</ion-col>
        <ion-col style="min-width: 90px; padding: 10px;">{{ patient.date_of_birth }}</ion-col>
        <ion-col style="min-width: 70px; padding: 10px;">{{ patient.ageYears }}y</ion-col>
        <ion-col style="min-width: 90px; padding: 10px;">{{ patient.gender }}</ion-col>
        <ion-col style="min-width: 100px; padding: 10px;">{{ patient.country }}</ion-col>
        <ion-col style="min-width: 90px; padding: 10px;">{{ patient.height }} cm</ion-col>
        <ion-col style="min-width: 90px; padding: 10px;">{{ patient.weight }} kg</ion-col>
        <ion-col style="min-width: 120px; padding: 10px;">{{ patient.mobile }}</ion-col>
        <ion-col style="min-width: 150px; padding: 10px;">{{ patient.email }}</ion-col>

        <!-- Photo -->
        <ion-col style="min-width: 90px; padding: 10px;">
          <img
            *ngIf="patient.profile?.imagepath"
            [src]="patient.profile.imagepath"
            alt="Photo"
            style="width: 45px; height: 45px; border-radius: 50%; cursor: pointer; border: 2px solid #0077b6;"
            (click)="openPhotoPopup(patient.profile.imagepath)"
          />
        </ion-col>

        <!-- Documents -->
        <ion-col style="min-width: 120px; padding: 10px;">
          <ul
            *ngIf="patient.documents && patient.documents.length > 0"
            style="list-style: none; padding: 0; margin: 0;"
          >
            <li
              *ngFor="let doc of patient.documents"
              style="cursor: pointer; color: #0077b6; text-decoration: underline; font-weight: 500; font-size: 13px;"
              (click)="openDocPopup(doc)"
            >
              📄 {{ doc.fileName }}
            </li>
          </ul>
        </ion-col>

        <!-- ✅ Actions Column at LAST -->
        <ion-col
          style="min-width: 160px; padding: 10px; display: flex; justify-content: center; gap: 6px;"
        >
          <ion-button
            fill="outline"
            size="small"
            color="primary"
            style="--border-radius: 6px; --padding-start: 8px; --padding-end: 8px; min-width: 60px;"
            (click)="editPatients(patient, i)"
          >
            <ion-icon name="create-outline" slot="start"></ion-icon>Edit
          </ion-button>

          <ion-button
            fill="outline"
            size="small"
            color="danger"
            style="--border-radius: 6px; --padding-start: 8px; --padding-end: 8px; min-width: 60px;"
            (click)="delete(patient)"
          >
            <ion-icon name="trash-outline" slot="start"></ion-icon>Del
          </ion-button>
        </ion-col>
      </ion-row>
    </ion-grid>
  </ion-card-content>
</ion-card>

<!-- Photo Modal -->
<ion-modal [isOpen]="photoPopupOpen" (didDismiss)="closePhotoPopup()">
  <ion-content style="text-align: center; padding: 20px;">
    <ion-img [src]="selectedPhoto" style="max-width: 95%; max-height: 75vh; border-radius: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);"></ion-img>
    <ion-button expand="block" color="primary" style="margin-top: 15px;" (click)="closePhotoPopup()">Close</ion-button>
  </ion-content>
</ion-modal>

<!-- Document Modal -->
<ion-modal [isOpen]="docPopupOpen" (didDismiss)="closeDocPopup()">
  <ion-content style="text-align: center; padding: 20px;">
    <ion-img
      *ngIf="selectedDoc?.fileType?.startsWith('image/')"
      [src]="selectedDoc?.data"
      style="max-width: 95%; max-height: 75vh; border-radius: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);"
    ></ion-img>
    <p *ngIf="!selectedDoc?.fileType?.startsWith('image/')" style="font-weight: 500; color: #555;">
      📄 Document preview not available.
    </p>
    <ion-button expand="block" color="primary" style="margin-top: 15px;" (click)="closeDocPopup()">Close</ion-button>
  </ion-content>
</ion-modal>
