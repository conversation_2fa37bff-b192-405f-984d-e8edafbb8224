1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="io.ionic.starter"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:42:5-67
12-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:42:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:43:5-79
13-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:43:22-76
14    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:44:5-81
14-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:44:22-78
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:45:5-80
15-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:45:22-77
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->[:capacitor-haptics] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
16-->[:capacitor-haptics] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
17    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
17-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaaca94d50affd3c3a86a8212751e60b\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
17-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaaca94d50affd3c3a86a8212751e60b\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
18    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
18-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaaca94d50affd3c3a86a8212751e60b\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
18-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaaca94d50affd3c3a86a8212751e60b\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
19
20    <permission
20-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
21        android:name="io.ionic.starter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
21-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
22        android:protectionLevel="signature" />
22-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
23
24    <uses-permission android:name="io.ionic.starter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
24-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
24-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
25
26    <application
26-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:4:5-39:19
27        android:allowBackup="true"
27-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:5:9-35
28        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
28-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
29        android:debuggable="true"
30        android:extractNativeLibs="false"
31        android:hardwareAccelerated="true"
31-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:11:9-43
32        android:icon="@mipmap/ic_launcher"
32-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:6:9-43
33        android:label="@string/app_name"
33-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:7:9-41
34        android:largeHeap="true"
34-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:13:9-33
35        android:roundIcon="@mipmap/ic_launcher_round"
35-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:8:9-54
36        android:supportsRtl="true"
36-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:9:9-35
37        android:theme="@style/AppTheme"
37-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:10:9-40
38        android:usesCleartextTraffic="true" >
38-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:12:9-44
39        <activity
39-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:15:9-28:20
40            android:name="io.ionic.starter.MainActivity"
40-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:17:13-41
41            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
41-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:16:13-140
42            android:exported="true"
42-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:21:13-36
43            android:label="@string/title_activity_main"
43-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:18:13-56
44            android:launchMode="singleTask"
44-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:20:13-44
45            android:theme="@style/AppTheme.NoActionBarLaunch" >
45-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:19:13-62
46            <intent-filter>
46-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:23:13-26:29
47                <action android:name="android.intent.action.MAIN" />
47-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:24:17-69
47-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:24:25-66
48
49                <category android:name="android.intent.category.LAUNCHER" />
49-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:25:17-77
49-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:25:27-74
50            </intent-filter>
51        </activity>
52
53        <provider
54            android:name="androidx.core.content.FileProvider"
54-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:31:13-62
55            android:authorities="io.ionic.starter.fileprovider"
55-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:32:13-64
56            android:exported="false"
56-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:33:13-37
57            android:grantUriPermissions="true" >
57-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:34:13-47
58            <meta-data
58-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:35:13-37:64
59                android:name="android.support.FILE_PROVIDER_PATHS"
59-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:36:17-67
60                android:resource="@xml/file_paths" />
60-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\22-07-25\pepouchdb\android\app\src\main\AndroidManifest.xml:37:17-51
61        </provider>
62        <provider
62-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
63            android:name="androidx.startup.InitializationProvider"
63-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
64            android:authorities="io.ionic.starter.androidx-startup"
64-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
65            android:exported="false" >
65-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
66            <meta-data
66-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
67                android:name="androidx.emoji2.text.EmojiCompatInitializer"
67-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
68                android:value="androidx.startup" />
68-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
69            <meta-data
69-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcaa2d94568f1111aa0d45f264971fb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
70                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
70-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcaa2d94568f1111aa0d45f264971fb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
71                android:value="androidx.startup" />
71-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcaa2d94568f1111aa0d45f264971fb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
72            <meta-data
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
73                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
74                android:value="androidx.startup" />
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
75        </provider>
76
77        <receiver
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
78            android:name="androidx.profileinstaller.ProfileInstallReceiver"
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
79            android:directBootAware="false"
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
80            android:enabled="true"
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
81            android:exported="true"
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
82            android:permission="android.permission.DUMP" >
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
84                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
85            </intent-filter>
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
87                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
88            </intent-filter>
89            <intent-filter>
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
90                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
91            </intent-filter>
92            <intent-filter>
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
93                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
94            </intent-filter>
95        </receiver>
96
97        <service
97-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc6d32dbdad3a4b2c0ef6b4bde666a61\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
98            android:name="androidx.room.MultiInstanceInvalidationService"
98-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc6d32dbdad3a4b2c0ef6b4bde666a61\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
99            android:directBootAware="true"
99-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc6d32dbdad3a4b2c0ef6b4bde666a61\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
100            android:exported="false" />
100-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc6d32dbdad3a4b2c0ef6b4bde666a61\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
101    </application>
102
103</manifest>
