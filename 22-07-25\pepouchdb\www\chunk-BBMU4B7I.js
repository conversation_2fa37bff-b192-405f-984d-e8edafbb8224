import{d as k,f as T}from"./chunk-KRSA4YKC.js";import{v as b,w as E}from"./chunk-DFAMUM5K.js";import{c as I,d as x,e as y}from"./chunk-HRORXLUI.js";import"./chunk-FPOZYJOD.js";import{i as g}from"./chunk-XFXTD7QR.js";import"./chunk-CKP3SGE2.js";import{f as h,i as m,m as a,n as f,o as u,p as w}from"./chunk-EHNA26RN.js";import{g as p}from"./chunk-2R6CW7ES.js";var R=":host([slot]){display:none;line-height:0;z-index:100}.reorder-icon{display:block}::slotted(ion-icon){font-size:dynamic-font(16px)}.reorder-icon{font-size:2.125rem;opacity:0.4}",B=":host([slot]){display:none;line-height:0;z-index:100}.reorder-icon{display:block}::slotted(ion-icon){font-size:dynamic-font(16px)}.reorder-icon{font-size:1.9375rem;opacity:0.3}",j=(()=>{let e=class{constructor(t){m(this,t)}onClick(t){let s=this.el.closest("ion-reorder-group");t.preventDefault(),(!s||!s.disabled)&&t.stopImmediatePropagation()}render(){let t=h(this);return a(f,{key:"e6807bb349725682e99e791ac65e729a360d64e8",class:t},a("slot",{key:"1c691cdbffa6427ba08dc12184c69559ed5d5506"},a("ion-icon",{key:"8b4150302cdca475379582b2251737b5e74079b1",icon:t==="ios"?b:E,lazy:!1,class:"reorder-icon",part:"icon","aria-hidden":"true"})))}get el(){return u(this)}};return e.style={ios:R,md:B},e})(),M=".reorder-list-active>*{display:block;-webkit-transition:-webkit-transform 300ms;transition:-webkit-transform 300ms;transition:transform 300ms;transition:transform 300ms, -webkit-transform 300ms;will-change:transform}.reorder-enabled{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.reorder-enabled ion-reorder{display:block;cursor:-webkit-grab;cursor:grab;pointer-events:all;-ms-touch-action:none;touch-action:none}.reorder-selected,.reorder-selected ion-reorder{cursor:-webkit-grabbing;cursor:grabbing}.reorder-selected{position:relative;-webkit-transition:none !important;transition:none !important;-webkit-box-shadow:0 0 10px rgba(0, 0, 0, 0.4);box-shadow:0 0 10px rgba(0, 0, 0, 0.4);opacity:0.8;z-index:100}.reorder-visible ion-reorder .reorder-icon{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}",z=class{constructor(e){m(this,e),this.ionItemReorder=w(this,"ionItemReorder",7),this.lastToIndex=-1,this.cachedHeights=[],this.scrollElTop=0,this.scrollElBottom=0,this.scrollElInitial=0,this.containerTop=0,this.containerBottom=0,this.state=0,this.disabled=!0}disabledChanged(){this.gesture&&this.gesture.enable(!this.disabled)}connectedCallback(){return p(this,null,function*(){let e=T(this.el);e&&(this.scrollEl=yield k(e)),this.gesture=(yield import("./chunk-Z4ZXIL76.js")).createGesture({el:this.el,gestureName:"reorder",gesturePriority:110,threshold:0,direction:"y",passive:!1,canStart:t=>this.canStart(t),onStart:t=>this.onStart(t),onMove:t=>this.onMove(t),onEnd:()=>this.onEnd()}),this.disabledChanged()})}disconnectedCallback(){this.onEnd(),this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}complete(e){return Promise.resolve(this.completeReorder(e))}canStart(e){if(this.selectedItemEl||this.state!==0)return!1;let s=e.event.target.closest("ion-reorder");if(!s)return!1;let o=H(s,this.el);return o?(e.data=o,!0):!1}onStart(e){e.event.preventDefault();let t=this.selectedItemEl=e.data,s=this.cachedHeights;s.length=0;let o=this.el,r=o.children;if(!r||r.length===0)return;let n=0;for(let l=0;l<r.length;l++){let c=r[l];n+=c.offsetHeight,s.push(n),c.$ionIndex=l}let i=o.getBoundingClientRect();if(this.containerTop=i.top,this.containerBottom=i.bottom,this.scrollEl){let l=this.scrollEl.getBoundingClientRect();this.scrollElInitial=this.scrollEl.scrollTop,this.scrollElTop=l.top+C,this.scrollElBottom=l.bottom-C}else this.scrollElInitial=0,this.scrollElTop=0,this.scrollElBottom=0;this.lastToIndex=d(t),this.selectedItemHeight=t.offsetHeight,this.state=1,t.classList.add(S),I()}onMove(e){let t=this.selectedItemEl;if(!t)return;let s=this.autoscroll(e.currentY),o=this.containerTop-s,r=this.containerBottom-s,n=Math.max(o,Math.min(e.currentY,r)),i=s+n-e.startY,l=n-o,c=this.itemIndexForTop(l);if(c!==this.lastToIndex){let v=d(t);this.lastToIndex=c,x(),this.reorderMove(v,c)}t.style.transform=`translateY(${i}px)`}onEnd(){let e=this.selectedItemEl;if(this.state=2,!e){this.state=0;return}let t=this.lastToIndex,s=d(e);t===s?this.completeReorder():this.ionItemReorder.emit({from:s,to:t,complete:this.completeReorder.bind(this)}),y()}completeReorder(e){let t=this.selectedItemEl;if(t&&this.state===2){let s=this.el.children,o=s.length,r=this.lastToIndex,n=d(t);g(()=>{if(r!==n&&(e===void 0||e===!0)){let i=n<r?s[r+1]:s[r];this.el.insertBefore(t,i)}for(let i=0;i<o;i++)s[i].style.transform=""}),Array.isArray(e)&&(e=_(e,n,r)),t.style.transition="",t.classList.remove(S),this.selectedItemEl=void 0,this.state=0}return e}itemIndexForTop(e){let t=this.cachedHeights;for(let s=0;s<t.length;s++)if(t[s]>e)return s;return t.length-1}reorderMove(e,t){let s=this.selectedItemHeight,o=this.el.children;for(let r=0;r<o.length;r++){let n=o[r].style,i="";r>e&&r<=t?i=`translateY(${-s}px)`:r<e&&r>=t&&(i=`translateY(${s}px)`),n.transform=i}}autoscroll(e){if(!this.scrollEl)return 0;let t=0;return e<this.scrollElTop?t=-10:e>this.scrollElBottom&&(t=Y),t!==0&&this.scrollEl.scrollBy(0,t),this.scrollEl.scrollTop-this.scrollElInitial}render(){let e=h(this);return a(f,{key:"dfcdc3a6aa1b2fba15f861ec868d6a11e667c9de",class:{[e]:!0,"reorder-enabled":!this.disabled,"reorder-list-active":this.state!==0}})}get el(){return u(this)}static get watchers(){return{disabled:["disabledChanged"]}}},d=e=>e.$ionIndex,H=(e,t)=>{let s;for(;e;){if(s=e.parentElement,s===t)return e;e=s}},C=60,Y=10,S="reorder-selected",_=(e,t,s)=>{let o=e[t];return e.splice(t,1),e.splice(s,0,o),e.slice()};z.style=M;export{j as ion_reorder,z as ion_reorder_group};
