import{b as ec,c as tc,d as nc,e as rc,f as oc}from"./chunk-B7SFH74S.js";import{g as wf,h as _f}from"./chunk-UOV5QIVR.js";import{a as xf,d as Of}from"./chunk-GIIU5PV3.js";import{a as ic}from"./chunk-M2X7KQLB.js";import{a as Ot,d as Af,e as Nf,f as Rf,h as Xa}from"./chunk-XTVTS2NW.js";import{a as be,b as bf,c as Sf,d as Mf,e as si,f as Tf}from"./chunk-C5RQ2IC2.js";import{b as xt}from"./chunk-42C7ZIID.js";import{a as g,b as A,d as If,g as se}from"./chunk-2R6CW7ES.js";var sc;function ai(){return sc}function et(e){let n=sc;return sc=e,n}var kf=Symbol("NotFound");function Ln(e){return e===kf||e?.name==="\u0275NotFound"}function fi(e,n){return Object.is(e,n)}var X=null,ci=!1,ac=1,tD=null,he=Symbol("SIGNAL");function N(e){let n=X;return X=e,n}function hi(){return X}var nn={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Vn(e){if(ci)throw new Error("");if(X===null)return;X.consumerOnSignalRead(e);let n=X.nextProducerIndex++;if(vi(X),n<X.producerNode.length&&X.producerNode[n]!==e&&$r(X)){let t=X.producerNode[n];mi(t,X.producerIndexOfThis[n])}X.producerNode[n]!==e&&(X.producerNode[n]=e,X.producerIndexOfThis[n]=$r(X)?Pf(e,X,n):0),X.producerLastReadVersion[n]=e.version}function Ff(){ac++}function pi(e){if(!($r(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===ac)){if(!e.producerMustRecompute(e)&&!zr(e)){di(e);return}e.producerRecomputeValue(e),di(e)}}function cc(e){if(e.liveConsumerNode===void 0)return;let n=ci;ci=!0;try{for(let t of e.liveConsumerNode)t.dirty||nD(t)}finally{ci=n}}function uc(){return X?.consumerAllowSignalWrites!==!1}function nD(e){e.dirty=!0,cc(e),e.consumerMarkedDirty?.(e)}function di(e){e.dirty=!1,e.lastCleanEpoch=ac}function rn(e){return e&&(e.nextProducerIndex=0),N(e)}function jn(e,n){if(N(n),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if($r(e))for(let t=e.nextProducerIndex;t<e.producerNode.length;t++)mi(e.producerNode[t],e.producerIndexOfThis[t]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function zr(e){vi(e);for(let n=0;n<e.producerNode.length;n++){let t=e.producerNode[n],r=e.producerLastReadVersion[n];if(r!==t.version||(pi(t),r!==t.version))return!0}return!1}function gi(e){if(vi(e),$r(e))for(let n=0;n<e.producerNode.length;n++)mi(e.producerNode[n],e.producerIndexOfThis[n]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Pf(e,n,t){if(Lf(e),e.liveConsumerNode.length===0&&Vf(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=Pf(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(t),e.liveConsumerNode.push(n)-1}function mi(e,n){if(Lf(e),e.liveConsumerNode.length===1&&Vf(e))for(let r=0;r<e.producerNode.length;r++)mi(e.producerNode[r],e.producerIndexOfThis[r]);let t=e.liveConsumerNode.length-1;if(e.liveConsumerNode[n]=e.liveConsumerNode[t],e.liveConsumerIndexOfThis[n]=e.liveConsumerIndexOfThis[t],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,n<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[n],o=e.liveConsumerNode[n];vi(o),o.producerIndexOfThis[r]=n}}function $r(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function vi(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Lf(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Vf(e){return e.producerNode!==void 0}function yi(e){tD?.(e)}function Di(e,n){let t=Object.create(rD);t.computation=e,n!==void 0&&(t.equal=n);let r=()=>{if(pi(t),Vn(t),t.value===Ur)throw t.error;return t.value};return r[he]=t,yi(t),r}var ui=Symbol("UNSET"),li=Symbol("COMPUTING"),Ur=Symbol("ERRORED"),rD=A(g({},nn),{value:ui,dirty:!0,error:null,equal:fi,kind:"computed",producerMustRecompute(e){return e.value===ui||e.value===li},producerRecomputeValue(e){if(e.value===li)throw new Error("");let n=e.value;e.value=li;let t=rn(e),r,o=!1;try{r=e.computation(),N(null),o=n!==ui&&n!==Ur&&r!==Ur&&e.equal(n,r)}catch(i){r=Ur,e.error=i}finally{jn(e,t)}if(o){e.value=n;return}e.value=r,e.version++}});function oD(){throw new Error}var jf=oD;function Bf(e){jf(e)}function lc(e){jf=e}var iD=null;function dc(e,n){let t=Object.create(Ci);t.value=e,n!==void 0&&(t.equal=n);let r=()=>Hf(t);return r[he]=t,yi(t),[r,s=>Bn(t,s),s=>fc(t,s)]}function Hf(e){return Vn(e),e.value}function Bn(e,n){uc()||Bf(e),e.equal(e.value,n)||(e.value=n,sD(e))}function fc(e,n){uc()||Bf(e),Bn(e,n(e.value))}var Ci=A(g({},nn),{equal:fi,value:void 0,kind:"signal"});function sD(e){e.version++,Ff(),cc(e),iD?.(e)}function w(e){return typeof e=="function"}function Hn(e){let t=e(r=>{Error.call(r),r.stack=new Error().stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}var Ei=Hn(e=>function(t){e(this),this.message=t?`${t.length} errors occurred during unsubscription:
${t.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=t});function Gr(e,n){if(e){let t=e.indexOf(n);0<=t&&e.splice(t,1)}}var K=class e{constructor(n){this.initialTeardown=n,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let n;if(!this.closed){this.closed=!0;let{_parentage:t}=this;if(t)if(this._parentage=null,Array.isArray(t))for(let i of t)i.remove(this);else t.remove(this);let{initialTeardown:r}=this;if(w(r))try{r()}catch(i){n=i instanceof Ei?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Uf(i)}catch(s){n=n??[],s instanceof Ei?n=[...n,...s.errors]:n.push(s)}}if(n)throw new Ei(n)}}add(n){var t;if(n&&n!==this)if(this.closed)Uf(n);else{if(n instanceof e){if(n.closed||n._hasParent(this))return;n._addParent(this)}(this._finalizers=(t=this._finalizers)!==null&&t!==void 0?t:[]).push(n)}}_hasParent(n){let{_parentage:t}=this;return t===n||Array.isArray(t)&&t.includes(n)}_addParent(n){let{_parentage:t}=this;this._parentage=Array.isArray(t)?(t.push(n),t):t?[t,n]:n}_removeParent(n){let{_parentage:t}=this;t===n?this._parentage=null:Array.isArray(t)&&Gr(t,n)}remove(n){let{_finalizers:t}=this;t&&Gr(t,n),n instanceof e&&n._removeParent(this)}};K.EMPTY=(()=>{let e=new K;return e.closed=!0,e})();var hc=K.EMPTY;function Ii(e){return e instanceof K||e&&"closed"in e&&w(e.remove)&&w(e.add)&&w(e.unsubscribe)}function Uf(e){w(e)?e():e.unsubscribe()}var He={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Un={setTimeout(e,n,...t){let{delegate:r}=Un;return r?.setTimeout?r.setTimeout(e,n,...t):setTimeout(e,n,...t)},clearTimeout(e){let{delegate:n}=Un;return(n?.clearTimeout||clearTimeout)(e)},delegate:void 0};function wi(e){Un.setTimeout(()=>{let{onUnhandledError:n}=He;if(n)n(e);else throw e})}function Wr(){}var $f=pc("C",void 0,void 0);function zf(e){return pc("E",void 0,e)}function Gf(e){return pc("N",e,void 0)}function pc(e,n,t){return{kind:e,value:n,error:t}}var on=null;function $n(e){if(He.useDeprecatedSynchronousErrorHandling){let n=!on;if(n&&(on={errorThrown:!1,error:null}),e(),n){let{errorThrown:t,error:r}=on;if(on=null,t)throw r}}else e()}function Wf(e){He.useDeprecatedSynchronousErrorHandling&&on&&(on.errorThrown=!0,on.error=e)}var sn=class extends K{constructor(n){super(),this.isStopped=!1,n?(this.destination=n,Ii(n)&&n.add(this)):this.destination=uD}static create(n,t,r){return new zn(n,t,r)}next(n){this.isStopped?mc(Gf(n),this):this._next(n)}error(n){this.isStopped?mc(zf(n),this):(this.isStopped=!0,this._error(n))}complete(){this.isStopped?mc($f,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(n){this.destination.next(n)}_error(n){try{this.destination.error(n)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},aD=Function.prototype.bind;function gc(e,n){return aD.call(e,n)}var vc=class{constructor(n){this.partialObserver=n}next(n){let{partialObserver:t}=this;if(t.next)try{t.next(n)}catch(r){_i(r)}}error(n){let{partialObserver:t}=this;if(t.error)try{t.error(n)}catch(r){_i(r)}else _i(n)}complete(){let{partialObserver:n}=this;if(n.complete)try{n.complete()}catch(t){_i(t)}}},zn=class extends sn{constructor(n,t,r){super();let o;if(w(n)||!n)o={next:n??void 0,error:t??void 0,complete:r??void 0};else{let i;this&&He.useDeprecatedNextContext?(i=Object.create(n),i.unsubscribe=()=>this.unsubscribe(),o={next:n.next&&gc(n.next,i),error:n.error&&gc(n.error,i),complete:n.complete&&gc(n.complete,i)}):o=n}this.destination=new vc(o)}};function _i(e){He.useDeprecatedSynchronousErrorHandling?Wf(e):wi(e)}function cD(e){throw e}function mc(e,n){let{onStoppedNotification:t}=He;t&&Un.setTimeout(()=>t(e,n))}var uD={closed:!0,next:Wr,error:cD,complete:Wr};var Gn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function pe(e){return e}function yc(...e){return Dc(e)}function Dc(e){return e.length===0?pe:e.length===1?e[0]:function(t){return e.reduce((r,o)=>o(r),t)}}var k=(()=>{class e{constructor(t){t&&(this._subscribe=t)}lift(t){let r=new e;return r.source=this,r.operator=t,r}subscribe(t,r,o){let i=dD(t)?t:new zn(t,r,o);return $n(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(t){try{return this._subscribe(t)}catch(r){t.error(r)}}forEach(t,r){return r=qf(r),new r((o,i)=>{let s=new zn({next:a=>{try{t(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(t){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(t)}[Gn](){return this}pipe(...t){return Dc(t)(this)}toPromise(t){return t=qf(t),new t((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=n=>new e(n),e})();function qf(e){var n;return(n=e??He.Promise)!==null&&n!==void 0?n:Promise}function lD(e){return e&&w(e.next)&&w(e.error)&&w(e.complete)}function dD(e){return e&&e instanceof sn||lD(e)&&Ii(e)}function Cc(e){return w(e?.lift)}function F(e){return n=>{if(Cc(n))return n.lift(function(t){try{return e(t,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function O(e,n,t,r,o){return new Ec(e,n,t,r,o)}var Ec=class extends sn{constructor(n,t,r,o,i,s){super(n),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=t?function(a){try{t(a)}catch(c){n.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){n.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){n.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var n;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:t}=this;super.unsubscribe(),!t&&((n=this.onFinalize)===null||n===void 0||n.call(this))}}};function Wn(){return F((e,n)=>{let t=null;e._refCount++;let r=O(n,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){t=null;return}let o=e._connection,i=t;t=null,o&&(!i||o===i)&&o.unsubscribe(),n.unsubscribe()});e.subscribe(r),r.closed||(t=e.connect())})}var qn=class extends k{constructor(n,t){super(),this.source=n,this.subjectFactory=t,this._subject=null,this._refCount=0,this._connection=null,Cc(n)&&(this.lift=n.lift)}_subscribe(n){return this.getSubject().subscribe(n)}getSubject(){let n=this._subject;return(!n||n.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:n}=this;this._subject=this._connection=null,n?.unsubscribe()}connect(){let n=this._connection;if(!n){n=this._connection=new K;let t=this.getSubject();n.add(this.source.subscribe(O(t,void 0,()=>{this._teardown(),t.complete()},r=>{this._teardown(),t.error(r)},()=>this._teardown()))),n.closed&&(this._connection=null,n=K.EMPTY)}return n}refCount(){return Wn()(this)}};var Zf=Hn(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var U=(()=>{class e extends k{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(t){let r=new bi(this,this);return r.operator=t,r}_throwIfClosed(){if(this.closed)throw new Zf}next(t){$n(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(t)}})}error(t){$n(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=t;let{observers:r}=this;for(;r.length;)r.shift().error(t)}})}complete(){$n(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:t}=this;for(;t.length;)t.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var t;return((t=this.observers)===null||t===void 0?void 0:t.length)>0}_trySubscribe(t){return this._throwIfClosed(),super._trySubscribe(t)}_subscribe(t){return this._throwIfClosed(),this._checkFinalizedStatuses(t),this._innerSubscribe(t)}_innerSubscribe(t){let{hasError:r,isStopped:o,observers:i}=this;return r||o?hc:(this.currentObservers=null,i.push(t),new K(()=>{this.currentObservers=null,Gr(i,t)}))}_checkFinalizedStatuses(t){let{hasError:r,thrownError:o,isStopped:i}=this;r?t.error(o):i&&t.complete()}asObservable(){let t=new k;return t.source=this,t}}return e.create=(n,t)=>new bi(n,t),e})(),bi=class extends U{constructor(n,t){super(),this.destination=n,this.source=t}next(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.next)===null||r===void 0||r.call(t,n)}error(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.error)===null||r===void 0||r.call(t,n)}complete(){var n,t;(t=(n=this.destination)===null||n===void 0?void 0:n.complete)===null||t===void 0||t.call(n)}_subscribe(n){var t,r;return(r=(t=this.source)===null||t===void 0?void 0:t.subscribe(n))!==null&&r!==void 0?r:hc}};var J=class extends U{constructor(n){super(),this._value=n}get value(){return this.getValue()}_subscribe(n){let t=super._subscribe(n);return!t.closed&&n.next(this._value),t}getValue(){let{hasError:n,thrownError:t,_value:r}=this;if(n)throw t;return this._throwIfClosed(),r}next(n){super.next(this._value=n)}};var Ce=new k(e=>e.complete());function Yf(e){return e&&w(e.schedule)}function Qf(e){return e[e.length-1]}function Si(e){return w(Qf(e))?e.pop():void 0}function kt(e){return Yf(Qf(e))?e.pop():void 0}function qr(e,n,t,r){var o=arguments.length,i=o<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,t):r,s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")i=Reflect.decorate(e,n,t,r);else for(var a=e.length-1;a>=0;a--)(s=e[a])&&(i=(o<3?s(i):o>3?s(n,t,i):s(n,t))||i);return o>3&&i&&Object.defineProperty(n,t,i),i}function Jf(e,n,t,r){function o(i){return i instanceof t?i:new t(function(s){s(i)})}return new(t||(t=Promise))(function(i,s){function a(l){try{u(r.next(l))}catch(d){s(d)}}function c(l){try{u(r.throw(l))}catch(d){s(d)}}function u(l){l.done?i(l.value):o(l.value).then(a,c)}u((r=r.apply(e,n||[])).next())})}function Kf(e){var n=typeof Symbol=="function"&&Symbol.iterator,t=n&&e[n],r=0;if(t)return t.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}function an(e){return this instanceof an?(this.v=e,this):new an(e)}function Xf(e,n,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=t.apply(e,n||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(v){return Promise.resolve(v).then(f,d)}}function a(f,v){r[f]&&(o[f]=function(E){return new Promise(function(T,R){i.push([f,E,T,R])>1||c(f,E)})},v&&(o[f]=v(o[f])))}function c(f,v){try{u(r[f](v))}catch(E){h(i[0][3],E)}}function u(f){f.value instanceof an?Promise.resolve(f.value.v).then(l,d):h(i[0][2],f)}function l(f){c("next",f)}function d(f){c("throw",f)}function h(f,v){f(v),i.shift(),i.length&&c(i[0][0],i[0][1])}}function eh(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=e[Symbol.asyncIterator],t;return n?n.call(e):(e=typeof Kf=="function"?Kf(e):e[Symbol.iterator](),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(i){t[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},s)}}var Zn=e=>e&&typeof e.length=="number"&&typeof e!="function";function Mi(e){return w(e?.then)}function Ti(e){return w(e[Gn])}function Ai(e){return Symbol.asyncIterator&&w(e?.[Symbol.asyncIterator])}function Ni(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function fD(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Ri=fD();function xi(e){return w(e?.[Ri])}function Oi(e){return Xf(this,arguments,function*(){let t=e.getReader();try{for(;;){let{value:r,done:o}=yield an(t.read());if(o)return yield an(void 0);yield yield an(r)}}finally{t.releaseLock()}})}function ki(e){return w(e?.getReader)}function Y(e){if(e instanceof k)return e;if(e!=null){if(Ti(e))return hD(e);if(Zn(e))return pD(e);if(Mi(e))return gD(e);if(Ai(e))return th(e);if(xi(e))return mD(e);if(ki(e))return vD(e)}throw Ni(e)}function hD(e){return new k(n=>{let t=e[Gn]();if(w(t.subscribe))return t.subscribe(n);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function pD(e){return new k(n=>{for(let t=0;t<e.length&&!n.closed;t++)n.next(e[t]);n.complete()})}function gD(e){return new k(n=>{e.then(t=>{n.closed||(n.next(t),n.complete())},t=>n.error(t)).then(null,wi)})}function mD(e){return new k(n=>{for(let t of e)if(n.next(t),n.closed)return;n.complete()})}function th(e){return new k(n=>{yD(e,n).catch(t=>n.error(t))})}function vD(e){return th(Oi(e))}function yD(e,n){var t,r,o,i;return Jf(this,void 0,void 0,function*(){try{for(t=eh(e);r=yield t.next(),!r.done;){let s=r.value;if(n.next(s),n.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=t.return)&&(yield i.call(t))}finally{if(o)throw o.error}}n.complete()})}function Ee(e,n,t,r=0,o=!1){let i=n.schedule(function(){t(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Fi(e,n=0){return F((t,r)=>{t.subscribe(O(r,o=>Ee(r,e,()=>r.next(o),n),()=>Ee(r,e,()=>r.complete(),n),o=>Ee(r,e,()=>r.error(o),n)))})}function Pi(e,n=0){return F((t,r)=>{r.add(e.schedule(()=>t.subscribe(r),n))})}function nh(e,n){return Y(e).pipe(Pi(n),Fi(n))}function rh(e,n){return Y(e).pipe(Pi(n),Fi(n))}function oh(e,n){return new k(t=>{let r=0;return n.schedule(function(){r===e.length?t.complete():(t.next(e[r++]),t.closed||this.schedule())})})}function ih(e,n){return new k(t=>{let r;return Ee(t,n,()=>{r=e[Ri](),Ee(t,n,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){t.error(s);return}i?t.complete():t.next(o)},0,!0)}),()=>w(r?.return)&&r.return()})}function Li(e,n){if(!e)throw new Error("Iterable cannot be null");return new k(t=>{Ee(t,n,()=>{let r=e[Symbol.asyncIterator]();Ee(t,n,()=>{r.next().then(o=>{o.done?t.complete():t.next(o.value)})},0,!0)})})}function sh(e,n){return Li(Oi(e),n)}function ah(e,n){if(e!=null){if(Ti(e))return nh(e,n);if(Zn(e))return oh(e,n);if(Mi(e))return rh(e,n);if(Ai(e))return Li(e,n);if(xi(e))return ih(e,n);if(ki(e))return sh(e,n)}throw Ni(e)}function W(e,n){return n?ah(e,n):Y(e)}function _(...e){let n=kt(e);return W(e,n)}function Yn(e,n){let t=w(e)?e:()=>e,r=o=>o.error(t());return new k(n?o=>n.schedule(r,0,o):r)}function Ic(e){return!!e&&(e instanceof k||w(e.lift)&&w(e.subscribe))}var gt=Hn(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function P(e,n){return F((t,r)=>{let o=0;t.subscribe(O(r,i=>{r.next(e.call(n,i,o++))}))})}var{isArray:DD}=Array;function CD(e,n){return DD(n)?e(...n):e(n)}function Qn(e){return P(n=>CD(e,n))}var{isArray:ED}=Array,{getPrototypeOf:ID,prototype:wD,keys:_D}=Object;function Vi(e){if(e.length===1){let n=e[0];if(ED(n))return{args:n,keys:null};if(bD(n)){let t=_D(n);return{args:t.map(r=>n[r]),keys:t}}}return{args:e,keys:null}}function bD(e){return e&&typeof e=="object"&&ID(e)===wD}function ji(e,n){return e.reduce((t,r,o)=>(t[r]=n[o],t),{})}function Kn(...e){let n=kt(e),t=Si(e),{args:r,keys:o}=Vi(e);if(r.length===0)return W([],n);let i=new k(SD(r,n,o?s=>ji(o,s):pe));return t?i.pipe(Qn(t)):i}function SD(e,n,t=pe){return r=>{ch(n,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)ch(n,()=>{let u=W(e[c],n),l=!1;u.subscribe(O(r,d=>{i[c]=d,l||(l=!0,a--),a||r.next(t(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function ch(e,n,t){e?Ee(t,e,n):n()}function uh(e,n,t,r,o,i,s,a){let c=[],u=0,l=0,d=!1,h=()=>{d&&!c.length&&!u&&n.complete()},f=E=>u<r?v(E):c.push(E),v=E=>{i&&n.next(E),u++;let T=!1;Y(t(E,l++)).subscribe(O(n,R=>{o?.(R),i?f(R):n.next(R)},()=>{T=!0},void 0,()=>{if(T)try{for(u--;c.length&&u<r;){let R=c.shift();s?Ee(n,s,()=>v(R)):v(R)}h()}catch(R){n.error(R)}}))};return e.subscribe(O(n,f,()=>{d=!0,h()})),()=>{a?.()}}function q(e,n,t=1/0){return w(n)?q((r,o)=>P((i,s)=>n(r,i,o,s))(Y(e(r,o))),t):(typeof n=="number"&&(t=n),F((r,o)=>uh(r,o,e,t)))}function Jn(e=1/0){return q(pe,e)}function lh(){return Jn(1)}function Xn(...e){return lh()(W(e,kt(e)))}function Zr(e){return new k(n=>{Y(e()).subscribe(n)})}function wc(...e){let n=Si(e),{args:t,keys:r}=Vi(e),o=new k(i=>{let{length:s}=t;if(!s){i.complete();return}let a=new Array(s),c=s,u=s;for(let l=0;l<s;l++){let d=!1;Y(t[l]).subscribe(O(i,h=>{d||(d=!0,u--),a[l]=h},()=>c--,void 0,()=>{(!c||!d)&&(u||i.next(r?ji(r,a):a),i.complete())}))}});return n?o.pipe(Qn(n)):o}var MD=["addListener","removeListener"],TD=["addEventListener","removeEventListener"],AD=["on","off"];function Yr(e,n,t,r){if(w(t)&&(r=t,t=void 0),r)return Yr(e,n,t).pipe(Qn(r));let[o,i]=xD(e)?TD.map(s=>a=>e[s](n,a,t)):ND(e)?MD.map(dh(e,n)):RD(e)?AD.map(dh(e,n)):[];if(!o&&Zn(e))return q(s=>Yr(s,n,t))(Y(e));if(!o)throw new TypeError("Invalid event target");return new k(s=>{let a=(...c)=>s.next(1<c.length?c:c[0]);return o(a),()=>i(a)})}function dh(e,n){return t=>r=>e[t](n,r)}function ND(e){return w(e.addListener)&&w(e.removeListener)}function RD(e){return w(e.on)&&w(e.off)}function xD(e){return w(e.addEventListener)&&w(e.removeEventListener)}function ae(e,n){return F((t,r)=>{let o=0;t.subscribe(O(r,i=>e.call(n,i,o++)&&r.next(i)))})}function tt(e){return F((n,t)=>{let r=null,o=!1,i;r=n.subscribe(O(t,void 0,void 0,s=>{i=Y(e(s,tt(e)(n))),r?(r.unsubscribe(),r=null,i.subscribe(t)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(t))})}function fh(e,n,t,r,o){return(i,s)=>{let a=t,c=n,u=0;i.subscribe(O(s,l=>{let d=u++;c=a?e(c,l,d):(a=!0,l),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function Ft(e,n){return w(n)?q(e,n,1):q(e,1)}function Pt(e){return F((n,t)=>{let r=!1;n.subscribe(O(t,o=>{r=!0,t.next(o)},()=>{r||t.next(e),t.complete()}))})}function mt(e){return e<=0?()=>Ce:F((n,t)=>{let r=0;n.subscribe(O(t,o=>{++r<=e&&(t.next(o),e<=r&&t.complete())}))})}function _c(e,n=pe){return e=e??OD,F((t,r)=>{let o,i=!0;t.subscribe(O(r,s=>{let a=n(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function OD(e,n){return e===n}function Bi(e=kD){return F((n,t)=>{let r=!1;n.subscribe(O(t,o=>{r=!0,t.next(o)},()=>r?t.complete():t.error(e())))})}function kD(){return new gt}function Qr(e){return F((n,t)=>{try{n.subscribe(t)}finally{t.add(e)}})}function vt(e,n){let t=arguments.length>=2;return r=>r.pipe(e?ae((o,i)=>e(o,i,r)):pe,mt(1),t?Pt(n):Bi(()=>new gt))}function er(e){return e<=0?()=>Ce:F((n,t)=>{let r=[];n.subscribe(O(t,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)t.next(o);t.complete()},void 0,()=>{r=null}))})}function bc(e,n){let t=arguments.length>=2;return r=>r.pipe(e?ae((o,i)=>e(o,i,r)):pe,er(1),t?Pt(n):Bi(()=>new gt))}function Sc(e,n){return F(fh(e,n,arguments.length>=2,!0))}function Mc(...e){let n=kt(e);return F((t,r)=>{(n?Xn(e,t,n):Xn(e,t)).subscribe(r)})}function le(e,n){return F((t,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();t.subscribe(O(r,c=>{o?.unsubscribe();let u=0,l=i++;Y(e(c,l)).subscribe(o=O(r,d=>r.next(n?n(c,d,l,u++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Hi(e){return F((n,t)=>{Y(e).subscribe(O(t,()=>t.complete(),Wr)),!t.closed&&n.subscribe(t)})}function te(e,n,t){let r=w(e)||n||t?{next:e,error:n,complete:t}:e;return r?F((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(O(i,c=>{var u;(u=r.next)===null||u===void 0||u.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,c),i.error(c)},()=>{var c,u;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):pe}function hh(e){let n=N(null);try{return e()}finally{N(n)}}var Wi="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",y=class extends Error{code;constructor(n,t){super(to(n,t)),this.code=n}};function FD(e){return`NG0${Math.abs(e)}`}function to(e,n){return`${FD(e)}${n?": "+n:""}`}var no=globalThis;function B(e){for(let n in e)if(e[n]===B)return n;throw Error("")}function mh(e,n){for(let t in n)n.hasOwnProperty(t)&&!e.hasOwnProperty(t)&&(e[t]=n[t])}function Dt(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(Dt).join(", ")}]`;if(e==null)return""+e;let n=e.overriddenName||e.name;if(n)return`${n}`;let t=e.toString();if(t==null)return""+t;let r=t.indexOf(`
`);return r>=0?t.slice(0,r):t}function Bc(e,n){return e?n?`${e} ${n}`:e:n||""}var PD=B({__forward_ref__:B});function ke(e){return e.__forward_ref__=ke,e.toString=function(){return Dt(this())},e}function ce(e){return Hc(e)?e():e}function Hc(e){return typeof e=="function"&&e.hasOwnProperty(PD)&&e.__forward_ref__===ke}function vh(e,n){e==null&&Uc(n,e,null,"!=")}function Uc(e,n,t,r){throw new Error(`ASSERTION ERROR: ${e}`+(r==null?"":` [Expected=> ${t} ${r} ${n} <=Actual]`))}function D(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function nt(e){return{providers:e.providers||[],imports:e.imports||[]}}function ro(e){return LD(e,qi)}function $c(e){return ro(e)!==null}function LD(e,n){return e.hasOwnProperty(n)&&e[n]||null}function VD(e){let n=e?.[qi]??null;return n||null}function Ac(e){return e&&e.hasOwnProperty($i)?e[$i]:null}var qi=B({\u0275prov:B}),$i=B({\u0275inj:B}),C=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(n,t){this._desc=n,this.\u0275prov=void 0,typeof t=="number"?this.__NG_ELEMENT_ID__=t:t!==void 0&&(this.\u0275prov=D({token:this,providedIn:t.providedIn||"root",factory:t.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function zc(e){return e&&!!e.\u0275providers}var Gc=B({\u0275cmp:B}),Wc=B({\u0275dir:B}),qc=B({\u0275pipe:B}),Zc=B({\u0275mod:B}),Xr=B({\u0275fac:B}),hn=B({__NG_ELEMENT_ID__:B}),ph=B({__NG_ENV_ID__:B});function pn(e){return typeof e=="string"?e:e==null?"":String(e)}function zi(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():pn(e)}var Yc=B({ngErrorCode:B}),yh=B({ngErrorMessage:B}),Jr=B({ngTokenPath:B});function Qc(e,n){return Dh("",-200,n)}function Zi(e,n){throw new y(-201,!1)}function jD(e,n){e[Jr]??=[];let t=e[Jr],r;typeof n=="object"&&"multi"in n&&n?.multi===!0?(vh(n.provide,"Token with multi: true should have a provide property"),r=zi(n.provide)):r=zi(n),t[0]!==r&&e[Jr].unshift(r)}function BD(e,n){let t=e[Jr],r=e[Yc],o=e[yh]||e.message;return e.message=UD(o,r,t,n),e}function Dh(e,n,t){let r=new y(n,e);return r[Yc]=n,r[yh]=e,t&&(r[Jr]=t),r}function HD(e){return e[Yc]}function UD(e,n,t=[],r=null){let o="";t&&t.length>1&&(o=` Path: ${t.join(" -> ")}.`);let i=r?` Source: ${r}.`:"";return to(n,`${e}${i}${o}`)}var Nc;function Ch(){return Nc}function Se(e){let n=Nc;return Nc=e,n}function Kc(e,n,t){let r=ro(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(t&8)return null;if(n!==void 0)return n;Zi(e,"Injector")}var $D={},cn=$D,Rc="__NG_DI_FLAG__",xc=class{injector;constructor(n){this.injector=n}retrieve(n,t){let r=un(t)||0;try{return this.injector.get(n,r&8?null:cn,r)}catch(o){if(Ln(o))return o;throw o}}};function zD(e,n=0){let t=ai();if(t===void 0)throw new y(-203,!1);if(t===null)return Kc(e,void 0,n);{let r=GD(n),o=t.retrieve(e,r);if(Ln(o)){if(r.optional)return null;throw o}return o}}function I(e,n=0){return(Ch()||zD)(ce(e),n)}function p(e,n){return I(e,un(n))}function un(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function GD(e){return{optional:!!(e&8),host:!!(e&1),self:!!(e&2),skipSelf:!!(e&4)}}function Oc(e){let n=[];for(let t=0;t<e.length;t++){let r=ce(e[t]);if(Array.isArray(r)){if(r.length===0)throw new y(900,!1);let o,i=0;for(let s=0;s<r.length;s++){let a=r[s],c=WD(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}n.push(I(o,i))}else n.push(I(r))}return n}function Jc(e,n){return e[Rc]=n,e.prototype[Rc]=n,e}function WD(e){return e[Rc]}function ln(e,n){let t=e.hasOwnProperty(Xr);return t?e[Xr]:null}function Eh(e,n,t){if(e.length!==n.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=n[r];if(t&&(o=t(o),i=t(i)),i!==o)return!1}return!0}function Ih(e){return e.flat(Number.POSITIVE_INFINITY)}function Yi(e,n){e.forEach(t=>Array.isArray(t)?Yi(t,n):n(t))}function Xc(e,n,t){n>=e.length?e.push(t):e.splice(n,0,t)}function oo(e,n){return n>=e.length-1?e.pop():e.splice(n,1)[0]}function wh(e,n){let t=[];for(let r=0;r<e;r++)t.push(n);return t}function _h(e,n,t,r){let o=e.length;if(o==n)e.push(t,r);else if(o===1)e.push(r,e[0]),e[0]=t;else{for(o--,e.push(e[o-1],e[o]);o>n;){let i=o-2;e[o]=e[i],o--}e[n]=t,e[n+1]=r}}function bh(e,n,t){let r=nr(e,n);return r>=0?e[r|1]=t:(r=~r,_h(e,r,n,t)),r}function Qi(e,n){let t=nr(e,n);if(t>=0)return e[t|1]}function nr(e,n){return qD(e,n,1)}function qD(e,n,t){let r=0,o=e.length>>t;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<t];if(n===s)return i<<t;s>n?o=i:r=i+1}return~(o<<t)}var jt={},Me=[],Bt=new C(""),eu=new C("",-1),tu=new C(""),eo=class{get(n,t=cn){if(t===cn){let o=Dh("",-201);throw o.name="\u0275NotFound",o}return t}};function nu(e){return e[Zc]||null}function rt(e){return e[Gc]||null}function ru(e){return e[Wc]||null}function Sh(e){return e[qc]||null}function Ki(e){return{\u0275providers:e}}function Mh(...e){return{\u0275providers:ou(!0,e),\u0275fromNgModule:!0}}function ou(e,...n){let t=[],r=new Set,o,i=s=>{t.push(s)};return Yi(n,s=>{let a=s;Gi(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Th(o,i),t}function Th(e,n){for(let t=0;t<e.length;t++){let{ngModule:r,providers:o}=e[t];iu(o,i=>{n(i,r)})}}function Gi(e,n,t,r){if(e=ce(e),!e)return!1;let o=null,i=Ac(e),s=!i&&rt(e);if(!i&&!s){let c=e.ngModule;if(i=Ac(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of c)Gi(u,n,t,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{Yi(i.imports,l=>{Gi(l,n,t,r)&&(u||=[],u.push(l))})}finally{}u!==void 0&&Th(u,n)}if(!a){let u=ln(o)||(()=>new o);n({provide:o,useFactory:u,deps:Me},o),n({provide:tu,useValue:o,multi:!0},o),n({provide:Bt,useValue:()=>I(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let u=e;iu(c,l=>{n(l,u)})}}else return!1;return o!==e&&e.providers!==void 0}function iu(e,n){for(let t of e)zc(t)&&(t=t.\u0275providers),Array.isArray(t)?iu(t,n):n(t)}var ZD=B({provide:String,useValue:B});function Ah(e){return e!==null&&typeof e=="object"&&ZD in e}function YD(e){return!!(e&&e.useExisting)}function QD(e){return!!(e&&e.useFactory)}function dn(e){return typeof e=="function"}function Nh(e){return!!e.useClass}var io=new C(""),Ui={},gh={},Tc;function rr(){return Tc===void 0&&(Tc=new eo),Tc}var Z=class{},fn=class extends Z{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(n,t,r,o){super(),this.parent=t,this.source=r,this.scopes=o,Fc(n,s=>this.processProvider(s)),this.records.set(eu,tr(void 0,this)),o.has("environment")&&this.records.set(Z,tr(void 0,this));let i=this.records.get(io);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(tu,Me,{self:!0}))}retrieve(n,t){let r=un(t)||0;try{return this.get(n,cn,r)}catch(o){if(Ln(o))return o;throw o}}destroy(){Kr(this),this._destroyed=!0;let n=N(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let t=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of t)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),N(n)}}onDestroy(n){return Kr(this),this._onDestroyHooks.push(n),()=>this.removeOnDestroy(n)}runInContext(n){Kr(this);let t=et(this),r=Se(void 0),o;try{return n()}finally{et(t),Se(r)}}get(n,t=cn,r){if(Kr(this),n.hasOwnProperty(ph))return n[ph](this);let o=un(r),i,s=et(this),a=Se(void 0);try{if(!(o&4)){let u=this.records.get(n);if(u===void 0){let l=tC(n)&&ro(n);l&&this.injectableDefInScope(l)?u=tr(kc(n),Ui):u=null,this.records.set(n,u)}if(u!=null)return this.hydrate(n,u,o)}let c=o&2?rr():this.parent;return t=o&8&&t===cn?null:t,c.get(n,t)}catch(c){let u=HD(c);throw u===-200||u===-201?new y(u,null):c}finally{Se(a),et(s)}}resolveInjectorInitializers(){let n=N(null),t=et(this),r=Se(void 0),o;try{let i=this.get(Bt,Me,{self:!0});for(let s of i)s()}finally{et(t),Se(r),N(n)}}toString(){let n=[],t=this.records;for(let r of t.keys())n.push(Dt(r));return`R3Injector[${n.join(", ")}]`}processProvider(n){n=ce(n);let t=dn(n)?n:ce(n&&n.provide),r=JD(n);if(!dn(n)&&n.multi===!0){let o=this.records.get(t);o||(o=tr(void 0,Ui,!0),o.factory=()=>Oc(o.multi),this.records.set(t,o)),t=n,o.multi.push(n)}this.records.set(t,r)}hydrate(n,t,r){let o=N(null);try{if(t.value===gh)throw Qc(Dt(n));return t.value===Ui&&(t.value=gh,t.value=t.factory(void 0,r)),typeof t.value=="object"&&t.value&&eC(t.value)&&this._ngOnDestroyHooks.add(t.value),t.value}finally{N(o)}}injectableDefInScope(n){if(!n.providedIn)return!1;let t=ce(n.providedIn);return typeof t=="string"?t==="any"||this.scopes.has(t):this.injectorDefTypes.has(t)}removeOnDestroy(n){let t=this._onDestroyHooks.indexOf(n);t!==-1&&this._onDestroyHooks.splice(t,1)}};function kc(e){let n=ro(e),t=n!==null?n.factory:ln(e);if(t!==null)return t;if(e instanceof C)throw new y(204,!1);if(e instanceof Function)return KD(e);throw new y(204,!1)}function KD(e){if(e.length>0)throw new y(204,!1);let t=VD(e);return t!==null?()=>t.factory(e):()=>new e}function JD(e){if(Ah(e))return tr(void 0,e.useValue);{let n=su(e);return tr(n,Ui)}}function su(e,n,t){let r;if(dn(e)){let o=ce(e);return ln(o)||kc(o)}else if(Ah(e))r=()=>ce(e.useValue);else if(QD(e))r=()=>e.useFactory(...Oc(e.deps||[]));else if(YD(e))r=(o,i)=>I(ce(e.useExisting),i!==void 0&&i&8?8:void 0);else{let o=ce(e&&(e.useClass||e.provide));if(XD(e))r=()=>new o(...Oc(e.deps));else return ln(o)||kc(o)}return r}function Kr(e){if(e.destroyed)throw new y(205,!1)}function tr(e,n,t=!1){return{factory:e,value:n,multi:t?[]:void 0}}function XD(e){return!!e.deps}function eC(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function tC(e){return typeof e=="function"||typeof e=="object"&&e.ngMetadataName==="InjectionToken"}function Fc(e,n){for(let t of e)Array.isArray(t)?Fc(t,n):t&&zc(t)?Fc(t.\u0275providers,n):n(t)}function ge(e,n){let t;e instanceof fn?(Kr(e),t=e):t=new xc(e);let r,o=et(t),i=Se(void 0);try{return n()}finally{et(o),Se(i)}}function Rh(){return Ch()!==void 0||ai()!=null}var $e=0,b=1,S=2,ne=3,Fe=4,me=5,or=6,ir=7,de=8,gn=9,ot=10,Q=11,sr=12,au=13,mn=14,Ie=15,Ht=16,vn=17,it=18,so=19,cu=20,yt=21,Ji=22,ao=23,Te=24,yn=25,re=26,xh=1;var Ut=7,co=8,Dn=9,ve=10;function st(e){return Array.isArray(e)&&typeof e[xh]=="object"}function ze(e){return Array.isArray(e)&&e[xh]===!0}function uu(e){return(e.flags&4)!==0}function $t(e){return e.componentOffset>-1}function ar(e){return(e.flags&1)===1}function at(e){return!!e.template}function cr(e){return(e[S]&512)!==0}function Cn(e){return(e[S]&256)===256}var Oh="svg",kh="math";function Pe(e){for(;Array.isArray(e);)e=e[$e];return e}function lu(e,n){return Pe(n[e])}function Ge(e,n){return Pe(n[e.index])}function Xi(e,n){return e.data[n]}function Le(e,n){let t=n[e];return st(t)?t:t[$e]}function Fh(e){return(e[S]&4)===4}function es(e){return(e[S]&128)===128}function Ph(e){return ze(e[ne])}function zt(e,n){return n==null?null:e[n]}function du(e){e[vn]=0}function fu(e){e[S]&1024||(e[S]|=1024,es(e)&&ur(e))}function Lh(e,n){for(;e>0;)n=n[mn],e--;return n}function uo(e){return!!(e[S]&9216||e[Te]?.dirty)}function ts(e){e[ot].changeDetectionScheduler?.notify(8),e[S]&64&&(e[S]|=1024),uo(e)&&ur(e)}function ur(e){e[ot].changeDetectionScheduler?.notify(0);let n=Lt(e);for(;n!==null&&!(n[S]&8192||(n[S]|=8192,!es(n)));)n=Lt(n)}function hu(e,n){if(Cn(e))throw new y(911,!1);e[yt]===null&&(e[yt]=[]),e[yt].push(n)}function Vh(e,n){if(e[yt]===null)return;let t=e[yt].indexOf(n);t!==-1&&e[yt].splice(t,1)}function Lt(e){let n=e[ne];return ze(n)?n[ne]:n}function pu(e){return e[ir]??=[]}function gu(e){return e.cleanup??=[]}function jh(e,n,t,r){let o=pu(n);o.push(t),e.firstCreatePass&&gu(e).push(r,o.length-1)}var x={lFrame:np(null),bindingsEnabled:!0,skipHydrationRootTNode:null},lo=function(e){return e[e.Off=0]="Off",e[e.Exhaustive=1]="Exhaustive",e[e.OnlyDirtyViews=2]="OnlyDirtyViews",e}(lo||{}),nC=0,Pc=!1;function Bh(){return x.lFrame.elementDepthCount}function Hh(){x.lFrame.elementDepthCount++}function Uh(){x.lFrame.elementDepthCount--}function ns(){return x.bindingsEnabled}function mu(){return x.skipHydrationRootTNode!==null}function $h(e){return x.skipHydrationRootTNode===e}function zh(){x.skipHydrationRootTNode=null}function V(){return x.lFrame.lView}function fe(){return x.lFrame.tView}function Gh(e){return x.lFrame.contextLView=e,e[de]}function Wh(e){return x.lFrame.contextLView=null,e}function oe(){let e=vu();for(;e!==null&&e.type===64;)e=e.parent;return e}function vu(){return x.lFrame.currentTNode}function qh(){let e=x.lFrame,n=e.currentTNode;return e.isParent?n:n.parent}function lr(e,n){let t=x.lFrame;t.currentTNode=e,t.isParent=n}function yu(){return x.lFrame.isParent}function Du(){x.lFrame.isParent=!1}function Cu(e){Uc("Must never be called in production mode"),nC=e}function Eu(){return Pc}function Iu(e){let n=Pc;return Pc=e,n}function Zh(){let e=x.lFrame,n=e.bindingRootIndex;return n===-1&&(n=e.bindingRootIndex=e.tView.bindingStartIndex),n}function Yh(){return x.lFrame.bindingIndex}function Qh(e){return x.lFrame.bindingIndex=e}function rs(){return x.lFrame.bindingIndex++}function wu(e){let n=x.lFrame,t=n.bindingIndex;return n.bindingIndex=n.bindingIndex+e,t}function Kh(){return x.lFrame.inI18n}function Jh(e,n){let t=x.lFrame;t.bindingIndex=t.bindingRootIndex=e,os(n)}function Xh(){return x.lFrame.currentDirectiveIndex}function os(e){x.lFrame.currentDirectiveIndex=e}function ep(e){let n=x.lFrame.currentDirectiveIndex;return n===-1?null:e[n]}function _u(){return x.lFrame.currentQueryIndex}function is(e){x.lFrame.currentQueryIndex=e}function rC(e){let n=e[b];return n.type===2?n.declTNode:n.type===1?e[me]:null}function bu(e,n,t){if(t&4){let o=n,i=e;for(;o=o.parent,o===null&&!(t&1);)if(o=rC(i),o===null||(i=i[mn],o.type&10))break;if(o===null)return!1;n=o,e=i}let r=x.lFrame=tp();return r.currentTNode=n,r.lView=e,!0}function ss(e){let n=tp(),t=e[b];x.lFrame=n,n.currentTNode=t.firstChild,n.lView=e,n.tView=t,n.contextLView=e,n.bindingIndex=t.bindingStartIndex,n.inI18n=!1}function tp(){let e=x.lFrame,n=e===null?null:e.child;return n===null?np(e):n}function np(e){let n={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=n),n}function rp(){let e=x.lFrame;return x.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Su=rp;function as(){let e=rp();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function op(e){return(x.lFrame.contextLView=Lh(e,x.lFrame.contextLView))[de]}function Gt(){return x.lFrame.selectedIndex}function Wt(e){x.lFrame.selectedIndex=e}function Mu(){let e=x.lFrame;return Xi(e.tView,e.selectedIndex)}function ip(){return x.lFrame.currentNamespace}var sp=!0;function cs(){return sp}function fo(e){sp=e}function Lc(e,n=null,t=null,r){let o=Tu(e,n,t,r);return o.resolveInjectorInitializers(),o}function Tu(e,n=null,t=null,r,o=new Set){let i=[t||Me,Mh(e)];return r=r||(typeof e=="object"?void 0:Dt(e)),new fn(i,n||rr(),r||null,o)}var ue=class e{static THROW_IF_NOT_FOUND=cn;static NULL=new eo;static create(n,t){if(Array.isArray(n))return Lc({name:""},t,n,"");{let r=n.name??"";return Lc({name:r},n.parent,n.providers,r)}}static \u0275prov=D({token:e,providedIn:"any",factory:()=>I(eu)});static __NG_ELEMENT_ID__=-1},ee=new C(""),ct=(()=>{class e{static __NG_ELEMENT_ID__=oC;static __NG_ENV_ID__=t=>t}return e})(),Vc=class extends ct{_lView;constructor(n){super(),this._lView=n}get destroyed(){return Cn(this._lView)}onDestroy(n){let t=this._lView;return hu(t,n),()=>Vh(t,n)}};function oC(){return new Vc(V())}var Ue=class{_console=console;handleError(n){this._console.error("ERROR",n)}},Ae=new C("",{providedIn:"root",factory:()=>{let e=p(Z),n;return t=>{e.destroyed&&!n?setTimeout(()=>{throw t}):(n??=e.get(Ue),n.handleError(t))}}}),ap={provide:Bt,useValue:()=>void p(Ue),multi:!0};function Ct(e,n){let[t,r,o]=dc(e,n?.equal),i=t,s=i[he];return i.set=r,i.update=o,i.asReadonly=cp.bind(i),i}function cp(){let e=this[he];if(e.readonlyFn===void 0){let n=()=>this();n[he]=e,e.readonlyFn=n}return e.readonlyFn}var Vt=class{},us=new C("",{providedIn:"root",factory:()=>!1});var Au=new C(""),Nu=new C("");var ls=(()=>{class e{view;node;constructor(t,r){this.view=t,this.node=r}static __NG_ELEMENT_ID__=iC}return e})();function iC(){return new ls(V(),oe())}var Et=(()=>{class e{taskId=0;pendingTasks=new Set;destroyed=!1;pendingTask=new J(!1);get hasPendingTasks(){return this.destroyed?!1:this.pendingTask.value}get hasPendingTasksObservable(){return this.destroyed?new k(t=>{t.next(!1),t.complete()}):this.pendingTask}add(){!this.hasPendingTasks&&!this.destroyed&&this.pendingTask.next(!0);let t=this.taskId++;return this.pendingTasks.add(t),t}has(t){return this.pendingTasks.has(t)}remove(t){this.pendingTasks.delete(t),this.pendingTasks.size===0&&this.hasPendingTasks&&this.pendingTask.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks&&this.pendingTask.next(!1),this.destroyed=!0,this.pendingTask.unsubscribe()}static \u0275prov=D({token:e,providedIn:"root",factory:()=>new e})}return e})();function ho(...e){}var Ru=(()=>{class e{static \u0275prov=D({token:e,providedIn:"root",factory:()=>new jc})}return e})(),jc=class{dirtyEffectCount=0;queues=new Map;add(n){this.enqueue(n),this.schedule(n)}schedule(n){n.dirty&&this.dirtyEffectCount++}remove(n){let t=n.zone,r=this.queues.get(t);r.has(n)&&(r.delete(n),n.dirty&&this.dirtyEffectCount--)}enqueue(n){let t=n.zone;this.queues.has(t)||this.queues.set(t,new Set);let r=this.queues.get(t);r.has(n)||r.add(n)}flush(){for(;this.dirtyEffectCount>0;){let n=!1;for(let[t,r]of this.queues)t===null?n||=this.flushQueue(r):n||=t.run(()=>this.flushQueue(r));n||(this.dirtyEffectCount=0)}}flushQueue(n){let t=!1;for(let r of n)r.dirty&&(this.dirtyEffectCount--,t=!0,r.run());return t}};function mr(e){return{toString:e}.toString()}var ds="__parameters__";function mC(e){return function(...t){if(e){let r=e(...t);for(let o in r)this[o]=r[o]}}}function Lp(e,n,t){return mr(()=>{let r=mC(n);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,u,l){let d=c.hasOwnProperty(ds)?c[ds]:Object.defineProperty(c,ds,{value:[]})[ds];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var pl=Jc(Lp("Optional"),8);var gl=Jc(Lp("SkipSelf"),4);function vC(e){return typeof e=="function"}var ys=class{previousValue;currentValue;firstChange;constructor(n,t,r){this.previousValue=n,this.currentValue=t,this.firstChange=r}isFirstChange(){return this.firstChange}};function Vp(e,n,t,r){n!==null?n.applyValueToInputSignal(n,r):e[t]=r}var we=(()=>{let e=()=>jp;return e.ngInherit=!0,e})();function jp(e){return e.type.prototype.ngOnChanges&&(e.setInput=DC),yC}function yC(){let e=Hp(this),n=e?.current;if(n){let t=e.previous;if(t===jt)e.previous=n;else for(let r in n)t[r]=n[r];e.current=null,this.ngOnChanges(n)}}function DC(e,n,t,r,o){let i=this.declaredInputs[r],s=Hp(e)||CC(e,{previous:jt,current:null}),a=s.current||(s.current={}),c=s.previous,u=c[i];a[i]=new ys(u&&u.currentValue,t,c===jt),Vp(e,n,o,t)}var Bp="__ngSimpleChanges__";function Hp(e){return e[Bp]||null}function CC(e,n){return e[Bp]=n}var up=[];var H=function(e,n=null,t){for(let r=0;r<up.length;r++){let o=up[r];o(e,n,t)}};function EC(e,n,t){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=n.type.prototype;if(r){let s=jp(n);(t.preOrderHooks??=[]).push(e,s),(t.preOrderCheckHooks??=[]).push(e,s)}o&&(t.preOrderHooks??=[]).push(0-e,o),i&&((t.preOrderHooks??=[]).push(e,i),(t.preOrderCheckHooks??=[]).push(e,i))}function Up(e,n){for(let t=n.directiveStart,r=n.directiveEnd;t<r;t++){let i=e.data[t].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:u,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-t,s),a&&((e.contentHooks??=[]).push(t,a),(e.contentCheckHooks??=[]).push(t,a)),c&&(e.viewHooks??=[]).push(-t,c),u&&((e.viewHooks??=[]).push(t,u),(e.viewCheckHooks??=[]).push(t,u)),l!=null&&(e.destroyHooks??=[]).push(t,l)}}function ps(e,n,t){$p(e,n,3,t)}function gs(e,n,t,r){(e[S]&3)===t&&$p(e,n,t,r)}function xu(e,n){let t=e[S];(t&3)===n&&(t&=16383,t+=1,e[S]=t)}function $p(e,n,t,r){let o=r!==void 0?e[vn]&65535:0,i=r??-1,s=n.length-1,a=0;for(let c=o;c<s;c++)if(typeof n[c+1]=="number"){if(a=n[c],r!=null&&a>=r)break}else n[c]<0&&(e[vn]+=65536),(a<i||i==-1)&&(IC(e,t,n,c),e[vn]=(e[vn]&**********)+c+2),c++}function lp(e,n){H(4,e,n);let t=N(null);try{n.call(e)}finally{N(t),H(5,e,n)}}function IC(e,n,t,r){let o=t[r]<0,i=t[r+1],s=o?-t[r]:t[r],a=e[s];o?e[S]>>14<e[vn]>>16&&(e[S]&3)===n&&(e[S]+=16384,lp(a,i)):lp(a,i)}var fr=-1,In=class{factory;name;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(n,t,r,o){this.factory=n,this.name=o,this.canSeeViewProviders=t,this.injectImpl=r}};function wC(e){return(e.flags&8)!==0}function _C(e){return(e.flags&16)!==0}function bC(e,n,t){let r=0;for(;r<t.length;){let o=t[r];if(typeof o=="number"){if(o!==0)break;r++;let i=t[r++],s=t[r++],a=t[r++];e.setAttribute(n,s,a,i)}else{let i=o,s=t[++r];SC(i)?e.setProperty(n,i,s):e.setAttribute(n,i,s),r++}}return r}function zp(e){return e===3||e===4||e===6}function SC(e){return e.charCodeAt(0)===64}function hr(e,n){if(!(n===null||n.length===0))if(e===null||e.length===0)e=n.slice();else{let t=-1;for(let r=0;r<n.length;r++){let o=n[r];typeof o=="number"?t=o:t===0||(t===-1||t===2?dp(e,t,o,null,n[++r]):dp(e,t,o,null,null))}}return e}function dp(e,n,t,r,o){let i=0,s=e.length;if(n===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===n){s=-1;break}else if(a>n){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===t){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,n),i=s+1),e.splice(i++,0,t),o!==null&&e.splice(i++,0,o)}function Gp(e){return e!==fr}function Ds(e){return e&32767}function MC(e){return e>>16}function Cs(e,n){let t=MC(e),r=n;for(;t>0;)r=r[mn],t--;return r}var Uu=!0;function fp(e){let n=Uu;return Uu=e,n}var TC=256,Wp=TC-1,qp=5,AC=0,ut={};function NC(e,n,t){let r;typeof t=="string"?r=t.charCodeAt(0)||0:t.hasOwnProperty(hn)&&(r=t[hn]),r==null&&(r=t[hn]=AC++);let o=r&Wp,i=1<<o;n.data[e+(o>>qp)]|=i}function Es(e,n){let t=Zp(e,n);if(t!==-1)return t;let r=n[b];r.firstCreatePass&&(e.injectorIndex=n.length,Ou(r.data,e),Ou(n,null),Ou(r.blueprint,null));let o=ml(e,n),i=e.injectorIndex;if(Gp(o)){let s=Ds(o),a=Cs(o,n),c=a[b].data;for(let u=0;u<8;u++)n[i+u]=a[s+u]|c[s+u]}return n[i+8]=o,i}function Ou(e,n){e.push(0,0,0,0,0,0,0,0,n)}function Zp(e,n){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||n[e.injectorIndex+8]===null?-1:e.injectorIndex}function ml(e,n){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let t=0,r=null,o=n;for(;o!==null;){if(r=Xp(o),r===null)return fr;if(t++,o=o[mn],r.injectorIndex!==-1)return r.injectorIndex|t<<16}return fr}function $u(e,n,t){NC(e,n,t)}function RC(e,n){if(n==="class")return e.classes;if(n==="style")return e.styles;let t=e.attrs;if(t){let r=t.length,o=0;for(;o<r;){let i=t[o];if(zp(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof t[o]=="string";)o++;else{if(i===n)return t[o+1];o=o+2}}}return null}function Yp(e,n,t){if(t&8||e!==void 0)return e;Zi(n,"NodeInjector")}function Qp(e,n,t,r){if(t&8&&r===void 0&&(r=null),(t&3)===0){let o=e[gn],i=Se(void 0);try{return o?o.get(n,r,t&8):Kc(n,r,t&8)}finally{Se(i)}}return Yp(r,n,t)}function Kp(e,n,t,r=0,o){if(e!==null){if(n[S]&2048&&!(r&2)){let s=FC(e,n,t,r,ut);if(s!==ut)return s}let i=Jp(e,n,t,r,ut);if(i!==ut)return i}return Qp(n,t,r,o)}function Jp(e,n,t,r,o){let i=OC(t);if(typeof i=="function"){if(!bu(n,e,r))return r&1?Yp(o,t,r):Qp(n,t,r,o);try{let s;if(s=i(r),s==null&&!(r&8))Zi(t);else return s}finally{Su()}}else if(typeof i=="number"){let s=null,a=Zp(e,n),c=fr,u=r&1?n[Ie][me]:null;for((a===-1||r&4)&&(c=a===-1?ml(e,n):n[a+8],c===fr||!pp(r,!1)?a=-1:(s=n[b],a=Ds(c),n=Cs(c,n)));a!==-1;){let l=n[b];if(hp(i,a,l.data)){let d=xC(a,n,t,s,r,u);if(d!==ut)return d}c=n[a+8],c!==fr&&pp(r,n[b].data[a+8]===u)&&hp(i,a,n)?(s=l,a=Ds(c),n=Cs(c,n)):a=-1}}return o}function xC(e,n,t,r,o,i){let s=n[b],a=s.data[e+8],c=r==null?$t(a)&&Uu:r!=s&&(a.type&3)!==0,u=o&1&&i===a,l=ms(a,s,t,c,u);return l!==null?mo(n,s,l,a,o):ut}function ms(e,n,t,r,o){let i=e.providerIndexes,s=n.data,a=i&1048575,c=e.directiveStart,u=e.directiveEnd,l=i>>20,d=r?a:a+l,h=o?a+l:u;for(let f=d;f<h;f++){let v=s[f];if(f<c&&t===v||f>=c&&v.type===t)return f}if(o){let f=s[c];if(f&&at(f)&&f.type===t)return c}return null}function mo(e,n,t,r,o){let i=e[t],s=n.data;if(i instanceof In){let a=i;if(a.resolving){let f=zi(s[t]);throw Qc(f)}let c=fp(a.canSeeViewProviders);a.resolving=!0;let u=s[t].type||s[t],l,d=a.injectImpl?Se(a.injectImpl):null,h=bu(e,r,0);try{i=e[t]=a.factory(void 0,o,s,e,r),n.firstCreatePass&&t>=r.directiveStart&&EC(t,s[t],n)}finally{d!==null&&Se(d),fp(c),a.resolving=!1,Su()}}return i}function OC(e){if(typeof e=="string")return e.charCodeAt(0)||0;let n=e.hasOwnProperty(hn)?e[hn]:void 0;return typeof n=="number"?n>=0?n&Wp:kC:n}function hp(e,n,t){let r=1<<e;return!!(t[n+(e>>qp)]&r)}function pp(e,n){return!(e&2)&&!(e&1&&n)}var En=class{_tNode;_lView;constructor(n,t){this._tNode=n,this._lView=t}get(n,t,r){return Kp(this._tNode,this._lView,n,un(r),t)}};function kC(){return new En(oe(),V())}function lt(e){return mr(()=>{let n=e.prototype.constructor,t=n[Xr]||zu(n),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[Xr]||zu(o);if(i&&i!==t)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function zu(e){return Hc(e)?()=>{let n=zu(ce(e));return n&&n()}:ln(e)}function FC(e,n,t,r,o){let i=e,s=n;for(;i!==null&&s!==null&&s[S]&2048&&!cr(s);){let a=Jp(i,s,t,r|2,ut);if(a!==ut)return a;let c=i.parent;if(!c){let u=s[cu];if(u){let l=u.get(t,ut,r);if(l!==ut)return l}c=Xp(s),s=s[mn]}i=c}return o}function Xp(e){let n=e[b],t=n.type;return t===2?n.declTNode:t===1?e[me]:null}function Yt(e){return RC(oe(),e)}function PC(){return vr(oe(),V())}function vr(e,n){return new G(Ge(e,n))}var G=(()=>{class e{nativeElement;constructor(t){this.nativeElement=t}static __NG_ELEMENT_ID__=PC}return e})();function LC(e){return e instanceof G?e.nativeElement:e}function VC(){return this._results[Symbol.iterator]()}var Is=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new U}constructor(n=!1){this._emitDistinctChangesOnly=n}get(n){return this._results[n]}map(n){return this._results.map(n)}filter(n){return this._results.filter(n)}find(n){return this._results.find(n)}reduce(n,t){return this._results.reduce(n,t)}forEach(n){this._results.forEach(n)}some(n){return this._results.some(n)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(n,t){this.dirty=!1;let r=Ih(n);(this._changesDetected=!Eh(this._results,r,t))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(n){this._onDirty=n}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=VC};function eg(e){return(e.flags&128)===128}var vl=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(vl||{}),tg=new Map,jC=0;function BC(){return jC++}function HC(e){tg.set(e[so],e)}function Gu(e){tg.delete(e[so])}var gp="__ngContext__";function pr(e,n){st(n)?(e[gp]=n[so],HC(n)):e[gp]=n}function ng(e){return og(e[sr])}function rg(e){return og(e[Fe])}function og(e){for(;e!==null&&!ze(e);)e=e[Fe];return e}var Wu;function yl(e){Wu=e}function ig(){if(Wu!==void 0)return Wu;if(typeof document<"u")return document;throw new y(210,!1)}var Ps=new C("",{providedIn:"root",factory:()=>UC}),UC="ng",Ls=new C(""),yr=new C("",{providedIn:"platform",factory:()=>"unknown"});var Vs=new C("",{providedIn:"root",factory:()=>ig().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var $C="h",zC="b";var sg=!1,ag=new C("",{providedIn:"root",factory:()=>sg});var GC=(e,n,t,r)=>{};function WC(e,n,t,r){GC(e,n,t,r)}function js(e){return(e.flags&32)===32}var qC=()=>null;function cg(e,n,t=!1){return qC(e,n,t)}function ug(e,n){let t=e.contentQueries;if(t!==null){let r=N(null);try{for(let o=0;o<t.length;o+=2){let i=t[o],s=t[o+1];if(s!==-1){let a=e.data[s];is(i),a.contentQueries(2,n[s],s)}}}finally{N(r)}}}function qu(e,n,t){is(0);let r=N(null);try{n(e,t)}finally{N(r)}}function Dl(e,n,t){if(uu(n)){let r=N(null);try{let o=n.directiveStart,i=n.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=t[s];a.contentQueries(1,c,s)}}}finally{N(r)}}}var It=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(It||{});var fs;function ZC(){if(fs===void 0&&(fs=null,no.trustedTypes))try{fs=no.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return fs}function mp(e){return ZC()?.createScriptURL(e)||e}var ws=class{changingThisBreaksApplicationSecurity;constructor(n){this.changingThisBreaksApplicationSecurity=n}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Wi})`}};function Bs(e){return e instanceof ws?e.changingThisBreaksApplicationSecurity:e}function Cl(e,n){let t=lg(e);if(t!=null&&t!==n){if(t==="ResourceURL"&&n==="URL")return!0;throw new Error(`Required a safe ${n}, got a ${t} (see ${Wi})`)}return t===n}function lg(e){return e instanceof ws&&e.getTypeName()||null}var YC=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function dg(e){return e=String(e),e.match(YC)?e:"unsafe:"+e}var Hs=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Hs||{});function fg(e){let n=pg();return n?n.sanitize(Hs.URL,e)||"":Cl(e,"URL")?Bs(e):dg(pn(e))}function hg(e){let n=pg();if(n)return mp(n.sanitize(Hs.RESOURCE_URL,e)||"");if(Cl(e,"ResourceURL"))return mp(Bs(e));throw new y(904,!1)}function QC(e,n){return n==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||n==="href"&&(e==="base"||e==="link")?hg:fg}function El(e,n,t){return QC(n,t)(e)}function pg(){let e=V();return e&&e[ot].sanitizer}var KC=/^>|^->|<!--|-->|--!>|<!-$/g,JC=/(<|>)/g,XC="\u200B$1\u200B";function eE(e){return e.replace(KC,n=>n.replace(JC,XC))}function gg(e){return e instanceof Function?e():e}function tE(e,n,t){let r=e.length;for(;;){let o=e.indexOf(n,t);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=n.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}t=o+1}}var mg="ng-template";function nE(e,n,t,r){let o=0;if(r){for(;o<n.length&&typeof n[o]=="string";o+=2)if(n[o]==="class"&&tE(n[o+1].toLowerCase(),t,0)!==-1)return!0}else if(Il(e))return!1;if(o=n.indexOf(1,o),o>-1){let i;for(;++o<n.length&&typeof(i=n[o])=="string";)if(i.toLowerCase()===t)return!0}return!1}function Il(e){return e.type===4&&e.value!==mg}function rE(e,n,t){let r=e.type===4&&!t?mg:e.value;return n===r}function oE(e,n,t){let r=4,o=e.attrs,i=o!==null?aE(o):0,s=!1;for(let a=0;a<n.length;a++){let c=n[a];if(typeof c=="number"){if(!s&&!We(r)&&!We(c))return!1;if(s&&We(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!rE(e,c,t)||c===""&&n.length===1){if(We(r))return!1;s=!0}}else if(r&8){if(o===null||!nE(e,o,c,t)){if(We(r))return!1;s=!0}}else{let u=n[++a],l=iE(c,o,Il(e),t);if(l===-1){if(We(r))return!1;s=!0;continue}if(u!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&u!==d){if(We(r))return!1;s=!0}}}}return We(r)||s}function We(e){return(e&1)===0}function iE(e,n,t,r){if(n===null)return-1;let o=0;if(r||!t){let i=!1;for(;o<n.length;){let s=n[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=n[++o];for(;typeof a=="string";)a=n[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return cE(n,e)}function vg(e,n,t=!1){for(let r=0;r<n.length;r++)if(oE(e,n[r],t))return!0;return!1}function sE(e){let n=e.attrs;if(n!=null){let t=n.indexOf(5);if((t&1)===0)return n[t+1]}return null}function aE(e){for(let n=0;n<e.length;n++){let t=e[n];if(zp(t))return n}return e.length}function cE(e,n){let t=e.indexOf(4);if(t>-1)for(t++;t<e.length;){let r=e[t];if(typeof r=="number")return-1;if(r===n)return t;t++}return-1}function uE(e,n){e:for(let t=0;t<n.length;t++){let r=n[t];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function vp(e,n){return e?":not("+n.trim()+")":n}function lE(e){let n=e[0],t=1,r=2,o="",i=!1;for(;t<e.length;){let s=e[t];if(typeof s=="string")if(r&2){let a=e[++t];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!We(s)&&(n+=vp(i,o),o=""),r=s,i=i||!We(r);t++}return o!==""&&(n+=vp(i,o)),n}function dE(e){return e.map(lE).join(",")}function fE(e){let n=[],t=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&n.push(i,e[++r]):o===8&&t.push(i);else{if(!We(o))break;o=i}r++}return t.length&&n.push(1,...t),n}var Ye={};function hE(e,n){return e.createText(n)}function pE(e,n,t){e.setValue(n,t)}function gE(e,n){return e.createComment(eE(n))}function yg(e,n,t){return e.createElement(n,t)}function _s(e,n,t,r,o){e.insertBefore(n,t,r,o)}function Dg(e,n,t){e.appendChild(n,t)}function yp(e,n,t,r,o){r!==null?_s(e,n,t,r,o):Dg(e,n,t)}function mE(e,n,t){e.removeChild(null,n,t)}function vE(e,n,t){e.setAttribute(n,"style",t)}function yE(e,n,t){t===""?e.removeAttribute(n,"class"):e.setAttribute(n,"class",t)}function Cg(e,n,t){let{mergedAttrs:r,classes:o,styles:i}=t;r!==null&&bC(e,n,r),o!==null&&yE(e,n,o),i!==null&&vE(e,n,i)}function wl(e,n,t,r,o,i,s,a,c,u,l){let d=re+r,h=d+o,f=DE(d,h),v=typeof u=="function"?u():u;return f[b]={type:e,blueprint:f,template:t,queries:null,viewQuery:a,declTNode:n,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:v,incompleteFirstPass:!1,ssrId:l}}function DE(e,n){let t=[];for(let r=0;r<n;r++)t.push(r<e?null:Ye);return t}function CE(e){let n=e.tView;return n===null||n.incompleteFirstPass?e.tView=wl(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):n}function _l(e,n,t,r,o,i,s,a,c,u,l){let d=n.blueprint.slice();return d[$e]=o,d[S]=r|4|128|8|64|1024,(u!==null||e&&e[S]&2048)&&(d[S]|=2048),du(d),d[ne]=d[mn]=e,d[de]=t,d[ot]=s||e&&e[ot],d[Q]=a||e&&e[Q],d[gn]=c||e&&e[gn]||null,d[me]=i,d[so]=BC(),d[or]=l,d[cu]=u,d[Ie]=n.type==2?e[Ie]:d,d}function EE(e,n,t){let r=Ge(n,e),o=CE(t),i=e[ot].rendererFactory,s=bl(e,_l(e,o,null,Eg(t),r,n,null,i.createRenderer(r,t),null,null,null));return e[n.index]=s}function Eg(e){let n=16;return e.signals?n=4096:e.onPush&&(n=64),n}function Ig(e,n,t,r){if(t===0)return-1;let o=n.length;for(let i=0;i<t;i++)n.push(r),e.blueprint.push(r),e.data.push(null);return o}function bl(e,n){return e[sr]?e[au][Fe]=n:e[sr]=n,e[au]=n,n}function IE(e=1){wg(fe(),V(),Gt()+e,!1)}function wg(e,n,t,r){if(!r)if((n[S]&3)===3){let i=e.preOrderCheckHooks;i!==null&&ps(n,i,t)}else{let i=e.preOrderHooks;i!==null&&gs(n,i,0,t)}Wt(t)}var Us=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Us||{});function Zu(e,n,t,r){let o=N(null);try{let[i,s,a]=e.inputs[t],c=null;(s&Us.SignalBased)!==0&&(c=n[i][he]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(n,r)),e.setInput!==null?e.setInput(n,c,r,t,i):Vp(n,c,i,r)}finally{N(o)}}var qe=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(qe||{}),wE;function Sl(e,n){return wE(e,n)}function dr(e,n,t,r,o){if(r!=null){let i,s=!1;ze(r)?i=r:st(r)&&(s=!0,r=r[$e]);let a=Pe(r);e===0&&t!==null?o==null?Dg(n,t,a):_s(n,t,a,o||null,!0):e===1&&t!==null?_s(n,t,a,o||null,!0):e===2?mE(n,a,s):e===3&&n.destroyNode(a),i!=null&&OE(n,e,i,t,o)}}function _E(e,n){_g(e,n),n[$e]=null,n[me]=null}function bE(e,n,t,r,o,i){r[$e]=o,r[me]=n,$s(e,r,t,1,o,i)}function _g(e,n){n[ot].changeDetectionScheduler?.notify(9),$s(e,n,n[Q],2,null,null)}function SE(e){let n=e[sr];if(!n)return ku(e[b],e);for(;n;){let t=null;if(st(n))t=n[sr];else{let r=n[ve];r&&(t=r)}if(!t){for(;n&&!n[Fe]&&n!==e;)st(n)&&ku(n[b],n),n=n[ne];n===null&&(n=e),st(n)&&ku(n[b],n),t=n&&n[Fe]}n=t}}function Ml(e,n){let t=e[Dn],r=t.indexOf(n);t.splice(r,1)}function bg(e,n){if(Cn(n))return;let t=n[Q];t.destroyNode&&$s(e,n,t,3,null,null),SE(n)}function ku(e,n){if(Cn(n))return;let t=N(null);try{n[S]&=-129,n[S]|=256,n[Te]&&gi(n[Te]),TE(e,n),ME(e,n),n[b].type===1&&n[Q].destroy();let r=n[Ht];if(r!==null&&ze(n[ne])){r!==n[ne]&&Ml(r,n);let o=n[it];o!==null&&o.detachView(e)}Gu(n)}finally{N(t)}}function ME(e,n){let t=e.cleanup,r=n[ir];if(t!==null)for(let s=0;s<t.length-1;s+=2)if(typeof t[s]=="string"){let a=t[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[t[s+1]];t[s].call(a)}r!==null&&(n[ir]=null);let o=n[yt];if(o!==null){n[yt]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=n[ao];if(i!==null){n[ao]=null;for(let s of i)s.destroy()}}function TE(e,n){let t;if(e!=null&&(t=e.destroyHooks)!=null)for(let r=0;r<t.length;r+=2){let o=n[t[r]];if(!(o instanceof In)){let i=t[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];H(4,a,c);try{c.call(a)}finally{H(5,a,c)}}else{H(4,o,i);try{i.call(o)}finally{H(5,o,i)}}}}}function Sg(e,n,t){return AE(e,n.parent,t)}function AE(e,n,t){let r=n;for(;r!==null&&r.type&168;)n=r,r=n.parent;if(r===null)return t[$e];if($t(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===It.None||o===It.Emulated)return null}return Ge(r,t)}function Mg(e,n,t){return RE(e,n,t)}function NE(e,n,t){return e.type&40?Ge(e,t):null}var RE=NE,Dp;function Tl(e,n,t,r){let o=Sg(e,r,n),i=n[Q],s=r.parent||n[me],a=Mg(s,r,n);if(o!=null)if(Array.isArray(t))for(let c=0;c<t.length;c++)yp(i,o,t[c],a,!1);else yp(i,o,t,a,!1);Dp!==void 0&&Dp(i,r,n,t,o)}function po(e,n){if(n!==null){let t=n.type;if(t&3)return Ge(n,e);if(t&4)return Yu(-1,e[n.index]);if(t&8){let r=n.child;if(r!==null)return po(e,r);{let o=e[n.index];return ze(o)?Yu(-1,o):Pe(o)}}else{if(t&128)return po(e,n.next);if(t&32)return Sl(n,e)()||Pe(e[n.index]);{let r=Tg(e,n);if(r!==null){if(Array.isArray(r))return r[0];let o=Lt(e[Ie]);return po(o,r)}else return po(e,n.next)}}}return null}function Tg(e,n){if(n!==null){let r=e[Ie][me],o=n.projection;return r.projection[o]}return null}function Yu(e,n){let t=ve+e+1;if(t<n.length){let r=n[t],o=r[b].firstChild;if(o!==null)return po(r,o)}return n[Ut]}function Al(e,n,t,r,o,i,s){for(;t!=null;){if(t.type===128){t=t.next;continue}let a=r[t.index],c=t.type;if(s&&n===0&&(a&&pr(Pe(a),r),t.flags|=2),!js(t))if(c&8)Al(e,n,t.child,r,o,i,!1),dr(n,e,o,a,i);else if(c&32){let u=Sl(t,r),l;for(;l=u();)dr(n,e,o,l,i);dr(n,e,o,a,i)}else c&16?Ag(e,n,r,t,o,i):dr(n,e,o,a,i);t=s?t.projectionNext:t.next}}function $s(e,n,t,r,o,i){Al(t,r,e.firstChild,n,o,i,!1)}function xE(e,n,t){let r=n[Q],o=Sg(e,t,n),i=t.parent||n[me],s=Mg(i,t,n);Ag(r,0,n,t,o,s)}function Ag(e,n,t,r,o,i){let s=t[Ie],c=s[me].projection[r.projection];if(Array.isArray(c))for(let u=0;u<c.length;u++){let l=c[u];dr(n,e,o,l,i)}else{let u=c,l=s[ne];eg(r)&&(u.flags|=128),Al(e,n,u,l,o,i,!0)}}function OE(e,n,t,r,o){let i=t[Ut],s=Pe(t);i!==s&&dr(n,e,r,i,o);for(let a=ve;a<t.length;a++){let c=t[a];$s(c[b],c,e,n,r,i)}}function kE(e,n,t,r,o){if(n)o?e.addClass(t,r):e.removeClass(t,r);else{let i=r.indexOf("-")===-1?void 0:qe.DashCase;o==null?e.removeStyle(t,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=qe.Important),e.setStyle(t,r,o,i))}}function Ng(e,n,t,r,o){let i=Gt(),s=r&2;try{Wt(-1),s&&n.length>re&&wg(e,n,re,!1),H(s?2:0,o,t),t(r,o)}finally{Wt(i),H(s?3:1,o,t)}}function zs(e,n,t){UE(e,n,t),(t.flags&64)===64&&$E(e,n,t)}function Co(e,n,t=Ge){let r=n.localNames;if(r!==null){let o=n.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?t(n,e):e[s];e[o++]=a}}}function FE(e,n,t,r){let i=r.get(ag,sg)||t===It.ShadowDom,s=e.selectRootElement(n,i);return PE(s),s}function PE(e){LE(e)}var LE=()=>null;function VE(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function jE(e,n,t,r,o,i){let s=n[b];if(Ol(e,s,n,t,r)){$t(e)&&HE(n,e.index);return}e.type&3&&(t=VE(t)),BE(e,n,t,r,o,i)}function BE(e,n,t,r,o,i){if(e.type&3){let s=Ge(e,n);r=i!=null?i(r,e.value||"",t):r,o.setProperty(s,t,r)}else e.type&12}function HE(e,n){let t=Le(n,e);t[S]&16||(t[S]|=64)}function UE(e,n,t){let r=t.directiveStart,o=t.directiveEnd;$t(t)&&EE(n,t,e.data[r+t.componentOffset]),e.firstCreatePass||Es(t,n);let i=t.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=mo(n,e,s,t);if(pr(c,n),i!==null&&qE(n,s-r,c,a,t,i),at(a)){let u=Le(t.index,n);u[de]=mo(n,e,s,t)}}}function $E(e,n,t){let r=t.directiveStart,o=t.directiveEnd,i=t.index,s=Xh();try{Wt(i);for(let a=r;a<o;a++){let c=e.data[a],u=n[a];os(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&zE(c,u)}}finally{Wt(-1),os(s)}}function zE(e,n){e.hostBindings!==null&&e.hostBindings(1,n)}function Nl(e,n){let t=e.directiveRegistry,r=null;if(t)for(let o=0;o<t.length;o++){let i=t[o];vg(n,i.selectors,!1)&&(r??=[],at(i)?r.unshift(i):r.push(i))}return r}function GE(e,n,t,r,o,i){let s=Ge(e,n);WE(n[Q],s,i,e.value,t,r,o)}function WE(e,n,t,r,o,i,s){if(i==null)e.removeAttribute(n,o,t);else{let a=s==null?pn(i):s(i,r||"",o);e.setAttribute(n,o,a,t)}}function qE(e,n,t,r,o,i){let s=i[n];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],u=s[a+1];Zu(r,t,c,u)}}function Rl(e,n,t,r,o){let i=re+t,s=n[b],a=o(s,n,e,r,t);n[i]=a,lr(e,!0);let c=e.type===2;return c?(Cg(n[Q],a,e),(Bh()===0||ar(e))&&pr(a,n),Hh()):pr(a,n),cs()&&(!c||!js(e))&&Tl(s,n,a,e),e}function xl(e){let n=e;return yu()?Du():(n=n.parent,lr(n,!1)),n}function ZE(e,n){let t=e[gn];if(!t)return;t.get(Ae,null)?.(n)}function Ol(e,n,t,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let u=s[c],l=s[c+1],d=n.data[u];Zu(d,t[u],l,o),a=!0}if(i)for(let c of i){let u=t[c],l=n.data[c];Zu(l,u,r,o),a=!0}return a}function YE(e,n){let t=Le(n,e),r=t[b];QE(r,t);let o=t[$e];o!==null&&t[or]===null&&(t[or]=cg(o,t[gn])),H(18),kl(r,t,t[de]),H(19,t[de])}function QE(e,n){for(let t=n.length;t<e.blueprint.length;t++)n.push(e.blueprint[t])}function kl(e,n,t){ss(n);try{let r=e.viewQuery;r!==null&&qu(1,r,t);let o=e.template;o!==null&&Ng(e,n,o,1,t),e.firstCreatePass&&(e.firstCreatePass=!1),n[it]?.finishViewCreation(e),e.staticContentQueries&&ug(e,n),e.staticViewQueries&&qu(2,e.viewQuery,t);let i=e.components;i!==null&&KE(n,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{n[S]&=-5,as()}}function KE(e,n){for(let t=0;t<n.length;t++)YE(e,n[t])}function Rg(e,n,t,r){let o=N(null);try{let i=n.tView,a=e[S]&4096?4096:16,c=_l(e,i,t,a,null,n,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=e[n.index];c[Ht]=u;let l=e[it];return l!==null&&(c[it]=l.createEmbeddedView(i)),kl(i,c,t),c}finally{N(o)}}function Qu(e,n){return!n||n.firstChild===null||eg(e)}var Cp=!1,JE=new C("");function vo(e,n,t,r,o=!1){for(;t!==null;){if(t.type===128){t=o?t.projectionNext:t.next;continue}let i=n[t.index];i!==null&&r.push(Pe(i)),ze(i)&&xg(i,r);let s=t.type;if(s&8)vo(e,n,t.child,r);else if(s&32){let a=Sl(t,n),c;for(;c=a();)r.push(c)}else if(s&16){let a=Tg(n,t);if(Array.isArray(a))r.push(...a);else{let c=Lt(n[Ie]);vo(c[b],c,a,r,!0)}}t=o?t.projectionNext:t.next}return r}function xg(e,n){for(let t=ve;t<e.length;t++){let r=e[t],o=r[b].firstChild;o!==null&&vo(r[b],r,o,n)}e[Ut]!==e[$e]&&n.push(e[Ut])}function Og(e){if(e[yn]!==null){for(let n of e[yn])n.impl.addSequence(n);e[yn].length=0}}var kg=[];function XE(e){return e[Te]??eI(e)}function eI(e){let n=kg.pop()??Object.create(nI);return n.lView=e,n}function tI(e){e.lView[Te]!==e&&(e.lView=null,kg.push(e))}var nI=A(g({},nn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{ur(e.lView)},consumerOnSignalRead(){this.lView[Te]=this}});function rI(e){let n=e[Te]??Object.create(oI);return n.lView=e,n}var oI=A(g({},nn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let n=Lt(e.lView);for(;n&&!Fg(n[b]);)n=Lt(n);n&&fu(n)},consumerOnSignalRead(){this.lView[Te]=this}});function Fg(e){return e.type!==2}function Pg(e){if(e[ao]===null)return;let n=!0;for(;n;){let t=!1;for(let r of e[ao])r.dirty&&(t=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));n=t&&!!(e[S]&8192)}}var iI=100;function Fl(e,n=0){let r=e[ot].rendererFactory,o=!1;o||r.begin?.();try{sI(e,n)}finally{o||r.end?.()}}function sI(e,n){let t=Eu();try{Iu(!0),Ku(e,n);let r=0;for(;uo(e);){if(r===iI)throw new y(103,!1);r++,Ku(e,1)}}finally{Iu(t)}}function Lg(e,n){Cu(n?lo.Exhaustive:lo.OnlyDirtyViews);try{Fl(e)}finally{Cu(lo.Off)}}function aI(e,n,t,r){if(Cn(n))return;let o=n[S],i=!1,s=!1;ss(n);let a=!0,c=null,u=null;i||(Fg(e)?(u=XE(n),c=rn(u)):hi()===null?(a=!1,u=rI(n),c=rn(u)):n[Te]&&(gi(n[Te]),n[Te]=null));try{du(n),Qh(e.bindingStartIndex),t!==null&&Ng(e,n,t,2,r);let l=(o&3)===3;if(!i)if(l){let f=e.preOrderCheckHooks;f!==null&&ps(n,f,null)}else{let f=e.preOrderHooks;f!==null&&gs(n,f,0,null),xu(n,0)}if(s||cI(n),Pg(n),Vg(n,0),e.contentQueries!==null&&ug(e,n),!i)if(l){let f=e.contentCheckHooks;f!==null&&ps(n,f)}else{let f=e.contentHooks;f!==null&&gs(n,f,1),xu(n,1)}lI(e,n);let d=e.components;d!==null&&Bg(n,d,0);let h=e.viewQuery;if(h!==null&&qu(2,h,r),!i)if(l){let f=e.viewCheckHooks;f!==null&&ps(n,f)}else{let f=e.viewHooks;f!==null&&gs(n,f,2),xu(n,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),n[Ji]){for(let f of n[Ji])f();n[Ji]=null}i||(Og(n),n[S]&=-73)}catch(l){throw i||ur(n),l}finally{u!==null&&(jn(u,c),a&&tI(u)),as()}}function Vg(e,n){for(let t=ng(e);t!==null;t=rg(t))for(let r=ve;r<t.length;r++){let o=t[r];jg(o,n)}}function cI(e){for(let n=ng(e);n!==null;n=rg(n)){if(!(n[S]&2))continue;let t=n[Dn];for(let r=0;r<t.length;r++){let o=t[r];fu(o)}}}function uI(e,n,t){H(18);let r=Le(n,e);jg(r,t),H(19,r[de])}function jg(e,n){es(e)&&Ku(e,n)}function Ku(e,n){let r=e[b],o=e[S],i=e[Te],s=!!(n===0&&o&16);if(s||=!!(o&64&&n===0),s||=!!(o&1024),s||=!!(i?.dirty&&zr(i)),s||=!1,i&&(i.dirty=!1),e[S]&=-9217,s)aI(r,e,r.template,e[de]);else if(o&8192){let a=N(null);try{Pg(e),Vg(e,1);let c=r.components;c!==null&&Bg(e,c,1),Og(e)}finally{N(a)}}}function Bg(e,n,t){for(let r=0;r<n.length;r++)uI(e,n[r],t)}function lI(e,n){let t=e.hostBindingOpCodes;if(t!==null)try{for(let r=0;r<t.length;r++){let o=t[r];if(o<0)Wt(~o);else{let i=o,s=t[++r],a=t[++r];Jh(s,i);let c=n[i];H(24,c),a(2,c),H(25,c)}}}finally{Wt(-1)}}function Pl(e,n){let t=Eu()?64:1088;for(e[ot].changeDetectionScheduler?.notify(n);e;){e[S]|=t;let r=Lt(e);if(cr(e)&&!r)return e;e=r}return null}function Hg(e,n,t,r){return[e,!0,0,n,null,r,null,t,null,null]}function Ug(e,n,t,r=!0){let o=n[b];if(dI(o,n,e,t),r){let s=Yu(t,e),a=n[Q],c=a.parentNode(e[Ut]);c!==null&&bE(o,e[me],a,n,c,s)}let i=n[or];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Ju(e,n){if(e.length<=ve)return;let t=ve+n,r=e[t];if(r){let o=r[Ht];o!==null&&o!==e&&Ml(o,r),n>0&&(e[t-1][Fe]=r[Fe]);let i=oo(e,ve+n);_E(r[b],r);let s=i[it];s!==null&&s.detachView(i[b]),r[ne]=null,r[Fe]=null,r[S]&=-129}return r}function dI(e,n,t,r){let o=ve+r,i=t.length;r>0&&(t[o-1][Fe]=n),r<i-ve?(n[Fe]=t[o],Xc(t,ve+r,n)):(t.push(n),n[Fe]=null),n[ne]=t;let s=n[Ht];s!==null&&t!==s&&$g(s,n);let a=n[it];a!==null&&a.insertView(e),ts(n),n[S]|=128}function $g(e,n){let t=e[Dn],r=n[ne];if(st(r))e[S]|=2;else{let o=r[ne][Ie];n[Ie]!==o&&(e[S]|=2)}t===null?e[Dn]=[n]:t.push(n)}var qt=class{_lView;_cdRefInjectingView;_appRef=null;_attachedToViewContainer=!1;exhaustive;get rootNodes(){let n=this._lView,t=n[b];return vo(t,n,t.firstChild,[])}constructor(n,t){this._lView=n,this._cdRefInjectingView=t}get context(){return this._lView[de]}set context(n){this._lView[de]=n}get destroyed(){return Cn(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let n=this._lView[ne];if(ze(n)){let t=n[co],r=t?t.indexOf(this):-1;r>-1&&(Ju(n,r),oo(t,r))}this._attachedToViewContainer=!1}bg(this._lView[b],this._lView)}onDestroy(n){hu(this._lView,n)}markForCheck(){Pl(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[S]&=-129}reattach(){ts(this._lView),this._lView[S]|=128}detectChanges(){this._lView[S]|=1024,Fl(this._lView)}checkNoChanges(){return;try{this.exhaustive??=this._lView[gn].get(JE,Cp)}catch{this.exhaustive=Cp}}attachToViewContainerRef(){if(this._appRef)throw new y(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let n=cr(this._lView),t=this._lView[Ht];t!==null&&!n&&Ml(t,this._lView),_g(this._lView[b],this._lView)}attachToAppRef(n){if(this._attachedToViewContainer)throw new y(902,!1);this._appRef=n;let t=cr(this._lView),r=this._lView[Ht];r!==null&&!t&&$g(r,this._lView),ts(this._lView)}};var Ze=(()=>{class e{_declarationLView;_declarationTContainer;elementRef;static __NG_ELEMENT_ID__=fI;constructor(t,r,o){this._declarationLView=t,this._declarationTContainer=r,this.elementRef=o}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,r){return this.createEmbeddedViewImpl(t,r)}createEmbeddedViewImpl(t,r,o){let i=Rg(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:r,dehydratedView:o});return new qt(i)}}return e})();function fI(){return Ll(oe(),V())}function Ll(e,n){return e.type&4?new Ze(n,e,vr(e,n)):null}function Dr(e,n,t,r,o){let i=e.data[n];if(i===null)i=hI(e,n,t,r,o),Kh()&&(i.flags|=32);else if(i.type&64){i.type=t,i.value=r,i.attrs=o;let s=qh();i.injectorIndex=s===null?-1:s.injectorIndex}return lr(i,!0),i}function hI(e,n,t,r,o){let i=vu(),s=yu(),a=s?i:i&&i.parent,c=e.data[n]=gI(e,a,t,n,r,o);return pI(e,c,i,s),c}function pI(e,n,t,r){e.firstChild===null&&(e.firstChild=n),t!==null&&(r?t.child==null&&n.parent!==null&&(t.child=n):t.next===null&&(t.next=n,n.prev=t))}function gI(e,n,t,r,o,i){let s=n?n.injectorIndex:-1,a=0;return mu()&&(a|=128),{type:t,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:n,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var pF=new RegExp(`^(\\d+)*(${zC}|${$C})*(.*)`);var mI=()=>null;function Xu(e,n){return mI(e,n)}var zg=class{},Gs=class{},el=class{resolveComponentFactory(n){throw new y(917,!1)}},Eo=class{static NULL=new el},wn=class{},dt=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>vI()}return e})();function vI(){let e=V(),n=oe(),t=Le(n.index,e);return(st(t)?t:e)[Q]}var Gg=(()=>{class e{static \u0275prov=D({token:e,providedIn:"root",factory:()=>null})}return e})();var vs={},tl=class{injector;parentInjector;constructor(n,t){this.injector=n,this.parentInjector=t}get(n,t,r){let o=this.injector.get(n,vs,r);return o!==vs||t===vs?o:this.parentInjector.get(n,t,r)}};function bs(e,n,t){let r=t?e.styles:null,o=t?e.classes:null,i=0;if(n!==null)for(let s=0;s<n.length;s++){let a=n[s];if(typeof a=="number")i=a;else if(i==1)o=Bc(o,a);else if(i==2){let c=a,u=n[++s];r=Bc(r,c+": "+u+";")}}t?e.styles=r:e.stylesWithoutHost=r,t?e.classes=o:e.classesWithoutHost=o}function m(e,n=0){let t=V();if(t===null)return I(e,n);let r=oe();return Kp(r,t,ce(e),n)}function Wg(e,n,t,r,o){let i=r===null?null:{"":-1},s=o(e,t);if(s!==null){let a=s,c=null,u=null;for(let l of s)if(l.resolveHostDirectives!==null){[a,c,u]=l.resolveHostDirectives(s);break}CI(e,n,t,a,i,c,u)}i!==null&&r!==null&&yI(t,r,i)}function yI(e,n,t){let r=e.localNames=[];for(let o=0;o<n.length;o+=2){let i=t[n[o+1]];if(i==null)throw new y(-301,!1);r.push(n[o],i)}}function DI(e,n,t){n.componentOffset=t,(e.components??=[]).push(n.index)}function CI(e,n,t,r,o,i,s){let a=r.length,c=!1;for(let h=0;h<a;h++){let f=r[h];!c&&at(f)&&(c=!0,DI(e,t,h)),$u(Es(t,n),e,f.type)}SI(t,e.data.length,a);for(let h=0;h<a;h++){let f=r[h];f.providersResolver&&f.providersResolver(f)}let u=!1,l=!1,d=Ig(e,n,a,null);a>0&&(t.directiveToIndex=new Map);for(let h=0;h<a;h++){let f=r[h];if(t.mergedAttrs=hr(t.mergedAttrs,f.hostAttrs),II(e,t,n,d,f),bI(d,f,o),s!==null&&s.has(f)){let[E,T]=s.get(f);t.directiveToIndex.set(f.type,[d,E+t.directiveStart,T+t.directiveStart])}else(i===null||!i.has(f))&&t.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(t.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(t.flags|=64);let v=f.type.prototype;!u&&(v.ngOnChanges||v.ngOnInit||v.ngDoCheck)&&((e.preOrderHooks??=[]).push(t.index),u=!0),!l&&(v.ngOnChanges||v.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(t.index),l=!0),d++}EI(e,t,i)}function EI(e,n,t){for(let r=n.directiveStart;r<n.directiveEnd;r++){let o=e.data[r];if(t===null||!t.has(o))Ep(0,n,o,r),Ep(1,n,o,r),wp(n,r,!1);else{let i=t.get(o);Ip(0,n,i,r),Ip(1,n,i,r),wp(n,r,!0)}}}function Ep(e,n,t,r){let o=e===0?t.inputs:t.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=n.inputs??={}:s=n.outputs??={},s[i]??=[],s[i].push(r),qg(n,i)}}function Ip(e,n,t,r){let o=e===0?t.inputs:t.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=n.hostDirectiveInputs??={}:a=n.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),qg(n,s)}}function qg(e,n){n==="class"?e.flags|=8:n==="style"&&(e.flags|=16)}function wp(e,n,t){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!t&&o===null||t&&i===null||Il(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!t&&o.hasOwnProperty(c)){let u=o[c];for(let l of u)if(l===n){s??=[],s.push(c,r[a+1]);break}}else if(t&&i.hasOwnProperty(c)){let u=i[c];for(let l=0;l<u.length;l+=2)if(u[l]===n){s??=[],s.push(u[l+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function II(e,n,t,r,o){e.data[r]=o;let i=o.factory||(o.factory=ln(o.type,!0)),s=new In(i,at(o),m,null);e.blueprint[r]=s,t[r]=s,wI(e,n,r,Ig(e,t,o.hostVars,Ye),o)}function wI(e,n,t,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~n.index;_I(s)!=a&&s.push(a),s.push(t,r,i)}}function _I(e){let n=e.length;for(;n>0;){let t=e[--n];if(typeof t=="number"&&t<0)return t}return 0}function bI(e,n,t){if(t){if(n.exportAs)for(let r=0;r<n.exportAs.length;r++)t[n.exportAs[r]]=e;at(n)&&(t[""]=e)}}function SI(e,n,t){e.flags|=1,e.directiveStart=n,e.directiveEnd=n+t,e.providerIndexes=n}function Vl(e,n,t,r,o,i,s,a){let c=n[b],u=c.consts,l=zt(u,s),d=Dr(c,e,t,r,l);return i&&Wg(c,n,d,zt(u,a),o),d.mergedAttrs=hr(d.mergedAttrs,d.attrs),d.attrs!==null&&bs(d,d.attrs,!1),d.mergedAttrs!==null&&bs(d,d.mergedAttrs,!0),c.queries!==null&&c.queries.elementStart(c,d),d}function jl(e,n){Up(e,n),uu(n)&&e.queries.elementEnd(n)}function MI(e,n,t,r,o,i){let s=n.consts,a=zt(s,o),c=Dr(n,e,t,r,a);if(c.mergedAttrs=hr(c.mergedAttrs,c.attrs),i!=null){let u=zt(s,i);c.localNames=[];for(let l=0;l<u.length;l+=2)c.localNames.push(u[l],-1)}return c.attrs!==null&&bs(c,c.attrs,!1),c.mergedAttrs!==null&&bs(c,c.mergedAttrs,!0),n.queries!==null&&n.queries.elementStart(n,c),c}function Bl(e){return Ws(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function Zg(e,n){if(Array.isArray(e))for(let t=0;t<e.length;t++)n(e[t]);else{let t=e[Symbol.iterator](),r;for(;!(r=t.next()).done;)n(r.value)}}function Ws(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function TI(e,n,t){return e[n]=t}function _n(e,n,t){if(t===Ye)return!1;let r=e[n];return Object.is(r,t)?!1:(e[n]=t,!0)}function AI(e,n,t,r){let o=_n(e,n,t);return _n(e,n+1,r)||o}function Fu(e,n,t){return function r(o){let i=$t(e)?Le(e.index,n):n;Pl(i,5);let s=n[de],a=_p(n,s,t,o),c=r.__ngNextListenerFn__;for(;c;)a=_p(n,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function _p(e,n,t,r){let o=N(null);try{return H(6,n,t),t(r)!==!1}catch(i){return ZE(e,i),!1}finally{H(7,n,t),N(o)}}function NI(e,n,t,r,o,i,s,a){let c=ar(e),u=!1,l=null;if(!r&&c&&(l=RI(n,t,i,e.index)),l!==null){let d=l.__ngLastListenerFn__||l;d.__ngNextListenerFn__=s,l.__ngLastListenerFn__=s,u=!0}else{let d=Ge(e,t),h=r?r(d):d;WC(t,h,i,a);let f=o.listen(h,i,a),v=r?E=>r(Pe(E[e.index])):e.index;Yg(v,n,t,i,a,f,!1)}return u}function RI(e,n,t,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===t&&o[i+1]===r){let a=n[ir],c=o[i+2];return a&&a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function Yg(e,n,t,r,o,i,s){let a=n.firstCreatePass?gu(n):null,c=pu(t),u=c.length;c.push(o,i),a&&a.push(r,e,u,(u+1)*(s?-1:1))}function bp(e,n,t,r,o,i){let s=n[t],a=n[b],u=a.data[t].outputs[r],d=s[u].subscribe(i);Yg(e.index,a,n,o,i,d,!0)}var nl=Symbol("BINDING");var Ss=class extends Eo{ngModule;constructor(n){super(),this.ngModule=n}resolveComponentFactory(n){let t=rt(n);return new Zt(t,this.ngModule)}};function xI(e){return Object.keys(e).map(n=>{let[t,r,o]=e[n],i={propName:t,templateName:n,isSignal:(r&Us.SignalBased)!==0};return o&&(i.transform=o),i})}function OI(e){return Object.keys(e).map(n=>({propName:e[n],templateName:n}))}function kI(e,n,t){let r=n instanceof Z?n:n?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new tl(t,r):t}function FI(e){let n=e.get(wn,null);if(n===null)throw new y(407,!1);let t=e.get(Gg,null),r=e.get(Vt,null);return{rendererFactory:n,sanitizer:t,changeDetectionScheduler:r,ngReflect:!1}}function PI(e,n){let t=(e.selectors[0][0]||"div").toLowerCase();return yg(n,t,t==="svg"?Oh:t==="math"?kh:null)}var Zt=class extends Gs{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=xI(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=OI(this.componentDef.outputs),this.cachedOutputs}constructor(n,t){super(),this.componentDef=n,this.ngModule=t,this.componentType=n.type,this.selector=dE(n.selectors),this.ngContentSelectors=n.ngContentSelectors??[],this.isBoundToModule=!!t}create(n,t,r,o,i,s){H(22);let a=N(null);try{let c=this.componentDef,u=LI(r,c,s,i),l=kI(c,o||this.ngModule,n),d=FI(l),h=d.rendererFactory.createRenderer(null,c),f=r?FE(h,r,c.encapsulation,l):PI(c,h),v=s?.some(Sp)||i?.some(R=>typeof R!="function"&&R.bindings.some(Sp)),E=_l(null,u,null,512|Eg(c),null,null,d,h,l,null,cg(f,l,!0));E[re]=f,ss(E);let T=null;try{let R=Vl(re,E,2,"#host",()=>u.directiveRegistry,!0,0);f&&(Cg(h,f,R),pr(f,E)),zs(u,E,R),Dl(u,R,E),jl(u,R),t!==void 0&&jI(R,this.ngContentSelectors,t),T=Le(R.index,E),E[de]=T[de],kl(u,E,null)}catch(R){throw T!==null&&Gu(T),Gu(E),R}finally{H(23),as()}return new Ms(this.componentType,E,!!v)}finally{N(a)}}};function LI(e,n,t,r){let o=e?["ng-version","20.1.3"]:fE(n.selectors[0]),i=null,s=null,a=0;if(t)for(let l of t)a+=l[nl].requiredVars,l.create&&(l.targetIdx=0,(i??=[]).push(l)),l.update&&(l.targetIdx=0,(s??=[]).push(l));if(r)for(let l=0;l<r.length;l++){let d=r[l];if(typeof d!="function")for(let h of d.bindings){a+=h[nl].requiredVars;let f=l+1;h.create&&(h.targetIdx=f,(i??=[]).push(h)),h.update&&(h.targetIdx=f,(s??=[]).push(h))}}let c=[n];if(r)for(let l of r){let d=typeof l=="function"?l:l.type,h=ru(d);c.push(h)}return wl(0,null,VI(i,s),1,a,c,null,null,null,[o],null)}function VI(e,n){return!e&&!n?null:t=>{if(t&1&&e)for(let r of e)r.create();if(t&2&&n)for(let r of n)r.update()}}function Sp(e){let n=e[nl].kind;return n==="input"||n==="twoWay"}var Ms=class extends zg{_rootLView;_hasInputBindings;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(n,t,r){super(),this._rootLView=t,this._hasInputBindings=r,this._tNode=Xi(t[b],re),this.location=vr(this._tNode,t),this.instance=Le(this._tNode.index,t)[de],this.hostView=this.changeDetectorRef=new qt(t,void 0),this.componentType=n}setInput(n,t){this._hasInputBindings;let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(n)&&Object.is(this.previousInputValues.get(n),t))return;let o=this._rootLView,i=Ol(r,o[b],o,n,t);this.previousInputValues.set(n,t);let s=Le(r.index,o);Pl(s,1)}get injector(){return new En(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(n){this.hostView.onDestroy(n)}};function jI(e,n,t){let r=e.projection=[];for(let o=0;o<n.length;o++){let i=t[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Ve=(()=>{class e{static __NG_ELEMENT_ID__=BI}return e})();function BI(){let e=oe();return Kg(e,V())}var HI=Ve,Qg=class extends HI{_lContainer;_hostTNode;_hostLView;constructor(n,t,r){super(),this._lContainer=n,this._hostTNode=t,this._hostLView=r}get element(){return vr(this._hostTNode,this._hostLView)}get injector(){return new En(this._hostTNode,this._hostLView)}get parentInjector(){let n=ml(this._hostTNode,this._hostLView);if(Gp(n)){let t=Cs(n,this._hostLView),r=Ds(n),o=t[b].data[r+8];return new En(o,t)}else return new En(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(n){let t=Mp(this._lContainer);return t!==null&&t[n]||null}get length(){return this._lContainer.length-ve}createEmbeddedView(n,t,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Xu(this._lContainer,n.ssrId),a=n.createEmbeddedViewImpl(t||{},i,s);return this.insertImpl(a,o,Qu(this._hostTNode,s)),a}createComponent(n,t,r,o,i,s,a){let c=n&&!vC(n),u;if(c)u=t;else{let T=t||{};u=T.index,r=T.injector,o=T.projectableNodes,i=T.environmentInjector||T.ngModuleRef,s=T.directives,a=T.bindings}let l=c?n:new Zt(rt(n)),d=r||this.parentInjector;if(!i&&l.ngModule==null){let R=(c?d:this.parentInjector).get(Z,null);R&&(i=R)}let h=rt(l.componentType??{}),f=Xu(this._lContainer,h?.id??null),v=f?.firstChild??null,E=l.create(d,o,v,i,s,a);return this.insertImpl(E.hostView,u,Qu(this._hostTNode,f)),E}insert(n,t){return this.insertImpl(n,t,!0)}insertImpl(n,t,r){let o=n._lView;if(Ph(o)){let a=this.indexOf(n);if(a!==-1)this.detach(a);else{let c=o[ne],u=new Qg(c,c[me],c[ne]);u.detach(u.indexOf(n))}}let i=this._adjustIndex(t),s=this._lContainer;return Ug(s,o,i,r),n.attachToViewContainerRef(),Xc(Pu(s),i,n),n}move(n,t){return this.insert(n,t)}indexOf(n){let t=Mp(this._lContainer);return t!==null?t.indexOf(n):-1}remove(n){let t=this._adjustIndex(n,-1),r=Ju(this._lContainer,t);r&&(oo(Pu(this._lContainer),t),bg(r[b],r))}detach(n){let t=this._adjustIndex(n,-1),r=Ju(this._lContainer,t);return r&&oo(Pu(this._lContainer),t)!=null?new qt(r):null}_adjustIndex(n,t=0){return n??this.length+t}};function Mp(e){return e[co]}function Pu(e){return e[co]||(e[co]=[])}function Kg(e,n){let t,r=n[e.index];return ze(r)?t=r:(t=Hg(r,n,null,e),n[e.index]=t,bl(n,t)),$I(t,n,e,r),new Qg(t,e,n)}function UI(e,n){let t=e[Q],r=t.createComment(""),o=Ge(n,e),i=t.parentNode(o);return _s(t,i,r,t.nextSibling(o),!1),r}var $I=WI,zI=()=>!1;function GI(e,n,t){return zI(e,n,t)}function WI(e,n,t,r){if(e[Ut])return;let o;t.type&8?o=Pe(r):o=UI(n,t),e[Ut]=o}var rl=class e{queryList;matches=null;constructor(n){this.queryList=n}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},ol=class e{queries;constructor(n=[]){this.queries=n}createEmbeddedView(n){let t=n.queries;if(t!==null){let r=n.contentQueries!==null?n.contentQueries[0]:t.length,o=[];for(let i=0;i<r;i++){let s=t.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(n){this.dirtyQueriesWithMatches(n)}detachView(n){this.dirtyQueriesWithMatches(n)}finishViewCreation(n){this.dirtyQueriesWithMatches(n)}dirtyQueriesWithMatches(n){for(let t=0;t<this.queries.length;t++)Hl(n,t).matches!==null&&this.queries[t].setDirty()}},Ts=class{flags;read;predicate;constructor(n,t,r=null){this.flags=t,this.read=r,typeof n=="string"?this.predicate=ew(n):this.predicate=n}},il=class e{queries;constructor(n=[]){this.queries=n}elementStart(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(n,t)}elementEnd(n){for(let t=0;t<this.queries.length;t++)this.queries[t].elementEnd(n)}embeddedTView(n){let t=null;for(let r=0;r<this.length;r++){let o=t!==null?t.length:0,i=this.getByIndex(r).embeddedTView(n,o);i&&(i.indexInDeclarationView=r,t!==null?t.push(i):t=[i])}return t!==null?new e(t):null}template(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].template(n,t)}getByIndex(n){return this.queries[n]}get length(){return this.queries.length}track(n){this.queries.push(n)}},sl=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(n,t=-1){this.metadata=n,this._declarationNodeIndex=t}elementStart(n,t){this.isApplyingToNode(t)&&this.matchTNode(n,t)}elementEnd(n){this._declarationNodeIndex===n.index&&(this._appliesToNextNode=!1)}template(n,t){this.elementStart(n,t)}embeddedTView(n,t){return this.isApplyingToNode(n)?(this.crossesNgTemplate=!0,this.addMatch(-n.index,t),new e(this.metadata)):null}isApplyingToNode(n){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let t=this._declarationNodeIndex,r=n.parent;for(;r!==null&&r.type&8&&r.index!==t;)r=r.parent;return t===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(n,t){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(n,t,qI(t,i)),this.matchTNodeWithReadOption(n,t,ms(t,n,i,!1,!1))}else r===Ze?t.type&4&&this.matchTNodeWithReadOption(n,t,-1):this.matchTNodeWithReadOption(n,t,ms(t,n,r,!1,!1))}matchTNodeWithReadOption(n,t,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===G||o===Ve||o===Ze&&t.type&4)this.addMatch(t.index,-2);else{let i=ms(t,n,o,!1,!1);i!==null&&this.addMatch(t.index,i)}else this.addMatch(t.index,r)}}addMatch(n,t){this.matches===null?this.matches=[n,t]:this.matches.push(n,t)}};function qI(e,n){let t=e.localNames;if(t!==null){for(let r=0;r<t.length;r+=2)if(t[r]===n)return t[r+1]}return null}function ZI(e,n){return e.type&11?vr(e,n):e.type&4?Ll(e,n):null}function YI(e,n,t,r){return t===-1?ZI(n,e):t===-2?QI(e,n,r):mo(e,e[b],t,n)}function QI(e,n,t){if(t===G)return vr(n,e);if(t===Ze)return Ll(n,e);if(t===Ve)return Kg(n,e)}function Jg(e,n,t,r){let o=n[it].queries[r];if(o.matches===null){let i=e.data,s=t.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let u=s[c];if(u<0)a.push(null);else{let l=i[u];a.push(YI(n,l,s[c+1],t.metadata.read))}}o.matches=a}return o.matches}function al(e,n,t,r){let o=e.queries.getByIndex(t),i=o.matches;if(i!==null){let s=Jg(e,n,o,t);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let u=i[a+1],l=n[-c];for(let d=ve;d<l.length;d++){let h=l[d];h[Ht]===h[ne]&&al(h[b],h,u,r)}if(l[Dn]!==null){let d=l[Dn];for(let h=0;h<d.length;h++){let f=d[h];al(f[b],f,u,r)}}}}}return r}function KI(e,n){return e[it].queries[n].queryList}function Xg(e,n,t){let r=new Is((t&4)===4);return jh(e,n,r,r.destroy),(n[it]??=new ol).queries.push(new rl(r))-1}function JI(e,n,t){let r=fe();return r.firstCreatePass&&(em(r,new Ts(e,n,t),-1),(n&2)===2&&(r.staticViewQueries=!0)),Xg(r,V(),n)}function XI(e,n,t,r){let o=fe();if(o.firstCreatePass){let i=oe();em(o,new Ts(n,t,r),i.index),tw(o,e),(t&2)===2&&(o.staticContentQueries=!0)}return Xg(o,V(),t)}function ew(e){return e.split(",").map(n=>n.trim())}function em(e,n,t){e.queries===null&&(e.queries=new il),e.queries.track(new sl(n,t))}function tw(e,n){let t=e.contentQueries||(e.contentQueries=[]),r=t.length?t[t.length-1]:-1;n!==r&&t.push(e.queries.length-1,n)}function Hl(e,n){return e.queries.getByIndex(n)}function nw(e,n){let t=e[b],r=Hl(t,n);return r.crossesNgTemplate?al(t,e,n,[]):Jg(t,e,r,n)}var Tp=new Set;function qs(e){Tp.has(e)||(Tp.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var bn=class{},Zs=class{};var As=class extends bn{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Ss(this);constructor(n,t,r,o=!0){super(),this.ngModuleType=n,this._parent=t;let i=nu(n);this._bootstrapComponents=gg(i.bootstrap),this._r3Injector=Tu(n,t,[{provide:bn,useValue:this},{provide:Eo,useValue:this.componentFactoryResolver},...r],Dt(n),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let n=this._r3Injector;!n.destroyed&&n.destroy(),this.destroyCbs.forEach(t=>t()),this.destroyCbs=null}onDestroy(n){this.destroyCbs.push(n)}},Ns=class extends Zs{moduleType;constructor(n){super(),this.moduleType=n}create(n){return new As(this.moduleType,n,[])}};var yo=class extends bn{injector;componentFactoryResolver=new Ss(this);instance=null;constructor(n){super();let t=new fn([...n.providers,{provide:bn,useValue:this},{provide:Eo,useValue:this.componentFactoryResolver}],n.parent||rr(),n.debugName,new Set(["environment"]));this.injector=t,n.runEnvironmentInitializers&&t.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(n){this.injector.onDestroy(n)}};function Cr(e,n,t=null){return new yo({providers:e,parent:n,debugName:t,runEnvironmentInitializers:!0}).injector}var rw=(()=>{class e{_injector;cachedInjectors=new Map;constructor(t){this._injector=t}getOrCreateStandaloneInjector(t){if(!t.standalone)return null;if(!this.cachedInjectors.has(t)){let r=ou(!1,t.type),o=r.length>0?Cr([r],this._injector,`Standalone[${t.type.name}]`):null;this.cachedInjectors.set(t,o)}return this.cachedInjectors.get(t)}ngOnDestroy(){try{for(let t of this.cachedInjectors.values())t!==null&&t.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=D({token:e,providedIn:"environment",factory:()=>new e(I(Z))})}return e})();function Ul(e){return mr(()=>{let n=tm(e),t=A(g({},n),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===vl.OnPush,directiveDefs:null,pipeDefs:null,dependencies:n.standalone&&e.dependencies||null,getStandaloneInjector:n.standalone?o=>o.get(rw).getOrCreateStandaloneInjector(t):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||It.Emulated,styles:e.styles||Me,_:null,schemas:e.schemas||null,tView:null,id:""});n.standalone&&qs("NgStandalone"),nm(t);let r=e.dependencies;return t.directiveDefs=Ap(r,ow),t.pipeDefs=Ap(r,Sh),t.id=aw(t),t})}function ow(e){return rt(e)||ru(e)}function wt(e){return mr(()=>({type:e.type,bootstrap:e.bootstrap||Me,declarations:e.declarations||Me,imports:e.imports||Me,exports:e.exports||Me,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function iw(e,n){if(e==null)return jt;let t={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=Us.None,c=null),t[i]=[r,a,c],n[i]=s}return t}function sw(e){if(e==null)return jt;let n={};for(let t in e)e.hasOwnProperty(t)&&(n[e[t]]=t);return n}function L(e){return mr(()=>{let n=tm(e);return nm(n),n})}function tm(e){let n={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:n,inputConfig:e.inputs||jt,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||Me,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,resolveHostDirectives:null,hostDirectives:null,inputs:iw(e.inputs,n),outputs:sw(e.outputs),debugInfo:null}}function nm(e){e.features?.forEach(n=>n(e))}function Ap(e,n){return e?()=>{let t=typeof e=="function"?e():e,r=[];for(let o of t){let i=n(o);i!==null&&r.push(i)}return r}:null}function aw(e){let n=0,t=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,t,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))n=Math.imul(31,n)+i.charCodeAt(0)<<0;return n+=2147483648,"c"+n}function cw(e){return Object.getPrototypeOf(e.prototype).constructor}function Ne(e){let n=cw(e.type),t=!0,r=[e];for(;n;){let o;if(at(e))o=n.\u0275cmp||n.\u0275dir;else{if(n.\u0275cmp)throw new y(903,!1);o=n.\u0275dir}if(o){if(t){r.push(o);let s=e;s.inputs=Lu(e.inputs),s.declaredInputs=Lu(e.declaredInputs),s.outputs=Lu(e.outputs);let a=o.hostBindings;a&&hw(e,a);let c=o.viewQuery,u=o.contentQueries;if(c&&dw(e,c),u&&fw(e,u),uw(e,o),mh(e.outputs,o.outputs),at(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===Ne&&(t=!1)}}n=Object.getPrototypeOf(n)}lw(r)}function uw(e,n){for(let t in n.inputs){if(!n.inputs.hasOwnProperty(t)||e.inputs.hasOwnProperty(t))continue;let r=n.inputs[t];r!==void 0&&(e.inputs[t]=r,e.declaredInputs[t]=n.declaredInputs[t])}}function lw(e){let n=0,t=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=n+=o.hostVars,o.hostAttrs=hr(o.hostAttrs,t=hr(t,o.hostAttrs))}}function Lu(e){return e===jt?{}:e===Me?[]:e}function dw(e,n){let t=e.viewQuery;t?e.viewQuery=(r,o)=>{n(r,o),t(r,o)}:e.viewQuery=n}function fw(e,n){let t=e.contentQueries;t?e.contentQueries=(r,o,i)=>{n(r,o,i),t(r,o,i)}:e.contentQueries=n}function hw(e,n){let t=e.hostBindings;t?e.hostBindings=(r,o)=>{n(r,o),t(r,o)}:e.hostBindings=n}function rm(e,n,t,r,o,i,s,a){if(t.firstCreatePass){e.mergedAttrs=hr(e.mergedAttrs,e.attrs);let l=e.tView=wl(2,e,o,i,s,t.directiveRegistry,t.pipeRegistry,null,t.schemas,t.consts,null);t.queries!==null&&(t.queries.template(t,e),l.queries=t.queries.embeddedTView(e))}a&&(e.flags|=a),lr(e,!1);let c=mw(t,n,e,r);cs()&&Tl(t,n,c,e),pr(c,n);let u=Hg(c,n,c,e);n[r+re]=u,bl(n,u),GI(u,e,n)}function pw(e,n,t,r,o,i,s,a,c,u,l){let d=t+re,h;return n.firstCreatePass?(h=Dr(n,d,4,s||null,a||null),ns()&&Wg(n,e,h,zt(n.consts,u),Nl),Up(n,h)):h=n.data[d],rm(h,e,n,t,r,o,i,c),ar(h)&&zs(n,e,h),u!=null&&Co(e,h,l),h}function gw(e,n,t,r,o,i,s,a,c,u,l){let d=t+re,h;if(n.firstCreatePass){if(h=Dr(n,d,4,s||null,a||null),u!=null){let f=zt(n.consts,u);h.localNames=[];for(let v=0;v<f.length;v+=2)h.localNames.push(f[v],-1)}}else h=n.data[d];return rm(h,e,n,t,r,o,i,c),u!=null&&Co(e,h,l),h}function om(e,n,t,r,o,i,s,a){let c=V(),u=fe(),l=zt(u.consts,i);return pw(c,u,e,n,t,r,o,l,void 0,s,a),om}var mw=vw;function vw(e,n,t,r){return fo(!0),n[Q].createComment("")}var Ys=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(Ys||{}),Mn=new C(""),im=!1,cl=class extends U{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(n=!1){super(),this.__isAsync=n,Rh()&&(this.destroyRef=p(ct,{optional:!0})??void 0,this.pendingTasks=p(Et,{optional:!0})??void 0)}emit(n){let t=N(null);try{super.next(n)}finally{N(t)}}subscribe(n,t,r){let o=n,i=t||(()=>null),s=r;if(n&&typeof n=="object"){let c=n;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return n instanceof K&&n.add(a),a}wrapInTimeout(n){return t=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{n(t)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},z=cl;function sm(e){let n,t;function r(){e=ho;try{t!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(t),n!==void 0&&clearTimeout(n)}catch{}}return n=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(t=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Np(e){return queueMicrotask(()=>e()),()=>{e=ho}}var $l="isAngularZone",Rs=$l+"_ID",yw=0,$=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new z(!1);onMicrotaskEmpty=new z(!1);onStable=new z(!1);onError=new z(!1);constructor(n){let{enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=im}=n;if(typeof Zone>"u")throw new y(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,Ew(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get($l)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new y(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new y(909,!1)}run(n,t,r){return this._inner.run(n,t,r)}runTask(n,t,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,n,Dw,ho,ho);try{return i.runTask(s,t,r)}finally{i.cancelTask(s)}}runGuarded(n,t,r){return this._inner.runGuarded(n,t,r)}runOutsideAngular(n){return this._outer.run(n)}},Dw={};function zl(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Cw(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function n(){sm(()=>{e.callbackScheduled=!1,ul(e),e.isCheckStableRunning=!0,zl(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{n()}):e._outer.run(()=>{n()}),ul(e)}function Ew(e){let n=()=>{Cw(e)},t=yw++;e._inner=e._inner.fork({name:"angular",properties:{[$l]:!0,[Rs]:t,[Rs+t]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(Iw(c))return r.invokeTask(i,s,a,c);try{return Rp(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&n(),xp(e)}},onInvoke:(r,o,i,s,a,c,u)=>{try{return Rp(e),r.invoke(i,s,a,c,u)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!ww(c)&&n(),xp(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,ul(e),zl(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function ul(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Rp(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function xp(e){e._nesting--,zl(e)}var xs=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new z;onMicrotaskEmpty=new z;onStable=new z;onError=new z;run(n,t,r){return n.apply(t,r)}runGuarded(n,t,r){return n.apply(t,r)}runOutsideAngular(n){return n()}runTask(n,t,r,o){return n.apply(t,r)}};function Iw(e){return am(e,"__ignore_ng_zone__")}function ww(e){return am(e,"__scheduler_tick__")}function am(e,n){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[n]===!0}var Gl=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=D({token:e,providedIn:"root",factory:()=>new e})}return e})(),cm=[0,1,2,3],um=(()=>{class e{ngZone=p($);scheduler=p(Vt);errorHandler=p(Ue,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){p(Mn,{optional:!0})}execute(){let t=this.sequences.size>0;t&&H(16),this.executing=!0;for(let r of cm)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),t&&H(17)}register(t){let{view:r}=t;r!==void 0?((r[yn]??=[]).push(t),ur(r),r[S]|=8192):this.executing?this.deferredRegistrations.add(t):this.addSequence(t)}addSequence(t){this.sequences.add(t),this.scheduler.notify(7)}unregister(t){this.executing&&this.sequences.has(t)?(t.erroredOrDestroyed=!0,t.pipelinedValue=void 0,t.once=!0):(this.sequences.delete(t),this.deferredRegistrations.delete(t))}maybeTrace(t,r){return r?r.run(Ys.AFTER_NEXT_RENDER,t):t()}static \u0275prov=D({token:e,providedIn:"root",factory:()=>new e})}return e})(),Os=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(n,t,r,o,i,s=null){this.impl=n,this.hooks=t,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let n=this.view?.[yn];n&&(this.view[yn]=n.filter(t=>t!==this))}};function Qs(e,n){let t=n?.injector??p(ue);return qs("NgAfterNextRender"),bw(e,t,n,!0)}function _w(e){return e instanceof Function?[void 0,void 0,e,void 0]:[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function bw(e,n,t,r){let o=n.get(Gl);o.impl??=n.get(um);let i=n.get(Mn,null,{optional:!0}),s=t?.manualCleanup!==!0?n.get(ct):null,a=n.get(ls,null,{optional:!0}),c=new Os(o.impl,_w(e),a?.view,r,s,i?.snapshot(null));return o.impl.register(c),c}var Wl=(()=>{class e{log(t){console.log(t)}warn(t){console.warn(t)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var ql=new C("");function Qt(e){return!!e&&typeof e.then=="function"}function Zl(e){return!!e&&typeof e.subscribe=="function"}var lm=new C("");var Yl=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((t,r)=>{this.resolve=t,this.reject=r});appInits=p(lm,{optional:!0})??[];injector=p(ue);constructor(){}runInitializers(){if(this.initialized)return;let t=[];for(let o of this.appInits){let i=ge(this.injector,o);if(Qt(i))t.push(i);else if(Zl(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});t.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(t).then(()=>{r()}).catch(o=>{this.reject(o)}),t.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Ks=new C("");function dm(){lc(()=>{let e="";throw new y(600,e)})}function fm(e){return e.isBoundToModule}var Sw=10;var Qe=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=p(Ae);afterRenderManager=p(Gl);zonelessEnabled=p(us);rootEffectScheduler=p(Ru);dirtyFlags=0;tracingSnapshot=null;allTestViews=new Set;autoDetectTestViews=new Set;includeAllTestViews=!1;afterTick=new U;get allViews(){return[...(this.includeAllTestViews?this.allTestViews:this.autoDetectTestViews).keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];internalPendingTask=p(Et);get isStable(){return this.internalPendingTask.hasPendingTasksObservable.pipe(P(t=>!t))}constructor(){p(Mn,{optional:!0})}whenStable(){let t;return new Promise(r=>{t=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{t.unsubscribe()})}_injector=p(Z);_rendererFactory=null;get injector(){return this._injector}bootstrap(t,r){return this.bootstrapImpl(t,r)}bootstrapImpl(t,r,o=ue.NULL){return this._injector.get($).run(()=>{H(10);let s=t instanceof Gs;if(!this._injector.get(Yl).done){let v="";throw new y(405,v)}let c;s?c=t:c=this._injector.get(Eo).resolveComponentFactory(t),this.componentTypes.push(c.componentType);let u=fm(c)?void 0:this._injector.get(bn),l=r||c.selector,d=c.create(o,[],l,u),h=d.location.nativeElement,f=d.injector.get(ql,null);return f?.registerApplication(h),d.onDestroy(()=>{this.detachView(d.hostView),go(this.components,d),f?.unregisterApplication(h)}),this._loadComponent(d),H(11,d),d})}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){H(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(Ys.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new y(101,!1);let t=N(null);try{this._runningTick=!0,this.synchronize()}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,N(t),this.afterTick.next(),H(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(wn,null,{optional:!0}));let t=0;for(;this.dirtyFlags!==0&&t++<Sw;)H(14),this.synchronizeOnce(),H(15)}synchronizeOnce(){this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush());let t=!1;if(this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:o}of this.allViews){if(!r&&!uo(o))continue;let i=r&&!this.zonelessEnabled?0:1;Fl(o,i),t=!0}if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}t||(this._rendererFactory?.begin?.(),this._rendererFactory?.end?.()),this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:t})=>uo(t))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(t){let r=t;this._views.push(r),r.attachToAppRef(this)}detachView(t){let r=t;go(this._views,r),r.detachFromAppRef()}_loadComponent(t){this.attachView(t.hostView);try{this.tick()}catch(o){this.internalErrorHandler(o)}this.components.push(t),this._injector.get(Ks,[]).forEach(o=>o(t))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(t=>t()),this._views.slice().forEach(t=>t.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(t){return this._destroyListeners.push(t),()=>go(this._destroyListeners,t)}destroy(){if(this._destroyed)throw new y(406,!1);let t=this._injector;t.destroy&&!t.destroyed&&t.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function go(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function Tn(e,n,t,r){let o=V(),i=rs();if(_n(o,i,n)){let s=fe(),a=Mu();GE(a,o,e,n,t,r)}return Tn}function hm(e,n,t){let r=V(),o=rs();if(_n(r,o,n)){let i=fe(),s=Mu();jE(s,r,e,n,r[Q],t)}return hm}function Op(e,n,t,r,o){Ol(n,e,t,o?"class":"style",r)}function Ql(e,n,t,r){let o=V(),i=o[b],s=e+re,a=i.firstCreatePass?Vl(s,o,2,n,Nl,ns(),t,r):i.data[s];if(Rl(a,o,e,n,Mw),ar(a)){let c=o[b];zs(c,o,a),Dl(c,a,o)}return r!=null&&Co(o,a),Ql}function Kl(){let e=fe(),n=oe(),t=xl(n);return e.firstCreatePass&&jl(e,t),$h(t)&&zh(),Uh(),t.classesWithoutHost!=null&&wC(t)&&Op(e,t,V(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&_C(t)&&Op(e,t,V(),t.stylesWithoutHost,!1),Kl}function Js(e,n,t,r){return Ql(e,n,t,r),Kl(),Js}var Mw=(e,n,t,r,o)=>(fo(!0),yg(n[Q],r,ip()));function Jl(e,n,t){let r=V(),o=r[b],i=e+re,s=o.firstCreatePass?Vl(i,r,8,"ng-container",Nl,ns(),n,t):o.data[i];if(Rl(s,r,e,"ng-container",mm),ar(s)){let a=r[b];zs(a,r,s),Dl(a,s,r)}return t!=null&&Co(r,s),Jl}function Xs(){let e=fe(),n=oe(),t=xl(n);return e.firstCreatePass&&jl(e,t),Xs}function pm(e,n,t){return Jl(e,n,t),Xs(),pm}function gm(e,n,t){let r=V(),o=r[b],i=e+re,s=o.firstCreatePass?MI(i,o,8,"ng-container",n,t):o.data[i];return Rl(s,r,e,"ng-container",mm),t!=null&&Co(r,s),gm}function Tw(){let e=oe(),n=xl(e);return Xs}var mm=(e,n,t,r,o)=>(fo(!0),gE(n[Q],""));function Aw(){return V()}var Io="en-US";var Nw=Io;function vm(e){typeof e=="string"&&(Nw=e.toLowerCase().replace(/_/g,"-"))}function ye(e,n,t){let r=V(),o=fe(),i=oe();return Rw(o,r,r[Q],i,e,n,t),ye}function Rw(e,n,t,r,o,i,s){let a=!0,c=null;if((r.type&3||s)&&(c??=Fu(r,n,i),NI(r,e,n,s,t,o,i,c)&&(a=!1)),a){let u=r.outputs?.[o],l=r.hostDirectiveOutputs?.[o];if(l&&l.length)for(let d=0;d<l.length;d+=2){let h=l[d],f=l[d+1];c??=Fu(r,n,i),bp(r,n,h,f,o,c)}if(u&&u.length)for(let d of u)c??=Fu(r,n,i),bp(r,n,d,o,o,c)}}function xw(e=1){return op(e)}function Ow(e,n){let t=null,r=sE(e);for(let o=0;o<n.length;o++){let i=n[o];if(i==="*"){t=o;continue}if(r===null?vg(e,i,!0):uE(r,i))return o}return t}function kw(e){let n=V()[Ie][me];if(!n.projection){let t=e?e.length:1,r=n.projection=wh(t,null),o=r.slice(),i=n.child;for(;i!==null;){if(i.type!==128){let s=e?Ow(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function Fw(e,n=0,t,r,o,i){let s=V(),a=fe(),c=r?e+1:null;c!==null&&gw(s,a,c,r,o,i,null,t);let u=Dr(a,re+e,16,null,t||null);u.projection===null&&(u.projection=n),Du();let d=!s[or]||mu();s[Ie][me].projection[u.projection]===null&&c!==null?Pw(s,a,c):d&&!js(u)&&xE(a,s,u)}function Pw(e,n,t){let r=re+t,o=n.data[r],i=e[r],s=Xu(i,o.tView.ssrId),a=Rg(e,o,void 0,{dehydratedView:s});Ug(i,a,0,Qu(o,s))}function wo(e,n,t,r){XI(e,n,t,r)}function Xl(e,n,t){JI(e,n,t)}function Er(e){let n=V(),t=fe(),r=_u();is(r+1);let o=Hl(t,r);if(e.dirty&&Fh(n)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=nw(n,r);e.reset(i,LC),e.notifyOnChanges()}return!0}return!1}function Ir(){return KI(V(),_u())}function hs(e,n){return e<<17|n<<2}function Sn(e){return e>>17&32767}function Lw(e){return(e&2)==2}function Vw(e,n){return e&131071|n<<17}function ll(e){return e|2}function gr(e){return(e&131068)>>2}function Vu(e,n){return e&-131069|n<<2}function jw(e){return(e&1)===1}function dl(e){return e|1}function Bw(e,n,t,r,o,i){let s=i?n.classBindings:n.styleBindings,a=Sn(s),c=gr(s);e[r]=t;let u=!1,l;if(Array.isArray(t)){let d=t;l=d[1],(l===null||nr(d,l)>0)&&(u=!0)}else l=t;if(o)if(c!==0){let h=Sn(e[a+1]);e[r+1]=hs(h,a),h!==0&&(e[h+1]=Vu(e[h+1],r)),e[a+1]=Vw(e[a+1],r)}else e[r+1]=hs(a,0),a!==0&&(e[a+1]=Vu(e[a+1],r)),a=r;else e[r+1]=hs(c,0),a===0?a=r:e[c+1]=Vu(e[c+1],r),c=r;u&&(e[r+1]=ll(e[r+1])),kp(e,l,r,!0),kp(e,l,r,!1),Hw(n,l,e,r,i),s=hs(a,c),i?n.classBindings=s:n.styleBindings=s}function Hw(e,n,t,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof n=="string"&&nr(i,n)>=0&&(t[r+1]=dl(t[r+1]))}function kp(e,n,t,r){let o=e[t+1],i=n===null,s=r?Sn(o):gr(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],u=e[s+1];Uw(c,n)&&(a=!0,e[s+1]=r?dl(u):ll(u)),s=r?Sn(u):gr(u)}a&&(e[t+1]=r?ll(o):dl(o))}function Uw(e,n){return e===null||n==null||(Array.isArray(e)?e[1]:e)===n?!0:Array.isArray(e)&&typeof n=="string"?nr(e,n)>=0:!1}function _o(e,n){return $w(e,n,null,!0),_o}function $w(e,n,t,r){let o=V(),i=fe(),s=wu(2);if(i.firstUpdatePass&&Gw(i,e,s,r),n!==Ye&&_n(o,s,n)){let a=i.data[Gt()];Qw(i,a,o,o[Q],e,o[s+1]=Kw(n,t),r,s)}}function zw(e,n){return n>=e.expandoStartIndex}function Gw(e,n,t,r){let o=e.data;if(o[t+1]===null){let i=o[Gt()],s=zw(e,t);Jw(i,r)&&n===null&&!s&&(n=!1),n=Ww(o,i,n,r),Bw(o,i,n,t,s,r)}}function Ww(e,n,t,r){let o=ep(e),i=r?n.residualClasses:n.residualStyles;if(o===null)(r?n.classBindings:n.styleBindings)===0&&(t=ju(null,e,n,t,r),t=Do(t,n.attrs,r),i=null);else{let s=n.directiveStylingLast;if(s===-1||e[s]!==o)if(t=ju(o,e,n,t,r),i===null){let c=qw(e,n,r);c!==void 0&&Array.isArray(c)&&(c=ju(null,e,n,c[1],r),c=Do(c,n.attrs,r),Zw(e,n,r,c))}else i=Yw(e,n,r)}return i!==void 0&&(r?n.residualClasses=i:n.residualStyles=i),t}function qw(e,n,t){let r=t?n.classBindings:n.styleBindings;if(gr(r)!==0)return e[Sn(r)]}function Zw(e,n,t,r){let o=t?n.classBindings:n.styleBindings;e[Sn(o)]=r}function Yw(e,n,t){let r,o=n.directiveEnd;for(let i=1+n.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=Do(r,s,t)}return Do(r,n.attrs,t)}function ju(e,n,t,r,o){let i=null,s=t.directiveEnd,a=t.directiveStylingLast;for(a===-1?a=t.directiveStart:a++;a<s&&(i=n[a],r=Do(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(t.directiveStylingLast=a),r}function Do(e,n,t){let r=t?1:2,o=-1;if(n!==null)for(let i=0;i<n.length;i++){let s=n[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),bh(e,s,t?!0:n[++i]))}return e===void 0?null:e}function Qw(e,n,t,r,o,i,s,a){if(!(n.type&3))return;let c=e.data,u=c[a+1],l=jw(u)?Fp(c,n,t,o,gr(u),s):void 0;if(!ks(l)){ks(i)||Lw(u)&&(i=Fp(c,null,t,o,a,s));let d=lu(Gt(),t);kE(r,s,d,o,i)}}function Fp(e,n,t,r,o,i){let s=n===null,a;for(;o>0;){let c=e[o],u=Array.isArray(c),l=u?c[1]:c,d=l===null,h=t[o+1];h===Ye&&(h=d?Me:void 0);let f=d?Qi(h,r):l===r?h:void 0;if(u&&!ks(f)&&(f=Qi(c,r)),ks(f)&&(a=f,s))return a;let v=e[o+1];o=s?Sn(v):gr(v)}if(n!==null){let c=i?n.residualClasses:n.residualStyles;c!=null&&(a=Qi(c,r))}return a}function ks(e){return e!==void 0}function Kw(e,n){return e==null||e===""||(typeof n=="string"?e=e+n:typeof e=="object"&&(e=Dt(Bs(e)))),e}function Jw(e,n){return(e.flags&(n?8:16))!==0}function Xw(e,n=""){let t=V(),r=fe(),o=e+re,i=r.firstCreatePass?Dr(r,o,1,n,null):r.data[o],s=e_(r,t,i,n,e);t[o]=s,cs()&&Tl(r,t,s,i),lr(i,!1)}var e_=(e,n,t,r,o)=>(fo(!0),hE(n[Q],r));function t_(e,n,t,r=""){return _n(e,rs(),t)?n+pn(t)+r:Ye}function n_(e,n,t,r,o,i=""){let s=Yh(),a=AI(e,s,t,o);return wu(2),a?n+pn(t)+r+pn(o)+i:Ye}function ym(e){return ed("",e),ym}function ed(e,n,t){let r=V(),o=t_(r,e,n,t);return o!==Ye&&Cm(r,Gt(),o),ed}function Dm(e,n,t,r,o){let i=V(),s=n_(i,e,n,t,r,o);return s!==Ye&&Cm(i,Gt(),s),Dm}function Cm(e,n,t){let r=lu(n,e);pE(e[Q],r,t)}function r_(e,n,t){let r=fe();if(r.firstCreatePass){let o=at(e);fl(t,r.data,r.blueprint,o,!0),fl(n,r.data,r.blueprint,o,!1)}}function fl(e,n,t,r,o){if(e=ce(e),Array.isArray(e))for(let i=0;i<e.length;i++)fl(e[i],n,t,r,o);else{let i=fe(),s=V(),a=oe(),c=dn(e)?e:ce(e.provide),u=su(e),l=a.providerIndexes&1048575,d=a.directiveStart,h=a.providerIndexes>>20;if(dn(e)||!e.multi){let f=new In(u,o,m,null),v=Hu(c,n,o?l:l+h,d);v===-1?($u(Es(a,s),i,c),Bu(i,e,n.length),n.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(f),s.push(f)):(t[v]=f,s[v]=f)}else{let f=Hu(c,n,l+h,d),v=Hu(c,n,l,l+h),E=f>=0&&t[f],T=v>=0&&t[v];if(o&&!T||!o&&!E){$u(Es(a,s),i,c);let R=s_(o?i_:o_,t.length,o,r,u,e);!o&&T&&(t[v].providerFactory=R),Bu(i,e,n.length,0),n.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(R),s.push(R)}else{let R=Em(t[o?v:f],u,!o&&r);Bu(i,e,f>-1?f:v,R)}!o&&r&&T&&t[v].componentProviders++}}}function Bu(e,n,t,r){let o=dn(n),i=Nh(n);if(o||i){let c=(i?ce(n.useClass):n).prototype.ngOnDestroy;if(c){let u=e.destroyHooks||(e.destroyHooks=[]);if(!o&&n.multi){let l=u.indexOf(t);l===-1?u.push(t,[r,c]):u[l+1].push(r,c)}else u.push(t,c)}}}function Em(e,n,t){return t&&e.componentProviders++,e.multi.push(n)-1}function Hu(e,n,t,r){for(let o=t;o<r;o++)if(n[o]===e)return o;return-1}function o_(e,n,t,r,o){return hl(this.multi,[])}function i_(e,n,t,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=mo(r,r[b],this.providerFactory.index,o);s=c.slice(0,a),hl(i,s);for(let u=a;u<c.length;u++)s.push(c[u])}else s=[],hl(i,s);return s}function hl(e,n){for(let t=0;t<e.length;t++){let r=e[t];n.push(r())}return n}function s_(e,n,t,r,o,i){let s=new In(e,t,m,null);return s.multi=[],s.index=n,s.componentProviders=0,Em(s,o,r&&!t),s}function _t(e,n=[]){return t=>{t.providersResolver=(r,o)=>r_(r,o?o(e):e,n)}}function a_(e,n,t,r){return u_(V(),Zh(),e,n,t,r)}function c_(e,n){let t=e[n];return t===Ye?void 0:t}function u_(e,n,t,r,o,i){let s=n+t;return _n(e,s,o)?TI(e,s+1,i?r.call(i,o):r(o)):c_(e,s+1)}var Fs=class{ngModuleFactory;componentFactories;constructor(n,t){this.ngModuleFactory=n,this.componentFactories=t}},td=(()=>{class e{compileModuleSync(t){return new Ns(t)}compileModuleAsync(t){return Promise.resolve(this.compileModuleSync(t))}compileModuleAndAllComponentsSync(t){let r=this.compileModuleSync(t),o=nu(t),i=gg(o.declarations).reduce((s,a)=>{let c=rt(a);return c&&s.push(new Zt(c)),s},[]);return new Fs(r,i)}compileModuleAndAllComponentsAsync(t){return Promise.resolve(this.compileModuleAndAllComponentsSync(t))}clearCache(){}clearCacheFor(t){}getModuleId(t){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var l_=(()=>{class e{zone=p($);changeDetectionScheduler=p(Vt);applicationRef=p(Qe);applicationErrorHandler=p(Ae);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{try{this.applicationRef.dirtyFlags|=1,this.applicationRef._tick()}catch(t){this.applicationErrorHandler(t)}})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Im({ngZoneFactory:e,ignoreChangesOutsideZone:n,scheduleInRootZone:t}){return e??=()=>new $(A(g({},wm()),{scheduleInRootZone:t})),[{provide:$,useFactory:e},{provide:Bt,multi:!0,useFactory:()=>{let r=p(l_,{optional:!0});return()=>r.initialize()}},{provide:Bt,multi:!0,useFactory:()=>{let r=p(d_);return()=>{r.initialize()}}},n===!0?{provide:Au,useValue:!0}:[],{provide:Nu,useValue:t??im},{provide:Ae,useFactory:()=>{let r=p($),o=p(Z),i;return s=>{r.runOutsideAngular(()=>{o.destroyed&&!i?setTimeout(()=>{throw s}):(i??=o.get(Ue),i.handleError(s))})}}}]}function wm(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var d_=(()=>{class e{subscription=new K;initialized=!1;zone=p($);pendingTasks=p(Et);initialize(){if(this.initialized)return;this.initialized=!0;let t=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(t=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{$.assertNotInAngularZone(),queueMicrotask(()=>{t!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(t),t=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{$.assertInAngularZone(),t??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var _m=(()=>{class e{applicationErrorHandler=p(Ae);appRef=p(Qe);taskService=p(Et);ngZone=p($);zonelessEnabled=p(us);tracing=p(Mn,{optional:!0});disableScheduling=p(Au,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new K;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Rs):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(p(Nu,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof xs||!this.zoneIsDefined)}notify(t){if(!this.zonelessEnabled&&t===5)return;let r=!1;switch(t){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Np:sm;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(t){return!(this.disableScheduling&&!t||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Rs+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let t=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){this.taskService.remove(t),this.applicationErrorHandler(r)}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Np(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(t)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let t=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(t)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function f_(){return typeof $localize<"u"&&$localize.locale||Io}var ea=new C("",{providedIn:"root",factory:()=>p(ea,{optional:!0,skipSelf:!0})||f_()});function je(e){return hh(e)}function wr(e,n){return Di(e,n?.equal)}var bm=class{[he];constructor(n){this[he]=n}destroy(){this[he].destroy()}};var Nm=Symbol("InputSignalNode#UNSET"),T_=A(g({},Ci),{transformFn:void 0,applyValueToInputSignal(e,n){Bn(e,n)}});function Rm(e,n){let t=Object.create(T_);t.value=e,t.transformFn=n?.transform;function r(){if(Vn(t),t.value===Nm){let o=null;throw new y(-950,o)}return t.value}return r[he]=t,r}var na=class{attributeName;constructor(n){this.attributeName=n}__NG_ELEMENT_ID__=()=>Yt(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},A_=new C("");A_.__NG_ELEMENT_ID__=e=>{let n=oe();if(n===null)throw new y(204,!1);if(n.type&2)return n.value;if(e&8)return null;throw new y(204,!1)};function Sm(e,n){return Rm(e,n)}function N_(e){return Rm(Nm,e)}var xm=(Sm.required=N_,Sm);var nd=new C(""),R_=new C("");function bo(e){return!e.moduleRef}function x_(e){let n=bo(e)?e.r3Injector:e.moduleRef.injector,t=n.get($);return t.run(()=>{bo(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=n.get(Ae),o;if(t.runOutsideAngular(()=>{o=t.onError.subscribe({next:r})}),bo(e)){let i=()=>n.destroy(),s=e.platformInjector.get(nd);s.add(i),n.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(nd);s.add(i),e.moduleRef.onDestroy(()=>{go(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return k_(r,t,()=>{let i=n.get(Et),s=i.add(),a=n.get(Yl);return a.runInitializers(),a.donePromise.then(()=>{let c=n.get(ea,Io);if(vm(c||Io),!n.get(R_,!0))return bo(e)?n.get(Qe):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(bo(e)){let l=n.get(Qe);return e.rootComponent!==void 0&&l.bootstrap(e.rootComponent),l}else return O_?.(e.moduleRef,e.allPlatformModules),e.moduleRef}).finally(()=>void i.remove(s))})})}var O_;function k_(e,n,t){try{let r=t();return Qt(r)?r.catch(o=>{throw n.runOutsideAngular(()=>e(o)),o}):r}catch(r){throw n.runOutsideAngular(()=>e(r)),r}}var ta=null;function F_(e=[],n){return ue.create({name:n,providers:[{provide:io,useValue:"platform"},{provide:nd,useValue:new Set([()=>ta=null])},...e]})}function P_(e=[]){if(ta)return ta;let n=F_(e);return ta=n,dm(),L_(n),n}function L_(e){let n=e.get(Ls,null);ge(e,()=>{n?.forEach(t=>t())})}var Ke=(()=>{class e{static __NG_ELEMENT_ID__=V_}return e})();function V_(e){return j_(oe(),V(),(e&16)===16)}function j_(e,n,t){if($t(e)&&!t){let r=Le(e.index,n);return new qt(r,r)}else if(e.type&175){let r=n[Ie];return new qt(r,n)}return null}var rd=class{constructor(){}supports(n){return Bl(n)}create(n){return new od(n)}},B_=(e,n)=>n,od=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(n){this._trackByFn=n||B_}forEachItem(n){let t;for(t=this._itHead;t!==null;t=t._next)n(t)}forEachOperation(n){let t=this._itHead,r=this._removalsHead,o=0,i=null;for(;t||r;){let s=!r||t&&t.currentIndex<Mm(r,o,i)?t:r,a=Mm(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(t=t._next,s.previousIndex==null)o++;else{i||(i=[]);let u=a-o,l=c-o;if(u!=l){for(let h=0;h<u;h++){let f=h<i.length?i[h]:i[h]=0,v=f+h;l<=v&&v<u&&(i[h]=f+1)}let d=s.previousIndex;i[d]=l-u}}a!==c&&n(s,a,c)}}forEachPreviousItem(n){let t;for(t=this._previousItHead;t!==null;t=t._nextPrevious)n(t)}forEachAddedItem(n){let t;for(t=this._additionsHead;t!==null;t=t._nextAdded)n(t)}forEachMovedItem(n){let t;for(t=this._movesHead;t!==null;t=t._nextMoved)n(t)}forEachRemovedItem(n){let t;for(t=this._removalsHead;t!==null;t=t._nextRemoved)n(t)}forEachIdentityChange(n){let t;for(t=this._identityChangesHead;t!==null;t=t._nextIdentityChange)n(t)}diff(n){if(n==null&&(n=[]),!Bl(n))throw new y(900,!1);return this.check(n)?this:null}onDestroy(){}check(n){this._reset();let t=this._itHead,r=!1,o,i,s;if(Array.isArray(n)){this.length=n.length;for(let a=0;a<this.length;a++)i=n[a],s=this._trackByFn(a,i),t===null||!Object.is(t.trackById,s)?(t=this._mismatch(t,i,s,a),r=!0):(r&&(t=this._verifyReinsertion(t,i,s,a)),Object.is(t.item,i)||this._addIdentityChange(t,i)),t=t._next}else o=0,Zg(n,a=>{s=this._trackByFn(o,a),t===null||!Object.is(t.trackById,s)?(t=this._mismatch(t,a,s,o),r=!0):(r&&(t=this._verifyReinsertion(t,a,s,o)),Object.is(t.item,a)||this._addIdentityChange(t,a)),t=t._next,o++}),this.length=o;return this._truncate(t),this.collection=n,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let n;for(n=this._previousItHead=this._itHead;n!==null;n=n._next)n._nextPrevious=n._next;for(n=this._additionsHead;n!==null;n=n._nextAdded)n.previousIndex=n.currentIndex;for(this._additionsHead=this._additionsTail=null,n=this._movesHead;n!==null;n=n._nextMoved)n.previousIndex=n.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(n,t,r,o){let i;return n===null?i=this._itTail:(i=n._prev,this._remove(n)),n=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),n!==null?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._reinsertAfter(n,i,o)):(n=this._linkedRecords===null?null:this._linkedRecords.get(r,o),n!==null?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._moveAfter(n,i,o)):n=this._addAfter(new id(t,r),i,o)),n}_verifyReinsertion(n,t,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?n=this._reinsertAfter(i,n._prev,o):n.currentIndex!=o&&(n.currentIndex=o,this._addToMoves(n,o)),n}_truncate(n){for(;n!==null;){let t=n._next;this._addToRemovals(this._unlink(n)),n=t}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(n,t,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(n);let o=n._prevRemoved,i=n._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(n,t,r),this._addToMoves(n,r),n}_moveAfter(n,t,r){return this._unlink(n),this._insertAfter(n,t,r),this._addToMoves(n,r),n}_addAfter(n,t,r){return this._insertAfter(n,t,r),this._additionsTail===null?this._additionsTail=this._additionsHead=n:this._additionsTail=this._additionsTail._nextAdded=n,n}_insertAfter(n,t,r){let o=t===null?this._itHead:t._next;return n._next=o,n._prev=t,o===null?this._itTail=n:o._prev=n,t===null?this._itHead=n:t._next=n,this._linkedRecords===null&&(this._linkedRecords=new ra),this._linkedRecords.put(n),n.currentIndex=r,n}_remove(n){return this._addToRemovals(this._unlink(n))}_unlink(n){this._linkedRecords!==null&&this._linkedRecords.remove(n);let t=n._prev,r=n._next;return t===null?this._itHead=r:t._next=r,r===null?this._itTail=t:r._prev=t,n}_addToMoves(n,t){return n.previousIndex===t||(this._movesTail===null?this._movesTail=this._movesHead=n:this._movesTail=this._movesTail._nextMoved=n),n}_addToRemovals(n){return this._unlinkedRecords===null&&(this._unlinkedRecords=new ra),this._unlinkedRecords.put(n),n.currentIndex=null,n._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=n,n._prevRemoved=null):(n._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=n),n}_addIdentityChange(n,t){return n.item=t,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=n:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=n,n}},id=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(n,t){this.item=n,this.trackById=t}},sd=class{_head=null;_tail=null;add(n){this._head===null?(this._head=this._tail=n,n._nextDup=null,n._prevDup=null):(this._tail._nextDup=n,n._prevDup=this._tail,n._nextDup=null,this._tail=n)}get(n,t){let r;for(r=this._head;r!==null;r=r._nextDup)if((t===null||t<=r.currentIndex)&&Object.is(r.trackById,n))return r;return null}remove(n){let t=n._prevDup,r=n._nextDup;return t===null?this._head=r:t._nextDup=r,r===null?this._tail=t:r._prevDup=t,this._head===null}},ra=class{map=new Map;put(n){let t=n.trackById,r=this.map.get(t);r||(r=new sd,this.map.set(t,r)),r.add(n)}get(n,t){let r=n,o=this.map.get(r);return o?o.get(n,t):null}remove(n){let t=n.trackById;return this.map.get(t).remove(n)&&this.map.delete(t),n}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function Mm(e,n,t){let r=e.previousIndex;if(r===null)return r;let o=0;return t&&r<t.length&&(o=t[r]),r+n+o}var ad=class{constructor(){}supports(n){return n instanceof Map||Ws(n)}create(){return new cd}},cd=class{_records=new Map;_mapHead=null;_appendAfter=null;_previousMapHead=null;_changesHead=null;_changesTail=null;_additionsHead=null;_additionsTail=null;_removalsHead=null;_removalsTail=null;get isDirty(){return this._additionsHead!==null||this._changesHead!==null||this._removalsHead!==null}forEachItem(n){let t;for(t=this._mapHead;t!==null;t=t._next)n(t)}forEachPreviousItem(n){let t;for(t=this._previousMapHead;t!==null;t=t._nextPrevious)n(t)}forEachChangedItem(n){let t;for(t=this._changesHead;t!==null;t=t._nextChanged)n(t)}forEachAddedItem(n){let t;for(t=this._additionsHead;t!==null;t=t._nextAdded)n(t)}forEachRemovedItem(n){let t;for(t=this._removalsHead;t!==null;t=t._nextRemoved)n(t)}diff(n){if(!n)n=new Map;else if(!(n instanceof Map||Ws(n)))throw new y(900,!1);return this.check(n)?this:null}onDestroy(){}check(n){this._reset();let t=this._mapHead;if(this._appendAfter=null,this._forEach(n,(r,o)=>{if(t&&t.key===o)this._maybeAddToChanges(t,r),this._appendAfter=t,t=t._next;else{let i=this._getOrCreateRecordForKey(o,r);t=this._insertBeforeOrAppend(t,i)}}),t){t._prev&&(t._prev._next=null),this._removalsHead=t;for(let r=t;r!==null;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(n,t){if(n){let r=n._prev;return t._next=n,t._prev=r,n._prev=t,r&&(r._next=t),n===this._mapHead&&(this._mapHead=t),this._appendAfter=n,n}return this._appendAfter?(this._appendAfter._next=t,t._prev=this._appendAfter):this._mapHead=t,this._appendAfter=t,null}_getOrCreateRecordForKey(n,t){if(this._records.has(n)){let o=this._records.get(n);this._maybeAddToChanges(o,t);let i=o._prev,s=o._next;return i&&(i._next=s),s&&(s._prev=i),o._next=null,o._prev=null,o}let r=new ud(n);return this._records.set(n,r),r.currentValue=t,this._addToAdditions(r),r}_reset(){if(this.isDirty){let n;for(this._previousMapHead=this._mapHead,n=this._previousMapHead;n!==null;n=n._next)n._nextPrevious=n._next;for(n=this._changesHead;n!==null;n=n._nextChanged)n.previousValue=n.currentValue;for(n=this._additionsHead;n!=null;n=n._nextAdded)n.previousValue=n.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(n,t){Object.is(t,n.currentValue)||(n.previousValue=n.currentValue,n.currentValue=t,this._addToChanges(n))}_addToAdditions(n){this._additionsHead===null?this._additionsHead=this._additionsTail=n:(this._additionsTail._nextAdded=n,this._additionsTail=n)}_addToChanges(n){this._changesHead===null?this._changesHead=this._changesTail=n:(this._changesTail._nextChanged=n,this._changesTail=n)}_forEach(n,t){n instanceof Map?n.forEach(t):Object.keys(n).forEach(r=>t(n[r],r))}},ud=class{key;previousValue=null;currentValue=null;_nextPrevious=null;_next=null;_prev=null;_nextAdded=null;_nextRemoved=null;_nextChanged=null;constructor(n){this.key=n}};function Tm(){return new ld([new rd])}var ld=(()=>{class e{factories;static \u0275prov=D({token:e,providedIn:"root",factory:Tm});constructor(t){this.factories=t}static create(t,r){if(r!=null){let o=r.factories.slice();t=t.concat(o)}return new e(t)}static extend(t){return{provide:e,useFactory:r=>e.create(t,r||Tm()),deps:[[e,new gl,new pl]]}}find(t){let r=this.factories.find(o=>o.supports(t));if(r!=null)return r;throw new y(901,!1)}}return e})();function Am(){return new dd([new ad])}var dd=(()=>{class e{static \u0275prov=D({token:e,providedIn:"root",factory:Am});factories;constructor(t){this.factories=t}static create(t,r){if(r){let o=r.factories.slice();t=t.concat(o)}return new e(t)}static extend(t){return{provide:e,useFactory:r=>e.create(t,r||Am()),deps:[[e,new gl,new pl]]}}find(t){let r=this.factories.find(o=>o.supports(t));if(r)return r;throw new y(901,!1)}}return e})();function Om(e){H(8);try{let{rootComponent:n,appProviders:t,platformProviders:r}=e,o=P_(r),i=[Im({}),{provide:Vt,useExisting:_m},ap,...t||[]],s=new yo({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return x_({r3Injector:s.injector,platformInjector:o,rootComponent:n})}catch(n){return Promise.reject(n)}finally{H(9)}}function So(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function km(e,n){let t=rt(e),r=n.elementInjector||rr();return new Zt(t).create(r,n.projectableNodes,n.hostElement,n.environmentInjector,n.directives,n.bindings)}function fd(e){let n=rt(e);if(!n)return null;let t=new Zt(n);return{get selector(){return t.selector},get type(){return t.componentType},get inputs(){return t.inputs},get outputs(){return t.outputs},get ngContentSelectors(){return t.ngContentSelectors},get isStandalone(){return n.standalone},get isSignal(){return n.signals}}}var Lm=null;function Be(){return Lm}function hd(e){Lm??=e}var Mo=class{},pd=(()=>{class e{historyGo(t){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(Vm),providedIn:"platform"})}return e})();var Vm=(()=>{class e extends pd{_location;_history;_doc=p(ee);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Be().getBaseHref(this._doc)}onPopState(t){let r=Be().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",t,!1),()=>r.removeEventListener("popstate",t)}onHashChange(t){let r=Be().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",t,!1),()=>r.removeEventListener("hashchange",t)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(t){this._location.pathname=t}pushState(t,r,o){this._history.pushState(t,r,o)}replaceState(t,r,o){this._history.replaceState(t,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(t=0){this._history.go(t)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function jm(e,n){return e?n?e.endsWith("/")?n.startsWith("/")?e+n.slice(1):e+n:n.startsWith("/")?e+n:`${e}/${n}`:e:n}function Fm(e){let n=e.search(/#|\?|$/);return e[n-1]==="/"?e.slice(0,n-1)+e.slice(n):e}function Kt(e){return e&&e[0]!=="?"?`?${e}`:e}var St=(()=>{class e{historyGo(t){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(Hm),providedIn:"root"})}return e})(),Bm=new C(""),Hm=(()=>{class e extends St{_platformLocation;_baseHref;_removeListenerFns=[];constructor(t,r){super(),this._platformLocation=t,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??p(ee).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}prepareExternalUrl(t){return jm(this._baseHref,t)}path(t=!1){let r=this._platformLocation.pathname+Kt(this._platformLocation.search),o=this._platformLocation.hash;return o&&t?`${r}${o}`:r}pushState(t,r,o,i){let s=this.prepareExternalUrl(o+Kt(i));this._platformLocation.pushState(t,r,s)}replaceState(t,r,o,i){let s=this.prepareExternalUrl(o+Kt(i));this._platformLocation.replaceState(t,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}static \u0275fac=function(r){return new(r||e)(I(pd),I(Bm,8))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Mt=(()=>{class e{_subject=new U;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(t){this._locationStrategy=t;let r=this._locationStrategy.getBaseHref();this._basePath=$_(Fm(Pm(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(t=!1){return this.normalize(this._locationStrategy.path(t))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(t,r=""){return this.path()==this.normalize(t+Kt(r))}normalize(t){return e.stripTrailingSlash(U_(this._basePath,Pm(t)))}prepareExternalUrl(t){return t&&t[0]!=="/"&&(t="/"+t),this._locationStrategy.prepareExternalUrl(t)}go(t,r="",o=null){this._locationStrategy.pushState(o,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+Kt(r)),o)}replaceState(t,r="",o=null){this._locationStrategy.replaceState(o,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+Kt(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(t=0){this._locationStrategy.historyGo?.(t)}onUrlChange(t){return this._urlChangeListeners.push(t),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(t);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(t="",r){this._urlChangeListeners.forEach(o=>o(t,r))}subscribe(t,r,o){return this._subject.subscribe({next:t,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=Kt;static joinWithSlash=jm;static stripTrailingSlash=Fm;static \u0275fac=function(r){return new(r||e)(I(St))};static \u0275prov=D({token:e,factory:()=>H_(),providedIn:"root"})}return e})();function H_(){return new Mt(I(St))}function U_(e,n){if(!e||!n.startsWith(e))return n;let t=n.substring(e.length);return t===""||["/",";","?","#"].includes(t[0])?t:n}function Pm(e){return e.replace(/\/index.html$/,"")}function $_(e){if(new RegExp("^(https?:)?//").test(e)){let[,t]=e.split(/\/\/[^\/]+/);return t}return e}var oa=class{$implicit;ngForOf;index;count;constructor(n,t,r,o){this.$implicit=n,this.ngForOf=t,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},zm=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(t){this._ngForOf=t,this._ngForOfDirty=!0}set ngForTrackBy(t){this._trackByFn=t}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(t,r,o){this._viewContainer=t,this._template=r,this._differs=o}set ngForTemplate(t){t&&(this._template=t)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let t=this._ngForOf;!this._differ&&t&&(this._differ=this._differs.find(t).create(this.ngForTrackBy))}if(this._differ){let t=this._differ.diff(this._ngForOf);t&&this._applyChanges(t)}}_applyChanges(t){let r=this._viewContainer;t.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new oa(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),Um(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}t.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);Um(i,o)})}static ngTemplateContextGuard(t,r){return!0}static \u0275fac=function(r){return new(r||e)(m(Ve),m(Ze),m(ld))};static \u0275dir=L({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function Um(e,n){e.context.$implicit=n.item}var z_=(()=>{class e{_viewContainer;_context=new ia;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(t,r){this._viewContainer=t,this._thenTemplateRef=r}set ngIf(t){this._context.$implicit=this._context.ngIf=t,this._updateView()}set ngIfThen(t){$m(t,!1),this._thenTemplateRef=t,this._thenViewRef=null,this._updateView()}set ngIfElse(t){$m(t,!1),this._elseTemplateRef=t,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(t,r){return!0}static \u0275fac=function(r){return new(r||e)(m(Ve),m(Ze))};static \u0275dir=L({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),ia=class{$implicit=null;ngIf=null};function $m(e,n){if(e&&!e.createEmbeddedView)throw new y(2020,!1)}var G_=(()=>{class e{_ngEl;_differs;_renderer;_ngStyle=null;_differ=null;constructor(t,r,o){this._ngEl=t,this._differs=r,this._renderer=o}set ngStyle(t){this._ngStyle=t,!this._differ&&t&&(this._differ=this._differs.find(t).create())}ngDoCheck(){if(this._differ){let t=this._differ.diff(this._ngStyle);t&&this._applyChanges(t)}}_setStyle(t,r){let[o,i]=t.split("."),s=o.indexOf("-")===-1?void 0:qe.DashCase;r!=null?this._renderer.setStyle(this._ngEl.nativeElement,o,i?`${r}${i}`:r,s):this._renderer.removeStyle(this._ngEl.nativeElement,o,s)}_applyChanges(t){t.forEachRemovedItem(r=>this._setStyle(r.key,null)),t.forEachAddedItem(r=>this._setStyle(r.key,r.currentValue)),t.forEachChangedItem(r=>this._setStyle(r.key,r.currentValue))}static \u0275fac=function(r){return new(r||e)(m(G),m(dd),m(dt))};static \u0275dir=L({type:e,selectors:[["","ngStyle",""]],inputs:{ngStyle:"ngStyle"}})}return e})(),W_=(()=>{class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(t){this._viewContainerRef=t}ngOnChanges(t){if(this._shouldRecreateView(t)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let o=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(t){return!!t.ngTemplateOutlet||!!t.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(t,r,o)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,o):!1,get:(t,r,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,o)}})}static \u0275fac=function(r){return new(r||e)(m(Ve))};static \u0275dir=L({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[we]})}return e})();var Gm=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=wt({type:e});static \u0275inj=nt({})}return e})();function gd(e,n){n=encodeURIComponent(n);for(let t of e.split(";")){let r=t.indexOf("="),[o,i]=r==-1?[t,""]:[t.slice(0,r),t.slice(r+1)];if(o.trim()===n)return decodeURIComponent(i)}return null}var To=class{};var Wm="browser";var aa=new C(""),Cd=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(t,r){this._zone=r,t.forEach(o=>{o.manager=this}),this._plugins=t.slice().reverse()}addEventListener(t,r,o,i){return this._findPluginFor(r).addEventListener(t,r,o,i)}getZone(){return this._zone}_findPluginFor(t){let r=this._eventNameToPlugin.get(t);if(r)return r;if(r=this._plugins.find(i=>i.supports(t)),!r)throw new y(5101,!1);return this._eventNameToPlugin.set(t,r),r}static \u0275fac=function(r){return new(r||e)(I(aa),I($))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Ao=class{_doc;constructor(n){this._doc=n}manager},md="ng-app-id";function Zm(e){for(let n of e)n.remove()}function Ym(e,n){let t=n.createElement("style");return t.textContent=e,t}function q_(e,n,t,r){let o=e.head?.querySelectorAll(`style[${md}="${n}"],link[${md}="${n}"]`);if(o)for(let i of o)i.removeAttribute(md),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&t.set(i.textContent,{usage:0,elements:[i]})}function yd(e,n){let t=n.createElement("link");return t.setAttribute("rel","stylesheet"),t.setAttribute("href",e),t}var Ed=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;constructor(t,r,o,i={}){this.doc=t,this.appId=r,this.nonce=o,q_(t,r,this.inline,this.external),this.hosts.add(t.head)}addStyles(t,r){for(let o of t)this.addUsage(o,this.inline,Ym);r?.forEach(o=>this.addUsage(o,this.external,yd))}removeStyles(t,r){for(let o of t)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(t,r,o){let i=r.get(t);i?i.usage++:r.set(t,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(t,this.doc)))})}removeUsage(t,r){let o=r.get(t);o&&(o.usage--,o.usage<=0&&(Zm(o.elements),r.delete(t)))}ngOnDestroy(){for(let[,{elements:t}]of[...this.inline,...this.external])Zm(t);this.hosts.clear()}addHost(t){this.hosts.add(t);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(t,Ym(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(t,yd(r,this.doc)))}removeHost(t){this.hosts.delete(t)}addElement(t,r){return this.nonce&&r.setAttribute("nonce",this.nonce),t.appendChild(r)}static \u0275fac=function(r){return new(r||e)(I(ee),I(Ps),I(Vs,8),I(yr))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),vd={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Id=/%COMP%/g;var Km="%COMP%",Z_=`_nghost-${Km}`,Y_=`_ngcontent-${Km}`,Q_=!0,K_=new C("",{providedIn:"root",factory:()=>Q_});function J_(e){return Y_.replace(Id,e)}function X_(e){return Z_.replace(Id,e)}function Jm(e,n){return n.map(t=>t.replace(Id,e))}var wd=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(t,r,o,i,s,a,c,u=null,l=null){this.eventManager=t,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=u,this.tracingService=l,this.platformIsServer=!1,this.defaultRenderer=new No(t,s,c,this.platformIsServer,this.tracingService)}createRenderer(t,r){if(!t||!r)return this.defaultRenderer;let o=this.getOrCreateRenderer(t,r);return o instanceof sa?o.applyToHost(t):o instanceof Ro&&o.applyStyles(),o}getOrCreateRenderer(t,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,d=this.platformIsServer,h=this.tracingService;switch(r.encapsulation){case It.Emulated:i=new sa(c,u,r,this.appId,l,s,a,d,h);break;case It.ShadowDom:return new Dd(c,u,t,r,s,a,this.nonce,d,h);default:i=new Ro(c,u,r,l,s,a,d,h);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(t){this.rendererByCompId.delete(t)}static \u0275fac=function(r){return new(r||e)(I(Cd),I(Ed),I(Ps),I(K_),I(ee),I(yr),I($),I(Vs),I(Mn,8))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),No=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(n,t,r,o,i){this.eventManager=n,this.doc=t,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(n,t){return t?this.doc.createElementNS(vd[t]||t,n):this.doc.createElement(n)}createComment(n){return this.doc.createComment(n)}createText(n){return this.doc.createTextNode(n)}appendChild(n,t){(Qm(n)?n.content:n).appendChild(t)}insertBefore(n,t,r){n&&(Qm(n)?n.content:n).insertBefore(t,r)}removeChild(n,t){t.remove()}selectRootElement(n,t){let r=typeof n=="string"?this.doc.querySelector(n):n;if(!r)throw new y(-5104,!1);return t||(r.textContent=""),r}parentNode(n){return n.parentNode}nextSibling(n){return n.nextSibling}setAttribute(n,t,r,o){if(o){t=o+":"+t;let i=vd[o];i?n.setAttributeNS(i,t,r):n.setAttribute(t,r)}else n.setAttribute(t,r)}removeAttribute(n,t,r){if(r){let o=vd[r];o?n.removeAttributeNS(o,t):n.removeAttribute(`${r}:${t}`)}else n.removeAttribute(t)}addClass(n,t){n.classList.add(t)}removeClass(n,t){n.classList.remove(t)}setStyle(n,t,r,o){o&(qe.DashCase|qe.Important)?n.style.setProperty(t,r,o&qe.Important?"important":""):n.style[t]=r}removeStyle(n,t,r){r&qe.DashCase?n.style.removeProperty(t):n.style[t]=""}setProperty(n,t,r){n!=null&&(n[t]=r)}setValue(n,t){n.nodeValue=t}listen(n,t,r,o){if(typeof n=="string"&&(n=Be().getGlobalEventTarget(this.doc,n),!n))throw new y(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(n,t,i)),this.eventManager.addEventListener(n,t,i,o)}decoratePreventDefault(n){return t=>{if(t==="__ngUnwrap__")return n;n(t)===!1&&t.preventDefault()}}};function Qm(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var Dd=class extends No{sharedStylesHost;hostEl;shadowRoot;constructor(n,t,r,o,i,s,a,c,u){super(n,i,s,c,u),this.sharedStylesHost=t,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=o.styles;l=Jm(o.id,l);for(let h of l){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=h,this.shadowRoot.appendChild(f)}let d=o.getExternalStyles?.();if(d)for(let h of d){let f=yd(h,i);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(n){return n===this.hostEl?this.shadowRoot:n}appendChild(n,t){return super.appendChild(this.nodeOrShadowRoot(n),t)}insertBefore(n,t,r){return super.insertBefore(this.nodeOrShadowRoot(n),t,r)}removeChild(n,t){return super.removeChild(null,t)}parentNode(n){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(n)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Ro=class extends No{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(n,t,r,o,i,s,a,c,u){super(n,i,s,a,c),this.sharedStylesHost=t,this.removeStylesOnCompDestroy=o;let l=r.styles;this.styles=u?Jm(u,l):l,this.styleUrls=r.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},sa=class extends Ro{contentAttr;hostAttr;constructor(n,t,r,o,i,s,a,c,u){let l=o+"-"+r.id;super(n,t,r,i,s,a,c,u,l),this.contentAttr=J_(l),this.hostAttr=X_(l)}applyToHost(n){this.applyStyles(),this.setAttribute(n,this.hostAttr,"")}createElement(n,t){let r=super.createElement(n,t);return super.setAttribute(r,this.contentAttr,""),r}};var ca=class e extends Mo{supportsDOMEvents=!0;static makeCurrent(){hd(new e)}onAndCancel(n,t,r,o){return n.addEventListener(t,r,o),()=>{n.removeEventListener(t,r,o)}}dispatchEvent(n,t){n.dispatchEvent(t)}remove(n){n.remove()}createElement(n,t){return t=t||this.getDefaultDocument(),t.createElement(n)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(n){return n.nodeType===Node.ELEMENT_NODE}isShadowRoot(n){return n instanceof DocumentFragment}getGlobalEventTarget(n,t){return t==="window"?window:t==="document"?n:t==="body"?n.body:null}getBaseHref(n){let t=eb();return t==null?null:tb(t)}resetBaseElement(){xo=null}getUserAgent(){return window.navigator.userAgent}getCookie(n){return gd(document.cookie,n)}},xo=null;function eb(){return xo=xo||document.head.querySelector("base"),xo?xo.getAttribute("href"):null}function tb(e){return new URL(e,document.baseURI).pathname}var nb=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),ev=(()=>{class e extends Ao{constructor(t){super(t)}supports(t){return!0}addEventListener(t,r,o,i){return t.addEventListener(r,o,i),()=>this.removeEventListener(t,r,o,i)}removeEventListener(t,r,o,i){return t.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(I(ee))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Xm=["alt","control","meta","shift"],rb={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},ob={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},tv=(()=>{class e extends Ao{constructor(t){super(t)}supports(t){return e.parseEventName(t)!=null}addEventListener(t,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Be().onAndCancel(t,s.domEventName,a,i))}static parseEventName(t){let r=t.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),Xm.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),s+=u+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(t,r){let o=rb[t.key]||t.key,i="";return r.indexOf("code.")>-1&&(o=t.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),Xm.forEach(s=>{if(s!==o){let a=ob[s];a(t)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(t,r,o){return i=>{e.matchEventFullKeyCode(i,t)&&o.runGuarded(()=>r(i))}}static _normalizeKey(t){return t==="esc"?"escape":t}static \u0275fac=function(r){return new(r||e)(I(ee))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})();function ib(e,n){let t=g({rootComponent:e},sb(n));return Om(t)}function sb(e){return{appProviders:[...db,...e?.providers??[]],platformProviders:lb}}function ab(){ca.makeCurrent()}function cb(){return new Ue}function ub(){return yl(document),document}var lb=[{provide:yr,useValue:Wm},{provide:Ls,useValue:ab,multi:!0},{provide:ee,useFactory:ub}];var db=[{provide:io,useValue:"root"},{provide:Ue,useFactory:cb},{provide:aa,useClass:ev,multi:!0,deps:[ee]},{provide:aa,useClass:tv,multi:!0,deps:[ee]},wd,Ed,Cd,{provide:wn,useExisting:wd},{provide:To,useClass:nb},[]];var nv=(()=>{class e{_doc;constructor(t){this._doc=t}getTitle(){return this._doc.title}setTitle(t){this._doc.title=t||""}static \u0275fac=function(r){return new(r||e)(I(ee))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var M="primary",qo=Symbol("RouteTitle"),Td=class{params;constructor(n){this.params=n||{}}has(n){return Object.prototype.hasOwnProperty.call(this.params,n)}get(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t[0]:t}return null}getAll(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t:[t]}return[]}get keys(){return Object.keys(this.params)}};function Rn(e){return new Td(e)}function lv(e,n,t){let r=t.path.split("/");if(r.length>e.length||t.pathMatch==="full"&&(n.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function hb(e,n){if(e.length!==n.length)return!1;for(let t=0;t<e.length;++t)if(!ft(e[t],n[t]))return!1;return!0}function ft(e,n){let t=e?Ad(e):void 0,r=n?Ad(n):void 0;if(!t||!r||t.length!=r.length)return!1;let o;for(let i=0;i<t.length;i++)if(o=t[i],!dv(e[o],n[o]))return!1;return!0}function Ad(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function dv(e,n){if(Array.isArray(e)&&Array.isArray(n)){if(e.length!==n.length)return!1;let t=[...e].sort(),r=[...n].sort();return t.every((o,i)=>r[i]===o)}else return e===n}function fv(e){return e.length>0?e[e.length-1]:null}function Nt(e){return Ic(e)?e:Qt(e)?W(Promise.resolve(e)):_(e)}var pb={exact:pv,subset:gv},hv={exact:gb,subset:mb,ignored:()=>!0};function rv(e,n,t){return pb[t.paths](e.root,n.root,t.matrixParams)&&hv[t.queryParams](e.queryParams,n.queryParams)&&!(t.fragment==="exact"&&e.fragment!==n.fragment)}function gb(e,n){return ft(e,n)}function pv(e,n,t){if(!An(e.segments,n.segments)||!da(e.segments,n.segments,t)||e.numberOfChildren!==n.numberOfChildren)return!1;for(let r in n.children)if(!e.children[r]||!pv(e.children[r],n.children[r],t))return!1;return!0}function mb(e,n){return Object.keys(n).length<=Object.keys(e).length&&Object.keys(n).every(t=>dv(e[t],n[t]))}function gv(e,n,t){return mv(e,n,n.segments,t)}function mv(e,n,t,r){if(e.segments.length>t.length){let o=e.segments.slice(0,t.length);return!(!An(o,t)||n.hasChildren()||!da(o,t,r))}else if(e.segments.length===t.length){if(!An(e.segments,t)||!da(e.segments,t,r))return!1;for(let o in n.children)if(!e.children[o]||!gv(e.children[o],n.children[o],r))return!1;return!0}else{let o=t.slice(0,e.segments.length),i=t.slice(e.segments.length);return!An(e.segments,o)||!da(e.segments,o,r)||!e.children[M]?!1:mv(e.children[M],n,i,r)}}function da(e,n,t){return n.every((r,o)=>hv[t](e[o].parameters,r.parameters))}var pt=class{root;queryParams;fragment;_queryParamMap;constructor(n=new j([],{}),t={},r=null){this.root=n,this.queryParams=t,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Rn(this.queryParams),this._queryParamMap}toString(){return Db.serialize(this)}},j=class{segments;children;parent=null;constructor(n,t){this.segments=n,this.children=t,Object.values(t).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return fa(this)}},Jt=class{path;parameters;_parameterMap;constructor(n,t){this.path=n,this.parameters=t}get parameterMap(){return this._parameterMap??=Rn(this.parameters),this._parameterMap}toString(){return yv(this)}};function vb(e,n){return An(e,n)&&e.every((t,r)=>ft(t.parameters,n[r].parameters))}function An(e,n){return e.length!==n.length?!1:e.every((t,r)=>t.path===n[r].path)}function yb(e,n){let t=[];return Object.entries(e.children).forEach(([r,o])=>{r===M&&(t=t.concat(n(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==M&&(t=t.concat(n(o,r)))}),t}var On=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>new xn,providedIn:"root"})}return e})(),xn=class{parse(n){let t=new Rd(n);return new pt(t.parseRootSegment(),t.parseQueryParams(),t.parseFragment())}serialize(n){let t=`/${Oo(n.root,!0)}`,r=Ib(n.queryParams),o=typeof n.fragment=="string"?`#${Cb(n.fragment)}`:"";return`${t}${r}${o}`}},Db=new xn;function fa(e){return e.segments.map(n=>yv(n)).join("/")}function Oo(e,n){if(!e.hasChildren())return fa(e);if(n){let t=e.children[M]?Oo(e.children[M],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==M&&r.push(`${o}:${Oo(i,!1)}`)}),r.length>0?`${t}(${r.join("//")})`:t}else{let t=yb(e,(r,o)=>o===M?[Oo(e.children[M],!1)]:[`${o}:${Oo(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[M]!=null?`${fa(e)}/${t[0]}`:`${fa(e)}/(${t.join("//")})`}}function vv(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function ua(e){return vv(e).replace(/%3B/gi,";")}function Cb(e){return encodeURI(e)}function Nd(e){return vv(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function ha(e){return decodeURIComponent(e)}function ov(e){return ha(e.replace(/\+/g,"%20"))}function yv(e){return`${Nd(e.path)}${Eb(e.parameters)}`}function Eb(e){return Object.entries(e).map(([n,t])=>`;${Nd(n)}=${Nd(t)}`).join("")}function Ib(e){let n=Object.entries(e).map(([t,r])=>Array.isArray(r)?r.map(o=>`${ua(t)}=${ua(o)}`).join("&"):`${ua(t)}=${ua(r)}`).filter(t=>t);return n.length?`?${n.join("&")}`:""}var wb=/^[^\/()?;#]+/;function _d(e){let n=e.match(wb);return n?n[0]:""}var _b=/^[^\/()?;=#]+/;function bb(e){let n=e.match(_b);return n?n[0]:""}var Sb=/^[^=?&#]+/;function Mb(e){let n=e.match(Sb);return n?n[0]:""}var Tb=/^[^&#]+/;function Ab(e){let n=e.match(Tb);return n?n[0]:""}var Rd=class{url;remaining;constructor(n){this.url=n,this.remaining=n}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new j([],{}):new j([],this.parseChildren())}parseQueryParams(){let n={};if(this.consumeOptional("?"))do this.parseQueryParam(n);while(this.consumeOptional("&"));return n}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let n=[];for(this.peekStartsWith("(")||n.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),n.push(this.parseSegment());let t={};this.peekStartsWith("/(")&&(this.capture("/"),t=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(n.length>0||Object.keys(t).length>0)&&(r[M]=new j(n,t)),r}parseSegment(){let n=_d(this.remaining);if(n===""&&this.peekStartsWith(";"))throw new y(4009,!1);return this.capture(n),new Jt(ha(n),this.parseMatrixParams())}parseMatrixParams(){let n={};for(;this.consumeOptional(";");)this.parseParam(n);return n}parseParam(n){let t=bb(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let o=_d(this.remaining);o&&(r=o,this.capture(r))}n[ha(t)]=ha(r)}parseQueryParam(n){let t=Mb(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let s=Ab(this.remaining);s&&(r=s,this.capture(r))}let o=ov(t),i=ov(r);if(n.hasOwnProperty(o)){let s=n[o];Array.isArray(s)||(s=[s],n[o]=s),s.push(i)}else n[o]=i}parseParens(n){let t={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=_d(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new y(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):n&&(i=M);let s=this.parseChildren();t[i]=Object.keys(s).length===1?s[M]:new j([],s),this.consumeOptional("//")}return t}peekStartsWith(n){return this.remaining.startsWith(n)}consumeOptional(n){return this.peekStartsWith(n)?(this.remaining=this.remaining.substring(n.length),!0):!1}capture(n){if(!this.consumeOptional(n))throw new y(4011,!1)}};function Dv(e){return e.segments.length>0?new j([],{[M]:e}):e}function Cv(e){let n={};for(let[r,o]of Object.entries(e.children)){let i=Cv(o);if(r===M&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))n[s]=a;else(i.segments.length>0||i.hasChildren())&&(n[r]=i)}let t=new j(e.segments,n);return Nb(t)}function Nb(e){if(e.numberOfChildren===1&&e.children[M]){let n=e.children[M];return new j(e.segments.concat(n.segments),n.children)}return e}function Xt(e){return e instanceof pt}function Ev(e,n,t=null,r=null){let o=Iv(e);return wv(o,n,t,r)}function Iv(e){let n;function t(i){let s={};for(let c of i.children){let u=t(c);s[c.outlet]=u}let a=new j(i.url,s);return i===e&&(n=a),a}let r=t(e.root),o=Dv(r);return n??o}function wv(e,n,t,r){let o=e;for(;o.parent;)o=o.parent;if(n.length===0)return bd(o,o,o,t,r);let i=Rb(n);if(i.toRoot())return bd(o,o,new j([],{}),t,r);let s=xb(i,o,e),a=s.processChildren?Fo(s.segmentGroup,s.index,i.commands):bv(s.segmentGroup,s.index,i.commands);return bd(o,s.segmentGroup,a,t,r)}function pa(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function Vo(e){return typeof e=="object"&&e!=null&&e.outlets}function bd(e,n,t,r,o){let i={};r&&Object.entries(r).forEach(([c,u])=>{i[c]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let s;e===n?s=t:s=_v(e,n,t);let a=Dv(Cv(s));return new pt(a,i,o)}function _v(e,n,t){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===n?r[o]=t:r[o]=_v(i,n,t)}),new j(e.segments,r)}var ga=class{isAbsolute;numberOfDoubleDots;commands;constructor(n,t,r){if(this.isAbsolute=n,this.numberOfDoubleDots=t,this.commands=r,n&&r.length>0&&pa(r[0]))throw new y(4003,!1);let o=r.find(Vo);if(o&&o!==fv(r))throw new y(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function Rb(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new ga(!0,0,e);let n=0,t=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,u])=>{a[c]=typeof u=="string"?u.split("/"):u}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?t=!0:a===".."?n++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new ga(t,n,r)}var Sr=class{segmentGroup;processChildren;index;constructor(n,t,r){this.segmentGroup=n,this.processChildren=t,this.index=r}};function xb(e,n,t){if(e.isAbsolute)return new Sr(n,!0,0);if(!t)return new Sr(n,!1,NaN);if(t.parent===null)return new Sr(t,!0,0);let r=pa(e.commands[0])?0:1,o=t.segments.length-1+r;return Ob(t,o,e.numberOfDoubleDots)}function Ob(e,n,t){let r=e,o=n,i=t;for(;i>o;){if(i-=o,r=r.parent,!r)throw new y(4005,!1);o=r.segments.length}return new Sr(r,!1,o-i)}function kb(e){return Vo(e[0])?e[0].outlets:{[M]:e}}function bv(e,n,t){if(e??=new j([],{}),e.segments.length===0&&e.hasChildren())return Fo(e,n,t);let r=Fb(e,n,t),o=t.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new j(e.segments.slice(0,r.pathIndex),{});return i.children[M]=new j(e.segments.slice(r.pathIndex),e.children),Fo(i,0,o)}else return r.match&&o.length===0?new j(e.segments,{}):r.match&&!e.hasChildren()?xd(e,n,t):r.match?Fo(e,0,o):xd(e,n,t)}function Fo(e,n,t){if(t.length===0)return new j(e.segments,{});{let r=kb(t),o={};if(Object.keys(r).some(i=>i!==M)&&e.children[M]&&e.numberOfChildren===1&&e.children[M].segments.length===0){let i=Fo(e.children[M],n,t);return new j(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=bv(e.children[i],n,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new j(e.segments,o)}}function Fb(e,n,t){let r=0,o=n,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=t.length)return i;let s=e.segments[o],a=t[r];if(Vo(a))break;let c=`${a}`,u=r<t.length-1?t[r+1]:null;if(o>0&&c===void 0)break;if(c&&u&&typeof u=="object"&&u.outlets===void 0){if(!sv(c,u,s))return i;r+=2}else{if(!sv(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function xd(e,n,t){let r=e.segments.slice(0,n),o=0;for(;o<t.length;){let i=t[o];if(Vo(i)){let c=Pb(i.outlets);return new j(r,c)}if(o===0&&pa(t[0])){let c=e.segments[n];r.push(new Jt(c.path,iv(t[0]))),o++;continue}let s=Vo(i)?i.outlets[M]:`${i}`,a=o<t.length-1?t[o+1]:null;s&&a&&pa(a)?(r.push(new Jt(s,iv(a))),o+=2):(r.push(new Jt(s,{})),o++)}return new j(r,{})}function Pb(e){let n={};return Object.entries(e).forEach(([t,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(n[t]=xd(new j([],{}),0,r))}),n}function iv(e){let n={};return Object.entries(e).forEach(([t,r])=>n[t]=`${r}`),n}function sv(e,n,t){return e==t.path&&ft(n,t.parameters)}var Po="imperative",ie=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(ie||{}),xe=class{id;url;constructor(n,t){this.id=n,this.url=t}},Tt=class extends xe{type=ie.NavigationStart;navigationTrigger;restoredState;constructor(n,t,r="imperative",o=null){super(n,t),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},Xe=class extends xe{urlAfterRedirects;type=ie.NavigationEnd;constructor(n,t,r){super(n,t),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},De=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e[e.Aborted=4]="Aborted",e}(De||{}),jo=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(jo||{}),ht=class extends xe{reason;code;type=ie.NavigationCancel;constructor(n,t,r,o){super(n,t),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},At=class extends xe{reason;code;type=ie.NavigationSkipped;constructor(n,t,r,o){super(n,t),this.reason=r,this.code=o}},Tr=class extends xe{error;target;type=ie.NavigationError;constructor(n,t,r,o){super(n,t),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},Bo=class extends xe{urlAfterRedirects;state;type=ie.RoutesRecognized;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ma=class extends xe{urlAfterRedirects;state;type=ie.GuardsCheckStart;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},va=class extends xe{urlAfterRedirects;state;shouldActivate;type=ie.GuardsCheckEnd;constructor(n,t,r,o,i){super(n,t),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},ya=class extends xe{urlAfterRedirects;state;type=ie.ResolveStart;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Da=class extends xe{urlAfterRedirects;state;type=ie.ResolveEnd;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ca=class{route;type=ie.RouteConfigLoadStart;constructor(n){this.route=n}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Ea=class{route;type=ie.RouteConfigLoadEnd;constructor(n){this.route=n}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},Ia=class{snapshot;type=ie.ChildActivationStart;constructor(n){this.snapshot=n}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},wa=class{snapshot;type=ie.ChildActivationEnd;constructor(n){this.snapshot=n}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},_a=class{snapshot;type=ie.ActivationStart;constructor(n){this.snapshot=n}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ba=class{snapshot;type=ie.ActivationEnd;constructor(n){this.snapshot=n}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}};var Ho=class{},Ar=class{url;navigationBehaviorOptions;constructor(n,t){this.url=n,this.navigationBehaviorOptions=t}};function Lb(e){return!(e instanceof Ho)&&!(e instanceof Ar)}function Vb(e,n){return e.providers&&!e._injector&&(e._injector=Cr(e.providers,n,`Route: ${e.path}`)),e._injector??n}function Je(e){return e.outlet||M}function jb(e,n){let t=e.filter(r=>Je(r)===n);return t.push(...e.filter(r=>Je(r)!==n)),t}function xr(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let n=e.parent;n;n=n.parent){let t=n.routeConfig;if(t?._loadedInjector)return t._loadedInjector;if(t?._injector)return t._injector}return null}var Sa=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return xr(this.route?.snapshot)??this.rootInjector}constructor(n){this.rootInjector=n,this.children=new Rt(this.rootInjector)}},Rt=(()=>{class e{rootInjector;contexts=new Map;constructor(t){this.rootInjector=t}onChildOutletCreated(t,r){let o=this.getOrCreateContext(t);o.outlet=r,this.contexts.set(t,o)}onChildOutletDestroyed(t){let r=this.getContext(t);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let t=this.contexts;return this.contexts=new Map,t}onOutletReAttached(t){this.contexts=t}getOrCreateContext(t){let r=this.getContext(t);return r||(r=new Sa(this.rootInjector),this.contexts.set(t,r)),r}getContext(t){return this.contexts.get(t)||null}static \u0275fac=function(r){return new(r||e)(I(Z))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Ma=class{_root;constructor(n){this._root=n}get root(){return this._root.value}parent(n){let t=this.pathFromRoot(n);return t.length>1?t[t.length-2]:null}children(n){let t=Od(n,this._root);return t?t.children.map(r=>r.value):[]}firstChild(n){let t=Od(n,this._root);return t&&t.children.length>0?t.children[0].value:null}siblings(n){let t=kd(n,this._root);return t.length<2?[]:t[t.length-2].children.map(o=>o.value).filter(o=>o!==n)}pathFromRoot(n){return kd(n,this._root).map(t=>t.value)}};function Od(e,n){if(e===n.value)return n;for(let t of n.children){let r=Od(e,t);if(r)return r}return null}function kd(e,n){if(e===n.value)return[n];for(let t of n.children){let r=kd(e,t);if(r.length)return r.unshift(n),r}return[]}var Re=class{value;children;constructor(n,t){this.value=n,this.children=t}toString(){return`TreeNode(${this.value})`}};function br(e){let n={};return e&&e.children.forEach(t=>n[t.value.outlet]=t),n}var Uo=class extends Ma{snapshot;constructor(n,t){super(n),this.snapshot=t,Ud(this,n)}toString(){return this.snapshot.toString()}};function Sv(e){let n=Bb(e),t=new J([new Jt("",{})]),r=new J({}),o=new J({}),i=new J({}),s=new J(""),a=new _e(t,r,i,s,o,M,e,n.root);return a.snapshot=n.root,new Uo(new Re(a,[]),n)}function Bb(e){let n={},t={},r={},o="",i=new Nn([],n,r,o,t,M,e,null,{});return new $o("",new Re(i,[]))}var _e=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(n,t,r,o,i,s,a,c){this.urlSubject=n,this.paramsSubject=t,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(P(u=>u[qo]))??_(void 0),this.url=n,this.params=t,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(P(n=>Rn(n))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(P(n=>Rn(n))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function Ta(e,n,t="emptyOnly"){let r,{routeConfig:o}=e;return n!==null&&(t==="always"||o?.path===""||!n.component&&!n.routeConfig?.loadComponent)?r={params:g(g({},n.params),e.params),data:g(g({},n.data),e.data),resolve:g(g(g(g({},e.data),n.data),o?.data),e._resolvedData)}:r={params:g({},e.params),data:g({},e.data),resolve:g(g({},e.data),e._resolvedData??{})},o&&Tv(o)&&(r.resolve[qo]=o.title),r}var Nn=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[qo]}constructor(n,t,r,o,i,s,a,c,u){this.url=n,this.params=t,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Rn(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Rn(this.queryParams),this._queryParamMap}toString(){let n=this.url.map(r=>r.toString()).join("/"),t=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${n}', path:'${t}')`}},$o=class extends Ma{url;constructor(n,t){super(t),this.url=n,Ud(this,t)}toString(){return Mv(this._root)}};function Ud(e,n){n.value._routerState=e,n.children.forEach(t=>Ud(e,t))}function Mv(e){let n=e.children.length>0?` { ${e.children.map(Mv).join(", ")} } `:"";return`${e.value}${n}`}function Sd(e){if(e.snapshot){let n=e.snapshot,t=e._futureSnapshot;e.snapshot=t,ft(n.queryParams,t.queryParams)||e.queryParamsSubject.next(t.queryParams),n.fragment!==t.fragment&&e.fragmentSubject.next(t.fragment),ft(n.params,t.params)||e.paramsSubject.next(t.params),hb(n.url,t.url)||e.urlSubject.next(t.url),ft(n.data,t.data)||e.dataSubject.next(t.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Fd(e,n){let t=ft(e.params,n.params)&&vb(e.url,n.url),r=!e.parent!=!n.parent;return t&&!r&&(!e.parent||Fd(e.parent,n.parent))}function Tv(e){return typeof e.title=="string"||e.title===null}var Av=new C(""),$d=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=M;activateEvents=new z;deactivateEvents=new z;attachEvents=new z;detachEvents=new z;routerOutletData=xm(void 0);parentContexts=p(Rt);location=p(Ve);changeDetector=p(Ke);inputBinder=p(xa,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(t){if(t.name){let{firstChange:r,previousValue:o}=t.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(t){return this.parentContexts.getContext(t)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let t=this.parentContexts.getContext(this.name);t?.route&&(t.attachRef?this.attach(t.attachRef,t.route):this.activateWith(t.route,t.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new y(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new y(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new y(4012,!1);this.location.detach();let t=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(t.instance),t}attach(t,r){this.activated=t,this._activatedRoute=r,this.location.insert(t.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(t.instance)}deactivate(){if(this.activated){let t=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new y(4013,!1);this._activatedRoute=t;let o=this.location,s=t.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new Pd(t,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=L({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[we]})}return e})(),Pd=class{route;childContexts;parent;outletData;constructor(n,t,r,o){this.route=n,this.childContexts=t,this.parent=r,this.outletData=o}get(n,t){return n===_e?this.route:n===Rt?this.childContexts:n===Av?this.outletData:this.parent.get(n,t)}},xa=new C("");var zd=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=Ul({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&Js(0,"router-outlet")},dependencies:[$d],encapsulation:2})}return e})();function Gd(e){let n=e.children&&e.children.map(Gd),t=n?A(g({},e),{children:n}):g({},e);return!t.component&&!t.loadComponent&&(n||t.loadChildren)&&t.outlet&&t.outlet!==M&&(t.component=zd),t}function Hb(e,n,t){let r=zo(e,n._root,t?t._root:void 0);return new Uo(r,n)}function zo(e,n,t){if(t&&e.shouldReuseRoute(n.value,t.value.snapshot)){let r=t.value;r._futureSnapshot=n.value;let o=Ub(e,n,t);return new Re(r,o)}else{if(e.shouldAttach(n.value)){let i=e.retrieve(n.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=n.value,s.children=n.children.map(a=>zo(e,a)),s}}let r=$b(n.value),o=n.children.map(i=>zo(e,i));return new Re(r,o)}}function Ub(e,n,t){return n.children.map(r=>{for(let o of t.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return zo(e,r,o);return zo(e,r)})}function $b(e){return new _e(new J(e.url),new J(e.params),new J(e.queryParams),new J(e.fragment),new J(e.data),e.outlet,e.component,e)}var Nr=class{redirectTo;navigationBehaviorOptions;constructor(n,t){this.redirectTo=n,this.navigationBehaviorOptions=t}},Nv="ngNavigationCancelingError";function Aa(e,n){let{redirectTo:t,navigationBehaviorOptions:r}=Xt(n)?{redirectTo:n,navigationBehaviorOptions:void 0}:n,o=Rv(!1,De.Redirect);return o.url=t,o.navigationBehaviorOptions=r,o}function Rv(e,n){let t=new Error(`NavigationCancelingError: ${e||""}`);return t[Nv]=!0,t.cancellationCode=n,t}function zb(e){return xv(e)&&Xt(e.url)}function xv(e){return!!e&&e[Nv]}var Gb=(e,n,t,r)=>P(o=>(new Ld(n,o.targetRouterState,o.currentRouterState,t,r).activate(e),o)),Ld=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(n,t,r,o,i){this.routeReuseStrategy=n,this.futureState=t,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(n){let t=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(t,r,n),Sd(this.futureState.root),this.activateChildRoutes(t,r,n)}deactivateChildRoutes(n,t,r){let o=br(t);n.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(n,t,r){let o=n.value,i=t?t.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(n,t,s.children)}else this.deactivateChildRoutes(n,t,r);else i&&this.deactivateRouteAndItsChildren(t,r)}deactivateRouteAndItsChildren(n,t){n.value.component&&this.routeReuseStrategy.shouldDetach(n.value.snapshot)?this.detachAndStoreRouteSubtree(n,t):this.deactivateRouteAndOutlet(n,t)}detachAndStoreRouteSubtree(n,t){let r=t.getContext(n.value.outlet),o=r&&n.value.component?r.children:t,i=br(n);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(n.value.snapshot,{componentRef:s,route:n,contexts:a})}}deactivateRouteAndOutlet(n,t){let r=t.getContext(n.value.outlet),o=r&&n.value.component?r.children:t,i=br(n);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(n,t,r){let o=br(t);n.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new ba(i.value.snapshot))}),n.children.length&&this.forwardEvent(new wa(n.value.snapshot))}activateRoutes(n,t,r){let o=n.value,i=t?t.value:null;if(Sd(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(n,t,s.children)}else this.activateChildRoutes(n,t,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),Sd(a.route.value),this.activateChildRoutes(n,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(n,null,s.children)}else this.activateChildRoutes(n,null,r)}},Na=class{path;route;constructor(n){this.path=n,this.route=this.path[this.path.length-1]}},Mr=class{component;route;constructor(n,t){this.component=n,this.route=t}};function Wb(e,n,t){let r=e._root,o=n?n._root:null;return ko(r,o,t,[r.value])}function qb(e){let n=e.routeConfig?e.routeConfig.canActivateChild:null;return!n||n.length===0?null:{node:e,guards:n}}function Or(e,n){let t=Symbol(),r=n.get(e,t);return r===t?typeof e=="function"&&!$c(e)?e:n.get(e):r}function ko(e,n,t,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=br(n);return e.children.forEach(s=>{Zb(s,i[s.value.outlet],t,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>Lo(a,t.getContext(s),o)),o}function Zb(e,n,t,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=n?n.value:null,a=t?t.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=Yb(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new Na(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?ko(e,n,a?a.children:null,r,o):ko(e,n,t,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new Mr(a.outlet.component,s))}else s&&Lo(n,a,o),o.canActivateChecks.push(new Na(r)),i.component?ko(e,null,a?a.children:null,r,o):ko(e,null,t,r,o);return o}function Yb(e,n,t){if(typeof t=="function")return t(e,n);switch(t){case"pathParamsChange":return!An(e.url,n.url);case"pathParamsOrQueryParamsChange":return!An(e.url,n.url)||!ft(e.queryParams,n.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Fd(e,n)||!ft(e.queryParams,n.queryParams);case"paramsChange":default:return!Fd(e,n)}}function Lo(e,n,t){let r=br(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?n?Lo(s,n.children.getContext(i),t):Lo(s,null,t):Lo(s,n,t)}),o.component?n&&n.outlet&&n.outlet.isActivated?t.canDeactivateChecks.push(new Mr(n.outlet.component,o)):t.canDeactivateChecks.push(new Mr(null,o)):t.canDeactivateChecks.push(new Mr(null,o))}function Zo(e){return typeof e=="function"}function Qb(e){return typeof e=="boolean"}function Kb(e){return e&&Zo(e.canLoad)}function Jb(e){return e&&Zo(e.canActivate)}function Xb(e){return e&&Zo(e.canActivateChild)}function eS(e){return e&&Zo(e.canDeactivate)}function tS(e){return e&&Zo(e.canMatch)}function Ov(e){return e instanceof gt||e?.name==="EmptyError"}var la=Symbol("INITIAL_VALUE");function Rr(){return le(e=>Kn(e.map(n=>n.pipe(mt(1),Mc(la)))).pipe(P(n=>{for(let t of n)if(t!==!0){if(t===la)return la;if(t===!1||nS(t))return t}return!0}),ae(n=>n!==la),mt(1)))}function nS(e){return Xt(e)||e instanceof Nr}function rS(e,n){return q(t=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=t;return s.length===0&&i.length===0?_(A(g({},t),{guardsResult:!0})):oS(s,r,o,e).pipe(q(a=>a&&Qb(a)?iS(r,i,e,n):_(a)),P(a=>A(g({},t),{guardsResult:a})))})}function oS(e,n,t,r){return W(e).pipe(q(o=>lS(o.component,o.route,t,n,r)),vt(o=>o!==!0,!0))}function iS(e,n,t,r){return W(n).pipe(Ft(o=>Xn(aS(o.route.parent,r),sS(o.route,r),uS(e,o.path,t),cS(e,o.route,t))),vt(o=>o!==!0,!0))}function sS(e,n){return e!==null&&n&&n(new _a(e)),_(!0)}function aS(e,n){return e!==null&&n&&n(new Ia(e)),_(!0)}function cS(e,n,t){let r=n.routeConfig?n.routeConfig.canActivate:null;if(!r||r.length===0)return _(!0);let o=r.map(i=>Zr(()=>{let s=xr(n)??t,a=Or(i,s),c=Jb(a)?a.canActivate(n,e):ge(s,()=>a(n,e));return Nt(c).pipe(vt())}));return _(o).pipe(Rr())}function uS(e,n,t){let r=n[n.length-1],i=n.slice(0,n.length-1).reverse().map(s=>qb(s)).filter(s=>s!==null).map(s=>Zr(()=>{let a=s.guards.map(c=>{let u=xr(s.node)??t,l=Or(c,u),d=Xb(l)?l.canActivateChild(r,e):ge(u,()=>l(r,e));return Nt(d).pipe(vt())});return _(a).pipe(Rr())}));return _(i).pipe(Rr())}function lS(e,n,t,r,o){let i=n&&n.routeConfig?n.routeConfig.canDeactivate:null;if(!i||i.length===0)return _(!0);let s=i.map(a=>{let c=xr(n)??o,u=Or(a,c),l=eS(u)?u.canDeactivate(e,n,t,r):ge(c,()=>u(e,n,t,r));return Nt(l).pipe(vt())});return _(s).pipe(Rr())}function dS(e,n,t,r){let o=n.canLoad;if(o===void 0||o.length===0)return _(!0);let i=o.map(s=>{let a=Or(s,e),c=Kb(a)?a.canLoad(n,t):ge(e,()=>a(n,t));return Nt(c)});return _(i).pipe(Rr(),kv(r))}function kv(e){return yc(te(n=>{if(typeof n!="boolean")throw Aa(e,n)}),P(n=>n===!0))}function fS(e,n,t,r){let o=n.canMatch;if(!o||o.length===0)return _(!0);let i=o.map(s=>{let a=Or(s,e),c=tS(a)?a.canMatch(n,t):ge(e,()=>a(n,t));return Nt(c)});return _(i).pipe(Rr(),kv(r))}var Go=class{segmentGroup;constructor(n){this.segmentGroup=n||null}},Wo=class extends Error{urlTree;constructor(n){super(),this.urlTree=n}};function _r(e){return Yn(new Go(e))}function hS(e){return Yn(new y(4e3,!1))}function pS(e){return Yn(Rv(!1,De.GuardRejected))}var Vd=class{urlSerializer;urlTree;constructor(n,t){this.urlSerializer=n,this.urlTree=t}lineralizeSegments(n,t){let r=[],o=t.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return _(r);if(o.numberOfChildren>1||!o.children[M])return hS(`${n.redirectTo}`);o=o.children[M]}}applyRedirectCommands(n,t,r,o,i){return gS(t,o,i).pipe(P(s=>{if(s instanceof pt)throw new Wo(s);let a=this.applyRedirectCreateUrlTree(s,this.urlSerializer.parse(s),n,r);if(s[0]==="/")throw new Wo(a);return a}))}applyRedirectCreateUrlTree(n,t,r,o){let i=this.createSegmentGroup(n,t.root,r,o);return new pt(i,this.createQueryParams(t.queryParams,this.urlTree.queryParams),t.fragment)}createQueryParams(n,t){let r={};return Object.entries(n).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=t[a]}else r[o]=i}),r}createSegmentGroup(n,t,r,o){let i=this.createSegments(n,t.segments,r,o),s={};return Object.entries(t.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(n,c,r,o)}),new j(i,s)}createSegments(n,t,r,o){return t.map(i=>i.path[0]===":"?this.findPosParam(n,i,o):this.findOrReturn(i,r))}findPosParam(n,t,r){let o=r[t.path.substring(1)];if(!o)throw new y(4001,!1);return o}findOrReturn(n,t){let r=0;for(let o of t){if(o.path===n.path)return t.splice(r),o;r++}return n}};function gS(e,n,t){if(typeof e=="string")return _(e);let r=e,{queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,params:u,data:l,title:d}=n;return Nt(ge(t,()=>r({params:u,data:l,queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,title:d})))}var jd={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function mS(e,n,t,r,o){let i=Fv(e,n,t);return i.matched?(r=Vb(n,r),fS(r,n,t,o).pipe(P(s=>s===!0?i:g({},jd)))):_(i)}function Fv(e,n,t){if(n.path==="**")return vS(t);if(n.path==="")return n.pathMatch==="full"&&(e.hasChildren()||t.length>0)?g({},jd):{matched:!0,consumedSegments:[],remainingSegments:t,parameters:{},positionalParamSegments:{}};let o=(n.matcher||lv)(t,e,n);if(!o)return g({},jd);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?g(g({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:t.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function vS(e){return{matched:!0,parameters:e.length>0?fv(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function av(e,n,t,r){return t.length>0&&CS(e,t,r)?{segmentGroup:new j(n,DS(r,new j(t,e.children))),slicedSegments:[]}:t.length===0&&ES(e,t,r)?{segmentGroup:new j(e.segments,yS(e,t,r,e.children)),slicedSegments:t}:{segmentGroup:new j(e.segments,e.children),slicedSegments:t}}function yS(e,n,t,r){let o={};for(let i of t)if(Oa(e,n,i)&&!r[Je(i)]){let s=new j([],{});o[Je(i)]=s}return g(g({},r),o)}function DS(e,n){let t={};t[M]=n;for(let r of e)if(r.path===""&&Je(r)!==M){let o=new j([],{});t[Je(r)]=o}return t}function CS(e,n,t){return t.some(r=>Oa(e,n,r)&&Je(r)!==M)}function ES(e,n,t){return t.some(r=>Oa(e,n,r))}function Oa(e,n,t){return(e.hasChildren()||n.length>0)&&t.pathMatch==="full"?!1:t.path===""}function IS(e,n,t){return n.length===0&&!e.children[t]}var Bd=class{};function wS(e,n,t,r,o,i,s="emptyOnly"){return new Hd(e,n,t,r,o,s,i).recognize()}var _S=31,Hd=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(n,t,r,o,i,s,a){this.injector=n,this.configLoader=t,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Vd(this.urlSerializer,this.urlTree)}noMatchError(n){return new y(4002,`'${n.segmentGroup}'`)}recognize(){let n=av(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(n).pipe(P(({children:t,rootSnapshot:r})=>{let o=new Re(r,t),i=new $o("",o),s=Ev(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(n){let t=new Nn([],Object.freeze({}),Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),M,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,n,M,t).pipe(P(r=>({children:r,rootSnapshot:t})),tt(r=>{if(r instanceof Wo)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Go?this.noMatchError(r):r}))}processSegmentGroup(n,t,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(n,t,r,i):this.processSegment(n,t,r,r.segments,o,!0,i).pipe(P(s=>s instanceof Re?[s]:[]))}processChildren(n,t,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return W(i).pipe(Ft(s=>{let a=r.children[s],c=jb(t,s);return this.processSegmentGroup(n,c,a,s,o)}),Sc((s,a)=>(s.push(...a),s)),Pt(null),bc(),q(s=>{if(s===null)return _r(r);let a=Pv(s);return bS(a),_(a)}))}processSegment(n,t,r,o,i,s,a){return W(t).pipe(Ft(c=>this.processSegmentAgainstRoute(c._injector??n,t,c,r,o,i,s,a).pipe(tt(u=>{if(u instanceof Go)return _(null);throw u}))),vt(c=>!!c),tt(c=>{if(Ov(c))return IS(r,o,i)?_(new Bd):_r(r);throw c}))}processSegmentAgainstRoute(n,t,r,o,i,s,a,c){return Je(r)!==s&&(s===M||!Oa(o,i,r))?_r(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(n,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(n,o,t,r,i,s,c):_r(o)}expandSegmentAgainstRouteUsingRedirect(n,t,r,o,i,s,a){let{matched:c,parameters:u,consumedSegments:l,positionalParamSegments:d,remainingSegments:h}=Fv(t,o,i);if(!c)return _r(t);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>_S&&(this.allowRedirects=!1));let f=new Nn(i,u,Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,cv(o),Je(o),o.component??o._loadedComponent??null,o,uv(o)),v=Ta(f,a,this.paramsInheritanceStrategy);return f.params=Object.freeze(v.params),f.data=Object.freeze(v.data),this.applyRedirects.applyRedirectCommands(l,o.redirectTo,d,f,n).pipe(le(T=>this.applyRedirects.lineralizeSegments(o,T)),q(T=>this.processSegment(n,r,t,T.concat(h),s,!1,a)))}matchSegmentAgainstRoute(n,t,r,o,i,s){let a=mS(t,r,o,n,this.urlSerializer);return r.path==="**"&&(t.children={}),a.pipe(le(c=>c.matched?(n=r._injector??n,this.getChildConfig(n,r,o).pipe(le(({routes:u})=>{let l=r._loadedInjector??n,{parameters:d,consumedSegments:h,remainingSegments:f}=c,v=new Nn(h,d,Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,cv(r),Je(r),r.component??r._loadedComponent??null,r,uv(r)),E=Ta(v,s,this.paramsInheritanceStrategy);v.params=Object.freeze(E.params),v.data=Object.freeze(E.data);let{segmentGroup:T,slicedSegments:R}=av(t,h,f,u);if(R.length===0&&T.hasChildren())return this.processChildren(l,u,T,v).pipe(P(tn=>new Re(v,tn)));if(u.length===0&&R.length===0)return _(new Re(v,[]));let Pn=Je(r)===i;return this.processSegment(l,u,T,R,Pn?M:i,!0,v).pipe(P(tn=>new Re(v,tn instanceof Re?[tn]:[])))}))):_r(t)))}getChildConfig(n,t,r){return t.children?_({routes:t.children,injector:n}):t.loadChildren?t._loadedRoutes!==void 0?_({routes:t._loadedRoutes,injector:t._loadedInjector}):dS(n,t,r,this.urlSerializer).pipe(q(o=>o?this.configLoader.loadChildren(n,t).pipe(te(i=>{t._loadedRoutes=i.routes,t._loadedInjector=i.injector})):pS(t))):_({routes:[],injector:n})}};function bS(e){e.sort((n,t)=>n.value.outlet===M?-1:t.value.outlet===M?1:n.value.outlet.localeCompare(t.value.outlet))}function SS(e){let n=e.value.routeConfig;return n&&n.path===""}function Pv(e){let n=[],t=new Set;for(let r of e){if(!SS(r)){n.push(r);continue}let o=n.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),t.add(o)):n.push(r)}for(let r of t){let o=Pv(r.children);n.push(new Re(r.value,o))}return n.filter(r=>!t.has(r))}function cv(e){return e.data||{}}function uv(e){return e.resolve||{}}function MS(e,n,t,r,o,i){return q(s=>wS(e,n,t,r,s.extractedUrl,o,i).pipe(P(({state:a,tree:c})=>A(g({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function TS(e,n){return q(t=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=t;if(!o.length)return _(t);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let u of Lv(c))s.add(u);let a=0;return W(s).pipe(Ft(c=>i.has(c)?AS(c,r,e,n):(c.data=Ta(c,c.parent,e).resolve,_(void 0))),te(()=>a++),er(1),q(c=>a===s.size?_(t):Ce))})}function Lv(e){let n=e.children.map(t=>Lv(t)).flat();return[e,...n]}function AS(e,n,t,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!Tv(o)&&(i[qo]=o.title),Zr(()=>(e.data=Ta(e,e.parent,t).resolve,NS(i,e,n,r).pipe(P(s=>(e._resolvedData=s,e.data=g(g({},e.data),s),null)))))}function NS(e,n,t,r){let o=Ad(e);if(o.length===0)return _({});let i={};return W(o).pipe(q(s=>RS(e[s],n,t,r).pipe(vt(),te(a=>{if(a instanceof Nr)throw Aa(new xn,a);i[s]=a}))),er(1),P(()=>i),tt(s=>Ov(s)?Ce:Yn(s)))}function RS(e,n,t,r){let o=xr(n)??r,i=Or(e,o),s=i.resolve?i.resolve(n,t):ge(o,()=>i(n,t));return Nt(s)}function Md(e){return le(n=>{let t=e(n);return t?W(t).pipe(P(()=>n)):_(n)})}var Wd=(()=>{class e{buildTitle(t){let r,o=t.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===M);return r}getResolvedTitleForRoute(t){return t.data[qo]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(Vv),providedIn:"root"})}return e})(),Vv=(()=>{class e extends Wd{title;constructor(t){super(),this.title=t}updateTitle(t){let r=this.buildTitle(t);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(I(nv))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),kr=new C("",{providedIn:"root",factory:()=>({})}),Yo=new C(""),qd=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=p(td);loadComponent(t,r){if(this.componentLoaders.get(r))return this.componentLoaders.get(r);if(r._loadedComponent)return _(r._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(r);let o=Nt(ge(t,()=>r.loadComponent())).pipe(P(Bv),te(s=>{this.onLoadEndListener&&this.onLoadEndListener(r),r._loadedComponent=s}),Qr(()=>{this.componentLoaders.delete(r)})),i=new qn(o,()=>new U).pipe(Wn());return this.componentLoaders.set(r,i),i}loadChildren(t,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return _({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=jv(r,this.compiler,t,this.onLoadEndListener).pipe(Qr(()=>{this.childrenLoaders.delete(r)})),s=new qn(i,()=>new U).pipe(Wn());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function jv(e,n,t,r){return Nt(ge(t,()=>e.loadChildren())).pipe(P(Bv),q(o=>o instanceof Zs||Array.isArray(o)?_(o):W(n.compileModuleAsync(o))),P(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(t).injector,s=i.get(Yo,[],{optional:!0,self:!0}).flat()),{routes:s.map(Gd),injector:i}}))}function xS(e){return e&&typeof e=="object"&&"default"in e}function Bv(e){return xS(e)?e.default:e}var ka=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(OS),providedIn:"root"})}return e})(),OS=(()=>{class e{shouldProcessUrl(t){return!0}extract(t){return t}merge(t,r){return t}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Hv=new C("");var Uv=new C(""),$v=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new U;transitionAbortWithErrorSubject=new U;configLoader=p(qd);environmentInjector=p(Z);destroyRef=p(ct);urlSerializer=p(On);rootContexts=p(Rt);location=p(Mt);inputBindingEnabled=p(xa,{optional:!0})!==null;titleStrategy=p(Wd);options=p(kr,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=p(ka);createViewTransition=p(Hv,{optional:!0});navigationErrorHandler=p(Uv,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>_(void 0);rootComponentType=null;destroyed=!1;constructor(){let t=o=>this.events.next(new Ca(o)),r=o=>this.events.next(new Ea(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=t,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(t){let r=++this.navigationId;this.transitions?.next(A(g({},t),{extractedUrl:this.urlHandlingStrategy.extract(t.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,abortController:new AbortController,id:r}))}setupNavigations(t){return this.transitions=new J(null),this.transitions.pipe(ae(r=>r!==null),le(r=>{let o=!1;return _(r).pipe(le(i=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",De.SupersededByNewNavigation),Ce;this.currentTransition=r,this.currentNavigation={id:i.id,initialUrl:i.rawUrl,extractedUrl:i.extractedUrl,targetBrowserUrl:typeof i.extras.browserUrl=="string"?this.urlSerializer.parse(i.extras.browserUrl):i.extras.browserUrl,trigger:i.source,extras:i.extras,previousNavigation:this.lastSuccessfulNavigation?A(g({},this.lastSuccessfulNavigation),{previousNavigation:null}):null,abort:()=>i.abortController.abort()};let s=!t.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),a=i.extras.onSameUrlNavigation??t.onSameUrlNavigation;if(!s&&a!=="reload"){let c="";return this.events.next(new At(i.id,this.urlSerializer.serialize(i.rawUrl),c,jo.IgnoredSameUrlNavigation)),i.resolve(!1),Ce}if(this.urlHandlingStrategy.shouldProcessUrl(i.rawUrl))return _(i).pipe(le(c=>(this.events.next(new Tt(c.id,this.urlSerializer.serialize(c.extractedUrl),c.source,c.restoredState)),c.id!==this.navigationId?Ce:Promise.resolve(c))),MS(this.environmentInjector,this.configLoader,this.rootComponentType,t.config,this.urlSerializer,this.paramsInheritanceStrategy),te(c=>{r.targetSnapshot=c.targetSnapshot,r.urlAfterRedirects=c.urlAfterRedirects,this.currentNavigation=A(g({},this.currentNavigation),{finalUrl:c.urlAfterRedirects});let u=new Bo(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(u)}));if(s&&this.urlHandlingStrategy.shouldProcessUrl(i.currentRawUrl)){let{id:c,extractedUrl:u,source:l,restoredState:d,extras:h}=i,f=new Tt(c,this.urlSerializer.serialize(u),l,d);this.events.next(f);let v=Sv(this.rootComponentType).snapshot;return this.currentTransition=r=A(g({},i),{targetSnapshot:v,urlAfterRedirects:u,extras:A(g({},h),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=u,_(r)}else{let c="";return this.events.next(new At(i.id,this.urlSerializer.serialize(i.extractedUrl),c,jo.IgnoredByUrlHandlingStrategy)),i.resolve(!1),Ce}}),te(i=>{let s=new ma(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot);this.events.next(s)}),P(i=>(this.currentTransition=r=A(g({},i),{guards:Wb(i.targetSnapshot,i.currentSnapshot,this.rootContexts)}),r)),rS(this.environmentInjector,i=>this.events.next(i)),te(i=>{if(r.guardsResult=i.guardsResult,i.guardsResult&&typeof i.guardsResult!="boolean")throw Aa(this.urlSerializer,i.guardsResult);let s=new va(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot,!!i.guardsResult);this.events.next(s)}),ae(i=>i.guardsResult?!0:(this.cancelNavigationTransition(i,"",De.GuardRejected),!1)),Md(i=>{if(i.guards.canActivateChecks.length!==0)return _(i).pipe(te(s=>{let a=new ya(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),le(s=>{let a=!1;return _(s).pipe(TS(this.paramsInheritanceStrategy,this.environmentInjector),te({next:()=>a=!0,complete:()=>{a||this.cancelNavigationTransition(s,"",De.NoDataFromResolver)}}))}),te(s=>{let a=new Da(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}))}),Md(i=>{let s=a=>{let c=[];if(a.routeConfig?.loadComponent&&!a.routeConfig._loadedComponent){let u=xr(a)??this.environmentInjector;c.push(this.configLoader.loadComponent(u,a.routeConfig).pipe(te(l=>{a.component=l}),P(()=>{})))}for(let u of a.children)c.push(...s(u));return c};return Kn(s(i.targetSnapshot.root)).pipe(Pt(null),mt(1))}),Md(()=>this.afterPreactivation()),le(()=>{let{currentSnapshot:i,targetSnapshot:s}=r,a=this.createViewTransition?.(this.environmentInjector,i.root,s.root);return a?W(a).pipe(P(()=>r)):_(r)}),P(i=>{let s=Hb(t.routeReuseStrategy,i.targetSnapshot,i.currentRouterState);return this.currentTransition=r=A(g({},i),{targetRouterState:s}),this.currentNavigation.targetRouterState=s,r}),te(()=>{this.events.next(new Ho)}),Gb(this.rootContexts,t.routeReuseStrategy,i=>this.events.next(i),this.inputBindingEnabled),mt(1),Hi(new k(i=>{let s=r.abortController.signal,a=()=>i.next();return s.addEventListener("abort",a),()=>s.removeEventListener("abort",a)}).pipe(ae(()=>!o&&!r.targetRouterState),te(()=>{this.cancelNavigationTransition(r,r.abortController.signal.reason+"",De.Aborted)}))),te({next:i=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Xe(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects))),this.titleStrategy?.updateTitle(i.targetRouterState.snapshot),i.resolve(!0)},complete:()=>{o=!0}}),Hi(this.transitionAbortWithErrorSubject.pipe(te(i=>{throw i}))),Qr(()=>{o||this.cancelNavigationTransition(r,"",De.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),tt(i=>{if(this.destroyed)return r.resolve(!1),Ce;if(o=!0,xv(i))this.events.next(new ht(r.id,this.urlSerializer.serialize(r.extractedUrl),i.message,i.cancellationCode)),zb(i)?this.events.next(new Ar(i.url,i.navigationBehaviorOptions)):r.resolve(!1);else{let s=new Tr(r.id,this.urlSerializer.serialize(r.extractedUrl),i,r.targetSnapshot??void 0);try{let a=ge(this.environmentInjector,()=>this.navigationErrorHandler?.(s));if(a instanceof Nr){let{message:c,cancellationCode:u}=Aa(this.urlSerializer,a);this.events.next(new ht(r.id,this.urlSerializer.serialize(r.extractedUrl),c,u)),this.events.next(new Ar(a.redirectTo,a.navigationBehaviorOptions))}else throw this.events.next(s),i}catch(a){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(a)}}return Ce}))}))}cancelNavigationTransition(t,r,o){let i=new ht(t.id,this.urlSerializer.serialize(t.extractedUrl),r,o);this.events.next(i),t.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let t=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return t.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function kS(e){return e!==Po}var zv=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(FS),providedIn:"root"})}return e})(),Ra=class{shouldDetach(n){return!1}store(n,t){}shouldAttach(n){return!1}retrieve(n){return null}shouldReuseRoute(n,t){return n.routeConfig===t.routeConfig}},FS=(()=>{class e extends Ra{static \u0275fac=(()=>{let t;return function(o){return(t||(t=lt(e)))(o||e)}})();static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Gv=(()=>{class e{urlSerializer=p(On);options=p(kr,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=p(Mt);urlHandlingStrategy=p(ka);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new pt;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:t,initialUrl:r,targetBrowserUrl:o}){let i=t!==void 0?this.urlHandlingStrategy.merge(t,r):r,s=o??i;return s instanceof pt?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:t,finalUrl:r,initialUrl:o}){r&&t?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=t):this.rawUrlTree=o}routerState=Sv(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:t}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,t??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(PS),providedIn:"root"})}return e})(),PS=(()=>{class e extends Gv{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(t){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{t(r.url,r.state,"popstate")})})}handleRouterEvent(t,r){t instanceof Tt?this.updateStateMemento():t instanceof At?this.commitTransition(r):t instanceof Bo?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):t instanceof Ho?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):t instanceof ht&&t.code!==De.SupersededByNewNavigation&&t.code!==De.Redirect?this.restoreHistory(r):t instanceof Tr?this.restoreHistory(r,!0):t instanceof Xe&&(this.lastSuccessfulId=t.id,this.currentPageId=this.browserPageId)}setBrowserUrl(t,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(t)||i){let a=this.browserPageId,c=g(g({},s),this.generateNgRouterState(o,a));this.location.replaceState(t,"",c)}else{let a=g(g({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(t,"",a)}}restoreHistory(t,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===t.finalUrl&&i===0&&(this.resetInternalState(t),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(t),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(t,r){return this.canceledNavigationResolution==="computed"?{navigationId:t,\u0275routerPageId:r}:{navigationId:t}}static \u0275fac=(()=>{let t;return function(o){return(t||(t=lt(e)))(o||e)}})();static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Zd(e,n){e.events.pipe(ae(t=>t instanceof Xe||t instanceof ht||t instanceof Tr||t instanceof At),P(t=>t instanceof Xe||t instanceof At?0:(t instanceof ht?t.code===De.Redirect||t.code===De.SupersededByNewNavigation:!1)?2:1),ae(t=>t!==2),mt(1)).subscribe(()=>{n()})}var LS={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},VS={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},Oe=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=p(Wl);stateManager=p(Gv);options=p(kr,{optional:!0})||{};pendingTasks=p(Et);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=p($v);urlSerializer=p(On);location=p(Mt);urlHandlingStrategy=p(ka);injector=p(Z);_events=new U;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=p(zv);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=p(Yo,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!p(xa,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:t=>{this.console.warn(t)}}),this.subscribeToNavigationEvents()}eventsSubscription=new K;subscribeToNavigationEvents(){let t=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof ht&&r.code!==De.Redirect&&r.code!==De.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof Xe)this.navigated=!0;else if(r instanceof Ar){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=g({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||kS(o.source)},s);this.scheduleNavigation(a,Po,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}Lb(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortWithErrorSubject.next(o)}});this.eventsSubscription.add(t)}resetRootComponentType(t){this.routerState.root.component=t,this.navigationTransitions.rootComponentType=t}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Po,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((t,r,o)=>{this.navigateToSyncWithBrowser(t,o,r)})}navigateToSyncWithBrowser(t,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=g({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(t);this.scheduleNavigation(a,r,s,i).catch(c=>{this.disposed||this.injector.get(Ae)(c)})}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(t){this.config=t.map(Gd),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(t,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,u=c?this.currentUrlTree.fragment:s,l=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":l=g(g({},this.currentUrlTree.queryParams),i);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=i||null}l!==null&&(l=this.removeEmptyProps(l));let d;try{let h=o?o.snapshot:this.routerState.snapshot.root;d=Iv(h)}catch{(typeof t[0]!="string"||t[0][0]!=="/")&&(t=[]),d=this.currentUrlTree.root}return wv(d,t,l,u??null)}navigateByUrl(t,r={skipLocationChange:!1}){let o=Xt(t)?t:this.parseUrl(t),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Po,null,r)}navigate(t,r={skipLocationChange:!1}){return jS(t),this.navigateByUrl(this.createUrlTree(t,r),r)}serializeUrl(t){return this.urlSerializer.serialize(t)}parseUrl(t){try{return this.urlSerializer.parse(t)}catch{return this.urlSerializer.parse("/")}}isActive(t,r){let o;if(r===!0?o=g({},LS):r===!1?o=g({},VS):o=r,Xt(t))return rv(this.currentUrlTree,t,o);let i=this.parseUrl(t);return rv(this.currentUrlTree,i,o)}removeEmptyProps(t){return Object.entries(t).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(t,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,u;s?(a=s.resolve,c=s.reject,u=s.promise):u=new Promise((d,h)=>{a=d,c=h});let l=this.pendingTasks.add();return Zd(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:t,extras:i,resolve:a,reject:c,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(d=>Promise.reject(d))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function jS(e){for(let n=0;n<e.length;n++)if(e[n]==null)throw new y(4008,!1)}var Ko=(()=>{class e{router;route;tabIndexAttribute;renderer;el;locationStrategy;reactiveHref=Ct(null);get href(){return je(this.reactiveHref)}set href(t){this.reactiveHref.set(t)}target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new U;applicationErrorHandler=p(Ae);options=p(kr,{optional:!0});constructor(t,r,o,i,s,a){this.router=t,this.route=r,this.tabIndexAttribute=o,this.renderer=i,this.el=s,this.locationStrategy=a,this.reactiveHref.set(p(new na("href"),{optional:!0}));let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area"||!!(typeof customElements=="object"&&customElements.get(c)?.observedAttributes?.includes?.("href")),this.isAnchorElement?this.setTabIndexIfNotOnNativeEl("0"):this.subscribeToNavigationEventsIfNecessary()}subscribeToNavigationEventsIfNecessary(){if(this.subscription!==void 0||!this.isAnchorElement)return;let t=this.preserveFragment,r=o=>o==="merge"||o==="preserve";t||=r(this.queryParamsHandling),t||=!this.queryParamsHandling&&!r(this.options?.defaultQueryParamsHandling),t&&(this.subscription=this.router.events.subscribe(o=>{o instanceof Xe&&this.updateHref()}))}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(t){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",t)}ngOnChanges(t){this.isAnchorElement&&(this.updateHref(),this.subscribeToNavigationEventsIfNecessary()),this.onChanges.next(this)}routerLinkInput=null;set routerLink(t){t==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(Xt(t)?this.routerLinkInput=t:this.routerLinkInput=Array.isArray(t)?t:[t],this.setTabIndexIfNotOnNativeEl("0"))}onClick(t,r,o,i,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(t!==0||r||o||i||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c)?.catch(u=>{this.applicationErrorHandler(u)}),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let t=this.urlTree;this.reactiveHref.set(t!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(t))??"":null)}applyAttributeValue(t,r){let o=this.renderer,i=this.el.nativeElement;r!==null?o.setAttribute(i,t,r):o.removeAttribute(i,t)}get urlTree(){return this.routerLinkInput===null?null:Xt(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||e)(m(Oe),m(_e),Yt("tabindex"),m(dt),m(G),m(St))};static \u0275dir=L({type:e,selectors:[["","routerLink",""]],hostVars:2,hostBindings:function(r,o){r&1&&ye("click",function(s){return o.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&Tn("href",o.reactiveHref(),El)("target",o.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",So],skipLocationChange:[2,"skipLocationChange","skipLocationChange",So],replaceUrl:[2,"replaceUrl","replaceUrl",So],routerLink:"routerLink"},features:[we]})}return e})();var Qo=class{},HS=(()=>{class e{preload(t,r){return r().pipe(tt(()=>_(null)))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Wv=(()=>{class e{router;injector;preloadingStrategy;loader;subscription;constructor(t,r,o,i){this.router=t,this.injector=r,this.preloadingStrategy=o,this.loader=i}setUpPreloading(){this.subscription=this.router.events.pipe(ae(t=>t instanceof Xe),Ft(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(t,r){let o=[];for(let i of r){i.providers&&!i._injector&&(i._injector=Cr(i.providers,t,`Route: ${i.path}`));let s=i._injector??t,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&i.canLoad===void 0||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return W(o).pipe(Jn())}preloadConfig(t,r){return this.preloadingStrategy.preload(r,()=>{let o;r.loadChildren&&r.canLoad===void 0?o=this.loader.loadChildren(t,r):o=_(null);let i=o.pipe(q(s=>s===null?_(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??t,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(t,r);return W([i,s]).pipe(Jn())}else return i})}static \u0275fac=function(r){return new(r||e)(I(Oe),I(Z),I(Qo),I(qd))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),US=new C("");function $S(e,...n){return Ki([{provide:Yo,multi:!0,useValue:e},[],{provide:_e,useFactory:zS,deps:[Oe]},{provide:Ks,multi:!0,useFactory:WS},n.map(t=>t.\u0275providers)])}function zS(e){return e.routerState.root}function GS(e,n){return{\u0275kind:e,\u0275providers:n}}function WS(){let e=p(ue);return n=>{let t=e.get(Qe);if(n!==t.components[0])return;let r=e.get(Oe),o=e.get(qS);e.get(ZS)===1&&r.initialNavigation(),e.get(qv,null,{optional:!0})?.setUpPreloading(),e.get(US,null,{optional:!0})?.init(),r.resetRootComponentType(t.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var qS=new C("",{factory:()=>new U}),ZS=new C("",{providedIn:"root",factory:()=>1});var qv=new C("");function YS(e){return GS(0,[{provide:qv,useExisting:Wv},{provide:Qo,useExisting:e}])}var oy=(()=>{class e{_renderer;_elementRef;onChange=t=>{};onTouched=()=>{};constructor(t,r){this._renderer=t,this._elementRef=r}setProperty(t,r){this._renderer.setProperty(this._elementRef.nativeElement,t,r)}registerOnTouched(t){this.onTouched=t}registerOnChange(t){this.onChange=t}setDisabledState(t){this.setProperty("disabled",t)}static \u0275fac=function(r){return new(r||e)(m(dt),m(G))};static \u0275dir=L({type:e})}return e})(),ef=(()=>{class e extends oy{static \u0275fac=(()=>{let t;return function(o){return(t||(t=lt(e)))(o||e)}})();static \u0275dir=L({type:e,features:[Ne]})}return e})(),za=new C("");var KS={provide:za,useExisting:ke(()=>iy),multi:!0};function JS(){let e=Be()?Be().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}var XS=new C(""),iy=(()=>{class e extends oy{_compositionMode;_composing=!1;constructor(t,r,o){super(t,r),this._compositionMode=o,this._compositionMode==null&&(this._compositionMode=!JS())}writeValue(t){let r=t??"";this.setProperty("value",r)}_handleInput(t){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(t)}_compositionStart(){this._composing=!0}_compositionEnd(t){this._composing=!1,this._compositionMode&&this.onChange(t)}static \u0275fac=function(r){return new(r||e)(m(dt),m(G),m(XS,8))};static \u0275dir=L({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,o){r&1&&ye("input",function(s){return o._handleInput(s.target.value)})("blur",function(){return o.onTouched()})("compositionstart",function(){return o._compositionStart()})("compositionend",function(s){return o._compositionEnd(s.target.value)})},standalone:!1,features:[_t([KS]),Ne]})}return e})();var Ga=new C(""),sy=new C("");function eM(e){return n=>{if(n.value==null||e==null)return null;let t=parseFloat(n.value);return!isNaN(t)&&t<e?{min:{min:e,actual:n.value}}:null}}function tM(e){return n=>{if(n.value==null||e==null)return null;let t=parseFloat(n.value);return!isNaN(t)&&t>e?{max:{max:e,actual:n.value}}:null}}function Zv(e){return null}function ay(e){return e!=null}function cy(e){return Qt(e)?W(e):e}function uy(e){let n={};return e.forEach(t=>{n=t!=null?g(g({},n),t):n}),Object.keys(n).length===0?null:n}function ly(e,n){return n.map(t=>t(e))}function nM(e){return!e.validate}function dy(e){return e.map(n=>nM(n)?n:t=>n.validate(t))}function rM(e){if(!e)return null;let n=e.filter(ay);return n.length==0?null:function(t){return uy(ly(t,n))}}function fy(e){return e!=null?rM(dy(e)):null}function oM(e){if(!e)return null;let n=e.filter(ay);return n.length==0?null:function(t){let r=ly(t,n).map(cy);return wc(r).pipe(P(uy))}}function hy(e){return e!=null?oM(dy(e)):null}function Yv(e,n){return e===null?[n]:Array.isArray(e)?[...e,n]:[e,n]}function py(e){return e._rawValidators}function gy(e){return e._rawAsyncValidators}function Yd(e){return e?Array.isArray(e)?e:[e]:[]}function La(e,n){return Array.isArray(e)?e.includes(n):e===n}function Qv(e,n){let t=Yd(n);return Yd(e).forEach(o=>{La(t,o)||t.push(o)}),t}function Kv(e,n){return Yd(n).filter(t=>!La(e,t))}var Va=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(n){this._rawValidators=n||[],this._composedValidatorFn=fy(this._rawValidators)}_setAsyncValidators(n){this._rawAsyncValidators=n||[],this._composedAsyncValidatorFn=hy(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(n){this._onDestroyCallbacks.push(n)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(n=>n()),this._onDestroyCallbacks=[]}reset(n=void 0){this.control&&this.control.reset(n)}hasError(n,t){return this.control?this.control.hasError(n,t):!1}getError(n,t){return this.control?this.control.getError(n,t):null}},Lr=class extends Va{name;get formDirective(){return null}get path(){return null}},kn=class extends Va{_parent=null;name=null;valueAccessor=null},ja=class{_cd;constructor(n){this._cd=n}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},iM={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},K$=A(g({},iM),{"[class.ng-submitted]":"isSubmitted"}),J$=(()=>{class e extends ja{constructor(t){super(t)}static \u0275fac=function(r){return new(r||e)(m(kn,2))};static \u0275dir=L({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,o){r&2&&_o("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)},standalone:!1,features:[Ne]})}return e})(),X$=(()=>{class e extends ja{constructor(t){super(t)}static \u0275fac=function(r){return new(r||e)(m(Lr,10))};static \u0275dir=L({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(r,o){r&2&&_o("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)("ng-submitted",o.isSubmitted)},standalone:!1,features:[Ne]})}return e})();var Jo="VALID",Fa="INVALID",Fr="PENDING",Xo="DISABLED",en=class{},Ba=class extends en{value;source;constructor(n,t){super(),this.value=n,this.source=t}},ei=class extends en{pristine;source;constructor(n,t){super(),this.pristine=n,this.source=t}},ti=class extends en{touched;source;constructor(n,t){super(),this.touched=n,this.source=t}},Pr=class extends en{status;source;constructor(n,t){super(),this.status=n,this.source=t}},Qd=class extends en{source;constructor(n){super(),this.source=n}},Kd=class extends en{source;constructor(n){super(),this.source=n}};function tf(e){return(Wa(e)?e.validators:e)||null}function sM(e){return Array.isArray(e)?fy(e):e||null}function nf(e,n){return(Wa(n)?n.asyncValidators:e)||null}function aM(e){return Array.isArray(e)?hy(e):e||null}function Wa(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}function my(e,n,t){let r=e.controls;if(!(n?Object.keys(r):r).length)throw new y(1e3,"");if(!r[t])throw new y(1001,"")}function vy(e,n,t){e._forEachChild((r,o)=>{if(t[o]===void 0)throw new y(1002,"")})}var Vr=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(n,t){this._assignValidators(n),this._assignAsyncValidators(t)}get validator(){return this._composedValidatorFn}set validator(n){this._rawValidators=this._composedValidatorFn=n}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(n){this._rawAsyncValidators=this._composedAsyncValidatorFn=n}get parent(){return this._parent}get status(){return je(this.statusReactive)}set status(n){je(()=>this.statusReactive.set(n))}_status=wr(()=>this.statusReactive());statusReactive=Ct(void 0);get valid(){return this.status===Jo}get invalid(){return this.status===Fa}get pending(){return this.status==Fr}get disabled(){return this.status===Xo}get enabled(){return this.status!==Xo}errors;get pristine(){return je(this.pristineReactive)}set pristine(n){je(()=>this.pristineReactive.set(n))}_pristine=wr(()=>this.pristineReactive());pristineReactive=Ct(!0);get dirty(){return!this.pristine}get touched(){return je(this.touchedReactive)}set touched(n){je(()=>this.touchedReactive.set(n))}_touched=wr(()=>this.touchedReactive());touchedReactive=Ct(!1);get untouched(){return!this.touched}_events=new U;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(n){this._assignValidators(n)}setAsyncValidators(n){this._assignAsyncValidators(n)}addValidators(n){this.setValidators(Qv(n,this._rawValidators))}addAsyncValidators(n){this.setAsyncValidators(Qv(n,this._rawAsyncValidators))}removeValidators(n){this.setValidators(Kv(n,this._rawValidators))}removeAsyncValidators(n){this.setAsyncValidators(Kv(n,this._rawAsyncValidators))}hasValidator(n){return La(this._rawValidators,n)}hasAsyncValidator(n){return La(this._rawAsyncValidators,n)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(n={}){let t=this.touched===!1;this.touched=!0;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsTouched(A(g({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new ti(!0,r))}markAllAsDirty(n={}){this.markAsDirty({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:this}),this._forEachChild(t=>t.markAllAsDirty(n))}markAllAsTouched(n={}){this.markAsTouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:this}),this._forEachChild(t=>t.markAllAsTouched(n))}markAsUntouched(n={}){let t=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=n.sourceControl??this;this._forEachChild(o=>{o.markAsUntouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:r})}),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,r),t&&n.emitEvent!==!1&&this._events.next(new ti(!1,r))}markAsDirty(n={}){let t=this.pristine===!0;this.pristine=!1;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsDirty(A(g({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new ei(!1,r))}markAsPristine(n={}){let t=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=n.sourceControl??this;this._forEachChild(o=>{o.markAsPristine({onlySelf:!0,emitEvent:n.emitEvent})}),this._parent&&!n.onlySelf&&this._parent._updatePristine(n,r),t&&n.emitEvent!==!1&&this._events.next(new ei(!0,r))}markAsPending(n={}){this.status=Fr;let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new Pr(this.status,t)),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.markAsPending(A(g({},n),{sourceControl:t}))}disable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=Xo,this.errors=null,this._forEachChild(o=>{o.disable(A(g({},n),{onlySelf:!0}))}),this._updateValue();let r=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new Ba(this.value,r)),this._events.next(new Pr(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(A(g({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(o=>o(!0))}enable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=Jo,this._forEachChild(r=>{r.enable(A(g({},n),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent}),this._updateAncestors(A(g({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(n,t){this._parent&&!n.onlySelf&&(this._parent.updateValueAndValidity(n),n.skipPristineCheck||this._parent._updatePristine({},t),this._parent._updateTouched({},t))}setParent(n){this._parent=n}getRawValue(){return this.value}updateValueAndValidity(n={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===Jo||this.status===Fr)&&this._runAsyncValidator(r,n.emitEvent)}let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new Ba(this.value,t)),this._events.next(new Pr(this.status,t)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.updateValueAndValidity(A(g({},n),{sourceControl:t}))}_updateTreeValidity(n={emitEvent:!0}){this._forEachChild(t=>t._updateTreeValidity(n)),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?Xo:Jo}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(n,t){if(this.asyncValidator){this.status=Fr,this._hasOwnPendingAsyncValidator={emitEvent:t!==!1,shouldHaveEmitted:n!==!1};let r=cy(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(o=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(o,{emitEvent:t,shouldHaveEmitted:n})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let n=(this._hasOwnPendingAsyncValidator?.emitEvent||this._hasOwnPendingAsyncValidator?.shouldHaveEmitted)??!1;return this._hasOwnPendingAsyncValidator=null,n}return!1}setErrors(n,t={}){this.errors=n,this._updateControlsErrors(t.emitEvent!==!1,this,t.shouldHaveEmitted)}get(n){let t=n;return t==null||(Array.isArray(t)||(t=t.split(".")),t.length===0)?null:t.reduce((r,o)=>r&&r._find(o),this)}getError(n,t){let r=t?this.get(t):this;return r&&r.errors?r.errors[n]:null}hasError(n,t){return!!this.getError(n,t)}get root(){let n=this;for(;n._parent;)n=n._parent;return n}_updateControlsErrors(n,t,r){this.status=this._calculateStatus(),n&&this.statusChanges.emit(this.status),(n||r)&&this._events.next(new Pr(this.status,t)),this._parent&&this._parent._updateControlsErrors(n,t,r)}_initObservables(){this.valueChanges=new z,this.statusChanges=new z}_calculateStatus(){return this._allControlsDisabled()?Xo:this.errors?Fa:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(Fr)?Fr:this._anyControlsHaveStatus(Fa)?Fa:Jo}_anyControlsHaveStatus(n){return this._anyControls(t=>t.status===n)}_anyControlsDirty(){return this._anyControls(n=>n.dirty)}_anyControlsTouched(){return this._anyControls(n=>n.touched)}_updatePristine(n,t){let r=!this._anyControlsDirty(),o=this.pristine!==r;this.pristine=r,this._parent&&!n.onlySelf&&this._parent._updatePristine(n,t),o&&this._events.next(new ei(this.pristine,t))}_updateTouched(n={},t){this.touched=this._anyControlsTouched(),this._events.next(new ti(this.touched,t)),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,t)}_onDisabledChange=[];_registerOnCollectionChange(n){this._onCollectionChange=n}_setUpdateStrategy(n){Wa(n)&&n.updateOn!=null&&(this._updateOn=n.updateOn)}_parentMarkedDirty(n){let t=this._parent&&this._parent.dirty;return!n&&!!t&&!this._parent._anyControlsDirty()}_find(n){return null}_assignValidators(n){this._rawValidators=Array.isArray(n)?n.slice():n,this._composedValidatorFn=sM(this._rawValidators)}_assignAsyncValidators(n){this._rawAsyncValidators=Array.isArray(n)?n.slice():n,this._composedAsyncValidatorFn=aM(this._rawAsyncValidators)}},Ha=class extends Vr{constructor(n,t,r){super(tf(t),nf(r,t)),this.controls=n,this._initObservables(),this._setUpdateStrategy(t),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;registerControl(n,t){return this.controls[n]?this.controls[n]:(this.controls[n]=t,t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange),t)}addControl(n,t,r={}){this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(n,t={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}setControl(n,t,r={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],t&&this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(n){return this.controls.hasOwnProperty(n)&&this.controls[n].enabled}setValue(n,t={}){vy(this,!0,n),Object.keys(n).forEach(r=>{my(this,!0,r),this.controls[r].setValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t)}patchValue(n,t={}){n!=null&&(Object.keys(n).forEach(r=>{let o=this.controls[r];o&&o.patchValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t))}reset(n={},t={}){this._forEachChild((r,o)=>{r.reset(n?n[o]:null,{onlySelf:!0,emitEvent:t.emitEvent})}),this._updatePristine(t,this),this._updateTouched(t,this),this.updateValueAndValidity(t)}getRawValue(){return this._reduceChildren({},(n,t,r)=>(n[r]=t.getRawValue(),n))}_syncPendingControls(){let n=this._reduceChildren(!1,(t,r)=>r._syncPendingControls()?!0:t);return n&&this.updateValueAndValidity({onlySelf:!0}),n}_forEachChild(n){Object.keys(this.controls).forEach(t=>{let r=this.controls[t];r&&n(r,t)})}_setUpControls(){this._forEachChild(n=>{n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(n){for(let[t,r]of Object.entries(this.controls))if(this.contains(t)&&n(r))return!0;return!1}_reduceValue(){let n={};return this._reduceChildren(n,(t,r,o)=>((r.enabled||this.disabled)&&(t[o]=r.value),t))}_reduceChildren(n,t){let r=n;return this._forEachChild((o,i)=>{r=t(r,o,i)}),r}_allControlsDisabled(){for(let n of Object.keys(this.controls))if(this.controls[n].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(n){return this.controls.hasOwnProperty(n)?this.controls[n]:null}};var Jd=class extends Ha{};var yy=new C("",{providedIn:"root",factory:()=>rf}),rf="always";function cM(e,n){return[...n.path,e]}function Jv(e,n,t=rf){of(e,n),n.valueAccessor.writeValue(e.value),(e.disabled||t==="always")&&n.valueAccessor.setDisabledState?.(e.disabled),lM(e,n),fM(e,n),dM(e,n),uM(e,n)}function Xv(e,n,t=!0){let r=()=>{};n.valueAccessor&&(n.valueAccessor.registerOnChange(r),n.valueAccessor.registerOnTouched(r)),$a(e,n),e&&(n._invokeOnDestroyCallbacks(),e._registerOnCollectionChange(()=>{}))}function Ua(e,n){e.forEach(t=>{t.registerOnValidatorChange&&t.registerOnValidatorChange(n)})}function uM(e,n){if(n.valueAccessor.setDisabledState){let t=r=>{n.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(t),n._registerOnDestroy(()=>{e._unregisterOnDisabledChange(t)})}}function of(e,n){let t=py(e);n.validator!==null?e.setValidators(Yv(t,n.validator)):typeof t=="function"&&e.setValidators([t]);let r=gy(e);n.asyncValidator!==null?e.setAsyncValidators(Yv(r,n.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let o=()=>e.updateValueAndValidity();Ua(n._rawValidators,o),Ua(n._rawAsyncValidators,o)}function $a(e,n){let t=!1;if(e!==null){if(n.validator!==null){let o=py(e);if(Array.isArray(o)&&o.length>0){let i=o.filter(s=>s!==n.validator);i.length!==o.length&&(t=!0,e.setValidators(i))}}if(n.asyncValidator!==null){let o=gy(e);if(Array.isArray(o)&&o.length>0){let i=o.filter(s=>s!==n.asyncValidator);i.length!==o.length&&(t=!0,e.setAsyncValidators(i))}}}let r=()=>{};return Ua(n._rawValidators,r),Ua(n._rawAsyncValidators,r),t}function lM(e,n){n.valueAccessor.registerOnChange(t=>{e._pendingValue=t,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&Dy(e,n)})}function dM(e,n){n.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&Dy(e,n),e.updateOn!=="submit"&&e.markAsTouched()})}function Dy(e,n){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),n.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function fM(e,n){let t=(r,o)=>{n.valueAccessor.writeValue(r),o&&n.viewToModelUpdate(r)};e.registerOnChange(t),n._registerOnDestroy(()=>{e._unregisterOnChange(t)})}function hM(e,n){e==null,of(e,n)}function pM(e,n){return $a(e,n)}function gM(e,n){if(!e.hasOwnProperty("model"))return!1;let t=e.model;return t.isFirstChange()?!0:!Object.is(n,t.currentValue)}function mM(e){return Object.getPrototypeOf(e.constructor)===ef}function vM(e,n){e._syncPendingControls(),n.forEach(t=>{let r=t.control;r.updateOn==="submit"&&r._pendingChange&&(t.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}function yM(e,n){if(!n)return null;Array.isArray(n);let t,r,o;return n.forEach(i=>{i.constructor===iy?t=i:mM(i)?r=i:o=i}),o||r||t||null}function DM(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function ey(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function ty(e){return typeof e=="object"&&e!==null&&Object.keys(e).length===2&&"value"in e&&"disabled"in e}var Pa=class extends Vr{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(n=null,t,r){super(tf(t),nf(r,t)),this._applyFormState(n),this._setUpdateStrategy(t),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),Wa(t)&&(t.nonNullable||t.initialValueIsDefault)&&(ty(n)?this.defaultValue=n.value:this.defaultValue=n)}setValue(n,t={}){this.value=this._pendingValue=n,this._onChange.length&&t.emitModelToViewChange!==!1&&this._onChange.forEach(r=>r(this.value,t.emitViewToModelChange!==!1)),this.updateValueAndValidity(t)}patchValue(n,t={}){this.setValue(n,t)}reset(n=this.defaultValue,t={}){this._applyFormState(n),this.markAsPristine(t),this.markAsUntouched(t),this.setValue(this.value,t),this._pendingChange=!1}_updateValue(){}_anyControls(n){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(n){this._onChange.push(n)}_unregisterOnChange(n){ey(this._onChange,n)}registerOnDisabledChange(n){this._onDisabledChange.push(n)}_unregisterOnDisabledChange(n){ey(this._onDisabledChange,n)}_forEachChild(n){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(n){ty(n)?(this.value=this._pendingValue=n.value,n.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=n}};var CM=e=>e instanceof Pa;var t2=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275dir=L({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""],standalone:!1})}return e})();var Cy=new C("");var EM={provide:Lr,useExisting:ke(()=>IM)},IM=(()=>{class e extends Lr{callSetDisabledState;get submitted(){return je(this._submittedReactive)}set submitted(t){this._submittedReactive.set(t)}_submitted=wr(()=>this._submittedReactive());_submittedReactive=Ct(!1);_oldForm;_onCollectionChange=()=>this._updateDomValue();directives=[];form=null;ngSubmit=new z;constructor(t,r,o){super(),this.callSetDisabledState=o,this._setValidators(t),this._setAsyncValidators(r)}ngOnChanges(t){t.hasOwnProperty("form")&&(this._updateValidators(),this._updateDomValue(),this._updateRegistrations(),this._oldForm=this.form)}ngOnDestroy(){this.form&&($a(this.form,this),this.form._onCollectionChange===this._onCollectionChange&&this.form._registerOnCollectionChange(()=>{}))}get formDirective(){return this}get control(){return this.form}get path(){return[]}addControl(t){let r=this.form.get(t.path);return Jv(r,t,this.callSetDisabledState),r.updateValueAndValidity({emitEvent:!1}),this.directives.push(t),r}getControl(t){return this.form.get(t.path)}removeControl(t){Xv(t.control||null,t,!1),DM(this.directives,t)}addFormGroup(t){this._setUpFormContainer(t)}removeFormGroup(t){this._cleanUpFormContainer(t)}getFormGroup(t){return this.form.get(t.path)}addFormArray(t){this._setUpFormContainer(t)}removeFormArray(t){this._cleanUpFormContainer(t)}getFormArray(t){return this.form.get(t.path)}updateModel(t,r){this.form.get(t.path).setValue(r)}onSubmit(t){return this._submittedReactive.set(!0),vM(this.form,this.directives),this.ngSubmit.emit(t),this.form._events.next(new Qd(this.control)),t?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(t=void 0,r={}){this.form.reset(t,r),this._submittedReactive.set(!1),r?.emitEvent!==!1&&this.form._events.next(new Kd(this.form))}_updateDomValue(){this.directives.forEach(t=>{let r=t.control,o=this.form.get(t.path);r!==o&&(Xv(r||null,t),CM(o)&&(Jv(o,t,this.callSetDisabledState),t.control=o))}),this.form._updateTreeValidity({emitEvent:!1})}_setUpFormContainer(t){let r=this.form.get(t.path);hM(r,t),r.updateValueAndValidity({emitEvent:!1})}_cleanUpFormContainer(t){if(this.form){let r=this.form.get(t.path);r&&pM(r,t)&&r.updateValueAndValidity({emitEvent:!1})}}_updateRegistrations(){this.form._registerOnCollectionChange(this._onCollectionChange),this._oldForm&&this._oldForm._registerOnCollectionChange(()=>{})}_updateValidators(){of(this.form,this),this._oldForm&&$a(this._oldForm,this)}static \u0275fac=function(r){return new(r||e)(m(Ga,10),m(sy,10),m(yy,8))};static \u0275dir=L({type:e,selectors:[["","formGroup",""]],hostBindings:function(r,o){r&1&&ye("submit",function(s){return o.onSubmit(s)})("reset",function(){return o.onReset()})},inputs:{form:[0,"formGroup","form"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],standalone:!1,features:[_t([EM]),Ne,we]})}return e})();var wM={provide:kn,useExisting:ke(()=>_M)},_M=(()=>{class e extends kn{_ngModelWarningConfig;_added=!1;viewModel;control;name=null;set isDisabled(t){}model;update=new z;static _ngModelWarningSentOnce=!1;_ngModelWarningSent=!1;constructor(t,r,o,i,s){super(),this._ngModelWarningConfig=s,this._parent=t,this._setValidators(r),this._setAsyncValidators(o),this.valueAccessor=yM(this,i)}ngOnChanges(t){this._added||this._setUpControl(),gM(t,this.viewModel)&&(this.viewModel=this.model,this.formDirective.updateModel(this,this.model))}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}viewToModelUpdate(t){this.viewModel=t,this.update.emit(t)}get path(){return cM(this.name==null?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_setUpControl(){this.control=this.formDirective.addControl(this),this._added=!0}static \u0275fac=function(r){return new(r||e)(m(Lr,13),m(Ga,10),m(sy,10),m(za,10),m(Cy,8))};static \u0275dir=L({type:e,selectors:[["","formControlName",""]],inputs:{name:[0,"formControlName","name"],isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"]},outputs:{update:"ngModelChange"},standalone:!1,features:[_t([wM]),Ne,we]})}return e})();var bM={provide:za,useExisting:ke(()=>Iy),multi:!0};function Ey(e,n){return e==null?`${n}`:(n&&typeof n=="object"&&(n="Object"),`${e}: ${n}`.slice(0,50))}function SM(e){return e.split(":")[0]}var Iy=(()=>{class e extends ef{value;_optionMap=new Map;_idCounter=0;set compareWith(t){this._compareWith=t}_compareWith=Object.is;appRefInjector=p(Qe).injector;appRefDestroyRef=this.appRefInjector.get(ct);destroyRef=p(ct);cdr=p(Ke);_queuedWrite=!1;_writeValueAfterRender(){this._queuedWrite||this.appRefDestroyRef.destroyed||(this._queuedWrite=!0,Qs({write:()=>{this.destroyRef.destroyed||(this._queuedWrite=!1,this.writeValue(this.value))}},{injector:this.appRefInjector}))}writeValue(t){this.cdr.markForCheck(),this.value=t;let r=this._getOptionId(t),o=Ey(r,t);this.setProperty("value",o)}registerOnChange(t){this.onChange=r=>{this.value=this._getOptionValue(r),t(this.value)}}_registerOption(){return(this._idCounter++).toString()}_getOptionId(t){for(let r of this._optionMap.keys())if(this._compareWith(this._optionMap.get(r),t))return r;return null}_getOptionValue(t){let r=SM(t);return this._optionMap.has(r)?this._optionMap.get(r):t}static \u0275fac=(()=>{let t;return function(o){return(t||(t=lt(e)))(o||e)}})();static \u0275dir=L({type:e,selectors:[["select","formControlName","",3,"multiple",""],["select","formControl","",3,"multiple",""],["select","ngModel","",3,"multiple",""]],hostBindings:function(r,o){r&1&&ye("change",function(s){return o.onChange(s.target.value)})("blur",function(){return o.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[_t([bM]),Ne]})}return e})(),n2=(()=>{class e{_element;_renderer;_select;id;constructor(t,r,o){this._element=t,this._renderer=r,this._select=o,this._select&&(this.id=this._select._registerOption())}set ngValue(t){this._select!=null&&(this._select._optionMap.set(this.id,t),this._setElementValue(Ey(this.id,t)),this._select._writeValueAfterRender())}set value(t){this._setElementValue(t),this._select&&this._select._writeValueAfterRender()}_setElementValue(t){this._renderer.setProperty(this._element.nativeElement,"value",t)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select._writeValueAfterRender())}static \u0275fac=function(r){return new(r||e)(m(G),m(dt),m(Iy,9))};static \u0275dir=L({type:e,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return e})(),MM={provide:za,useExisting:ke(()=>wy),multi:!0};function ny(e,n){return e==null?`${n}`:(typeof n=="string"&&(n=`'${n}'`),n&&typeof n=="object"&&(n="Object"),`${e}: ${n}`.slice(0,50))}function TM(e){return e.split(":")[0]}var wy=(()=>{class e extends ef{value;_optionMap=new Map;_idCounter=0;set compareWith(t){this._compareWith=t}_compareWith=Object.is;writeValue(t){this.value=t;let r;if(Array.isArray(t)){let o=t.map(i=>this._getOptionId(i));r=(i,s)=>{i._setSelected(o.indexOf(s.toString())>-1)}}else r=(o,i)=>{o._setSelected(!1)};this._optionMap.forEach(r)}registerOnChange(t){this.onChange=r=>{let o=[],i=r.selectedOptions;if(i!==void 0){let s=i;for(let a=0;a<s.length;a++){let c=s[a],u=this._getOptionValue(c.value);o.push(u)}}else{let s=r.options;for(let a=0;a<s.length;a++){let c=s[a];if(c.selected){let u=this._getOptionValue(c.value);o.push(u)}}}this.value=o,t(o)}}_registerOption(t){let r=(this._idCounter++).toString();return this._optionMap.set(r,t),r}_getOptionId(t){for(let r of this._optionMap.keys())if(this._compareWith(this._optionMap.get(r)._value,t))return r;return null}_getOptionValue(t){let r=TM(t);return this._optionMap.has(r)?this._optionMap.get(r)._value:t}static \u0275fac=(()=>{let t;return function(o){return(t||(t=lt(e)))(o||e)}})();static \u0275dir=L({type:e,selectors:[["select","multiple","","formControlName",""],["select","multiple","","formControl",""],["select","multiple","","ngModel",""]],hostBindings:function(r,o){r&1&&ye("change",function(s){return o.onChange(s.target)})("blur",function(){return o.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[_t([MM]),Ne]})}return e})(),r2=(()=>{class e{_element;_renderer;_select;id;_value;constructor(t,r,o){this._element=t,this._renderer=r,this._select=o,this._select&&(this.id=this._select._registerOption(this))}set ngValue(t){this._select!=null&&(this._value=t,this._setElementValue(ny(this.id,t)),this._select.writeValue(this._select.value))}set value(t){this._select?(this._value=t,this._setElementValue(ny(this.id,t)),this._select.writeValue(this._select.value)):this._setElementValue(t)}_setElementValue(t){this._renderer.setProperty(this._element.nativeElement,"value",t)}_setSelected(t){this._renderer.setProperty(this._element.nativeElement,"selected",t)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static \u0275fac=function(r){return new(r||e)(m(G),m(dt),m(wy,9))};static \u0275dir=L({type:e,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return e})();function _y(e){return typeof e=="number"?e:parseFloat(e)}var by=(()=>{class e{_validator=Zv;_onChange;_enabled;ngOnChanges(t){if(this.inputName in t){let r=this.normalizeInput(t[this.inputName].currentValue);this._enabled=this.enabled(r),this._validator=this._enabled?this.createValidator(r):Zv,this._onChange&&this._onChange()}}validate(t){return this._validator(t)}registerOnValidatorChange(t){this._onChange=t}enabled(t){return t!=null}static \u0275fac=function(r){return new(r||e)};static \u0275dir=L({type:e,features:[we]})}return e})(),AM={provide:Ga,useExisting:ke(()=>NM),multi:!0},NM=(()=>{class e extends by{max;inputName="max";normalizeInput=t=>_y(t);createValidator=t=>tM(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=lt(e)))(o||e)}})();static \u0275dir=L({type:e,selectors:[["input","type","number","max","","formControlName",""],["input","type","number","max","","formControl",""],["input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&Tn("max",o._enabled?o.max:null)},inputs:{max:"max"},standalone:!1,features:[_t([AM]),Ne]})}return e})(),RM={provide:Ga,useExisting:ke(()=>xM),multi:!0},xM=(()=>{class e extends by{min;inputName="min";normalizeInput=t=>_y(t);createValidator=t=>eM(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=lt(e)))(o||e)}})();static \u0275dir=L({type:e,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&Tn("min",o._enabled?o.min:null)},inputs:{min:"min"},standalone:!1,features:[_t([RM]),Ne]})}return e})();var OM=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=wt({type:e});static \u0275inj=nt({})}return e})(),Xd=class extends Vr{constructor(n,t,r){super(tf(t),nf(r,t)),this.controls=n,this._initObservables(),this._setUpdateStrategy(t),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;at(n){return this.controls[this._adjustIndex(n)]}push(n,t={}){this.controls.push(n),this._registerControl(n),this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}insert(n,t,r={}){this.controls.splice(n,0,t),this._registerControl(t),this.updateValueAndValidity({emitEvent:r.emitEvent})}removeAt(n,t={}){let r=this._adjustIndex(n);r<0&&(r=0),this.controls[r]&&this.controls[r]._registerOnCollectionChange(()=>{}),this.controls.splice(r,1),this.updateValueAndValidity({emitEvent:t.emitEvent})}setControl(n,t,r={}){let o=this._adjustIndex(n);o<0&&(o=0),this.controls[o]&&this.controls[o]._registerOnCollectionChange(()=>{}),this.controls.splice(o,1),t&&(this.controls.splice(o,0,t),this._registerControl(t)),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}get length(){return this.controls.length}setValue(n,t={}){vy(this,!1,n),n.forEach((r,o)=>{my(this,!1,o),this.at(o).setValue(r,{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t)}patchValue(n,t={}){n!=null&&(n.forEach((r,o)=>{this.at(o)&&this.at(o).patchValue(r,{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t))}reset(n=[],t={}){this._forEachChild((r,o)=>{r.reset(n[o],{onlySelf:!0,emitEvent:t.emitEvent})}),this._updatePristine(t,this),this._updateTouched(t,this),this.updateValueAndValidity(t)}getRawValue(){return this.controls.map(n=>n.getRawValue())}clear(n={}){this.controls.length<1||(this._forEachChild(t=>t._registerOnCollectionChange(()=>{})),this.controls.splice(0),this.updateValueAndValidity({emitEvent:n.emitEvent}))}_adjustIndex(n){return n<0?n+this.length:n}_syncPendingControls(){let n=this.controls.reduce((t,r)=>r._syncPendingControls()?!0:t,!1);return n&&this.updateValueAndValidity({onlySelf:!0}),n}_forEachChild(n){this.controls.forEach((t,r)=>{n(t,r)})}_updateValue(){this.value=this.controls.filter(n=>n.enabled||this.disabled).map(n=>n.value)}_anyControls(n){return this.controls.some(t=>t.enabled&&n(t))}_setUpControls(){this._forEachChild(n=>this._registerControl(n))}_allControlsDisabled(){for(let n of this.controls)if(n.enabled)return!1;return this.controls.length>0||this.disabled}_registerControl(n){n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange)}_find(n){return this.at(n)??null}};function ry(e){return!!e&&(e.asyncValidators!==void 0||e.validators!==void 0||e.updateOn!==void 0)}var o2=(()=>{class e{useNonNullable=!1;get nonNullable(){let t=new e;return t.useNonNullable=!0,t}group(t,r=null){let o=this._reduceControls(t),i={};return ry(r)?i=r:r!==null&&(i.validators=r.validator,i.asyncValidators=r.asyncValidator),new Ha(o,i)}record(t,r=null){let o=this._reduceControls(t);return new Jd(o,r)}control(t,r,o){let i={};return this.useNonNullable?(ry(r)?i=r:(i.validators=r,i.asyncValidators=o),new Pa(t,A(g({},i),{nonNullable:!0}))):new Pa(t,r,o)}array(t,r,o){let i=t.map(s=>this._createControl(s));return new Xd(i,r,o)}_reduceControls(t){let r={};return Object.keys(t).forEach(o=>{r[o]=this._createControl(t[o])}),r}_createControl(t){if(t instanceof Pa)return t;if(t instanceof Vr)return t;if(Array.isArray(t)){let r=t[0],o=t.length>1?t[1]:null,i=t.length>2?t[2]:null;return this.control(r,o,i)}else return this.control(t)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var i2=(()=>{class e{static withConfig(t){return{ngModule:e,providers:[{provide:Cy,useValue:t.warnOnNgModelWithFormControl??"always"},{provide:yy,useValue:t.callSetDisabledState??rf}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=wt({type:e});static \u0275inj=nt({imports:[OM]})}return e})();var c2=(e,n,t,r,o)=>FM(e[1],n[1],t[1],r[1],o).map(i=>kM(e[0],n[0],t[0],r[0],i)),kM=(e,n,t,r,o)=>{let i=3*n*Math.pow(o-1,2),s=-3*t*o+3*t+r*o,a=e*Math.pow(o-1,3);return o*(i+o*s)-a},FM=(e,n,t,r,o)=>(e-=o,n-=o,t-=o,r-=o,LM(r-3*t+3*n-e,3*t-6*n+3*e,3*n-3*e,e).filter(s=>s>=0&&s<=1)),PM=(e,n,t)=>{let r=n*n-4*e*t;return r<0?[]:[(-n+Math.sqrt(r))/(2*e),(-n-Math.sqrt(r))/(2*e)]},LM=(e,n,t,r)=>{if(e===0)return PM(n,t,r);n/=e,t/=e,r/=e;let o=(3*t-n*n)/3,i=(2*n*n*n-9*n*t+27*r)/27;if(o===0)return[Math.pow(-i,.3333333333333333)];if(i===0)return[Math.sqrt(-o),-Math.sqrt(-o)];let s=Math.pow(i/2,2)+Math.pow(o/3,3);if(s===0)return[Math.pow(i/2,.5)-n/3];if(s>0)return[Math.pow(-(i/2)+Math.sqrt(s),.3333333333333333)-Math.pow(i/2+Math.sqrt(s),.3333333333333333)-n/3];let a=Math.sqrt(Math.pow(-(o/3),3)),c=Math.acos(-(i/(2*Math.sqrt(Math.pow(-(o/3),3))))),u=2*Math.pow(a,1/3);return[u*Math.cos(c/3)-n/3,u*Math.cos((c+2*Math.PI)/3)-n/3,u*Math.cos((c+4*Math.PI)/3)-n/3]};var qa=e=>My(e),Br=(e,n)=>(typeof e=="string"&&(n=e,e=void 0),qa(e).includes(n)),My=(e=window)=>{if(typeof e>"u")return[];e.Ionic=e.Ionic||{};let n=e.Ionic.platforms;return n==null&&(n=e.Ionic.platforms=VM(e),n.forEach(t=>e.document.documentElement.classList.add(`plt-${t}`))),n},VM=e=>{let n=be.get("platform");return Object.keys(Sy).filter(t=>{let r=n?.[t];return typeof r=="function"?r(e):Sy[t](e)})},jM=e=>Za(e)&&!Ay(e),sf=e=>!!(Fn(e,/iPad/i)||Fn(e,/Macintosh/i)&&Za(e)),BM=e=>Fn(e,/iPhone/i),HM=e=>Fn(e,/iPhone|iPod/i)||sf(e),Ty=e=>Fn(e,/android|sink/i),UM=e=>Ty(e)&&!Fn(e,/mobile/i),$M=e=>{let n=e.innerWidth,t=e.innerHeight,r=Math.min(n,t),o=Math.max(n,t);return r>390&&r<520&&o>620&&o<800},zM=e=>{let n=e.innerWidth,t=e.innerHeight,r=Math.min(n,t),o=Math.max(n,t);return sf(e)||UM(e)||r>460&&r<820&&o>780&&o<1400},Za=e=>ZM(e,"(any-pointer:coarse)"),GM=e=>!Za(e),Ay=e=>Ny(e)||Ry(e),Ny=e=>!!(e.cordova||e.phonegap||e.PhoneGap),Ry=e=>{let n=e.Capacitor;return!!(n?.isNative||n?.isNativePlatform&&n.isNativePlatform())},WM=e=>Fn(e,/electron/i),qM=e=>{var n;return!!(!((n=e.matchMedia)===null||n===void 0)&&n.call(e,"(display-mode: standalone)").matches||e.navigator.standalone)},Fn=(e,n)=>n.test(e.navigator.userAgent),ZM=(e,n)=>{var t;return(t=e.matchMedia)===null||t===void 0?void 0:t.call(e,n).matches},Sy={ipad:sf,iphone:BM,ios:HM,android:Ty,phablet:$M,tablet:zM,cordova:Ny,capacitor:Ry,electron:WM,pwa:qM,mobile:Za,mobileweb:jM,desktop:GM,hybrid:Ay},jr,af=e=>e&&_f(e)||jr,YM=(e={})=>{if(typeof window>"u")return;let n=window.document,t=window,r=t.Ionic=t.Ionic||{},o=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},bf(t)),{persistConfig:!1}),r.config),Mf(t)),e);be.reset(o),be.getBoolean("persistConfig")&&Sf(t,o),My(t),r.config=be,r.mode=jr=be.get("mode",n.documentElement.getAttribute("mode")||(Br(t,"ios")?"ios":"md")),be.set("mode",jr),n.documentElement.setAttribute("mode",jr),n.documentElement.classList.add(jr),be.getBoolean("_testing")&&be.set("animated",!1);let i=a=>{var c;return(c=a.tagName)===null||c===void 0?void 0:c.startsWith("ION-")},s=a=>["ios","md"].includes(a);wf(a=>{for(;a;){let c=a.mode||a.getAttribute("mode");if(c){if(s(c))return c;i(a)&&si('Invalid ionic mode: "'+c+'", expected: "ios" or "md"')}a=a.parentElement}return jr})};var QM=e=>e!==void 0?(Array.isArray(e)?e:e.split(" ")).filter(t=>t!=null).map(t=>t.trim()).filter(t=>t!==""):[],h2=e=>{let n={};return QM(e).forEach(t=>n[t]=!0),n};var v2=(e,n,t,r,o,i)=>se(null,null,function*(){var s;if(e)return e.attachViewToDom(n,t,o,r);if(!i&&typeof t!="string"&&!(t instanceof HTMLElement))throw new Error("framework delegate is missing");let a=typeof t=="string"?(s=n.ownerDocument)===null||s===void 0?void 0:s.createElement(t):t;return r&&r.forEach(c=>a.classList.add(c)),o&&Object.assign(a,o),n.appendChild(a),yield new Promise(c=>Ot(a,c)),a}),y2=(e,n)=>{if(n){if(e){let t=n.parentElement;return e.removeViewFromDom(t,n)}n.remove()}return Promise.resolve()},D2=()=>{let e,n;return{attachViewToDom:(c,u,...l)=>se(null,[c,u,...l],function*(o,i,s={},a=[]){var d,h;e=o;let f;if(i){let E=typeof i=="string"?(d=e.ownerDocument)===null||d===void 0?void 0:d.createElement(i):i;a.forEach(T=>E.classList.add(T)),Object.assign(E,s),e.appendChild(E),f=E,yield new Promise(T=>Ot(E,T))}else if(e.children.length>0&&(e.tagName==="ION-MODAL"||e.tagName==="ION-POPOVER")&&!(f=e.children[0]).classList.contains("ion-delegate-host")){let T=(h=e.ownerDocument)===null||h===void 0?void 0:h.createElement("div");T.classList.add("ion-delegate-host"),a.forEach(R=>T.classList.add(R)),T.append(...e.children),e.appendChild(T),f=T}let v=document.querySelector("ion-app")||document.body;return n=document.createComment("ionic teleport"),e.parentNode.insertBefore(n,e),v.appendChild(e),f??e}),removeViewFromDom:()=>(e&&n&&(n.parentNode.insertBefore(e,n),n.remove()),Promise.resolve())}};var ri='[tabindex]:not([tabindex^="-"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^="-"]):not([hidden]):not([disabled]), textarea:not([tabindex^="-"]):not([hidden]):not([disabled]), button:not([tabindex^="-"]):not([hidden]):not([disabled]), select:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable[disabled="false"]:not([tabindex^="-"]):not([hidden])',xy=(e,n)=>{let t=e.querySelector(ri);Py(t,n??e)},Oy=(e,n)=>{let t=Array.from(e.querySelectorAll(ri)),r=t.length>0?t[t.length-1]:null;Py(r,n??e)},Py=(e,n)=>{let t=e,r=e?.shadowRoot;if(r&&(t=r.querySelector(ri)||e),t){let o=t.closest("ion-radio-group");o?o.setFocus():Xa(t)}else n.focus()},cf=0,KM=0,Ya=new WeakMap,Ly=e=>({create(t){return eT(e,t)},dismiss(t,r,o){return oT(document,t,r,e,o)},getTop(){return se(this,null,function*(){return ni(document,e)})}});var JM=Ly("ion-modal");var XM=Ly("ion-popover");var T2=e=>{typeof document<"u"&&rT(document);let n=cf++;e.overlayIndex=n},A2=e=>(e.hasAttribute("id")||(e.id=`ion-overlay-${++KM}`),e.id),eT=(e,n)=>typeof window<"u"&&typeof window.customElements<"u"?window.customElements.whenDefined(e).then(()=>{let t=document.createElement(e);return t.classList.add("overlay-hidden"),Object.assign(t,Object.assign(Object.assign({},n),{hasController:!0})),jy(document).appendChild(t),new Promise(r=>Ot(t,r))}):Promise.resolve(),tT=e=>e.classList.contains("overlay-hidden"),ky=(e,n)=>{let t=e,r=e?.shadowRoot;r&&(t=r.querySelector(ri)||e),t?Xa(t):n.focus()},nT=(e,n)=>{let t=ni(n,"ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover"),r=e.target;if(!t||!r||t.classList.contains(dT))return;let o=()=>{if(t===r)t.lastFocus=void 0;else if(r.tagName==="ION-TOAST")ky(t.lastFocus,t);else{let s=Rf(t);if(!s.contains(r))return;let a=s.querySelector(".ion-overlay-wrapper");if(!a)return;if(a.contains(r)||r===s.querySelector("ion-backdrop"))t.lastFocus=r;else{let c=t.lastFocus;xy(a,t),c===n.activeElement&&Oy(a,t),t.lastFocus=n.activeElement}}},i=()=>{if(t.contains(r))t.lastFocus=r;else if(r.tagName==="ION-TOAST")ky(t.lastFocus,t);else{let s=t.lastFocus;xy(t),s===n.activeElement&&Oy(t),t.lastFocus=n.activeElement}};t.shadowRoot?i():o()},rT=e=>{cf===0&&(cf=1,e.addEventListener("focus",n=>{nT(n,e)},!0),e.addEventListener("ionBackButton",n=>{let t=ni(e);t?.backdropDismiss&&n.detail.register(Of,()=>{t.dismiss(void 0,Fy)})}),xf()||e.addEventListener("keydown",n=>{if(n.key==="Escape"){let t=ni(e);t?.backdropDismiss&&t.dismiss(void 0,Fy)}}))},oT=(e,n,t,r,o)=>{let i=ni(e,r,o);return i?i.dismiss(n,t):Promise.reject("overlay does not exist")},iT=(e,n)=>(n===void 0&&(n="ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast"),Array.from(e.querySelectorAll(n)).filter(t=>t.overlayIndex>0)),Qa=(e,n)=>iT(e,n).filter(t=>!tT(t)),ni=(e,n,t)=>{let r=Qa(e,n);return t===void 0?r[r.length-1]:r.find(o=>o.id===t)},Vy=(e=!1)=>{let t=jy(document).querySelector("ion-router-outlet, ion-nav, #ion-view-container-root");t&&(e?t.setAttribute("aria-hidden","true"):t.removeAttribute("aria-hidden"))},N2=(e,n,t,r,o)=>se(null,null,function*(){var i,s;if(e.presented)return;e.el.tagName!=="ION-TOAST"&&(Vy(!0),document.body.classList.add(ic)),uT(e.el),Hy(e.el),e.presented=!0,e.willPresent.emit(),(i=e.willPresentShorthand)===null||i===void 0||i.emit();let a=af(e),c=e.enterAnimation?e.enterAnimation:be.get(n,a==="ios"?t:r);(yield By(e,c,e.el,o))&&(e.didPresent.emit(),(s=e.didPresentShorthand)===null||s===void 0||s.emit()),e.el.tagName!=="ION-TOAST"&&sT(e.el),e.keyboardClose&&(document.activeElement===null||!e.el.contains(document.activeElement))&&e.el.focus(),e.el.removeAttribute("aria-hidden")}),sT=e=>se(null,null,function*(){let n=document.activeElement;if(!n)return;let t=n?.shadowRoot;t&&(n=t.querySelector(ri)||n),yield e.onDidDismiss(),(document.activeElement===null||document.activeElement===document.body)&&n.focus()}),R2=(e,n,t,r,o,i,s)=>se(null,null,function*(){var a,c;if(!e.presented)return!1;let l=(xt!==void 0?Qa(xt):[]).filter(h=>h.tagName!=="ION-TOAST");l.length===1&&l[0].id===e.el.id&&(Vy(!1),document.body.classList.remove(ic)),e.presented=!1;try{Hy(e.el),e.el.style.setProperty("pointer-events","none"),e.willDismiss.emit({data:n,role:t}),(a=e.willDismissShorthand)===null||a===void 0||a.emit({data:n,role:t});let h=af(e),f=e.leaveAnimation?e.leaveAnimation:be.get(r,h==="ios"?o:i);t!==cT&&(yield By(e,f,e.el,s)),e.didDismiss.emit({data:n,role:t}),(c=e.didDismissShorthand)===null||c===void 0||c.emit({data:n,role:t}),(Ya.get(e)||[]).forEach(E=>E.destroy()),Ya.delete(e),e.el.classList.add("overlay-hidden"),e.el.style.removeProperty("pointer-events"),e.el.lastFocus!==void 0&&(e.el.lastFocus=void 0)}catch(h){Tf(`[${e.el.tagName.toLowerCase()}] - `,h)}return e.el.remove(),lT(),!0}),jy=e=>e.querySelector("ion-app")||e.body,By=(e,n,t,r)=>se(null,null,function*(){t.classList.remove("overlay-hidden");let o=e.el,i=n(o,r);(!e.animated||!be.getBoolean("animated",!0))&&i.duration(0),e.keyboardClose&&i.beforeAddWrite(()=>{let a=t.ownerDocument.activeElement;a?.matches("input,ion-input, ion-textarea")&&a.blur()});let s=Ya.get(e)||[];return Ya.set(e,[...s,i]),yield i.play(),!0}),x2=(e,n)=>{let t,r=new Promise(o=>t=o);return aT(e,n,o=>{t(o.detail)}),r},aT=(e,n,t)=>{let r=o=>{Nf(e,n,r),t(o)};Af(e,n,r)};var Fy="backdrop",cT="gesture",O2=39;var k2=()=>{let e,n=()=>{e&&(e(),e=void 0)};return{addClickListener:(r,o)=>{n();let i=o!==void 0?document.getElementById(o):null;if(!i){si(`[${r.tagName.toLowerCase()}] - A trigger element with the ID "${o}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on an overlay component.`,r);return}e=((a,c)=>{let u=()=>{c.present()};return a.addEventListener("click",u),()=>{a.removeEventListener("click",u)}})(i,r)},removeClickListener:n}},Hy=e=>{xt!==void 0&&Br("android")&&e.setAttribute("aria-hidden","true")},uT=e=>{var n;if(xt===void 0)return;let t=Qa(xt);for(let r=t.length-1;r>=0;r--){let o=t[r],i=(n=t[r+1])!==null&&n!==void 0?n:e;(i.hasAttribute("aria-hidden")||i.tagName!=="ION-TOAST")&&o.setAttribute("aria-hidden","true")}},lT=()=>{if(xt===void 0)return;let e=Qa(xt);for(let n=e.length-1;n>=0;n--){let t=e[n];if(t.removeAttribute("aria-hidden"),t.tagName!=="ION-TOAST")break}},dT="ion-disable-focus-trap";var fT=["tabsInner"];var hT=(()=>{class e{doc;_readyPromise;win;backButton=new U;keyboardDidShow=new U;keyboardDidHide=new U;pause=new U;resume=new U;resize=new U;constructor(t,r){this.doc=t,r.run(()=>{this.win=t.defaultView,this.backButton.subscribeWithPriority=function(i,s){return this.subscribe(a=>a.register(i,c=>r.run(()=>s(c))))},Hr(this.pause,t,"pause",r),Hr(this.resume,t,"resume",r),Hr(this.backButton,t,"ionBackButton",r),Hr(this.resize,this.win,"resize",r),Hr(this.keyboardDidShow,this.win,"ionKeyboardDidShow",r),Hr(this.keyboardDidHide,this.win,"ionKeyboardDidHide",r);let o;this._readyPromise=new Promise(i=>{o=i}),this.win?.cordova?t.addEventListener("deviceready",()=>{o("cordova")},{once:!0}):o("dom")})}is(t){return Br(this.win,t)}platforms(){return qa(this.win)}ready(){return this._readyPromise}get isRTL(){return this.doc.dir==="rtl"}getQueryParam(t){return pT(this.win.location.href,t)}isLandscape(){return!this.isPortrait()}isPortrait(){return this.win.matchMedia?.("(orientation: portrait)").matches}testUserAgent(t){let r=this.win.navigator;return!!(r?.userAgent&&r.userAgent.indexOf(t)>=0)}url(){return this.win.location.href}width(){return this.win.innerWidth}height(){return this.win.innerHeight}static \u0275fac=function(r){return new(r||e)(I(ee),I($))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),pT=(e,n)=>{n=n.replace(/[[\]\\]/g,"\\$&");let r=new RegExp("[\\?&]"+n+"=([^&#]*)").exec(e);return r?decodeURIComponent(r[1].replace(/\+/g," ")):null},Hr=(e,n,t,r)=>{n&&n.addEventListener(t,o=>{r.run(()=>{let i=o?.detail;e.next(i)})})},ii=(()=>{class e{location;serializer;router;topOutlet;direction=Uy;animated=$y;animationBuilder;guessDirection="forward";guessAnimation;lastNavId=-1;constructor(t,r,o,i){this.location=r,this.serializer=o,this.router=i,i&&i.events.subscribe(s=>{if(s instanceof Tt){let a=s.restoredState?s.restoredState.navigationId:s.id;this.guessDirection=this.guessAnimation=a<this.lastNavId?"back":"forward",this.lastNavId=this.guessDirection==="forward"?s.id:a}}),t.backButton.subscribeWithPriority(0,s=>{this.pop(),s()})}navigateForward(t,r={}){return this.setDirection("forward",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}navigateBack(t,r={}){return this.setDirection("back",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}navigateRoot(t,r={}){return this.setDirection("root",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}back(t={animated:!0,animationDirection:"back"}){return this.setDirection("back",t.animated,t.animationDirection,t.animation),this.location.back()}pop(){return se(this,null,function*(){let t=this.topOutlet;for(;t;){if(yield t.pop())return!0;t=t.parentOutlet}return!1})}setDirection(t,r,o,i){this.direction=t,this.animated=gT(t,r,o),this.animationBuilder=i}setTopOutlet(t){this.topOutlet=t}consumeTransition(){let t="root",r,o=this.animationBuilder;return this.direction==="auto"?(t=this.guessDirection,r=this.guessAnimation):(r=this.animated,t=this.direction),this.direction=Uy,this.animated=$y,this.animationBuilder=void 0,{direction:t,animation:r,animationBuilder:o}}navigate(t,r){if(Array.isArray(t))return this.router.navigate(t,r);{let o=this.serializer.parse(t.toString());return r.queryParams!==void 0&&(o.queryParams=g({},r.queryParams)),r.fragment!==void 0&&(o.fragment=r.fragment),this.router.navigateByUrl(o,r)}}static \u0275fac=function(r){return new(r||e)(I(hT),I(Mt),I(On),I(Oe,8))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),gT=(e,n,t)=>{if(n!==!1){if(t!==void 0)return t;if(e==="forward"||e==="back")return e;if(e==="root"&&n===!0)return"forward"}},Uy="auto",$y=void 0,qy=(()=>{class e{get(t,r){let o=uf();return o?o.get(t,r):null}getBoolean(t,r){let o=uf();return o?o.getBoolean(t,r):!1}getNumber(t,r){let o=uf();return o?o.getNumber(t,r):0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),mT=new C("USERCONFIG"),uf=()=>{if(typeof window<"u"){let e=window.Ionic;if(e?.config)return e.config}return null},Ka=class{data;constructor(n={}){this.data=n,console.warn("[Ionic Warning]: NavParams has been deprecated in favor of using Angular's input API. Developers should migrate to either the @Input decorator or the Signals-based input API.")}get(n){return this.data[n]}},vT=(()=>{class e{zone=p($);applicationRef=p(Qe);config=p(mT);create(t,r,o){return new df(t,r,this.applicationRef,this.zone,o,this.config.useSetInputAPI??!1)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),df=class{environmentInjector;injector;applicationRef;zone;elementReferenceKey;enableSignalsSupport;elRefMap=new WeakMap;elEventsMap=new WeakMap;constructor(n,t,r,o,i,s){this.environmentInjector=n,this.injector=t,this.applicationRef=r,this.zone=o,this.elementReferenceKey=i,this.enableSignalsSupport=s}attachViewToDom(n,t,r,o){return this.zone.run(()=>new Promise(i=>{let s=g({},r);this.elementReferenceKey!==void 0&&(s[this.elementReferenceKey]=n);let a=yT(this.zone,this.environmentInjector,this.injector,this.applicationRef,this.elRefMap,this.elEventsMap,n,t,s,o,this.elementReferenceKey,this.enableSignalsSupport);i(a)}))}removeViewFromDom(n,t){return this.zone.run(()=>new Promise(r=>{let o=this.elRefMap.get(t);if(o){o.destroy(),this.elRefMap.delete(t);let i=this.elEventsMap.get(t);i&&(i(),this.elEventsMap.delete(t))}r()}))}},yT=(e,n,t,r,o,i,s,a,c,u,l,d)=>{let h=ue.create({providers:CT(c),parent:t}),f=km(a,{environmentInjector:n,elementInjector:h}),v=f.instance,E=f.location.nativeElement;if(c)if(l&&v[l]!==void 0&&console.error(`[Ionic Error]: ${l} is a reserved property when using ${s.tagName.toLowerCase()}. Rename or remove the "${l}" property from ${a.name}.`),d===!0&&f.setInput!==void 0){let R=c,{modal:Pn,popover:tn}=R,Cf=If(R,["modal","popover"]);for(let Ef in Cf)f.setInput(Ef,Cf[Ef]);Pn!==void 0&&Object.assign(v,{modal:Pn}),tn!==void 0&&Object.assign(v,{popover:tn})}else Object.assign(v,c);if(u)for(let Pn of u)E.classList.add(Pn);let T=Zy(e,v,E);return s.appendChild(E),r.attachView(f.hostView),o.set(E,f),i.set(E,T),E},DT=[ec,tc,nc,rc,oc],Zy=(e,n,t)=>e.run(()=>{let r=DT.filter(o=>typeof n[o]=="function").map(o=>{let i=s=>n[o](s.detail);return t.addEventListener(o,i),()=>t.removeEventListener(o,i)});return()=>r.forEach(o=>o())}),zy=new C("NavParamsToken"),CT=e=>[{provide:zy,useValue:e},{provide:Ka,useFactory:ET,deps:[zy]}],ET=e=>new Ka(e),IT=(e,n)=>{let t=e.prototype;n.forEach(r=>{Object.defineProperty(t,r,{get(){return this.el[r]},set(o){this.z.runOutsideAngular(()=>this.el[r]=o)}})})},wT=(e,n)=>{let t=e.prototype;n.forEach(r=>{t[r]=function(){let o=arguments;return this.z.runOutsideAngular(()=>this.el[r].apply(this.el,o))}})},Df=(e,n,t)=>{t.forEach(r=>e[r]=Yr(n,r))};function Ja(e){return function(t){let{defineCustomElementFn:r,inputs:o,methods:i}=e;return r!==void 0&&r(),o&&IT(t,o),i&&wT(t,i),t}}var _T=["alignment","animated","arrow","keepContentsMounted","backdropDismiss","cssClass","dismissOnSelect","enterAnimation","event","focusTrap","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","translucent","trigger","triggerAction","reference","size","side"],bT=["present","dismiss","onDidDismiss","onWillDismiss"],hz=(()=>{let e=class ff{z;template;isCmpOpen=!1;el;constructor(t,r,o){this.z=o,this.el=r.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),Df(this,this.el,["ionPopoverDidPresent","ionPopoverWillPresent","ionPopoverWillDismiss","ionPopoverDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||ff)(m(Ke),m(G),m($))};static \u0275dir=L({type:ff,selectors:[["ion-popover"]],contentQueries:function(r,o,i){if(r&1&&wo(i,Ze,5),r&2){let s;Er(s=Ir())&&(o.template=s.first)}},inputs:{alignment:"alignment",animated:"animated",arrow:"arrow",keepContentsMounted:"keepContentsMounted",backdropDismiss:"backdropDismiss",cssClass:"cssClass",dismissOnSelect:"dismissOnSelect",enterAnimation:"enterAnimation",event:"event",focusTrap:"focusTrap",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger",triggerAction:"triggerAction",reference:"reference",size:"size",side:"side"},standalone:!1})};return e=qr([Ja({inputs:_T,methods:bT})],e),e})(),ST=["animated","keepContentsMounted","backdropBreakpoint","backdropDismiss","breakpoints","canDismiss","cssClass","enterAnimation","expandToScroll","event","focusTrap","handle","handleBehavior","initialBreakpoint","isOpen","keyboardClose","leaveAnimation","mode","presentingElement","showBackdrop","translucent","trigger"],MT=["present","dismiss","onDidDismiss","onWillDismiss","setCurrentBreakpoint","getCurrentBreakpoint"],pz=(()=>{let e=class hf{z;template;isCmpOpen=!1;el;constructor(t,r,o){this.z=o,this.el=r.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),Df(this,this.el,["ionModalDidPresent","ionModalWillPresent","ionModalWillDismiss","ionModalDidDismiss","ionBreakpointDidChange","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||hf)(m(Ke),m(G),m($))};static \u0275dir=L({type:hf,selectors:[["ion-modal"]],contentQueries:function(r,o,i){if(r&1&&wo(i,Ze,5),r&2){let s;Er(s=Ir())&&(o.template=s.first)}},inputs:{animated:"animated",keepContentsMounted:"keepContentsMounted",backdropBreakpoint:"backdropBreakpoint",backdropDismiss:"backdropDismiss",breakpoints:"breakpoints",canDismiss:"canDismiss",cssClass:"cssClass",enterAnimation:"enterAnimation",expandToScroll:"expandToScroll",event:"event",focusTrap:"focusTrap",handle:"handle",handleBehavior:"handleBehavior",initialBreakpoint:"initialBreakpoint",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",presentingElement:"presentingElement",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger"},standalone:!1})};return e=qr([Ja({inputs:ST,methods:MT})],e),e})(),TT=(e,n,t)=>t==="root"?Yy(e,n):t==="forward"?AT(e,n):NT(e,n),Yy=(e,n)=>(e=e.filter(t=>t.stackId!==n.stackId),e.push(n),e),AT=(e,n)=>(e.indexOf(n)>=0?e=e.filter(r=>r.stackId!==n.stackId||r.id<=n.id):e.push(n),e),NT=(e,n)=>e.indexOf(n)>=0?e.filter(r=>r.stackId!==n.stackId||r.id<=n.id):Yy(e,n),pf=(e,n)=>{let t=e.createUrlTree(["."],{relativeTo:n});return e.serializeUrl(t)},Qy=(e,n)=>n?e.stackId!==n.stackId:!0,RT=(e,n)=>{if(!e)return;let t=Ky(n);for(let r=0;r<t.length;r++){if(r>=e.length)return t[r];if(t[r]!==e[r])return}},Ky=e=>e.split("/").map(n=>n.trim()).filter(n=>n!==""),Jy=e=>{e&&(e.ref.destroy(),e.unlistenEvents())},gf=class{containerEl;router;navCtrl;zone;location;views=[];runningTask;skipTransition=!1;tabsPrefix;activeView;nextId=0;constructor(n,t,r,o,i,s){this.containerEl=t,this.router=r,this.navCtrl=o,this.zone=i,this.location=s,this.tabsPrefix=n!==void 0?Ky(n):void 0}createView(n,t){let r=pf(this.router,t),o=n?.location?.nativeElement,i=Zy(this.zone,n.instance,o);return{id:this.nextId++,stackId:RT(this.tabsPrefix,r),unlistenEvents:i,element:o,ref:n,url:r}}getExistingView(n){let t=pf(this.router,n),r=this.views.find(o=>o.url===t);return r&&r.ref.changeDetectorRef.reattach(),r}setActive(n){let t=this.navCtrl.consumeTransition(),{direction:r,animation:o,animationBuilder:i}=t,s=this.activeView,a=Qy(n,s);a&&(r="back",o=void 0);let c=this.views.slice(),u,l=this.router;l.getCurrentNavigation?u=l.getCurrentNavigation():l.navigations?.value&&(u=l.navigations.value),u?.extras?.replaceUrl&&this.views.length>0&&this.views.splice(-1,1);let d=this.views.includes(n),h=this.insertView(n,r);d||n.ref.changeDetectorRef.detectChanges();let f=n.animationBuilder;return i===void 0&&r==="back"&&!a&&f!==void 0&&(i=f),s&&(s.animationBuilder=i),this.zone.runOutsideAngular(()=>this.wait(()=>(s&&s.ref.changeDetectorRef.detach(),n.ref.changeDetectorRef.reattach(),this.transition(n,s,o,this.canGoBack(1),!1,i).then(()=>xT(n,h,c,this.location,this.zone)).then(()=>({enteringView:n,direction:r,animation:o,tabSwitch:a})))))}canGoBack(n,t=this.getActiveStackId()){return this.getStack(t).length>n}pop(n,t=this.getActiveStackId()){return this.zone.run(()=>{let r=this.getStack(t);if(r.length<=n)return Promise.resolve(!1);let o=r[r.length-n-1],i=o.url,s=o.savedData;if(s){let c=s.get("primary");c?.route?._routerState?.snapshot.url&&(i=c.route._routerState.snapshot.url)}let{animationBuilder:a}=this.navCtrl.consumeTransition();return this.navCtrl.navigateBack(i,A(g({},o.savedExtras),{animation:a})).then(()=>!0)})}startBackTransition(){let n=this.activeView;if(n){let t=this.getStack(n.stackId),r=t[t.length-2],o=r.animationBuilder;return this.wait(()=>this.transition(r,n,"back",this.canGoBack(2),!0,o))}return Promise.resolve()}endBackTransition(n){n?(this.skipTransition=!0,this.pop(1)):this.activeView&&Xy(this.activeView,this.views,this.views,this.location,this.zone)}getLastUrl(n){let t=this.getStack(n);return t.length>0?t[t.length-1]:void 0}getRootUrl(n){let t=this.getStack(n);return t.length>0?t[0]:void 0}getActiveStackId(){return this.activeView?this.activeView.stackId:void 0}getActiveView(){return this.activeView}hasRunningTask(){return this.runningTask!==void 0}destroy(){this.containerEl=void 0,this.views.forEach(Jy),this.activeView=void 0,this.views=[]}getStack(n){return this.views.filter(t=>t.stackId===n)}insertView(n,t){return this.activeView=n,this.views=TT(this.views,n,t),this.views.slice()}transition(n,t,r,o,i,s){if(this.skipTransition)return this.skipTransition=!1,Promise.resolve(!1);if(t===n)return Promise.resolve(!1);let a=n?n.element:void 0,c=t?t.element:void 0,u=this.containerEl;return a&&a!==c&&(a.classList.add("ion-page"),a.classList.add("ion-page-invisible"),u.commit)?u.commit(a,c,{duration:r===void 0?0:void 0,direction:r,showGoBack:o,progressAnimation:i,animationBuilder:s}):Promise.resolve(!1)}wait(n){return se(this,null,function*(){this.runningTask!==void 0&&(yield this.runningTask,this.runningTask=void 0);let t=this.runningTask=n();return t.finally(()=>this.runningTask=void 0),t})}},xT=(e,n,t,r,o)=>typeof requestAnimationFrame=="function"?new Promise(i=>{requestAnimationFrame(()=>{Xy(e,n,t,r,o),i()})}):Promise.resolve(),Xy=(e,n,t,r,o)=>{o.run(()=>t.filter(i=>!n.includes(i)).forEach(Jy)),n.forEach(i=>{let a=r.path().split("?")[0].split("#")[0];if(i!==e&&i.url!==a){let c=i.element;c.setAttribute("aria-hidden","true"),c.classList.add("ion-page-hidden"),i.ref.changeDetectorRef.detach()}})},OT=(()=>{class e{parentOutlet;nativeEl;activatedView=null;tabsPrefix;_swipeGesture;stackCtrl;proxyMap=new WeakMap;currentActivatedRoute$=new J(null);activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=M;stackWillChange=new z;stackDidChange=new z;activateEvents=new z;deactivateEvents=new z;parentContexts=p(Rt);location=p(Ve);environmentInjector=p(Z);inputBinder=p(eD,{optional:!0});supportsBindingToComponentInputs=!0;config=p(qy);navCtrl=p(ii);set animation(t){this.nativeEl.animation=t}set animated(t){this.nativeEl.animated=t}set swipeGesture(t){this._swipeGesture=t,this.nativeEl.swipeHandler=t?{canStart:()=>this.stackCtrl.canGoBack(1)&&!this.stackCtrl.hasRunningTask(),onStart:()=>this.stackCtrl.startBackTransition(),onEnd:r=>this.stackCtrl.endBackTransition(r)}:void 0}constructor(t,r,o,i,s,a,c,u){this.parentOutlet=u,this.nativeEl=i.nativeElement,this.name=t||M,this.tabsPrefix=r==="true"?pf(s,c):void 0,this.stackCtrl=new gf(this.tabsPrefix,this.nativeEl,s,this.navCtrl,a,o),this.parentContexts.onChildOutletCreated(this.name,this)}ngOnDestroy(){this.stackCtrl.destroy(),this.inputBinder?.unsubscribeFromRouteData(this)}getContext(){return this.parentContexts.getContext(this.name)}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(!this.activated){let t=this.getContext();t?.route&&this.activateWith(t.route,t.injector)}new Promise(t=>Ot(this.nativeEl,t)).then(()=>{this._swipeGesture===void 0&&(this.swipeGesture=this.config.getBoolean("swipeBackEnabled",this.nativeEl.mode==="ios"))})}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new Error("Outlet is not activated");return this.activated.instance}get activatedRoute(){if(!this.activated)throw new Error("Outlet is not activated");return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){throw new Error("incompatible reuse strategy")}attach(t,r){throw new Error("incompatible reuse strategy")}deactivate(){if(this.activated){if(this.activatedView){let r=this.getContext();this.activatedView.savedData=new Map(r.children.contexts);let o=this.activatedView.savedData.get("primary");if(o&&r.route&&(o.route=g({},r.route)),this.activatedView.savedExtras={},r.route){let i=r.route.snapshot;this.activatedView.savedExtras.queryParams=i.queryParams,this.activatedView.savedExtras.fragment=i.fragment}}let t=this.component;this.activatedView=null,this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new Error("Cannot activate an already activated outlet");this._activatedRoute=t;let o,i=this.stackCtrl.getExistingView(t);if(i){o=this.activated=i.ref;let a=i.savedData;if(a){let c=this.getContext();c.children.contexts=a}this.updateActivatedRouteProxy(o.instance,t)}else{let a=t._futureSnapshot,c=this.parentContexts.getOrCreateContext(this.name).children,u=new J(null),l=this.createActivatedRouteProxy(u,t),d=new mf(l,c,this.location.injector),h=a.routeConfig.component??a.component;o=this.activated=this.outletContent.createComponent(h,{index:this.outletContent.length,injector:d,environmentInjector:r??this.environmentInjector}),u.next(o.instance),i=this.stackCtrl.createView(this.activated,t),this.proxyMap.set(o.instance,l),this.currentActivatedRoute$.next({component:o.instance,activatedRoute:t})}this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activatedView=i,this.navCtrl.setTopOutlet(this);let s=this.stackCtrl.getActiveView();this.stackWillChange.emit({enteringView:i,tabSwitch:Qy(i,s)}),this.stackCtrl.setActive(i).then(a=>{this.activateEvents.emit(o.instance),this.stackDidChange.emit(a)})}canGoBack(t=1,r){return this.stackCtrl.canGoBack(t,r)}pop(t=1,r){return this.stackCtrl.pop(t,r)}getLastUrl(t){let r=this.stackCtrl.getLastUrl(t);return r?r.url:void 0}getLastRouteView(t){return this.stackCtrl.getLastUrl(t)}getRootView(t){return this.stackCtrl.getRootUrl(t)}getActiveStackId(){return this.stackCtrl.getActiveStackId()}createActivatedRouteProxy(t,r){let o=new _e;return o._futureSnapshot=r._futureSnapshot,o._routerState=r._routerState,o.snapshot=r.snapshot,o.outlet=r.outlet,o.component=r.component,o._paramMap=this.proxyObservable(t,"paramMap"),o._queryParamMap=this.proxyObservable(t,"queryParamMap"),o.url=this.proxyObservable(t,"url"),o.params=this.proxyObservable(t,"params"),o.queryParams=this.proxyObservable(t,"queryParams"),o.fragment=this.proxyObservable(t,"fragment"),o.data=this.proxyObservable(t,"data"),o}proxyObservable(t,r){return t.pipe(ae(o=>!!o),le(o=>this.currentActivatedRoute$.pipe(ae(i=>i!==null&&i.component===o),le(i=>i&&i.activatedRoute[r]),_c())))}updateActivatedRouteProxy(t,r){let o=this.proxyMap.get(t);if(!o)throw new Error("Could not find activated route proxy for view");o._futureSnapshot=r._futureSnapshot,o._routerState=r._routerState,o.snapshot=r.snapshot,o.outlet=r.outlet,o.component=r.component,this.currentActivatedRoute$.next({component:t,activatedRoute:r})}static \u0275fac=function(r){return new(r||e)(Yt("name"),Yt("tabs"),m(Mt),m(G),m(Oe),m($),m(_e),m(e,12))};static \u0275dir=L({type:e,selectors:[["ion-router-outlet"]],inputs:{animated:"animated",animation:"animation",mode:"mode",swipeGesture:"swipeGesture",name:"name"},outputs:{stackWillChange:"stackWillChange",stackDidChange:"stackDidChange",activateEvents:"activate",deactivateEvents:"deactivate"},exportAs:["outlet"],standalone:!1})}return e})(),mf=class{route;childContexts;parent;constructor(n,t,r){this.route=n,this.childContexts=t,this.parent=r}get(n,t){return n===_e?this.route:n===Rt?this.childContexts:this.parent.get(n,t)}},eD=new C(""),kT=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(t){this.unsubscribeFromRouteData(t),this.subscribeToRouteData(t)}unsubscribeFromRouteData(t){this.outletDataSubscriptions.get(t)?.unsubscribe(),this.outletDataSubscriptions.delete(t)}subscribeToRouteData(t){let{activatedRoute:r}=t,o=Kn([r.queryParams,r.params,r.data]).pipe(le(([i,s,a],c)=>(a=g(g(g({},i),s),a),c===0?_(a):Promise.resolve(a)))).subscribe(i=>{if(!t.isActivated||!t.activatedComponentRef||t.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(t);return}let s=fd(r.component);if(!s){this.unsubscribeFromRouteData(t);return}for(let{templateName:a}of s.inputs)t.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(t,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),gz=()=>({provide:eD,useFactory:FT,deps:[Oe]});function FT(e){return e?.componentInputBindingEnabled?new kT:null}var PT=["color","defaultHref","disabled","icon","mode","routerAnimation","text","type"],mz=(()=>{let e=class vf{routerOutlet;navCtrl;config;r;z;el;constructor(t,r,o,i,s,a){this.routerOutlet=t,this.navCtrl=r,this.config=o,this.r=i,this.z=s,a.detach(),this.el=this.r.nativeElement}onClick(t){let r=this.defaultHref||this.config.get("backButtonDefaultHref");this.routerOutlet?.canGoBack()?(this.navCtrl.setDirection("back",void 0,void 0,this.routerAnimation),this.routerOutlet.pop(),t.preventDefault()):r!=null&&(this.navCtrl.navigateBack(r,{animation:this.routerAnimation}),t.preventDefault())}static \u0275fac=function(r){return new(r||vf)(m(OT,8),m(ii),m(qy),m(G),m($),m(Ke))};static \u0275dir=L({type:vf,hostBindings:function(r,o){r&1&&ye("click",function(s){return o.onClick(s)})},inputs:{color:"color",defaultHref:"defaultHref",disabled:"disabled",icon:"icon",mode:"mode",routerAnimation:"routerAnimation",text:"text",type:"type"},standalone:!1})};return e=qr([Ja({inputs:PT})],e),e})(),vz=(()=>{class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(t,r,o,i,s){this.locationStrategy=t,this.navCtrl=r,this.elementRef=o,this.router=i,this.routerLink=s}ngOnInit(){this.updateTargetUrlAndHref(),this.updateTabindex()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTabindex(){let t=["ION-BACK-BUTTON","ION-BREADCRUMB","ION-BUTTON","ION-CARD","ION-FAB-BUTTON","ION-ITEM","ION-ITEM-OPTION","ION-MENU-BUTTON","ION-SEGMENT-BUTTON","ION-TAB-BUTTON"],r=this.elementRef.nativeElement;t.includes(r.tagName)&&r.getAttribute("tabindex")==="0"&&r.removeAttribute("tabindex")}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){let t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(t){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation),t.preventDefault()}static \u0275fac=function(r){return new(r||e)(m(St),m(ii),m(G),m(Oe),m(Ko,8))};static \u0275dir=L({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],hostBindings:function(r,o){r&1&&ye("click",function(s){return o.onClick(s)})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},standalone:!1,features:[we]})}return e})(),yz=(()=>{class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(t,r,o,i,s){this.locationStrategy=t,this.navCtrl=r,this.elementRef=o,this.router=i,this.routerLink=s}ngOnInit(){this.updateTargetUrlAndHref()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){let t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation)}static \u0275fac=function(r){return new(r||e)(m(St),m(ii),m(G),m(Oe),m(Ko,8))};static \u0275dir=L({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],hostBindings:function(r,o){r&1&&ye("click",function(){return o.onClick()})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},standalone:!1,features:[we]})}return e})(),LT=["animated","animation","root","rootParams","swipeGesture"],VT=["push","insert","insertPages","pop","popTo","popToRoot","removeIndex","setRoot","setPages","getActive","getByIndex","canGoBack","getPrevious"],Dz=(()=>{let e=class yf{z;el;constructor(t,r,o,i,s,a){this.z=s,a.detach(),this.el=t.nativeElement,t.nativeElement.delegate=i.create(r,o),Df(this,this.el,["ionNavDidChange","ionNavWillChange"])}static \u0275fac=function(r){return new(r||yf)(m(G),m(Z),m(ue),m(vT),m($),m(Ke))};static \u0275dir=L({type:yf,inputs:{animated:"animated",animation:"animation",root:"root",rootParams:"rootParams",swipeGesture:"swipeGesture"},standalone:!1})};return e=qr([Ja({inputs:LT,methods:VT})],e),e})(),Cz=(()=>{class e{navCtrl;tabsInner;ionTabsWillChange=new z;ionTabsDidChange=new z;tabBarSlot="bottom";hasTab=!1;selectedTab;leavingTab;constructor(t){this.navCtrl=t}ngAfterViewInit(){let t=this.tabs.length>0?this.tabs.first:void 0;t&&(this.hasTab=!0,this.setActiveTab(t.tab),this.tabSwitch())}ngAfterContentInit(){this.detectSlotChanges()}ngAfterContentChecked(){this.detectSlotChanges()}onStackWillChange({enteringView:t,tabSwitch:r}){let o=t.stackId;r&&o!==void 0&&this.ionTabsWillChange.emit({tab:o})}onStackDidChange({enteringView:t,tabSwitch:r}){let o=t.stackId;r&&o!==void 0&&(this.tabBar&&(this.tabBar.selectedTab=o),this.ionTabsDidChange.emit({tab:o}))}select(t){let r=typeof t=="string",o=r?t:t.detail.tab;if(this.hasTab){this.setActiveTab(o),this.tabSwitch();return}let i=this.outlet.getActiveStackId()===o,s=`${this.outlet.tabsPrefix}/${o}`;if(r||t.stopPropagation(),i){let a=this.outlet.getActiveStackId();if(this.outlet.getLastRouteView(a)?.url===s)return;let u=this.outlet.getRootView(o),l=u&&s===u.url&&u.savedExtras;return this.navCtrl.navigateRoot(s,A(g({},l),{animated:!0,animationDirection:"back"}))}else{let a=this.outlet.getLastRouteView(o),c=a?.url||s,u=a?.savedExtras;return this.navCtrl.navigateRoot(c,A(g({},u),{animated:!0,animationDirection:"back"}))}}setActiveTab(t){let o=this.tabs.find(i=>i.tab===t);if(!o){console.error(`[Ionic Error]: Tab with id: "${t}" does not exist`);return}this.leavingTab=this.selectedTab,this.selectedTab=o,this.ionTabsWillChange.emit({tab:t}),o.el.active=!0}tabSwitch(){let{selectedTab:t,leavingTab:r}=this;this.tabBar&&t&&(this.tabBar.selectedTab=t.tab),r?.tab!==t?.tab&&r?.el&&(r.el.active=!1),t&&this.ionTabsDidChange.emit({tab:t.tab})}getSelected(){return this.hasTab?this.selectedTab?.tab:this.outlet.getActiveStackId()}detectSlotChanges(){this.tabBars.forEach(t=>{let r=t.el.getAttribute("slot");r!==this.tabBarSlot&&(this.tabBarSlot=r,this.relocateTabBar())})}relocateTabBar(){let t=this.tabBar.el;this.tabBarSlot==="top"?this.tabsInner.nativeElement.before(t):this.tabsInner.nativeElement.after(t)}static \u0275fac=function(r){return new(r||e)(m(ii))};static \u0275dir=L({type:e,selectors:[["ion-tabs"]],viewQuery:function(r,o){if(r&1&&Xl(fT,7,G),r&2){let i;Er(i=Ir())&&(o.tabsInner=i.first)}},hostBindings:function(r,o){r&1&&ye("ionTabButtonClick",function(s){return o.select(s)})},outputs:{ionTabsWillChange:"ionTabsWillChange",ionTabsDidChange:"ionTabsDidChange"},standalone:!1})}return e})(),jT=e=>typeof __zone_symbol__requestAnimationFrame=="function"?__zone_symbol__requestAnimationFrame(e):typeof requestAnimationFrame=="function"?requestAnimationFrame(e):setTimeout(e),Ez=(()=>{class e{injector;elementRef;onChange=()=>{};onTouched=()=>{};lastValue;statusChanges;constructor(t,r){this.injector=t,this.elementRef=r}writeValue(t){this.elementRef.nativeElement.value=this.lastValue=t,oi(this.elementRef)}handleValueChange(t,r){t===this.elementRef.nativeElement&&(r!==this.lastValue&&(this.lastValue=r,this.onChange(r)),oi(this.elementRef))}_handleBlurEvent(t){t===this.elementRef.nativeElement?(this.onTouched(),oi(this.elementRef)):t.closest("ion-radio-group")===this.elementRef.nativeElement&&this.onTouched()}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.elementRef.nativeElement.disabled=t}ngOnDestroy(){this.statusChanges&&this.statusChanges.unsubscribe()}ngAfterViewInit(){let t;try{t=this.injector.get(kn)}catch{}if(!t)return;t.statusChanges&&(this.statusChanges=t.statusChanges.subscribe(()=>oi(this.elementRef)));let r=t.control;r&&["markAsTouched","markAllAsTouched","markAsUntouched","markAsDirty","markAsPristine"].forEach(i=>{if(typeof r[i]<"u"){let s=r[i].bind(r);r[i]=(...a)=>{s(...a),oi(this.elementRef)}}})}static \u0275fac=function(r){return new(r||e)(m(ue),m(G))};static \u0275dir=L({type:e,hostBindings:function(r,o){r&1&&ye("ionBlur",function(s){return o._handleBlurEvent(s.target)})},standalone:!1})}return e})(),oi=e=>{jT(()=>{let n=e.nativeElement,t=n.value!=null&&n.value.toString().length>0,r=BT(n);lf(n,r);let o=n.closest("ion-item");o&&(t?lf(o,[...r,"item-has-value"]):lf(o,r))})},BT=e=>{let n=e.classList,t=[];for(let r=0;r<n.length;r++){let o=n.item(r);o!==null&&HT(o,"ng-")&&t.push(`ion-${o.substring(3)}`)}return t},lf=(e,n)=>{let t=e.classList;t.remove("ion-valid","ion-invalid","ion-touched","ion-untouched","ion-dirty","ion-pristine"),t.add(...n)},HT=(e,n)=>e.substring(0,n.length)===n,Gy=class{shouldDetach(n){return!1}shouldAttach(n){return!1}store(n,t){}retrieve(n){return null}shouldReuseRoute(n,t){if(n.routeConfig!==t.routeConfig)return!1;let r=n.params,o=t.params,i=Object.keys(r),s=Object.keys(o);if(i.length!==s.length)return!1;for(let a of i)if(o[a]!==r[a])return!1;return!0}},Wy=class{ctrl;constructor(n){this.ctrl=n}create(n){return this.ctrl.create(n||{})}dismiss(n,t,r){return this.ctrl.dismiss(n,t,r)}getTop(){return this.ctrl.getTop()}};export{qr as a,Yr as b,ke as c,D as d,nt as e,p as f,Ki as g,Z as h,Gh as i,Wh as j,ue as k,ee as l,lt as m,Yt as n,G as o,fg as p,IE as q,m as r,Ve as s,Ul as t,wt as u,L as v,Ne as w,om as x,$ as y,lm as z,Tn as A,hm as B,Ql as C,Kl as D,Js as E,Jl as F,Xs as G,pm as H,gm as I,Tw as J,Aw as K,ye as L,xw as M,kw as N,Fw as O,wo as P,Xl as Q,Er as R,Ir as S,Xw as T,ym as U,ed as V,Dm as W,_t as X,a_ as Y,Ke as Z,Mt as _,zm as $,z_ as aa,G_ as ba,W_ as ca,Gm as da,ib as ea,_e as fa,zv as ga,Oe as ha,HS as ia,$S as ja,YS as ka,c2 as la,Br as ma,af as na,YM as oa,h2 as pa,v2 as qa,y2 as ra,D2 as sa,xy as ta,JM as ua,XM as va,T2 as wa,A2 as xa,N2 as ya,R2 as za,x2 as Aa,Fy as Ba,cT as Ca,O2 as Da,k2 as Ea,dT as Fa,za as Ga,iy as Ha,Ga as Ia,J$ as Ja,X$ as Ka,t2 as La,IM as Ma,_M as Na,n2 as Oa,r2 as Pa,NM as Qa,xM as Ra,o2 as Sa,i2 as Ta,ii as Ua,qy as Va,mT as Wa,vT as Xa,Ja as Ya,hz as Za,pz as _a,OT as $a,gz as ab,mz as bb,vz as cb,yz as db,Dz as eb,Cz as fb,jT as gb,Ez as hb,oi as ib,Gy as jb,Wy as kb};
